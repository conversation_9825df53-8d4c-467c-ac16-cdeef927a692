import{ah as e,a0 as t,c as o,ai as n,aj as r,ak as a,a7 as l,a8 as s,a9 as i,a2 as d,o as c,d as u,f as m,q as p,e as v,F as h,h as g,g as y,t as f,j as I,L as x,M as b,E as w}from"./index-15d7e8a5.js";import{_ as T}from"./men.d710d635.js";import{_ as k}from"./_plugin-vue_export-helper.1b428a4d.js";import{H as C}from"./HubConnectionBuilder.f43695dc.js";const E=""+new URL("sendImg-icon-7551a013.svg",import.meta.url).href;const R=k({name:"ChatRoom",props:{show:{type:Boolean,default:!1,required:!0},getReadNum:{type:Function,default:()=>{}},user:{type:Object,default:()=>{}},chatRoomType:{type:Number,default:1},conferenceId:{type:String,default:"",required:!0},connectionId:{type:String,default:"",required:!0}},setup(c){const u=e(null);let m=location.origin.replace("gwc","dct")+"/BackDoor/videotalk";location.origin.includes("http://localhost")&&(m="https://dct.test.trialdata.cn/BackDoor/videotalk");const p=(new C).withUrl(m).build(),v=()=>{p.start().then((()=>{p.invoke("JoinGroup",null==c?void 0:c.connectionId)})).catch((e=>{setTimeout((()=>{console.error(e.toString()),v()}),1e3)}))};p.on("ReceiveChatMessage",(async(e,t)=>{var o,n;let r=JSON.parse(t);if(r.userId!=(null==(o=null==c?void 0:c.user)?void 0:o.userId)&&r.chatRoomType===c.chatRoomType){g.onlineCustomerServiceList.push(JSON.parse(t));const e=(null==(n=g.onlineCustomerServiceList.filter((e=>{var t;return e.userId!==(null==(t=null==c?void 0:c.user)?void 0:t.userId)})))?void 0:n.length)||0,o=e?e-g.readNum:0;c.getReadNum(o>0?o:0)}}));const h=e=>{var t;p.invoke("SendMessageToGroup",null==c?void 0:c.connectionId,null==(t=null==c?void 0:c.user)?void 0:t.userId,JSON.stringify(e)).catch((e=>{console.error(e.toString())}))};p.onclose((()=>{setTimeout((()=>{v(),g.getChatRecords()}),1e3)}));const g=t({readNum:0,curUser:c.user,curConference:c.conference,loadImgNum:0,endLoadImgNum:0,showPop:c.show,sendMessagValue:"",chatEntity:{id:"",conferenceId:"",userId:"",userName:"",avatarUrl:"",identity:0,chatTime:"",contentType:0,chatRoomType:0,content:"",contentUrl:"",thumbnail:""},chatImgEntity:{conferenceId:"",userId:"",userName:"",identity:0,contentType:0,chatRoomType:0,patientId:"",checkImageFiles:null,avatarUrl:""},onlineCustomerServiceList:[],closePop:()=>{var e;g.readNum=(null==(e=g.onlineCustomerServiceList.filter((e=>{var t;return e.userId!==(null==(t=null==c?void 0:c.user)?void 0:t.userId)})))?void 0:e.length)||0,c.getReadNum(0),g.showPop=!1},addMyText:()=>{var e,t,n,r;g.sendMessagValue.length>0&&(g.chatEntity.identity=null==(e=null==c?void 0:c.user)?void 0:e.identity,g.chatEntity.conferenceId=null==c?void 0:c.conferenceId,g.chatEntity.contentType=1,g.chatEntity.chatRoomType=null==c?void 0:c.chatRoomType,g.chatEntity.avatarUrl=null==(t=c.user)?void 0:t.avatarUrl,g.chatEntity.content=g.sendMessagValue,g.chatEntity.userId=null==(n=null==c?void 0:c.user)?void 0:n.userId,g.chatEntity.userName=null==(r=null==c?void 0:c.user)?void 0:r.userName,o.insertConferenceChatRecord(g.chatEntity).then((e=>{e&&(h(e),g.sendMessagValue="",g.onlineCustomerServiceList.push(JSON.parse(JSON.stringify(e))),g.scrollAuto())})))},getChatRecords:e=>{o.getConferenceRecords(null==c?void 0:c.conferenceId,{chatRoomType:null==c?void 0:c.chatRoomType}).then((t=>{1===e&&(g.readNum=t.length),g.onlineCustomerServiceList=t.filter((e=>e.chatRoomType===c.chatRoomType)),g.onlineCustomerServiceList.length>0&&(g.onlineCustomerServiceList.forEach((e=>{2===e.contentType&&g.loadImgNum++})),g.scrollAuto())}))},beforeRead:e=>{const t=/(.jpg|.jpeg|.png|.bmp|.gif|.jfif)$/;let o=!0;return Array.isArray(e)?(e.forEach((e=>{t.test(e.type)||(n("图片格式不支持上传"),o=!1)})),e.length>9&&r({type:"warning",message:"一次最多只能上传9张图片"})):t.test(e.type)||(n("图片格式不支持上传"),o=!1),o},afterRead:e=>{Array.isArray(e)?e.map(((e,t)=>{g.sendImage(e.file,t)})):g.sendImage(e.file)},dataURLtoFile:(e,t)=>{const o=e.split(","),n=o[0].match(/:(.*?);/)[1],r=atob(o[1]);let a=r.length;const l=new Uint8Array(a);for(;a--;)l[a]=r.charCodeAt(a);return new File([l],t,{type:n})},sendImage:(e,t)=>{var n,r,a,l,s;g.chatImgEntity.checkImageFiles=e,g.chatImgEntity.conferenceId=null==c?void 0:c.conferenceId,g.chatImgEntity.identity=null==(n=null==c?void 0:c.user)?void 0:n.identity,g.chatImgEntity.contentType=2,g.chatImgEntity.chatRoomType=null==c?void 0:c.chatRoomType,g.chatImgEntity.userId=null==(r=null==c?void 0:c.user)?void 0:r.userId,g.chatImgEntity.userName=null==(a=null==c?void 0:c.user)?void 0:a.userName,g.chatImgEntity.avatarUrl=(null==(l=null==c?void 0:c.user)?void 0:l.avatarUrl)||"",g.chatImgEntity.patientId=null==(s=null==c?void 0:c.user)?void 0:s.patientId,o.insertConferenceImgRecord(g.chatImgEntity).then((e=>{g.onlineCustomerServiceList.push(e),h(e),g.loadImgNum++,g.scrollAuto()}))},handlePreviewPicture:e=>{var t,o;const n=e.target.dataset.msg,r=[];(null==(t=g.onlineCustomerServiceList)?void 0:t.length)&&g.onlineCustomerServiceList.forEach((e=>{e.contentUrl&&r.push(e.contentUrl)})),(null==(o=g.onlineCustomerServiceList)?void 0:o.length)&&(null==r?void 0:r.length)&&(g.myPreview=[...r],r.forEach(((e,t)=>{e!==n||setTimeout((()=>{a({images:g.myPreview,startPosition:t,closeable:!0})}),500)})))},scrollAuto:()=>{l((()=>{const e=u.value,t=e.clientHeight,o=e.scrollHeight;e.scrollTop=o-t}))},loadImg:()=>{g.endLoadImgNum++,g.loadImgNum===g.endLoadImgNum&&g.scrollAuto()},loadImgErr:()=>{g.endLoadImgNum++}});return s((()=>{g.getChatRecords(1),v()})),i((()=>{p.start().then((()=>{p.invoke("LeaveGroup",null==c?void 0:c.connectionId).catch((e=>console.error(e.toString())))})).catch((e=>console.error(e.toString()))),p.stop().catch((e=>console.error("Error while stopping SignalR connection:",e)))})),{...d(g),OnlineCustomerServiceRef:u}}},[["render",function(e,t,o,n,r,a){const l=w("van-divider"),s=w("van-field"),i=w("van-uploader"),d=w("van-popup");return c(),u("div",null,[m(d,{round:"",closeable:"",show:e.showPop,position:"bottom",class:"outer",onClose:e.closePop,"overlay-class":"overlay"},{default:p((()=>[v("p",{class:"title"},"聊天"),m(l,{class:"divederLine"}),v("div",{class:"contentPad",ref:"OnlineCustomerServiceRef"},[(c(!0),u(h,null,g(e.onlineCustomerServiceList,((o,n)=>{var r;return c(),u("div",{key:n,class:"onlineservice-body"},[o.chatTime?(c(),u("div",{key:0,innerHTML:o.chatTime,class:"displayChatTime"},null,8,["innerHTML"])):y("",!0),o.userId!=(null==(r=e.curUser)?void 0:r.userId)?(c(),u("div",{key:1,class:"onlineservice-module"},[v("div",{class:"onlineservice-items"},[v("div",null,[2===(null==o?void 0:o.identity)?(c(),u("div",{key:0,style:{width:"48px",height:"48px","margin-right":"10px","font-size":"12px",color:"#fff","background-color":"#41b592","border-radius":"50%",padding:"5px 0px","box-sizing":"border-box"},class:"centerflex-wrap"},[v("div",{class:"no-wrap"},"公正"),v("div",{class:"no-wrap"},"见证人")])):1===(null==o?void 0:o.identity)?(c(),u("div",{key:1,style:{width:"48px",height:"48px","margin-right":"10px","font-size":"12px",color:"#fff","background-color":"#5860da","border-radius":"50%",padding:"5px 0px","box-sizing":"border-box"},class:"centerflex"},[v("div",{class:"no-wrap"},"监护人")])):(null==o?void 0:o.avatarUrl)?(c(),u("img",{key:2,class:"info-header",src:o.avatarUrl,alt:""},null,8,["src"])):(c(),u("img",{key:3,src:T,class:"info-header"})),v("div",{class:"user-name"},[v("div",{class:"user-name-text"},f(o.userName||""),1)])]),1===o.contentType?(c(),u("p",{key:0},f(o.content),1)):2===o.contentType?(c(),u("p",{key:1},[v("img",{style:{"max-width":"100%"},src:o.content,onClick:t[0]||(t[0]=t=>e.handlePreviewPicture(t)),"data-msg":o.contentUrl,onLoad:t[1]||(t[1]=(...t)=>e.loadImg&&e.loadImg(...t)),onError:t[2]||(t[2]=(...t)=>e.loadImgErr&&e.loadImgErr(...t))},null,40,["src","data-msg"])])):y("",!0)])])):(c(),u("div",{key:2,class:"mysend-module"},[v("div",{class:"mysend-items"},[1===o.contentType?(c(),u("p",{key:0},f(o.content),1)):2===o.contentType?(c(),u("p",{key:1},[v("img",{style:{"max-width":"100%"},src:o.content,onClick:t[3]||(t[3]=t=>e.handlePreviewPicture(t)),"data-msg":o.contentUrl,onLoad:t[4]||(t[4]=(...t)=>e.loadImg&&e.loadImg(...t)),onError:t[5]||(t[5]=(...t)=>e.loadImgErr&&e.loadImgErr(...t))},null,40,["src","data-msg"])])):y("",!0),v("div",{class:"doctor-module"},[2===(null==o?void 0:o.identity)?(c(),u("div",{key:0,style:{width:"48px",height:"48px","margin-right":"10px","font-size":"12px",color:"#fff","background-color":"#41b592","border-radius":"50%",padding:"5px 0px","box-sizing":"border-box"},class:"centerflex-wrap"},[v("div",{class:"no-wrap"},"公正"),v("div",{class:"no-wrap"},"见证人")])):1===(null==o?void 0:o.identity)?(c(),u("div",{key:1,style:{width:"48px",height:"48px","margin-right":"10px","font-size":"12px",color:"#fff","background-color":"#5860da","border-radius":"50%",padding:"5px 0px","box-sizing":"border-box"},class:"centerflex"},[v("div",{class:"no-wrap"},"监护人")])):(null==o?void 0:o.avatarUrl)?(c(),u("img",{key:2,class:"info-header",src:o.avatarUrl,alt:""},null,8,["src"])):(c(),u("img",{key:3,src:T,class:"info-header"})),v("div",{class:"user-name"},[v("div",{class:"user-name-text"},f(o.userName||""),1)])])])]))])})),128))],512),v("div",{class:"chatLine"},[m(s,{center:"",class:"content",modelValue:e.sendMessagValue,"onUpdate:modelValue":t[6]||(t[6]=t=>e.sendMessagValue=t),modelModifiers:{trim:!0},placeholder:"请输入内容",maxlength:200},null,8,["modelValue"]),e.sendMessagValue?y("",!0):(c(),I(i,{key:0,style:{width:"80px",display:"flex"},multiple:"","before-read":e.beforeRead,"after-read":e.afterRead,accept:"image/*","max-size":"'10240 * 1024'","max-count":9},{default:p((()=>[v("img",{style:{width:"25px"},src:E,class:"centerflex"})])),_:1},8,["before-read","after-read"])),x(v("span",{onClick:t[7]||(t[7]=(...t)=>e.addMyText&&e.addMyText(...t)),style:{width:"80px"},class:"centerflex"},"发送",512),[[b,e.sendMessagValue]])])])),_:1},8,["show","onClose"])])}],["__scopeId","data-v-77410490"]]),N=""+new URL("chat-icon-5bab7c68.svg",import.meta.url).href;export{R as C,N as _};
