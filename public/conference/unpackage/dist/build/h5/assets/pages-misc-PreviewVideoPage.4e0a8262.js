import{o,j as e,q as a,u as r,f as n,D as s}from"./index-15d7e8a5.js";import{_ as t}from"./_plugin-vue_export-helper.1b428a4d.js";const l=t({name:"PreviewVideoPage",data:()=>({url:""}),onLoad(o){console.log("onLoad option url",decodeURIComponent(o.url)),this.url=decodeURIComponent(o.url)}},[["render",function(t,l,d,u,c,i){const p=s,f=r;return o(),e(f,{class:"video-container"},{default:a((()=>[n(p,{src:c.url,autoplay:"true",controls:"false"},null,8,["src"])])),_:1})}],["__scopeId","data-v-da371975"]]);export{l as default};
