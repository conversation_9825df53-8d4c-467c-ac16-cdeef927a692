import{_ as a}from"./FacialRecognition.vue_vue_type_style_index_0_scoped_8938bbe6_lang.d44d675b.js";import{o as e,d as s,f as t,e as l,L as i,M as r,t as n,E as c}from"./index-15d7e8a5.js";import{_ as o}from"./_plugin-vue_export-helper.1b428a4d.js";const h=""+new URL("identitySuc-7a56382a.svg",import.meta.url).href,d=""+new URL("identityFail-ad4d6c70.svg",import.meta.url).href;const p=o(a,[["render",function(a,o,p,u,f,v){const m=c("van-nav-bar");return e(),s("div",{class:"facialRecognition"},[t(m,{title:a.myTitle},null,8,["title"]),l("div",null,[i(l("div",{class:"flex-col centerflex-h overflow-auto scrollnone",style:{height:"calc(100vh - 106px)"}},[l("div",{class:"facial-img"},[i(l("img",{src:h,class:"w-full",alt:"已通过"},null,512),[[r,1===a.whetherPsaa]]),i(l("img",{src:d,class:"w-full",alt:"未通过"},null,512),[[r,2===a.whetherPsaa]])]),l("div",{class:"whether-psaa-text"},[i(l("span",null,"通过人脸认证",512),[[r,1===a.whetherPsaa]]),i(l("span",null,"未通过人脸认证",512),[[r,2===a.whetherPsaa]])]),i(l("div",{class:"ft-15-px"},"即将自动跳转",512),[[r,1===a.whetherPsaa]]),i(l("div",{class:"ft-15-px"}," 请确保为 "+n(a.certName)+" 本人操作 ",513),[[r,2===a.whetherPsaa]]),i(l("div",{class:"ft-15-px"},"请确保网络环境",512),[[r,2===a.whetherPsaa]])],512),[[r,0!==a.whetherPsaa]])])])}],["__scopeId","data-v-8938bbe6"]]);export{p as default};
