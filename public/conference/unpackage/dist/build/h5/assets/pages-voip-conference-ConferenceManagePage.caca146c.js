import{al as e,am as n,an as t,o,j as i,q as s,f as r,P as a,n as l,g as c,d,F as p,h as u,af as f,v as h,t as g,ao as m,B as v,i as x,u as y,ap as w,K as C,c as b,l as k,m as A,aq as U,ar as I,O as P,e as T,L as M,M as L,as as F,E as S,at as D,w as _}from"./index-15d7e8a5.js";import{_ as j}from"./_plugin-vue_export-helper.1b428a4d.js";import{M as z,_ as O}from"./men.d710d635.js";import{_ as N,a as H}from"./checkInformedIcon.29557f46.js";import{H as V}from"./HubConnectionBuilder.f43695dc.js";import"./MyPopupShow.vue_vue_type_script_lang.dace47fb.js";const R=j({props:{maskBg:{type:String,default:"rgba(0,0,0,0)"},placement:{type:String,default:"default"},direction:{type:String,default:"column"},x:{type:Number,default:0},y:{type:Number,default:0},modelValue:{type:Boolean,default:!1},popData:{type:Array,default:()=>[]},theme:{type:String,default:"light"},dynamic:{type:Boolean,default:!1},gap:{type:Number,default:20},triangle:{type:Boolean,default:!0}},data:()=>({popupsTop:"0px",popupsLeft:"0px",show:!1,dynPlace:""}),mounted(){this.popupsPosition()},methods:{tapMask(){this.$emit("update:modelValue",!1)},tapItem(e){e.disabled||(this.$emit("tapPopup",e),this.$emit("update:modelValue",!1))},getStatusBar:()=>new Promise(((n,t)=>{e({success:function(e){let t;t=e.statusBarHeight+e.windowTop,n(t)}})})),async popupsPosition(){let e=await this.getStatusBar();return new Promise(((t,o)=>{n().in(this).select(".popups").fields({size:!0},(n=>{let o=n.width,i=n.height,s=this.dynamic?this.dynamicGetY(this.y,this.gap):this.transformRpx(this.y),r=this.dynamic?this.dynamicGetX(this.x,this.gap):this.transformRpx(this.x);switch(s=this.dynamic?this.y+e:this.transformRpx(this.y+e),this.dynPlace="default"==this.placement?this.getPlacement(r,s):this.placement,this.dynPlace){case"top-start":this.popupsTop=`${s+9}px`,this.popupsLeft=r-15+"px";break;case"top-end":this.popupsTop=`${s+9}px`,this.popupsLeft=r+15-o+"px";break;case"bottom-start":this.popupsTop=s-18-i+"px",this.popupsLeft=r-15+"px";break;case"bottom-end":this.popupsTop=s-9-i+"px",this.popupsLeft=r+15-o+"px"}t()})).exec()}))},getPlacement(e,n){let o=t().windowWidth,i=t().windowHeight;return e>o/2&&n>i/2?"bottom-end":e<o/2&&n<i/2?"top-start":e>o/2&&n<i/2?"top-end":e<o/2&&n>i/2?"bottom-start":e>o/2?"top-end":"top-start"},dynamicGetY(e,n){let o=t().windowHeight;return e=o-(e=e<n?n:e)<n?o-n:e},dynamicGetX(e,n){let o=t().windowWidth;return e=o-(e=e<n?n:e)<n?o-n:e},transformRpx:e=>e*t().screenWidth/375},watch:{modelValue:{immediate:!0,handler:async function(e,n){e&&await this.popupsPosition(),this.show=e}},placement:{immediate:!0,handler(e,n){this.dynPlace=e}}}},[["render",function(e,n,t,w,C,b){const k=v,A=x,U=y;return o(),i(U,{class:a(["mask",C.show?"mask-show":""]),style:l({backgroundColor:C.show?t.maskBg:"rgba(0,0,0,0)"}),onClick:b.tapMask},{default:s((()=>[r(U,{class:a(["popups",[t.theme]]),style:l({top:C.popupsTop,left:C.popupsLeft,flexDirection:t.direction})},{default:s((()=>[t.triangle?(o(),i(k,{key:0,class:a(C.dynPlace),style:{width:"0px",height:"0px"}},null,8,["class"])):c("",!0),(o(!0),d(p,null,u(t.popData,((e,n)=>(o(),i(U,{key:n,onClick:f((n=>b.tapItem(e)),["stop"]),class:a(["itemChild view",["row"===t.direction?"solid-right":"solid-bottom",e.disabled?"disabledColor":""]])},{default:s((()=>[e.icon?(o(),i(A,{key:0,class:"image",src:e.icon},null,8,["src"])):c("",!0),h(g(e.title),1)])),_:2},1032,["onClick","class"])))),128)),m(e.$slots,"default",{},void 0,!0)])),_:3},8,["class","style"])])),_:3},8,["class","style","onClick"])}],["__scopeId","data-v-9c7343a7"]]);const B=j({name:"ConferenceManageView",data:()=>({conferenceManager:C,selfUserId:C.selfUserId,isContextMenuShow:!1,currentParticipant:{},showParticipantList:!0,showAudioApplyList:!1,showVideoApplyList:!1,showHandUpList:!1,session:C.session,conferenceParticipantUpdateTime:(new Date).getTime()}),props:{currentPageParticipants:{type:Object,required:!0}},components:{ConferenceParticipantListView:j({name:"ConferenceParticipantListView",props:{participants:{type:Array,required:!0}},inject:["updateWaittingNum"],data:()=>({conferenceManager:C,session:C.session,selfUserId:C.selfUserId,isContextMenuShow:!1,currentParticipant:{},contextMenuItems:[],contextMenuX:0,contextMenuY:0,alertDialogOptions:{},outerHeight:document.documentElement.scrollHeight,showAddconfereePoup:!1,addRoleFlag:1,confereeDoctorList:[],showConfereeDoctorPoup:!1,showAddTutelaLegitimaPoup:!1,legalGuardiansPhone:"",conferenceUsersList:[],removeInfos:{},removePopupFlag:!1,setAsHostLoadingFlag:!1,connectionStartFlag:0,addConferenceUserLoadingFlag:!1,handleAdmittanceFlag:!1,getDoctorConferenceUserLoadingFlag:!1}),mounted(){let e=location.origin.replace("gwc","dct")+"/BackDoor/videotalk";location.origin.includes("http://localhost")&&(e="https://dct.test.trialdata.cn/BackDoor/videotalk");const n=(new V).withUrl(e).build();n.start().then((()=>{var e,t;this.connectionStartFlag=1,n.invoke("JoinGroup",null==(t=null==(e=this.conferenceManager)?void 0:e.conferenceInfo)?void 0:t.conferenceId)})).catch((e=>console.error(e.toString()))),n.on("ReceiveMessage",(async(e,n)=>{6!==e&&7!==e||setTimeout((()=>{this.getConferenceUsersFun()}),1e3)})),this.connection=n,setTimeout((()=>{var e;this.getConferenceUsersFun(),this.session=null==(e=C)?void 0:e.session,this.outerHeight=document.documentElement.scrollHeight}),0)},components:{MyPopupShow:z},watch:{participants:{handler(e,n){this.getConferenceUsersFun()},deep:!0}},methods:{handleAdmittance(e){this.handleAdmittanceFlag||(this.handleAdmittanceFlag=!0,b.postOperateConference({conferenceId:this.conferenceManager.conferenceInfo.id,operateType:12,userId:e.userId,userType:e.userType}).then((()=>{var n;const t=JSON.stringify({userId:e.userId});this.connection.invoke("SendMessage",null==(n=this.conferenceManager.conferenceInfo)?void 0:n.conferenceId,5,t).then((()=>{this.getConferenceUsersFun(),this.handleAdmittanceFlag=!1}))})).catch((()=>this.handleAdmittanceFlag=!1)))},handleSaveRemove(){this.removePopupFlag=!1,this.handleAdmittanceFlag||(this.handleAdmittanceFlag=!0,b.postOperateConference({conferenceId:this.conferenceManager.conferenceInfo.id,operateType:5,userId:this.removeInfos.userId,userType:this.removeInfos.userType}).then((()=>{var e;const n=JSON.stringify({userId:this.removeInfos.userId});this.connection.invoke("SendMessage",null==(e=this.conferenceManager.conferenceInfo)?void 0:e.conferenceId,4,n).then((()=>{this.getConferenceUsersFun(),this.handleAdmittanceFlag=!1}))})).catch((()=>this.handleAdmittanceFlag=!1)))},handleRemove(e){this.removeInfos=e,this.removePopupFlag=!0},handleSetAsHost(e){this.setAsHostLoadingFlag||(this.setAsHostLoadingFlag=!0,b.postOperateConference({conferenceId:this.conferenceManager.conferenceInfo.id,operateType:6,userId:e.userId,userType:0}).then((()=>{var n;if(this.connectionStartFlag){const t=JSON.stringify({owner:e.userId});this.connection.invoke("SendMessage",null==(n=this.conferenceManager.conferenceInfo)?void 0:n.conferenceId,3,t).then((()=>{this.conferenceManager.conferenceInfo.owner=e.userId})).catch((e=>console.error(e.toString())))}this.setAsHostLoadingFlag=!1})).catch((()=>{this.setAsHostLoadingFlag=!1})))},getConferenceUsersFun(){var e,n,t;(null==(n=null==(e=null==this?void 0:this.conferenceManager)?void 0:e.conferenceInfo)?void 0:n.id)&&(!this.session&&(null==(t=C)?void 0:t.session)&&(this.session=C.session),b.getConferenceUsers(this.conferenceManager.conferenceInfo.id).then((e=>{var n,t,o,i;if(this.conferenceUsersList=e,Array.isArray(e)){const n=e.filter((e=>"等候中"===e.joinConferenceType)).length;this.updateWaittingNum(n)}const s=JSON.parse(sessionStorage.getItem("userInfoIM"));(null==(t=null==(n=this.session)?void 0:n.selfUserInfo)?void 0:t.uid)&&this.session.selfUserInfo.uid===(null==(i=null==(o=this.conferenceManager)?void 0:o.conferenceInfo)?void 0:i.owner)&&!this.confereeDoctorList.length&&(null==s?void 0:s.patientId)&&!this.getDoctorConferenceUserLoadingFlag&&(this.getDoctorConferenceUserLoadingFlag=!0,k.getDoctorConferenceUser(s.patientId).then((e=>{(null==e?void 0:e.length)&&(this.confereeDoctorList=this.confereeDoctorList.concat(e.map((e=>(e.directionFlag="right",this.conferenceUsersList.forEach((n=>{-1!==e.mobile.indexOf(n.mobile)&&(e.directionFlag="left")})),e))))),this.getDoctorConferenceUserLoadingFlag=!1})).catch((()=>this.getDoctorConferenceUserLoadingFlag=!1)))})))},handleSaveAddTutelaLegitima(){if(this.conferenceUsersList.find((e=>this.legalGuardiansPhone===e.mobile))||this.confereeDoctorList.find((e=>this.legalGuardiansPhone===e.mobile)))this.$notify({text:"手机号不可重复",type:"warn"});else if(/^[1][3-9][0-9]{9}$/.test(this.legalGuardiansPhone)){const e=[{conferenceId:C.conferenceInfo.id,mobile:this.legalGuardiansPhone,roleName:2===this.addRoleFlag?"监护人":"公正见证人",userName:"",userType:this.addRoleFlag-1}];this.showAddTutelaLegitimaPoup=!1,b.addConferenceUser(e).then((()=>{this.showAddTutelaLegitimaPoup=!1,A({title:"添加成功",icon:"none"}),this.getConferenceUsersFun()}))}},handleSaveConfereeDoctor(){let e=[];this.confereeDoctorList.forEach((n=>{"right"===(null==n?void 0:n.directionFlag)&&n.checked&&(n.directionFlag="left",n.conferenceId=C.conferenceInfo.id,e.push(n))})),(null==e?void 0:e.length)?this.addConferenceUserLoadingFlag||(this.addConferenceUserLoadingFlag=!0,b.addConferenceUser(e).then((()=>{this.showConfereeDoctorPoup=!1,A({title:"添加成功",icon:"none"}),this.getConferenceUsersFun(),this.addConferenceUserLoadingFlag=!1})).catch((()=>{this.addConferenceUserLoadingFlag=!1}))):this.$notify({text:"请选择研究者",type:"warn"})},handleSaveAddconferee(){if(this.addRoleFlag>1)this.legalGuardiansPhone="",this.showAddTutelaLegitimaPoup=!0;else{if(!this.confereeDoctorList.find((e=>"right"===(null==e?void 0:e.directionFlag))))return void this.$notify({text:"没有可选择的研究人员",type:"warn"});this.showConfereeDoctorPoup=!0}this.showAddconfereePoup=!1},handleAddconferee(){var e;(null==(e=this.confereeDoctorList)?void 0:e.length)||this.getConferenceUsersFun(),this.showAddconfereePoup=!0},invite(){let e=this.session,n=C.conferenceInfo,t=new U(e.callId,n.owner,n.conferenceTitle,e.desc,e.startTime,e.audioOnly,!1,e.advance,e.pin);console.log("invite",t,e,n);let o=new F(null,t);this.$forward({forwardType:I.NORMAL,messages:[o]}),this.showParticipantList=!1},requestChangeMode(e){e.uid!==this.selfUserInfo.uid&&this.showAlertDialog({content:e._isAudience?`邀请${this.participantName(e)}参与互动?`:`取消${this.participantName(e)}参与互动?`,onClose:()=>{},onConfirm:()=>{this.session.requestChangeMode(e.uid,!e._isAudience)}})},kickoff(e){console.log(e),console.log(P),P.kickoffParticipant(this.session.callId,e.uid),this.showAlertDialog({showIcon:!0,content:`确认将${this.participantName(e)}移除会议?`,onClose:()=>{},onConfirm:()=>{P.kickoffParticipant(this.session.callId,e.uid)}})},participantName(e){let n="";return n=e.groupAlias?e.groupAlias:e.friendAlias?e.friendAlias:e.displayName?e.displayName:e.name,n},participantDesc(e){var n,t,o;let i="";return(null==e?void 0:e.userId)===(null==(n=C)?void 0:n.selfUserId)&&(i="我，"),(null==e?void 0:e.userId)===(null==(o=null==(t=C)?void 0:t.conferenceInfo)?void 0:o.owner)&&(i+="主持人，"),i+=null==e?void 0:e.roleName.replace(/,/g,"、"),i},buildParticipantContextMenu(e){var n,t;let o=C.selfUserId,i=[];if(!e)return i;i.push({title:"查看用户信息",tag:"aa",handler:()=>{this.showUserCard(e)}}),o===e.uid&&(e._isAudience?(e._isAudioMuted&&i.push({title:"开启音频",handler:()=>{P.switchAudience(!1),P.muteAudio(!1)}}),e._isVideoMuted&&i.push({title:"开启视频",handler:()=>{P.switchAudience(!1),P.muteVideo(!1)}})):(e._isAudioMuted||i.push({title:"关闭音频",handler:()=>{P.muteAudio(!0),e._isVideoMuted&&P.switchAudience(!0)},styleObject:{color:"red"}}),e._isVideoMuted||i.push({title:"关闭视频",handler:()=>{P.muteVideo(!0),e._isAudioMuted&&P.switchAudience(!0)},styleObject:{color:"red"}}),e._isVideoMuted||e._isAudioMuted||i.push({title:"关闭音视频",handler:()=>{P.muteAudio(!0),P.muteVideo(!0),P.switchAudience(!0)},styleObject:{color:"red"}}))),o===(null==(t=null==(n=C)?void 0:n.conferenceInfo)?void 0:t.owner)&&(e.uid!==o&&(e._isAudience||e._isAudioMuted||i.push({title:"取消发言",handler:()=>{C.requestMemberMute(e.uid,!0,!0)}}),e._isAudience||e._isVideoMuted||i.push({title:"关闭摄像头",handler:()=>{C.requestMemberMute(e.uid,!1,!0)}})),e.uid!==o&&i.push({title:" 移除成员",handler:()=>{this.kickoff(e)}}),C.conferenceInfo.focus===e.uid?i.push({title:"取消焦点用户",handler:()=>{C.requestCancelFocus()}}):i.push({title:"设置为焦点用户",handler:()=>{C.requestFocus(e.uid)}})),this.contextMenuItems.length=0,this.contextMenuItems.push(...i)},showContextMenu(e,n){if(this.isContextMenuShow)return this.$refs.menu.close(),this.isContextMenuShow=!1,void(this.currentParticipant={});console.log(e,n),this.buildParticipantContextMenu(n),this.contextMenuX=e.clientX,this.contextMenuY=e.clientY,this.isContextMenuShow=!0,this.currentParticipant=n},showUserCard(e){},onContextMenuItemSelect(e){console.log("onContextMenuItemSelect",e),e.handler&&e.handler()},showAlertDialog(e){this.alertDialogOptions={cancelText:e.cancelText,confirmText:e.confirmText,title:e.title,content:e.content,onConfirm:()=>{e.onConfirm&&e.onConfirm()},onClose:()=>{e.onClose&&e.onClose(),this.alertDialogOptions={}}},this.$refs.alertDialog.open()}}},[["render",function(e,n,t,a,f,m){var v,y,C,b,k,A,U,I,P,F;const _=x,j=S("van-button"),z=S("MyPopupShow"),V=S("van-field"),B=S("van-form"),$=(E=D("chunLei-popups"),G=R,w(E)?G:E);var E,G;const q=S("uni-popup-dialog"),W=S("uni-popup");return o(),d("div",{class:"participant-list-container",ref:"rootContainer"},[T("div",{style:l({height:f.outerHeight?f.outerHeight-94+"px":"",overflow:"auto"})},[T("div",{style:{"font-size":"13px",color:"#5860DA","font-weight":"700",padding:"18px 10px 12px"}}," 参会中 "),c("",!0),(null==(v=f.conferenceUsersList)?void 0:v.length)&&f.conferenceUsersList.filter((e=>"参与"===e.joinConferenceType)).length?(o(),d("ul",{key:1},[(o(!0),d(p,null,u(f.conferenceUsersList.filter((e=>"参与"===e.joinConferenceType)),((e,n)=>{var t,s,r,a,l,p,u,h,v,x,y,w,C,b,k,A;return o(),d("li",{key:n},[T("div",{class:"participant-user"},[T("div",{class:"avatar-container"},[(null==e?void 0:e.avatarUrl)?(o(),i(_,{key:0,style:{"border-radius":"50%"},class:"avatar",src:e.avatarUrl,alt:""},null,8,["src"])):2===(null==e?void 0:e.userType)?(o(),d("div",{key:1,style:{width:"48px",height:"48px","margin-right":"10px","font-size":"12px",color:"#fff","background-color":"#41B592","border-radius":"50%",padding:"5px 0px","box-sizing":"border-box"}},[T("div",{class:"centerflex no-wrap w-full"},"公正"),T("div",{class:"centerflex no-wrap w-full"},"见证人")])):1===(null==e?void 0:e.userType)?(o(),d("div",{key:2,style:{width:"48px",height:"48px","margin-right":"10px","font-size":"12px",color:"#fff","background-color":"#5860DA","border-radius":"50%",padding:"5px 0px","box-sizing":"border-box"},class:"centerflex"},[T("div",{class:"no-wrap"},"监护人")])):(o(),i(_,{key:3,class:"avatar",style:{"border-radius":"50%"},src:O,mode:""}))]),T("div",{class:"name-desc"},[T("p",{class:"single-line name",style:{color:"#333","font-size":"15px"}},g(e.userName),1),T("p",{class:"single-line desc",style:{color:"#9A9A9A","font-size":"12px"}},g(m.participantDesc(e)),1),e.durationOfParticipation?(o(),d("div",{key:0,style:{"font-size":"8px",color:"#9A9A9A"}},"参会时长："+g(e.durationOfParticipation),1)):c("",!0)]),0===(null==e?void 0:e.userType)&&(null==(s=null==(t=f.session)?void 0:t.selfUserInfo)?void 0:s.uid)&&(null==(a=null==(r=f.session)?void 0:r.selfUserInfo)?void 0:a.uid)===(null==(p=null==(l=f.conferenceManager)?void 0:l.conferenceInfo)?void 0:p.owner)&&(null==(h=null==(u=f.conferenceManager)?void 0:u.conferenceInfo)?void 0:h.owner)!==(null==e?void 0:e.userId)?(o(),d("div",{key:0,style:{"font-size":"10px",color:"#fff",background:"#5860DA",padding:"3px 6px","border-radius":"4px"},onClick:n=>m.handleSetAsHost(e)},"设为主持人 ",8,["onClick"])):c("",!0),(null==(x=null==(v=f.session)?void 0:v.selfUserInfo)?void 0:x.uid)&&(null==(w=null==(y=f.session)?void 0:y.selfUserInfo)?void 0:w.uid)===(null==(b=null==(C=f.conferenceManager)?void 0:C.conferenceInfo)?void 0:b.owner)&&(null==(A=null==(k=f.conferenceManager)?void 0:k.conferenceInfo)?void 0:A.owner)!==(null==e?void 0:e.userId)?(o(),d("div",{key:1,class:"centerflex",style:{"font-size":"10px",color:"#fff",background:"#E26375",padding:"2.5px 0px",width:"40px","margin-left":"10px","border-radius":"4px"},onClick:n=>m.handleRemove(e)},"移出",8,["onClick"])):c("",!0)]),n!==f.conferenceUsersList.filter((e=>"参与"===e.joinConferenceType)).length-1?(o(),d("div",{key:0,class:"my-br"})):c("",!0)])})),128))])):c("",!0),(null==(y=f.conferenceUsersList)?void 0:y.length)&&f.conferenceUsersList.filter((e=>"等候中"===e.joinConferenceType)).length?(o(),d("div",{key:2,style:{"font-size":"13px",color:"#5860DA","font-weight":"700",padding:"18px 10px 12px"}}," 等候中 ")):c("",!0),(null==(C=f.conferenceUsersList)?void 0:C.length)&&f.conferenceUsersList.filter((e=>"等候中"===e.joinConferenceType)).length?(o(),d("ul",{key:3},[(o(!0),d(p,null,u(f.conferenceUsersList.filter((e=>"等候中"===e.joinConferenceType)),((e,n)=>{var t,s,r,a,l,p;return o(),d("li",{key:n},[T("div",{class:"participant-user"},[T("div",{class:"avatar-container"},[(null==e?void 0:e.avatarUrl)?(o(),i(_,{key:0,style:{"border-radius":"50%"},class:"avatar",src:e.avatarUrl,alt:""},null,8,["src"])):2===(null==e?void 0:e.userType)?(o(),d("div",{key:1,style:{width:"48px",height:"48px","margin-right":"10px","font-size":"12px",color:"#fff","background-color":"#41B592","border-radius":"50%",padding:"5px 0px","box-sizing":"border-box"}},[T("div",{class:"centerflex no-wrap w-full"},"公正"),T("div",{class:"centerflex no-wrap w-full"},"见证人")])):1===(null==e?void 0:e.userType)?(o(),d("div",{key:2,style:{width:"48px",height:"48px","margin-right":"10px","font-size":"12px",color:"#fff","background-color":"#5860DA","border-radius":"50%",padding:"5px 0px","box-sizing":"border-box"},class:"centerflex"},[T("div",{class:"no-wrap"},"监护人")])):(o(),i(_,{key:3,class:"avatar",style:{"border-radius":"50%"},src:O,mode:""}))]),T("div",{class:"name-desc"},[T("p",{class:"single-line name",style:{color:"#333","font-size":"15px"}},g(e.userName),1),T("p",{class:"single-line desc",style:{color:"#9A9A9A","font-size":"12px"}},g(m.participantDesc(e)),1),e.durationOfParticipation?(o(),d("div",{key:0,style:{"font-size":"8px",color:"#9A9A9A"}},"参会时长："+g(e.durationOfParticipation),1)):c("",!0)]),(null==(s=null==(t=f.session)?void 0:t.selfUserInfo)?void 0:s.uid)&&(null==(a=null==(r=f.session)?void 0:r.selfUserInfo)?void 0:a.uid)===(null==(p=null==(l=f.conferenceManager)?void 0:l.conferenceInfo)?void 0:p.owner)?(o(),d("div",{key:0,class:"centerflex",style:{"font-size":"10px",color:"#fff",background:"#5860DA",padding:"2.5px 0px",width:"40px","margin-left":"10px","border-radius":"4px"},onClick:n=>m.handleAdmittance(e)},"准入",8,["onClick"])):c("",!0)]),n!==f.conferenceUsersList.filter((e=>"参与"===e.joinConferenceType)).length-1?(o(),d("div",{key:0,class:"my-br"})):c("",!0)])})),128))])):c("",!0),(null==(b=f.conferenceUsersList)?void 0:b.length)&&f.conferenceUsersList.filter((e=>"已离会"===e.joinConferenceType)).length?(o(),d("div",{key:4,style:{"font-size":"13px",color:"#5860DA","font-weight":"700",padding:"18px 10px 12px"}}," 已离会 ")):c("",!0),(null==(k=f.conferenceUsersList)?void 0:k.length)&&f.conferenceUsersList.filter((e=>"已离会"===e.joinConferenceType)).length?(o(),d("ul",{key:5},[(o(!0),d(p,null,u(f.conferenceUsersList.filter((e=>"已离会"===e.joinConferenceType)),((e,n)=>(o(),d("li",{key:n},[T("div",{class:"participant-user"},[T("div",{class:"avatar-container"},[(null==e?void 0:e.avatarUrl)?(o(),i(_,{key:0,style:{"border-radius":"50%"},class:"avatar",src:e.avatarUrl,alt:""},null,8,["src"])):2===(null==e?void 0:e.userType)?(o(),d("div",{key:1,style:{width:"48px",height:"48px","margin-right":"10px","font-size":"12px",color:"#fff","background-color":"#41B592","border-radius":"50%",padding:"5px 0px","box-sizing":"border-box"}},[T("div",{class:"centerflex no-wrap w-full"},"公正"),T("div",{class:"centerflex no-wrap w-full"},"见证人")])):1===(null==e?void 0:e.userType)?(o(),d("div",{key:2,style:{width:"48px",height:"48px","margin-right":"10px","font-size":"12px",color:"#fff","background-color":"#5860DA","border-radius":"50%",padding:"5px 0px","box-sizing":"border-box"},class:"centerflex"},[T("div",{class:"no-wrap"},"监护人")])):(o(),i(_,{key:3,class:"avatar",style:{"border-radius":"50%"},src:O,mode:""}))]),T("div",{class:"name-desc"},[T("p",{class:"single-line name",style:{color:"#333","font-size":"15px"}},g(e.userName),1),T("p",{class:"single-line desc",style:{color:"#9A9A9A","font-size":"12px"}},g(m.participantDesc(e)),1)]),e.durationOfParticipation?(o(),d("div",{key:0,style:{"font-size":"8px",color:"#9A9A9A",bottom:"-10px",right:"12px",position:"relative"}},"参会时长："+g(e.durationOfParticipation),1)):c("",!0)]),n!==f.conferenceUsersList.filter((e=>"已离会"===e.joinConferenceType)).length-1?(o(),d("div",{key:0,class:"my-br"})):c("",!0)])))),128))])):c("",!0),(null==(A=f.conferenceUsersList)?void 0:A.length)&&f.conferenceUsersList.filter((e=>"参与"!==e.joinConferenceType&&"已离会"!==e.joinConferenceType&&"等候中"!==e.joinConferenceType)).length?(o(),d("div",{key:6,style:{"font-size":"13px",color:"#5860DA","font-weight":"700",padding:"18px 10px 12px"}}," 未参会 ")):c("",!0),(null==(U=f.conferenceUsersList)?void 0:U.length)&&f.conferenceUsersList.filter((e=>"参与"!==e.joinConferenceType&&"已离会"!==e.joinConferenceType&&"等候中"!==e.joinConferenceType)).length?(o(),d("ul",{key:7},[(o(!0),d(p,null,u((null==(I=f.conferenceUsersList)?void 0:I.length)&&f.conferenceUsersList.filter((e=>"参与"!==e.joinConferenceType&&"已离会"!==e.joinConferenceType&&"等候中"!==e.joinConferenceType)),((e,n)=>(o(),d("li",{key:n},[T("div",{class:"participant-user"},[T("div",{class:"avatar-container"},[(null==e?void 0:e.avatarUrl)?(o(),i(_,{key:0,style:{"border-radius":"50%"},class:"avatar",src:e.avatarUrl,alt:""},null,8,["src"])):2===(null==e?void 0:e.userType)?(o(),d("div",{key:1,style:{width:"48px",height:"48px","margin-right":"10px","font-size":"12px",color:"#fff","background-color":"#41B592","border-radius":"50%",padding:"5px 0px","box-sizing":"border-box"}},[T("div",{class:"centerflex no-wrap w-full"},"公正"),T("div",{class:"centerflex no-wrap w-full"},"见证人")])):1===(null==e?void 0:e.userType)?(o(),d("div",{key:2,style:{width:"48px",height:"48px","margin-right":"10px","font-size":"12px",color:"#fff","background-color":"#5860DA","border-radius":"50%",padding:"5px 0px","box-sizing":"border-box"},class:"centerflex"},[T("div",{class:"no-wrap"},"监护人")])):(o(),i(_,{key:3,class:"avatar",style:{"border-radius":"50%"},src:O,mode:""}))]),T("div",{class:"name-desc"},[T("p",{class:"single-line name",style:{color:"#333","font-size":"15px"}},g(e.userName),1),T("p",{class:"single-line desc",style:{color:"#9A9A9A","font-size":"12px"}},g(m.participantDesc(e)),1)]),e.durationOfParticipation?(o(),d("div",{key:0,style:{"font-size":"8px",color:"#9A9A9A",bottom:"-10px",right:"12px",position:"relative"}},"参会时长："+g(e.durationOfParticipation),1)):c("",!0)]),n!==f.conferenceUsersList.filter((e=>"参与"!==e.joinConferenceType&&"已离会"!==e.joinConferenceType)).length-1?(o(),d("div",{key:0,class:"my-br"})):c("",!0)])))),128))])):c("",!0)],4),(null==(F=null==(P=f.session)?void 0:P.selfUserInfo)?void 0:F.uid)&&f.session.selfUserInfo.uid===f.conferenceManager.conferenceInfo.owner?(o(),i(j,{key:0,style:{width:"100%","border-radius":"0",height:"50px"},type:"primary",onClick:m.handleAddconferee},{default:s((()=>[h("添加参会人员")])),_:1},8,["onClick"])):c("",!0),r(z,{myPopupShow:f.showAddconfereePoup,title:"请选择",cancelText:"取消",saveText:"确认",handleSave:m.handleSaveAddconferee,handleCancel:()=>{f.showAddconfereePoup=!1}},{bodyslot:s((()=>[T("div",{class:"centerflex",style:l([{"border-radius":"4px",padding:"10px 0",width:"90%","box-shadow":"0 1px 12px rgba(0, 0, 0, 0.07)",margin:"15px auto"},1===f.addRoleFlag?{background:"#E9EFFC",color:"#5860da"}:{}]),onClick:n[0]||(n[0]=()=>{f.addRoleFlag=1})}," 研究人员 ",4),T("div",{class:"centerflex",style:l([{"border-radius":"4px",padding:"10px 0",width:"90%","box-shadow":"0 1px 12px rgba(0, 0, 0, 0.07)",margin:"15px auto"},2===f.addRoleFlag?{background:"#E9EFFC",color:"#5860da"}:{}]),onClick:n[1]||(n[1]=()=>{f.addRoleFlag=2})}," 监护人 ",4),T("div",{class:"centerflex",style:l([{"border-radius":"4px",padding:"10px 0",width:"90%","box-shadow":"0 1px 12px rgba(0, 0, 0, 0.07)",margin:"15px auto"},3===f.addRoleFlag?{background:"#E9EFFC",color:"#5860da"}:{}]),onClick:n[2]||(n[2]=()=>{f.addRoleFlag=3})}," 公正见证人 ",4)])),_:1},8,["myPopupShow","handleSave","handleCancel"]),r(z,{myPopupShow:f.showAddTutelaLegitimaPoup,title:2===f.addRoleFlag?"监护人":"公正见证人",cancelText:"取消",saveText:"确认",handleSave:m.handleSaveAddTutelaLegitima,handleCancel:()=>{f.showAddTutelaLegitimaPoup=!1}},{bodyslot:s((()=>[T("div",{style:{padding:"0 16px"}},"该手机号将用于用户注册登录，以及接收会议通知"),r(B,{class:"participant-form"},{default:s((()=>[r(V,{modelValue:f.legalGuardiansPhone,"onUpdate:modelValue":n[3]||(n[3]=e=>f.legalGuardiansPhone=e),modelModifiers:{trim:!0},name:"legalGuardiansPhone",rules:[{required:!0,message:"请输入"},{validator:e=>/^[1][3-9][0-9]{9}$/.test(e),message:"手机格式错误",trigger:"onBlur"}],label:"手机号",maxlength:"11",placeholder:"请输入"},null,8,["modelValue","rules"])])),_:1})])),_:1},8,["myPopupShow","title","handleSave","handleCancel"]),r(z,{myPopupShow:f.showConfereeDoctorPoup,title:"研究人员",cancelText:"取消",saveText:"确认",handleSave:m.handleSaveConfereeDoctor,handleCancel:()=>{f.showConfereeDoctorPoup=!1}},{bodyslot:s((()=>[T("div",{style:l({maxHeight:f.outerHeight-240+"px",overflow:"auto"})},[(o(!0),d(p,null,u(f.confereeDoctorList,((e,n)=>(o(),d("div",{key:n},["right"===(null==e?void 0:e.directionFlag)&&0===(null==e?void 0:e.userType)?(o(),d("div",{key:0,class:"centerflex-h justify-between",style:{padding:"10px"}},[T("div",null,g(e.userName)+"，"+g(e.roleName.replace(/,/g,"、")),1),M(T("img",{style:{width:"15px"},src:N,alt:"",onClick:()=>{e.checked=!(null==e?void 0:e.checked)}},null,8,["onClick"]),[[L,!(null==e?void 0:e.checked)]]),M(T("img",{style:{width:"15px"},src:H,alt:"",onClick:()=>{e.checked=!(null==e?void 0:e.checked)}},null,8,["onClick"]),[[L,null==e?void 0:e.checked]])])):c("",!0)])))),128))],4)])),_:1},8,["myPopupShow","handleSave","handleCancel"]),r(z,{myPopupShow:f.removePopupFlag,title:"提示",cancelText:"取消",saveText:"确认移出",saveClass:"text-D9001B",handleSave:m.handleSaveRemove,handleCancel:()=>{f.removePopupFlag=!1}},{bodyslot:s((()=>{var e,n;return[T("div",{style:l({maxHeight:f.outerHeight-240+"px",overflow:"auto"})},[(null==(n=null==(e=f.conferenceManager)?void 0:e.conferenceInfo)?void 0:n.enableWaitingRoom)?(o(),d("div",{key:0,style:{padding:"20px"}},[T("div",{class:"centerflex"},"被移出的成员将进入等待室中等候，"),T("div",{class:"centerflex"},"是否确认？")])):(o(),d("div",{key:1,class:"centerflex"},"是否确认？"))],4)]})),_:1},8,["myPopupShow","handleSave","handleCancel"]),r($,{modelValue:f.isContextMenuShow,"onUpdate:modelValue":n[4]||(n[4]=e=>f.isContextMenuShow=e),popData:f.contextMenuItems,onTapPopup:m.onContextMenuItemSelect,x:f.contextMenuX,y:f.contextMenuY,direction:"column",theme:"dark",triangle:!1,dynamic:""},null,8,["modelValue","popData","onTapPopup","x","y"]),r(W,{ref:"alertDialog",type:"dialog"},{default:s((()=>[r(q,{cancelText:f.alertDialogOptions.cancelText,confirmText:f.alertDialogOptions.confirmText,title:f.alertDialogOptions.title,onConfirm:f.alertDialogOptions.onConfirm,onClose:f.alertDialogOptions.onClose},null,8,["cancelText","confirmText","title","onConfirm","onClose"])])),_:1},512)],512)}],["__scopeId","data-v-08e43020"]])},mounted(){_.eventEmitter.on("conferenceParticipantUpdated",this.onConferenceParticipantUpdated)},beforeUnmount(){_.eventEmitter.removeListener("conferenceParticipantUpdated",this.onConferenceParticipantUpdated)},methods:{onConferenceParticipantUpdated(){this.conferenceParticipantUpdateTime=(new Date).getTime()},profile2UserInfo(e){let n=_.getUserInfo(e.userId);return n._isAudience=e.audience,n._isHost=this.session.host===e.userId,this.session._isHost=n._isHost,n._isVideoMuted=e.videoMuted,n._isAudioMuted=e.audioMuted,n._volume=0,n._isScreenSharing=e.screenSharing,n}},computed:{handUpMembers(){return this.conferenceManager.handUpMembers},handUpTip(){let e=C.handUpMembers,n=_.getUserInfos(e,""),t=n[0].displayName;return n.length>1&&(t+=" 等"),t+="正在举手",t},participants(){let e=[],n=this.session.getSelfProfile(),t=this.profile2UserInfo(n);t.__conferenceParticipantUpdateTime=this.conferenceParticipantUpdateTime,e.push(t);let o=this.session.getParticipantProfiles();for(const i of o){let n=this.profile2UserInfo(i);e.push(n)}return e},audioApplyUnmuteTip(){let e=C.applyingUnmuteAudioMembers;if(e.length>0){let n=_.getUserInfos(e,""),t=n[0].displayName;return n.length>1&&(t+=" 等"),t+="正在申请解除静音",t}return""},videoApplyUnmuteTip(){let e=C.applyingUnmuteVideoMembers;if(e.length>0){let n=_.getUserInfos(e,""),t=n[0].displayName;return n.length>1&&(t+=" 等"),t+="正在申请开启摄像头",t}return""}},watch:{}},[["render",function(e,n,t,s,r,a){const l=S("ConferenceParticipantListView");return o(),d("div",{class:"conference-manage-view-container",ref:"rootContainer"},[(r.selfUserId,r.conferenceManager.conferenceInfo.owner,c("",!0)),r.showParticipantList?(o(),i(l,{key:1,style:{flex:"1"},participants:t.currentPageParticipants},null,8,["participants"])):c("",!0)],512)}],["__scopeId","data-v-7eb2a9da"]]);export{B as default};
