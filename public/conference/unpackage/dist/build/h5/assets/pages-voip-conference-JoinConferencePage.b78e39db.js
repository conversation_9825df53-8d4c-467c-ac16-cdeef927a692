import{c as e,b as n,m as o,o as t,d as i,e as a,f as c,q as l,i as d,I as s,E as r,v as f}from"./index-15d7e8a5.js";import{d as p}from"./debounce.3401e956.js";import"./MyPopupShow.vue_vue_type_script_lang.dace47fb.js";import{_ as u}from"./_plugin-vue_export-helper.1b428a4d.js";import"./FacialRecognition.vue_vue_type_style_index_0_scoped_8938bbe6_lang.d44d675b.js";import{_ as v}from"./leftArrows.3362d02a.js";/* empty css                                                                           */const h=u({name:"JoinConferenceView",data:()=>({conferenceId:"",password:""}),onShow(){p()},methods:{joinConference(){e.queryConferenceInfo(this.conferenceId,this.password).then((t=>{(null==t?void 0:t.isComplete)?this.$notify({text:"会议已结束",type:"warn"}):t.conferenceId&&e.dctJoinConference({conferenceId:t.id}).then((()=>{const e=JSON.stringify(t);n({url:"/pages/voip/conference/ConferenceInfoPage?conferenceInfo="+e})})).catch((e=>{var n,t,i,a;((null==(n=null==e?void 0:e.data)?void 0:n.detail)||(null==(t=null==e?void 0:e.data)?void 0:t.message))&&o({title:(null==(i=null==e?void 0:e.data)?void 0:i.detail)||(null==(a=null==e?void 0:e.data)?void 0:a.message),icon:"none"})}))})).catch((e=>{console.log("queryConferenceInfo failed",e),this.$notify({text:"获取会议信息失败",type:"warn"})}))},cancel(){n({url:"/pages/voip/conference/ConferencePortalPage"})}},watch:{advance(){this.advance&&(this.audience=!1)}}},[["render",function(e,n,o,p,u,h){const _=d,m=s,y=r("van-button");return t(),i("div",null,[a("div",{class:"nav-title"},[a("div",{style:{width:"18px",height:"18px",overflow:"hidden"},onClick:n[0]||(n[0]=(...e)=>h.cancel&&h.cancel(...e))},[c(_,{style:{width:"100%",height:"100%"},src:v,mode:""})]),a("div",null,"加入会议"),a("div",{style:{width:"18px",height:"18px",overflow:"hidden"}})]),a("div",{class:"join-conference-container"},[a("div",{class:"conf-item"},[a("p",null,"会议号"),c(m,{class:"conf-item input",modelValue:u.conferenceId,"onUpdate:modelValue":n[1]||(n[1]=e=>u.conferenceId=e),type:"text",placeholder:"请输入会议号"},null,8,["modelValue"])]),a("div",{class:"action-container"},[c(y,{style:{width:"100%"},type:"primary",disabled:""===u.conferenceId.trim(),onClick:h.joinConference},{default:l((()=>[f("加入会议")])),_:1},8,["disabled","onClick"])])])])}],["__scopeId","data-v-8494d6cb"]]);export{h as default};
