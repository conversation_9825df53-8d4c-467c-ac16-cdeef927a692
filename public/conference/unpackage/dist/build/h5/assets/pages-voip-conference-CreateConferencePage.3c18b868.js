import{w as e,G as t,l as i,c as o,H as l,b as n,J as s,K as a,m as r,o as c,d,L as h,M as u,e as f,f as p,q as g,g as m,j as v,n as w,F as x,h as y,i as I,N as C,E as P,v as S,t as k,P as T,I as F}from"./index-15d7e8a5.js";import{C as V}from"./conferenceInfo.3f4517e7.js";import{p as b,d as D}from"./debounce.3401e956.js";import{M,_}from"./men.d710d635.js";import{_ as A}from"./leftArrows.3362d02a.js";import{_ as L,a as N}from"./checkInformedIcon.29557f46.js";import{_ as O}from"./_plugin-vue_export-helper.1b428a4d.js";import"./MyPopupShow.vue_vue_type_script_lang.dace47fb.js";const R=O({name:"CreateConferenceView",components:{MyPopupShow:M},data:()=>({title:"",desc:"",endDefault:b(new Date(new Date((new Date).getTime()-6e4*(new Date).getTimezoneOffset()+36e5).toISOString().split(".")[0]),"{y}-{m}-{d} {h}:{i}"),endTime:new Date((new Date).getTime()-6e4*(new Date).getTimezoneOffset()+36e5).toISOString().split(".")[0],notAudience:!1,advance:!1,allowTurnOnMic:!0,enablePassword:!1,password:"",test:[],showTimes:!1,currentDate:"",currentTime:"",loading:!1,outerHeight:document.documentElement.scrollHeight,showSelectTimesPicker:!1,selectTimesColumns:[{text:"15分钟",value:900},{text:"30分钟",value:1800},{text:"45分钟",value:2700},{text:"1小时",value:3600},{text:"2小时",value:7200},{text:"3小时",value:10800}],selectTimesValueColumns:[{text:"15分钟",value:"15分钟"},{text:"30分钟",value:"30分钟"},{text:"45分钟",value:"45分钟"},{text:"1小时",value:"1小时"},{text:"2小时",value:"2小时"},{text:"3小时",value:"3小时"}],selectTimesFieldValue:["1小时"],selectTimesFieldValueStr:"1小时",showSelectMeetingPropertiesPicker:!1,selectMeetingPropertiesfieldValue:"",showSelectIcfPicker:!1,selectIcfFieldValue:"",showSelectInterviewPicker:!1,selectInterviewFieldValue:"",icfInfoId:"",visitId:"",showConfereeFlag:0,showAddconfereePoup:!1,addRoleFlag:1,confereeDoctorList:[],showConfereeDoctorPoup:!1,showAddTutelaLegitimaPoup:!1,legalGuardiansPhone:"",conferenceVisits:[],conferenceStatements:[],ySort:2,identityConfigRes:{skipFaceIdentity:!0},selectMeetingPropertiescolumns:[]}),created(){const i=JSON.parse(sessionStorage.getItem("userInfoIM")),o=e.getUserDisplayName(t("userId"));this.title=o?`${o}的快速会议${null==i?void 0:i.patientNo}`:`快速会议${null==i?void 0:i.patientNo}`},mounted(){const e=JSON.parse(sessionStorage.getItem("userInfoIM"));i.getConferenceStatements(e.patientId).then((t=>{this.conferenceStatements=t,(null==t?void 0:t.length)&&(this.selectMeetingPropertiescolumns[0]={text:"知情会议",value:"知情会议"}),i.getConferenceVisits(e.patientId).then((e=>{this.conferenceVisits=e,(null==e?void 0:e.length)&&(this.selectMeetingPropertiescolumns[this.selectMeetingPropertiescolumns.length]={text:"访视会议",value:"访视会议"})}))})).catch((()=>{i.getConferenceVisits(e.patientId).then((e=>{this.conferenceVisits=e,(null==e?void 0:e.length)&&this.selectMeetingPropertiescolumns.push({text:"访视会议",value:"访视会议"})}))})),i.getCurrentConferenceUserInfo(e.patientId).then((e=>{e.map(((e,t)=>{e.ySort=t+1})),this.confereeDoctorList=e})),setTimeout((()=>{this.outerHeight=document.documentElement.scrollHeight;let t=0;location.href.includes("doctorui")?t=11:location.href.includes("patientui")&&(t=12),(null==e?void 0:e.studyId)&&t&&o.getIdentityVerificationResult({studyId:e.studyId,configType:12===t?1:2,triggerEvent:2}).then((e=>{e.skipFaceIdentity=e.skipVerification,this.identityConfigRes=e}))}),0)},onShow(){D()},methods:{handleSaveAddTutelaLegitima(){this.confereeDoctorList.find((e=>this.legalGuardiansPhone===e.mobile))?this.$notify({text:"手机号不可重复",type:"warn"}):/^[1][3-9][0-9]{9}$/.test(this.legalGuardiansPhone)&&(this.ySort+=1,this.confereeDoctorList.push({mobile:this.legalGuardiansPhone,roleName:2===this.addRoleFlag?"监护人":"公正见证人",userName:"",userType:this.addRoleFlag-1,directionFlag:"left",ySort:this.ySort}),this.showAddTutelaLegitimaPoup=!1)},handleSaveConfereeDoctor(){this.confereeDoctorList.forEach((e=>{"right"===(null==e?void 0:e.directionFlag)&&e.checked&&(e.directionFlag="left",this.ySort+=1,e.ySort=this.ySort)})),this.showConfereeDoctorPoup=!1},handleSaveAddconferee(){if(this.addRoleFlag>1)this.legalGuardiansPhone="",this.showAddTutelaLegitimaPoup=!0;else{if(!this.confereeDoctorList.find((e=>"right"===(null==e?void 0:e.directionFlag))))return void this.$notify({text:"没有可选择的研究人员",type:"warn"});this.showConfereeDoctorPoup=!0}this.showAddconfereePoup=!1},handleAddconferee(){this.showAddconfereePoup=!0;const e=JSON.parse(sessionStorage.getItem("userInfoIM"));2===this.confereeDoctorList.length&&(null==e?void 0:e.patientId)&&i.getDoctorConferenceUser(null==e?void 0:e.patientId).then((e=>{(null==e?void 0:e.length)&&(this.confereeDoctorList=this.confereeDoctorList.concat(e.map((e=>(e.ySort=999,e.directionFlag="right",e)))))}))},pickerTimeGroup(){this.showSelectTimesPicker=!0},onSelectTimesConfirm({selectedOptions:e}){this.showSelectTimesPicker=!1,this.selectTimesFieldValueStr=e[0].text,this.selectTimesFieldValue=[e[0].text]},filterStartTime:(e,t)=>"minute"===e?t.filter((e=>"00"===e.value||"15"===e.value||"30"===e.value||"45"===e.value)):t,onSelectInterviewConfirm({selectedOptions:e}){this.showSelectInterviewPicker=!1,this.selectInterviewFieldValue=e[0].visitName,this.visitId=e[0].visitTemplateId,this.selectIcfFieldValue=""},onSelectIcfConfirm({selectedOptions:e}){this.showSelectIcfPicker=!1,this.selectIcfFieldValue=e[0].icfInfoName,this.icfInfoId=e[0].icfInfoId,this.selectInterviewFieldValue=""},onSelectMeetingPropertiesConfirm({selectedOptions:e}){this.showSelectMeetingPropertiesPicker=!1,this.selectMeetingPropertiesfieldValue=e[0].text,"知情会议"===this.selectMeetingPropertiesfieldValue?this.selectInterviewFieldValue="":this.selectIcfFieldValue=""},async _createConference(t){if(!this.selectTimesFieldValueStr)return void this.$notify({text:"请选择时长",type:"warn"});if(!this.selectMeetingPropertiesfieldValue)return void this.$notify({text:"请选择会议属性",type:"warn"});if("知情会议"===this.selectMeetingPropertiesfieldValue&&!this.selectIcfFieldValue)return void this.$notify({text:"请选择知情",type:"warn"});if("访视会议"===this.selectMeetingPropertiesfieldValue&&!this.selectInterviewFieldValue)return void this.$notify({text:"请选择访视",type:"warn"});let i=new V;i.conferenceTitle=this.title,this.password&&this.enablePassword&&(i.password=this.password),i.pin=""+Math.ceil((1+1e5*Math.random())/10),i.owner=e.getUserId()||l("userId"),i.managers="",i.startTime=Math.ceil(new Date(b(Math.ceil((new Date).getTime()/1e3),"{y}-{m}-{d} {h}:{i}")).getTime()/1e3);let c=3600;this.selectTimesColumns.map((e=>{e.text===this.selectTimesFieldValueStr&&(c=e.value)}));let d=(new Date).getTime()+1e3*c;i.endTime=Math.ceil(d/1e3),i.start=b(Math.ceil((new Date).getTime()/1e3),"{y}-{m}-{d} {h}:{i}"),i.end=b(new Date(d),"{y}-{m}-{d} {h}:{i}"),i.audience=!this.notAudience,i.allowSwitchMode=this.allowTurnOnMic,i.advance=this.advance,"知情会议"===this.selectMeetingPropertiesfieldValue?i.icfStatementId=this.icfInfoId:i.visitId=this.visitId,i.conferenceType="知情会议"===this.selectMeetingPropertiesfieldValue?1:2,i.users=this.confereeDoctorList.filter(((e,t)=>t>1&&"right"!==e.directionFlag));const h=JSON.parse(sessionStorage.getItem("userInfoIM"));i.dctPatientId=h.patientId,this.loading||(this.loading=!0,o.createConference(i).then((e=>{this.identityConfigRes.skipFaceIdentity?(t?(null==e?void 0:e.conferenceId)&&o.dctJoinConference({conferenceId:e.id}).then((()=>{s.joinConference(e.conferenceId,!1,e.pin,e.owner,e.conferenceTitle,"",!0,e.advance,!0,!0),a.conferenceInfo=i})).catch((e=>{var t,i,o,l;((null==(t=null==e?void 0:e.data)?void 0:t.detail)||(null==(i=null==e?void 0:e.data)?void 0:i.message))&&r({title:(null==(o=null==e?void 0:e.data)?void 0:o.detail)||(null==(l=null==e?void 0:e.data)?void 0:l.message),icon:"none"})})):(r({title:"创建会议成功",icon:"none"}),this.goBack()),this.loading=!1):n({url:`/pages/voip/conference/ConferenceInfoPage?conferenceInfo=${JSON.stringify(e)}&joinConferenceFlag=true`})})).catch((e=>{r({title:"创建会议失败",icon:"none"}),this.loading=!1})))},goBack(){n({url:"/pages/voip/conference/ConferencePortalPage"})},createConference(){!this.actionEnable||this.enablePassword&&!this.password||this._createConference(0)},async createAndJoinConference(){this.actionEnable&&this._createConference(1)}},computed:{actionEnable(){let e=(new Date).getTime();return this.title&&this.title.trim()&&this.endDefault&&new Date(this.endDefault).getTime()>e}},watch:{advance(){this.advance&&(this.notAudience=!1)},endDefault(){this.endDefault&&new Date(this.endDefault).getTime()<(new Date).getTime()&&(this.endDefault="",r({title:"结束时间不能小于当前时间",icon:"none"}))}}},[["render",function(e,t,i,o,l,n){const s=I,a=F,r=C,V=P("van-icon"),b=P("van-button"),D=P("van-picker"),M=P("van-popup"),O=P("MyPopupShow"),R=P("van-field"),z=P("van-form");return c(),d("div",null,[h(f("div",{style:w({height:l.outerHeight?l.outerHeight-44+"px":"calc(100vh - 44px)",overflow:"auto"})},[f("div",{class:"nav-title"},[f("div",{style:{width:"18px",height:"18px",overflow:"hidden"},onClick:t[0]||(t[0]=(...e)=>n.goBack&&n.goBack(...e))},[p(s,{style:{width:"100%",height:"100%"},src:A,mode:""})]),f("div",null,"快速会议"),f("div",{style:{opacity:"0","text-align":"right",width:"40px","max-height":"40px",overflow:"hidden","font-size":"14px","font-weight":"400",color:"#5860DA"}}," 创建 ")]),f("div",{class:"order-conference-container"},[p(r,null,{default:g((()=>[f("span",{style:{"white-space":"nowrap","margin-right":"10px"}},"会议标题"),p(a,{modelValue:l.title,"onUpdate:modelValue":t[1]||(t[1]=e=>l.title=e),class:"text-input tips-box",placeholder:"请输入"},null,8,["modelValue"])])),_:1}),m("",!0),p(r,null,{default:g((()=>[S(" 开始时间 "),f("span",null,"现在")])),_:1}),p(r,null,{default:g((()=>[S(" 时长 "),f("div",{style:{flex:"1","text-align":"right"},onClick:t[3]||(t[3]=(...e)=>n.pickerTimeGroup&&n.pickerTimeGroup(...e))},k(l.selectTimesFieldValueStr+""),1),p(V,{style:{"font-size":"18px","margin-left":"10px"},name:"arrow"})])),_:1}),p(r,null,{default:g((()=>[f("div",null,"参会人员"),f("div",{class:"centerflex-h",onClick:t[4]||(t[4]=()=>{l.showConfereeFlag=1})},[f("div",null,k(l.confereeDoctorList.filter((e=>"right"!==e.directionFlag)).length||2),1),p(V,{style:{"font-size":"18px","margin-left":"10px"},name:"arrow"})])])),_:1}),p(r,null,{default:g((()=>[f("div",null,"会议属性"),f("div",{class:"centerflex-h",onClick:t[5]||(t[5]=()=>{l.showSelectMeetingPropertiesPicker=!0})},[f("div",{class:T({"color-e3e3e3":!l.selectMeetingPropertiesfieldValue})},k(l.selectMeetingPropertiesfieldValue||"请选择"),3),p(V,{style:{"font-size":"18px","margin-left":"10px"},name:"arrow"})])])),_:1}),"知情会议"===l.selectMeetingPropertiesfieldValue?(c(),v(r,{key:1},{default:g((()=>[f("div",null,"关联知情"),f("div",{class:"centerflex-h",onClick:t[6]||(t[6]=()=>{l.showSelectIcfPicker=!0})},[f("div",{style:{"max-width":"260px"},class:T({"color-e3e3e3":!l.selectIcfFieldValue})},k(l.selectIcfFieldValue||"请选择"),3),p(V,{style:{"font-size":"18px","margin-left":"10px"},name:"arrow"})])])),_:1})):"访视会议"===l.selectMeetingPropertiesfieldValue?(c(),v(r,{key:2},{default:g((()=>[f("div",null,"关联访视"),f("div",{class:"centerflex-h",onClick:t[7]||(t[7]=()=>{l.showSelectInterviewPicker=!0})},[f("div",{style:{"max-width":"260px"},class:T({"color-e3e3e3":!l.selectInterviewFieldValue})},k(l.selectInterviewFieldValue||"请选择"),3),p(V,{style:{"font-size":"18px","margin-left":"10px"},name:"arrow"})])])),_:1})):m("",!0)])],4),[[u,!l.showConfereeFlag]]),f("div",{class:"action-container"},[h(p(b,{style:{width:"100%"},type:"primary",loading:l.loading,"loading-text":"进入会议",block:"",onClick:n.createAndJoinConference},{default:g((()=>[S("进入会议")])),_:1},8,["loading","onClick"]),[[u,!l.showConfereeFlag]])]),p(M,{show:l.showSelectTimesPicker,"onUpdate:show":t[10]||(t[10]=e=>l.showSelectTimesPicker=e),round:"",position:"bottom"},{default:g((()=>[p(D,{modelValue:l.selectTimesFieldValue,"onUpdate:modelValue":t[8]||(t[8]=e=>l.selectTimesFieldValue=e),columns:l.selectTimesValueColumns,onCancel:t[9]||(t[9]=e=>l.showSelectTimesPicker=!1),onConfirm:n.onSelectTimesConfirm},null,8,["modelValue","columns","onConfirm"])])),_:1},8,["show"]),h(f("div",null,[f("div",{style:w({height:l.outerHeight?l.outerHeight-44+"px":"calc(100vh - 44px)",padding:"0 15px",overflow:"auto"})},[f("div",{class:"nav-title"},[f("div",{style:{width:"18px",height:"18px",overflow:"hidden"},onClick:t[11]||(t[11]=e=>l.showConfereeFlag=0)},[p(s,{style:{width:"100%",height:"100%"},src:A,mode:""})]),f("div",null,"参会人员"),f("div",{style:{width:"18px",height:"18px",overflow:"hidden"}})]),f("div",{class:"bt-hr"}),(c(!0),d(x,null,y(l.confereeDoctorList.sort(((e,t)=>e.ySort-t.ySort)),((e,t)=>(c(),d("div",{key:t},[t<2||t>1&&"right"!==(null==e?void 0:e.directionFlag)?(c(),d("div",{key:0,class:"centerflex-h bt-hr",style:{padding:"15px 0"}},[(null==e?void 0:e.avatarUrl)?(c(),v(s,{key:0,style:{width:"48px",height:"48px","margin-right":"15px","border-radius":"50%"},src:e.avatarUrl,mode:""},null,8,["src"])):2===(null==e?void 0:e.userType)?(c(),d("div",{key:1,style:{width:"48px",height:"48px","margin-right":"15px","font-size":"12px",color:"#fff","background-color":"#41B592","border-radius":"50%",padding:"5px 0px","box-sizing":"border-box"}},[f("div",{class:"centerflex no-wrap w-full"},"公正"),f("div",{class:"centerflex no-wrap w-full"},"见证人")])):1===(null==e?void 0:e.userType)?(c(),d("div",{key:2,style:{width:"48px",height:"48px","margin-right":"15px","font-size":"12px",color:"#fff","background-color":"#5860DA","border-radius":"50%",padding:"5px 0px","box-sizing":"border-box"},class:"centerflex"},[f("div",{class:"no-wrap"},"监护人")])):(c(),v(s,{key:3,style:{width:"48px",height:"48px","margin-right":"15px"},src:_,mode:""})),f("div",{class:"flex-1"},[f("div",{style:{"font-size":"15px","margin-bottom":"8px"},class:"flex justify-between"},[(null==e?void 0:e.userName)?(c(),d("div",{key:0},k(e.userName),1)):(c(),d("div",{key:1}," ")),t>1?(c(),d("div",{key:2,style:{color:"#D9001B"},onClick:()=>{e.directionFlag="right",0===(null==e?void 0:e.userType)?e.checked=!1:e.mobile=""}},"移除",8,["onClick"])):m("",!0)]),f("div",{style:{"font-size":"12px",color:"#9A9A9A"}},[t?m("",!0):(c(),d("span",{key:0},"我，主持人，")),(null==e?void 0:e.roleName)?(c(),d("span",{key:1},k(e.roleName.replace(/,/g,"、")),1)):m("",!0)])])])):m("",!0)])))),128))],4),p(b,{style:{width:"100%","border-radius":"0"},type:"primary",onClick:n.handleAddconferee},{default:g((()=>[S("添加参会人员")])),_:1},8,["onClick"]),p(O,{myPopupShow:l.showAddconfereePoup,title:"请选择",cancelText:"取消",saveText:"确认",handleSave:n.handleSaveAddconferee,handleCancel:()=>{l.showAddconfereePoup=!1}},{bodyslot:g((()=>[f("div",{class:"centerflex",style:w([{"border-radius":"4px",padding:"10px 0",width:"90%","box-shadow":"0 1px 12px rgba(0, 0, 0, 0.07)",margin:"15px auto"},1===l.addRoleFlag?{background:"#E9EFFC",color:"#5860da"}:{}]),onClick:t[12]||(t[12]=()=>{l.addRoleFlag=1})}," 研究人员 ",4),f("div",{class:"centerflex",style:w([{"border-radius":"4px",padding:"10px 0",width:"90%","box-shadow":"0 1px 12px rgba(0, 0, 0, 0.07)",margin:"15px auto"},2===l.addRoleFlag?{background:"#E9EFFC",color:"#5860da"}:{}]),onClick:t[13]||(t[13]=()=>{l.addRoleFlag=2})}," 监护人 ",4),f("div",{class:"centerflex",style:w([{"border-radius":"4px",padding:"10px 0",width:"90%","box-shadow":"0 1px 12px rgba(0, 0, 0, 0.07)",margin:"15px auto"},3===l.addRoleFlag?{background:"#E9EFFC",color:"#5860da"}:{}]),onClick:t[14]||(t[14]=()=>{l.addRoleFlag=3})}," 公正见证人 ",4)])),_:1},8,["myPopupShow","handleSave","handleCancel"]),p(O,{myPopupShow:l.showAddTutelaLegitimaPoup,title:2===l.addRoleFlag?"监护人":"公正见证人",cancelText:"取消",saveText:"确认",handleSave:n.handleSaveAddTutelaLegitima,handleCancel:()=>{l.showAddTutelaLegitimaPoup=!1}},{bodyslot:g((()=>[f("div",{style:{padding:"0 16px"}},"该手机号将用于用户注册登录，以及接收会议通知"),p(z,{class:"participant-form"},{default:g((()=>[p(R,{modelValue:l.legalGuardiansPhone,"onUpdate:modelValue":t[15]||(t[15]=e=>l.legalGuardiansPhone=e),modelModifiers:{trim:!0},name:"legalGuardiansPhone",rules:[{required:!0,message:"请输入"},{validator:e=>/^[1][3-9][0-9]{9}$/.test(e),message:"手机格式错误",trigger:"onBlur"}],label:"手机号",maxlength:"11",placeholder:"请输入"},null,8,["modelValue","rules"])])),_:1})])),_:1},8,["myPopupShow","title","handleSave","handleCancel"]),p(O,{myPopupShow:l.showConfereeDoctorPoup,title:"研究人员",cancelText:"取消",saveText:"确认",handleSave:n.handleSaveConfereeDoctor,handleCancel:()=>{l.showConfereeDoctorPoup=!1}},{bodyslot:g((()=>[f("div",{style:w({maxHeight:l.outerHeight-240+"px",overflow:"auto"})},[(c(!0),d(x,null,y(l.confereeDoctorList,((e,t)=>(c(),d("div",{key:t},["right"===(null==e?void 0:e.directionFlag)&&0===(null==e?void 0:e.userType)?(c(),d("div",{key:0,class:"centerflex-h justify-between",style:{padding:"10px"}},[f("div",null,k(e.userName)+"，"+k(e.roleName.replace(/,/g,"、")),1),h(f("img",{style:{width:"15px"},src:L,alt:"",onClick:()=>{e.checked=!(null==e?void 0:e.checked)}},null,8,["onClick"]),[[u,!(null==e?void 0:e.checked)]]),h(f("img",{style:{width:"15px"},src:N,alt:"",onClick:()=>{e.checked=!(null==e?void 0:e.checked)}},null,8,["onClick"]),[[u,null==e?void 0:e.checked]])])):m("",!0)])))),128))],4)])),_:1},8,["myPopupShow","handleSave","handleCancel"])],512),[[u,1===l.showConfereeFlag]]),p(M,{show:l.showSelectMeetingPropertiesPicker,"onUpdate:show":t[17]||(t[17]=e=>l.showSelectMeetingPropertiesPicker=e),round:"",position:"bottom"},{default:g((()=>[p(D,{columns:l.selectMeetingPropertiescolumns,onCancel:t[16]||(t[16]=e=>l.showSelectMeetingPropertiesPicker=!1),onConfirm:n.onSelectMeetingPropertiesConfirm},null,8,["columns","onConfirm"])])),_:1},8,["show"]),p(M,{show:l.showSelectIcfPicker,"onUpdate:show":t[19]||(t[19]=e=>l.showSelectIcfPicker=e),round:"",position:"bottom"},{default:g((()=>[p(D,{columns:l.conferenceStatements,"columns-field-names":{text:"icfInfoName",value:"icfInfoId"},onCancel:t[18]||(t[18]=e=>l.showSelectIcfPicker=!1),onConfirm:n.onSelectIcfConfirm},null,8,["columns","onConfirm"])])),_:1},8,["show"]),p(M,{show:l.showSelectInterviewPicker,"onUpdate:show":t[21]||(t[21]=e=>l.showSelectInterviewPicker=e),round:"",position:"bottom"},{default:g((()=>[p(D,{columns:l.conferenceVisits,"columns-field-names":{text:"visitName",value:"visitTemplateId"},onCancel:t[20]||(t[20]=e=>l.showSelectInterviewPicker=!1),onConfirm:n.onSelectInterviewConfirm},null,8,["columns","onConfirm"])])),_:1},8,["show"])])}],["__scopeId","data-v-47454c33"]]);export{R as default};
