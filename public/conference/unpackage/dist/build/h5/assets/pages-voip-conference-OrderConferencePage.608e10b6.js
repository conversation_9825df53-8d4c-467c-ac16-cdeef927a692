import{w as e,G as t,l as i,H as l,c as o,b as s,o as n,d as a,L as r,M as c,e as d,n as h,f as u,q as p,j as m,g as f,F as g,h as v,i as w,N as x,Q as y,E as T,v as k,P,t as C,I as S,R as I}from"./index-15d7e8a5.js";import{C as b}from"./conferenceInfo.3f4517e7.js";import{d as V,p as F}from"./debounce.3401e956.js";import{M as D,_ as M}from"./men.d710d635.js";import{_}from"./leftArrows.3362d02a.js";import{_ as N,a as A}from"./checkInformedIcon.29557f46.js";import{_ as L}from"./_plugin-vue_export-helper.1b428a4d.js";import"./MyPopupShow.vue_vue_type_script_lang.dace47fb.js";const R=L({name:"OrderConferenceView",components:{MyPopupShow:D},data:()=>({title:"",desc:"",startTime:"",endTime:"",audioOnly:!1,audience:!1,advance:!1,allowTurnOnMic:!0,enablePassword:!1,pin:"",callId:"1234567",showTimes:!1,currentDate:"",currentTime:"",typeTime:0,activeTab:0,pickerTitle:"",loading:!1,password:"",outerHeight:document.documentElement.scrollHeight,showSelectTimesPicker:!1,selectTimesColumns:[{text:"15分钟",value:900},{text:"30分钟",value:1800},{text:"45分钟",value:2700},{text:"1小时",value:3600},{text:"2小时",value:7200},{text:"3小时",value:10800}],selectTimesValueColumns:[{text:"15分钟",value:"15分钟"},{text:"30分钟",value:"30分钟"},{text:"45分钟",value:"45分钟"},{text:"1小时",value:"1小时"},{text:"2小时",value:"2小时"},{text:"3小时",value:"3小时"}],selectTimesFieldValue:[],selectTimesFieldValueStr:"",showSelectMeetingPropertiesPicker:!1,selectMeetingPropertiesfieldValue:"",showSelectIcfPicker:!1,selectIcfFieldValue:"",showSelectInterviewPicker:!1,selectInterviewFieldValue:"",icfInfoId:"",visitId:"",showConfereeFlag:0,showAddconfereePoup:!1,addRoleFlag:1,confereeDoctorList:[],showConfereeDoctorPoup:!1,showAddTutelaLegitimaPoup:!1,legalGuardiansPhone:"",conferenceVisits:[],conferenceStatements:[],ySort:2,enableWaitingRoom:!0,selectMeetingPropertiescolumns:[]}),created(){const i=JSON.parse(sessionStorage.getItem("userInfoIM")),l=e.getUserDisplayName(t("userId"));this.title=l?`${l}预定的会议${null==i?void 0:i.patientNo}`:`预定的会议${null==i?void 0:i.patientNo}`},mounted(){const e=JSON.parse(sessionStorage.getItem("userInfoIM"));this.setCurrentDateTime(),this.selectTimesFieldValueStr="1小时",this.selectTimesFieldValue=["1小时"],i.getCurrentConferenceUserInfo(e.patientId).then((e=>{e.map(((e,t)=>{e.ySort=t+1})),this.confereeDoctorList=e})),i.getConferenceStatements(e.patientId).then((t=>{this.conferenceStatements=t,(null==t?void 0:t.length)&&(this.selectMeetingPropertiescolumns[0]={text:"知情会议",value:"知情会议"}),i.getConferenceVisits(e.patientId).then((e=>{this.conferenceVisits=e,(null==e?void 0:e.length)&&(this.selectMeetingPropertiescolumns[this.selectMeetingPropertiescolumns.length]={text:"访视会议",value:"访视会议"})}))})).catch((()=>{i.getConferenceVisits(e.patientId).then((e=>{this.conferenceVisits=e,(null==e?void 0:e.length)&&this.selectMeetingPropertiescolumns.push({text:"访视会议",value:"访视会议"})}))})),setTimeout((()=>{this.outerHeight=document.documentElement.scrollHeight}),0)},onShow(){V()},methods:{handleSaveAddTutelaLegitima(){this.confereeDoctorList.find((e=>this.legalGuardiansPhone===e.mobile))?this.$notify({text:"手机号不可重复",type:"warn"}):/^[1][3-9][0-9]{9}$/.test(this.legalGuardiansPhone)&&(this.ySort+=1,this.confereeDoctorList.push({mobile:this.legalGuardiansPhone,roleName:2===this.addRoleFlag?"监护人":"公正见证人",userName:"",userType:this.addRoleFlag-1,directionFlag:"left",ySort:this.ySort}),this.showAddTutelaLegitimaPoup=!1)},handleSaveConfereeDoctor(){this.confereeDoctorList.forEach((e=>{"right"===(null==e?void 0:e.directionFlag)&&e.checked&&(e.directionFlag="left",this.ySort+=1,e.ySort=this.ySort)})),this.showConfereeDoctorPoup=!1},handleSaveAddconferee(){if(this.addRoleFlag>1)this.legalGuardiansPhone="",this.showAddTutelaLegitimaPoup=!0;else{if(!this.confereeDoctorList.find((e=>"right"===(null==e?void 0:e.directionFlag))))return void this.$notify({text:"没有可选择的研究人员",type:"warn"});this.showConfereeDoctorPoup=!0}this.showAddconfereePoup=!1},handleAddconferee(){this.showAddconfereePoup=!0;const e=JSON.parse(sessionStorage.getItem("userInfoIM"));2===this.confereeDoctorList.length&&(null==e?void 0:e.patientId)&&i.getDoctorConferenceUser(null==e?void 0:e.patientId).then((e=>{(null==e?void 0:e.length)&&(this.confereeDoctorList=this.confereeDoctorList.concat(e.map((e=>(e.ySort=999,e.directionFlag="right",e)))))}))},pickerTimeGroup(){this.showSelectTimesPicker=!0},onSelectTimesConfirm({selectedOptions:e}){this.showSelectTimesPicker=!1,this.selectTimesFieldValueStr=e[0].text,this.selectTimesFieldValue=[e[0].text]},filterStartTime:(e,t)=>"minute"===e?t.filter((e=>"00"===e.value||"05"===e.value||"10"===e.value||"15"===e.value||"20"===e.value||"25"===e.value||"30"===e.value||"35"===e.value||"40"===e.value||"45"===e.value||"50"===e.value||"55"===e.value)):t,onSelectInterviewConfirm({selectedOptions:e}){this.showSelectInterviewPicker=!1,this.selectInterviewFieldValue=e[0].visitName,this.visitId=e[0].visitTemplateId,this.selectIcfFieldValue=""},onSelectIcfConfirm({selectedOptions:e}){this.showSelectIcfPicker=!1,this.selectIcfFieldValue=e[0].icfInfoName,this.icfInfoId=e[0].icfInfoId,this.selectInterviewFieldValue=""},onSelectMeetingPropertiesConfirm({selectedOptions:e}){this.showSelectMeetingPropertiesPicker=!1,this.selectMeetingPropertiesfieldValue=e[0].text,"知情会议"===this.selectMeetingPropertiesfieldValue?this.selectInterviewFieldValue="":this.selectIcfFieldValue=""},onConfirm(){1===this.typeTime?(this.startTime="",this.startTime=this.currentDate.join("-")+" "+this.currentTime.join(":")):2===this.typeTime&&(this.endTime=this.currentDate.join("-")+" "+this.currentTime.join(":")),this.showTimes=!1},setCurrentDateTime(){const e=F(new Date,"{y}-{m}-{d} {h}:{i}");this.currentDate=e.split(" ")[0].split("-");const t=e.split(" ")[1].split(":"),i=Number(t[1]),l=Number(t[0]);i&&(i/5==0||(i<5?t[1]="05":i<10?t[1]="10":i<15?t[1]="15":i<20?t[1]="20":i<25?t[1]="25":i<30?t[1]="30":i<35?t[1]="35":i<40?t[1]="40":i<45?t[1]="45":i<50?t[1]="50":i<55?t[1]="55":i>55&&(t[1]="00",t[0]=l+1<=9?`0${l+1}`:`${l+1}`))),this.currentTime=t,this.startTime=this.currentDate.join("-")+" "+this.currentTime.join(":")},pickerGroup(e){this.activeTab=0,this.typeTime=e,1===e&&(this.pickerTitle="开始时间",this.startTime?(this.currentDate=this.startTime.split(" ")[0].split("-"),this.currentTime=this.startTime.split(" ")[1].split(":")):this.setCurrentDateTime()),this.showTimes=!0},orderConference(){if(this.enablePassword&&!this.password)return void this.$notify({text:"请输入会议密码",type:"warn"});if(!this.startTime)return void this.$notify({text:"请选择开始时间",type:"warn"});if(!this.selectTimesFieldValueStr)return void this.$notify({text:"请选择时长",type:"warn"});if(!this.selectMeetingPropertiesfieldValue)return void this.$notify({text:"请选择会议属性",type:"warn"});if("知情会议"===this.selectMeetingPropertiesfieldValue&&!this.selectIcfFieldValue)return void this.$notify({text:"请选择知情",type:"warn"});if("访视会议"===this.selectMeetingPropertiesfieldValue&&!this.selectInterviewFieldValue)return void this.$notify({text:"请选择访视",type:"warn"});let t=new b;t.conferenceTitle=this.title,this.password&&this.enablePassword&&(t.password=this.password),t.pin=""+Math.ceil((1+1e5*Math.random())/10),t.owner=e.getUserId()||l("userId"),t.managers="",t.startTime=Math.ceil(new Date(this.startTime).getTime()/1e3),t.start=F(new Date(this.startTime),"{y}-{m}-{d} {h}:{i}");let i=0;this.selectTimesColumns.map((e=>{e.text===this.selectTimesFieldValueStr&&(i=e.value)}));let s=1e3*(t.startTime+i);t.endTime=Math.ceil(s/1e3),t.end=F(new Date(s),"{y}-{m}-{d} {h}:{i}"),t.audience=this.audience,t.allowSwitchMode=this.allowTurnOnMic,t.advance=this.advance,"知情会议"===this.selectMeetingPropertiesfieldValue?t.icfStatementId=this.icfInfoId:t.visitId=this.visitId,t.conferenceType="知情会议"===this.selectMeetingPropertiesfieldValue?1:2,t.users=this.confereeDoctorList.filter(((e,t)=>t>1&&"right"!==(null==e?void 0:e.directionFlag)));const n=JSON.parse(sessionStorage.getItem("userInfoIM"));t.dctPatientId=n.patientId,t.isBookingConference=!0,t.enableWaitingRoom=this.enableWaitingRoom,this.loading||(this.loading=!0,o.createConference(t).then((e=>{this.goBack(),this.loading=!1})).catch((()=>{this.loading=!1})))},goBack(){s({url:"/pages/voip/conference/ConferencePortalPage"})}},computed:{actionEnable(){if(this.title&&this.title.trim()&&this.startTime&&this.endTime){let e=(new Date).getTime(),t=new Date(this.startTime).getTime(),i=new Date(this.endTime).getTime();return t>e&&i>t}return!1}},watch:{advance(){this.advance&&(this.audience=!1)},startTime(){if(this.startTime){let e=new Date(this.startTime).getTime();e<(new Date).getTime()&&((new Date).getTime()-e)/1e3>60&&(this.$notify({text:"开始时间不能早于当前时间",type:"warn"}),this.startTime="")}}}},[["render",function(e,t,i,l,o,s){const b=w,V=S,F=x,D=T("van-icon"),L=I,R=y,j=T("van-button"),U=T("van-picker"),$=T("van-popup"),G=T("MyPopupShow"),z=T("van-field"),O=T("van-form"),E=T("van-date-picker"),H=T("van-time-picker"),B=T("van-picker-group");return n(),a("div",null,[r(d("div",null,[d("div",{style:h({height:o.outerHeight?o.outerHeight-44+"px":"calc(100vh - 44px)",overflow:"auto"})},[d("div",{class:"nav-title"},[d("div",{style:{width:"18px",height:"18px",overflow:"hidden"},onClick:t[0]||(t[0]=(...e)=>s.goBack&&s.goBack(...e))},[u(b,{style:{width:"100%",height:"100%"},src:_,mode:""})]),d("div",null,"预定会议"),d("div",{style:{width:"18px",height:"18px",overflow:"hidden"}})]),d("div",{class:"order-conference-container"},[u(F,null,{default:p((()=>[d("span",{style:{"white-space":"nowrap","margin-right":"10px"}},"会议标题"),u(V,{modelValue:o.title,"onUpdate:modelValue":t[1]||(t[1]=e=>o.title=e),class:"text-input tips-box",placeholder:"请输入"},null,8,["modelValue"])])),_:1}),u(F,null,{default:p((()=>[k(" 开始时间 "),d("div",{class:P({"color-e3e3e3":!o.startTime}),style:{flex:"1","text-align":"right"},onClick:t[2]||(t[2]=e=>s.pickerGroup(1))},C(o.startTime||"请选择"),3),u(D,{style:{"font-size":"18px","margin-left":"10px"},name:"arrow"})])),_:1}),u(F,null,{default:p((()=>[k(" 会议时长 "),d("div",{style:{flex:"1","text-align":"right"},class:P({"color-e3e3e3":!o.selectTimesFieldValueStr}),onClick:t[3]||(t[3]=(...e)=>s.pickerTimeGroup&&s.pickerTimeGroup(...e))},C(o.selectTimesFieldValueStr||"请选择"),3),u(D,{style:{"font-size":"18px","margin-left":"10px"},name:"arrow"})])),_:1}),d("div",null,[u(R,{onChange:t[4]||(t[4]=e=>o.enableWaitingRoom=!o.enableWaitingRoom)},{default:p((()=>[u(F,null,{default:p((()=>[k(" 等待室 "),u(L,{checked:o.enableWaitingRoom},null,8,["checked"])])),_:1})])),_:1})]),u(F,null,{default:p((()=>[d("div",null,"参会人员"),d("div",{class:"centerflex-h",onClick:t[5]||(t[5]=()=>{o.showConfereeFlag=1})},[d("div",null,C(o.confereeDoctorList.filter((e=>"right"!==e.directionFlag)).length||2),1),u(D,{style:{"font-size":"18px","margin-left":"10px"},name:"arrow"})])])),_:1}),u(F,null,{default:p((()=>[d("div",null,"会议属性"),d("div",{class:"centerflex-h",onClick:t[6]||(t[6]=()=>{o.showSelectMeetingPropertiesPicker=!0})},[d("div",{class:P({"color-e3e3e3":!o.selectMeetingPropertiesfieldValue})},C(o.selectMeetingPropertiesfieldValue||"请选择"),3),u(D,{style:{"font-size":"18px","margin-left":"10px"},name:"arrow"})])])),_:1}),"知情会议"===o.selectMeetingPropertiesfieldValue?(n(),m(F,{key:0},{default:p((()=>[d("div",null,"关联知情"),d("div",{class:"centerflex-h",onClick:t[7]||(t[7]=()=>{o.showSelectIcfPicker=!0})},[d("div",{style:{"max-width":"260px"},class:P({"color-e3e3e3":!o.selectIcfFieldValue})},C(o.selectIcfFieldValue||"请选择"),3),u(D,{style:{"font-size":"18px","margin-left":"10px"},name:"arrow"})])])),_:1})):"访视会议"===o.selectMeetingPropertiesfieldValue?(n(),m(F,{key:1},{default:p((()=>[d("div",null,"关联访视"),d("div",{class:"centerflex-h",onClick:t[8]||(t[8]=()=>{o.showSelectInterviewPicker=!0})},[d("div",{style:{"max-width":"260px"},class:P({"color-e3e3e3":!o.selectInterviewFieldValue})},C(o.selectInterviewFieldValue||"请选择"),3),u(D,{style:{"font-size":"18px","margin-left":"10px"},name:"arrow"})])])),_:1})):f("",!0)])],4),u(j,{style:{width:"100%","border-radius":"0"},type:"primary",loading:o.loading,"loading-text":"预定会议",onClick:s.orderConference},{default:p((()=>[k("预定会议")])),_:1},8,["loading","onClick"])],512),[[c,!o.showConfereeFlag]]),u($,{show:o.showSelectTimesPicker,"onUpdate:show":t[11]||(t[11]=e=>o.showSelectTimesPicker=e),round:"",position:"bottom"},{default:p((()=>[u(U,{modelValue:o.selectTimesFieldValue,"onUpdate:modelValue":t[9]||(t[9]=e=>o.selectTimesFieldValue=e),columns:o.selectTimesValueColumns,onCancel:t[10]||(t[10]=e=>o.showSelectTimesPicker=!1),onConfirm:s.onSelectTimesConfirm},null,8,["modelValue","columns","onConfirm"])])),_:1},8,["show"]),r(d("div",null,[d("div",{style:h({height:o.outerHeight?o.outerHeight-44+"px":"calc(100vh - 44px)",padding:"0 15px",overflow:"auto"})},[d("div",{class:"nav-title"},[d("div",{style:{width:"18px",height:"18px",overflow:"hidden"},onClick:t[12]||(t[12]=e=>o.showConfereeFlag=0)},[u(b,{style:{width:"100%",height:"100%"},src:_,mode:""})]),d("div",null,"参会人员"),d("div",{style:{width:"18px",height:"18px",overflow:"hidden"}})]),d("div",{class:"bt-hr"}),(n(!0),a(g,null,v(o.confereeDoctorList.sort(((e,t)=>e.ySort-t.ySort)),((e,t)=>(n(),a("div",{key:t},[t<2||t>1&&"right"!==(null==e?void 0:e.directionFlag)?(n(),a("div",{key:0,class:"centerflex-h bt-hr",style:{padding:"15px 0"}},[(null==e?void 0:e.avatarUrl)?(n(),m(b,{key:0,style:{width:"48px",height:"48px","margin-right":"15px","border-radius":"50%"},src:e.avatarUrl,mode:""},null,8,["src"])):2===(null==e?void 0:e.userType)?(n(),a("div",{key:1,style:{width:"48px",height:"48px","margin-right":"15px","font-size":"12px",color:"#fff","background-color":"#41B592","border-radius":"50%",padding:"5px 0px","box-sizing":"border-box"}},[d("div",{class:"centerflex no-wrap w-full"},"公正"),d("div",{class:"centerflex no-wrap w-full"},"见证人")])):1===(null==e?void 0:e.userType)?(n(),a("div",{key:2,style:{width:"48px",height:"48px","margin-right":"15px","font-size":"12px",color:"#fff","background-color":"#5860DA","border-radius":"50%",padding:"5px 0px","box-sizing":"border-box"},class:"centerflex"},[d("div",{class:"no-wrap"},"监护人")])):(n(),m(b,{key:3,style:{width:"48px",height:"48px","margin-right":"15px"},src:M,mode:""})),d("div",{class:"flex-1"},[d("div",{style:{"font-size":"15px","margin-bottom":"8px"},class:"flex justify-between"},[(null==e?void 0:e.userName)?(n(),a("div",{key:0},C(e.userName),1)):(n(),a("div",{key:1}," ")),t>1?(n(),a("div",{key:2,style:{color:"#D9001B"},onClick:()=>{e.directionFlag="right",0===(null==e?void 0:e.userType)?e.checked=!1:e.mobile=""}},"移除",8,["onClick"])):f("",!0)]),d("div",{style:{"font-size":"12px",color:"#9A9A9A"}},[t?f("",!0):(n(),a("span",{key:0},"我，主持人，")),(null==e?void 0:e.roleName)?(n(),a("span",{key:1},C(e.roleName.replace(/,/g,"、")),1)):f("",!0)])])])):f("",!0)])))),128))],4),u(j,{style:{width:"100%","border-radius":"0"},type:"primary",onClick:s.handleAddconferee},{default:p((()=>[k("添加参会人员")])),_:1},8,["onClick"]),u(G,{myPopupShow:o.showAddconfereePoup,title:"请选择",cancelText:"取消",saveText:"确认",handleSave:s.handleSaveAddconferee,handleCancel:()=>{o.showAddconfereePoup=!1}},{bodyslot:p((()=>[d("div",{class:"centerflex",style:h([{"border-radius":"4px",padding:"10px 0",width:"90%","box-shadow":"0 1px 12px rgba(0, 0, 0, 0.07)",margin:"15px auto"},1===o.addRoleFlag?{background:"#E9EFFC",color:"#5860da"}:{}]),onClick:t[13]||(t[13]=()=>{o.addRoleFlag=1})}," 研究人员 ",4),d("div",{class:"centerflex",style:h([{"border-radius":"4px",padding:"10px 0",width:"90%","box-shadow":"0 1px 12px rgba(0, 0, 0, 0.07)",margin:"15px auto"},2===o.addRoleFlag?{background:"#E9EFFC",color:"#5860da"}:{}]),onClick:t[14]||(t[14]=()=>{o.addRoleFlag=2})}," 监护人 ",4),d("div",{class:"centerflex",style:h([{"border-radius":"4px",padding:"10px 0",width:"90%","box-shadow":"0 1px 12px rgba(0, 0, 0, 0.07)",margin:"15px auto"},3===o.addRoleFlag?{background:"#E9EFFC",color:"#5860da"}:{}]),onClick:t[15]||(t[15]=()=>{o.addRoleFlag=3})}," 公正见证人 ",4)])),_:1},8,["myPopupShow","handleSave","handleCancel"]),u(G,{myPopupShow:o.showAddTutelaLegitimaPoup,title:2===o.addRoleFlag?"监护人":"公正见证人",cancelText:"取消",saveText:"确认",handleSave:s.handleSaveAddTutelaLegitima,handleCancel:()=>{o.showAddTutelaLegitimaPoup=!1}},{bodyslot:p((()=>[d("div",{style:{padding:"0 16px"}},"该手机号将用于用户注册登录，以及接收会议通知"),u(O,{class:"participant-form"},{default:p((()=>[u(z,{modelValue:o.legalGuardiansPhone,"onUpdate:modelValue":t[16]||(t[16]=e=>o.legalGuardiansPhone=e),modelModifiers:{trim:!0},name:"legalGuardiansPhone",rules:[{required:!0,message:"请输入"},{validator:e=>/^[1][3-9][0-9]{9}$/.test(e),message:"手机格式错误",trigger:"onBlur"}],label:"手机号",maxlength:"11",placeholder:"请输入"},null,8,["modelValue","rules"])])),_:1})])),_:1},8,["myPopupShow","title","handleSave","handleCancel"]),u(G,{myPopupShow:o.showConfereeDoctorPoup,title:"研究人员",cancelText:"取消",saveText:"确认",handleSave:s.handleSaveConfereeDoctor,handleCancel:()=>{o.showConfereeDoctorPoup=!1}},{bodyslot:p((()=>[d("div",{style:h({maxHeight:o.outerHeight-240+"px",overflow:"auto"})},[(n(!0),a(g,null,v(o.confereeDoctorList,((e,t)=>(n(),a("div",{key:t},["right"===(null==e?void 0:e.directionFlag)&&0===(null==e?void 0:e.userType)?(n(),a("div",{key:0,class:"centerflex-h justify-between",style:{padding:"10px"}},[d("div",null,C(e.userName)+"，"+C(e.roleName.replace(/,/g,"、")),1),r(d("img",{style:{width:"15px"},src:N,alt:"",onClick:()=>{e.checked=!(null==e?void 0:e.checked)}},null,8,["onClick"]),[[c,!(null==e?void 0:e.checked)]]),r(d("img",{style:{width:"15px"},src:A,alt:"",onClick:()=>{e.checked=!(null==e?void 0:e.checked)}},null,8,["onClick"]),[[c,null==e?void 0:e.checked]])])):f("",!0)])))),128))],4)])),_:1},8,["myPopupShow","handleSave","handleCancel"])],512),[[c,1===o.showConfereeFlag]]),u($,{show:o.showSelectMeetingPropertiesPicker,"onUpdate:show":t[18]||(t[18]=e=>o.showSelectMeetingPropertiesPicker=e),round:"",position:"bottom"},{default:p((()=>[u(U,{columns:o.selectMeetingPropertiescolumns,onCancel:t[17]||(t[17]=e=>o.showSelectMeetingPropertiesPicker=!1),onConfirm:s.onSelectMeetingPropertiesConfirm},null,8,["columns","onConfirm"])])),_:1},8,["show"]),u($,{show:o.showSelectIcfPicker,"onUpdate:show":t[20]||(t[20]=e=>o.showSelectIcfPicker=e),round:"",position:"bottom"},{default:p((()=>[u(U,{columns:o.conferenceStatements,"columns-field-names":{text:"icfInfoName",value:"icfInfoId"},onCancel:t[19]||(t[19]=e=>o.showSelectIcfPicker=!1),onConfirm:s.onSelectIcfConfirm},null,8,["columns","onConfirm"])])),_:1},8,["show"]),u($,{show:o.showSelectInterviewPicker,"onUpdate:show":t[22]||(t[22]=e=>o.showSelectInterviewPicker=e),round:"",position:"bottom"},{default:p((()=>[u(U,{columns:o.conferenceVisits,"columns-field-names":{text:"visitName",value:"visitTemplateId"},onCancel:t[21]||(t[21]=e=>o.showSelectInterviewPicker=!1),onConfirm:s.onSelectInterviewConfirm},null,8,["columns","onConfirm"])])),_:1},8,["show"]),u($,{show:o.showTimes,"onUpdate:show":t[27]||(t[27]=e=>o.showTimes=e),position:"bottom"},{default:p((()=>[u(B,{title:o.pickerTitle,tabs:["选择日期","选择时间"],"next-step-text":"下一步",onConfirm:s.onConfirm,onCancel:t[25]||(t[25]=e=>o.showTimes=!1),"active-tab":o.activeTab,"onUpdate:activeTab":t[26]||(t[26]=e=>o.activeTab=e)},{default:p((()=>[u(E,{modelValue:o.currentDate,"onUpdate:modelValue":t[23]||(t[23]=e=>o.currentDate=e)},null,8,["modelValue"]),u(H,{modelValue:o.currentTime,"onUpdate:modelValue":t[24]||(t[24]=e=>o.currentTime=e),filter:s.filterStartTime},null,8,["modelValue","filter"])])),_:1},8,["title","onConfirm","active-tab"])])),_:1},8,["show"])])}],["__scopeId","data-v-63d5d937"]]);export{R as default};
