import{r as e,c as o,a as n,b as s,w as t,o as i,d as r,e as c,f as a,n as l,g as d,F as f,h as v,i as m,t as u}from"./index-15d7e8a5.js";import{d as h,p as g}from"./debounce.3401e956.js";import{_ as p}from"./leftArrows.3362d02a.js";import{_ as y}from"./_plugin-vue_export-helper.1b428a4d.js";const C=y({name:"ConferencePortalPage",data:()=>({favConferenceInfos:[],historyConferenceInfos:[],makeReservation:!1,pageShow:!1,backDctFlag:location.host.includes("dct"),outerHeight:document.documentElement.scrollHeight}),mounted(){setTimeout((()=>{this.outerHeight=document.documentElement.scrollHeight}),100),this.loadFavConferences()},created(){const e=JSON.parse(sessionStorage.getItem("userInfoIM"));this.makeReservation=null==e?void 0:e.makeReservation},onShow(){h()},methods:{goDct(){JSON.parse(sessionStorage.getItem("userInfoIM")),sessionStorage.removeItem("userInfoIM"),e("token"),e("clientId"),e("userId"),e("authToken-app"),sessionStorage.removeItem("conferenceIMNum"),window.parent.postMessage("back","*")},loadFavConferences(){const e=JSON.parse(sessionStorage.getItem("userInfoIM"));o.getFavConferences({StudyID:null==e?void 0:e.studyId,PatientID:null==e?void 0:e.patientId}).then((o=>{n(),this.pageShow=!0;const s=[],t=[];if(o){if(o.forEach((e=>{(null==e?void 0:e.isDestroy)||(this.historyConference(e)||(null==e?void 0:e.isComplete)?s.push(e):t.push(e))})),e.serialNo&&!sessionStorage.getItem("conferenceIMNum")){const n=o.filter((e=>!e.isDestroy&&!e.isComplete&&!this.historyConference(e)));if((null==n?void 0:n.length)>0){let o=n[n.length-1];(null==e?void 0:e.conferenceId)&&n.map((n=>{n.conferenceId===(null==e?void 0:e.conferenceId)&&(o=n)})),setTimeout((()=>{this.showConferenceInfo(o)}),200)}}if((null==e?void 0:e.backUrl)&&e.backUrl.includes("waitExplainInformed")&&!sessionStorage.getItem("conferenceIMNum")){const n=o.filter((e=>!e.isDestroy&&!e.isComplete&&!this.historyConference(e)));if((null==n?void 0:n.length)>0){let o=n[n.length-1];(null==e?void 0:e.waitExplainInformedId)&&n.map((n=>{n.conferenceId===(null==e?void 0:e.waitExplainInformedId)&&(o=n)})),setTimeout((()=>{this.showConferenceInfo(o)}),500)}}}this.favConferenceInfos=t,this.historyConferenceInfos=s})).catch((e=>{n(),console.log("getFavConferences error",e)}))},joinConference(){s({url:"/pages/voip/conference/JoinConferencePage"})},createConference(){s({url:"/pages/voip/conference/CreateConferencePage"})},orderConference(){s({url:"/pages/voip/conference/OrderConferencePage"})},showConferenceInfo(e){const o=JSON.stringify(e);sessionStorage.setItem("conferenceIMNum",1),s({url:"/pages/voip/conference/ConferenceInfoPage?conferenceInfo="+o})},historyConference(e){let o=new Date(1e3*e.startTime).getTime(),n=new Date(1e3*e.endTime).getTime();return(new Date).getTime()>n||o>n},favConferenceDesc(e){let o=new Date(1e3*e.startTime).getTime(),n=new Date(1e3*e.endTime).getTime(),s=(new Date).getTime();if(s<o){return(g(new Date,"{y}-{m}-{d}")===g(new Date(o),"{y}-{m}-{d}")?`今天 ${null==e?void 0:e.startHourStr}`:g(new Date(o),"{y}-{m}-{d} {h}:{i}"))+" 开始会议"}return s>n?"会议已结束":o<n?`今天  ${null==e?void 0:e.startHourStr} 会议已开始，请尽快加入`:"会议已结束"},historyConferenceDesc(e){let o=this.formatDurationMS(e.endTime-e.startTime),n=t.getUserDisplayName(e.owner);return`时间：${g(new Date(1e3*e.startTime),"{y}-{m}-{d}")} 发起人：${n} 时长：${o}`},formatDurationMS(e){let o="",n=Math.floor(e/60),s=e%60;return s=s<10?"0"+s:s,n=n<10?"0"+n:n,o=n+":"+s,o},formatDuration(e){let o="",n=Math.floor(e/3600);var s=Math.floor(e%3600/60);return n=n<10?"0"+n:n,o=n+":"+(s=s<10?"0"+s:s),o}}},[["render",function(e,o,n,s,t,h){const g=m;return i(),r("div",null,[c("div",{class:"nav-title"},[t.backDctFlag?(i(),r("div",{key:0,style:{width:"18px",height:"18px",overflow:"hidden"},onClick:o[0]||(o[0]=(...e)=>h.goDct&&h.goDct(...e))},[a(g,{style:{width:"100%",height:"100%"},src:p,mode:""})])):(i(),r("div",{key:1})),c("div",null,"会议"),c("div",{style:{width:"18px",height:"18px",overflow:"hidden"}})]),t.pageShow?(i(),r("div",{key:0,class:"conference-portal-container scrollnone",style:l({height:t.outerHeight?t.outerHeight-46+"px":"calc(100vh - 46px)"})},[c("div",{class:"left-slider"},[c("div",{class:"action-container"},[t.makeReservation?d("",!0):(i(),r("div",{key:0,class:"action",style:{background:"#e1e9ff"},onClick:o[1]||(o[1]=(...e)=>h.joinConference&&h.joinConference(...e))},[c("div",{class:"icon"},[c("i",{class:"icon-ion-android-add",style:{color:"#3570fe"}})]),c("p",{class:"title"},"加入会议")])),t.makeReservation?(i(),r("div",{key:1,class:"action",style:{background:"#d9f3fe"},onClick:o[2]||(o[2]=(...e)=>h.createConference&&h.createConference(...e))},[c("div",{class:"icon"},[c("i",{class:"icon-ion-ios-videocam",style:{color:"#3570fe"}})]),c("p",{class:"title"},"发起会议")])):d("",!0),t.makeReservation?(i(),r("div",{key:2,class:"action",style:{background:"#fee9d3"},onClick:o[3]||(o[3]=(...e)=>h.orderConference&&h.orderConference(...e))},[c("div",{class:"icon"},[c("i",{class:"icon-ion-android-calendar",style:{color:"#ff8700"}})]),c("p",{class:"title"},"预定会议")])):d("",!0)])]),c("div",{class:"right-slider"},[c("div",{class:"fav-container"},[c("p",{style:{"margin-bottom":"10px"}},"即将开始"),t.favConferenceInfos.length>0?(i(),r("div",{key:0,class:"fav-list"},[(i(!0),r(f,null,v(t.favConferenceInfos,((e,o)=>(i(),r("div",{class:"fav-conference",onClick:o=>h.showConferenceInfo(e),key:o},[c("div",{class:"icon"},[c("i",{class:"icon-ion-ios-videocam",style:{color:"#5d7ce8"}})]),c("div",{class:"title-box"},[c("p",{class:"title"},u(e.conferenceTitle),1),c("p",{class:"desc"},u(h.favConferenceDesc(e)),1)]),1===(null==e?void 0:e.userType)?(i(),r("div",{key:0,class:"fd-gp-title"},"作为监护人")):2===(null==e?void 0:e.userType)?(i(),r("div",{key:1,class:"fd-gp-title"},"作为公正见证人")):d("",!0)],8,["onClick"])))),128))])):(i(),r("div",{key:1,class:"empty"}," 没有即将开始的会议 "))]),c("div",{class:"history-container"},[c("p",{style:{"margin-bottom":"10px"}},"历史记录"),t.historyConferenceInfos.length>0?(i(),r("div",{key:0,class:"fav-list"},[(i(!0),r(f,null,v(t.historyConferenceInfos,((e,o)=>(i(),r("div",{class:"fav-conference",onClick:o=>h.showConferenceInfo(e),key:o},[c("div",{class:"icon"},[c("i",{class:"icon-ion-ios-videocam",style:{color:"#5d7ce8"}})]),c("div",{class:"title-box"},[c("p",{class:"title"},u(e.conferenceTitle),1),c("p",{class:"desc"},u(h.historyConferenceDesc(e)),1)]),1===(null==e?void 0:e.userType)?(i(),r("div",{key:0,class:"fd-gp-title"},"作为监护人")):2===(null==e?void 0:e.userType)?(i(),r("div",{key:1,class:"fd-gp-title"},"作为公正见证人")):d("",!0)],8,["onClick"])))),128))])):(i(),r("div",{key:1,class:"empty"}," 无历史会议记录 "))])])],4)):d("",!0)])}],["__scopeId","data-v-e2974e09"]]);export{C as default};
