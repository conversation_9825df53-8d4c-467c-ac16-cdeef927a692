function e(e,t){if(0===arguments.length||!e)return null;const n=t||"{y}-{m}-{d} {h}:{i}:{s}";let o;"object"==typeof e?o=e:("string"==typeof e&&(e=/^[0-9]+$/.test(e)?parseInt(e):e.replace(new RegExp(/-/gm),"/")),"number"==typeof e&&10===e.toString().length&&(e*=1e3),o=new Date(e));const r={y:o.getFullYear(),m:o.getMonth()+1,d:o.getDate(),h:o.getHours(),i:o.getMinutes(),s:o.getSeconds(),a:o.getDay()},s=n.replace(/{([ymdhisa])+}/g,((e,t)=>{const n=r[t];return"a"===t?["日","一","二","三","四","五","六"][n]:n.toString().padStart(2,"0")}));return s}function t(){sessionStorage.getItem("userInfoIM")&&setTimeout((()=>{const e=JSON.parse(sessionStorage.getItem("userInfoIM"));document.title=e.documentTitle}),0)}function n(e){if(!e&&"object"!=typeof e)throw new Error("error arguments","deepClone");const t=e.constructor===Array?[]:{};return Object.keys(e).forEach((o=>{e[o]&&"object"==typeof e[o]?t[o]=n(e[o]):t[o]=e[o]})),t}export{n as a,t as d,e as p};
