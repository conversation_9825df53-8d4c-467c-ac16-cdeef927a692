import{Z as t}from"./index-15d7e8a5.js";const e=t({name:"TrialPopup",props:{myPopupShow:{type:Boolean,default:!1},title:{type:String,default:"提示"},texts:{type:String,default:""},cancelText:{type:String,default:""},saveText:{type:String,default:""},cancelClass:{type:String,default:""},saveClass:{type:String,default:""},handleCancel:{type:Function,default:()=>{}},handleSave:{type:Function,default:()=>{}},myPopupShowClass:{type:String,default:""},centerBtnText:{type:String,default:""},centerBtnClass:{type:String,default:""},handleCenterBtn:{type:Function,default:()=>{}}}});export{e as _};
