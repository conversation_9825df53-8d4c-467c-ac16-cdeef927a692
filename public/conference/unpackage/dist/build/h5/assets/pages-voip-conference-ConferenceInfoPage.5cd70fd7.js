import{S as e,T as t,U as i,V as n,W as o,f as l,X as s,Y as a,K as r,l as c,w as d,c as h,m as f,b as u,J as m,H as p,o as g,d as v,L as y,M as w,e as I,t as C,q as x,g as T,j as b,n as k,F,h as S,i as V,N as P,Q as M,E as _,v as R,P as L,R as N,I as D}from"./index-15d7e8a5.js";import{d as j,a as U,p as A}from"./debounce.3401e956.js";import{M as B,_ as $}from"./men.d710d635.js";import z from"./pages-voip-conference-FacialRecognition.bb056bd6.js";import{_ as H}from"./leftArrows.3362d02a.js";import{_ as O,a as G}from"./checkInformedIcon.29557f46.js";/* empty css                                                                           */import{_ as E}from"./_plugin-vue_export-helper.1b428a4d.js";import{H as J}from"./HubConnectionBuilder.f43695dc.js";import"./MyPopupShow.vue_vue_type_script_lang.dace47fb.js";import"./FacialRecognition.vue_vue_type_style_index_0_scoped_8938bbe6_lang.d44d675b.js";let q,W;let K={type:"danger",color:void 0,message:"",onClose:void 0,onClick:void 0,onOpened:void 0,duration:3e3,position:void 0,className:"",lockScroll:!1,background:void 0};const Q=()=>{W&&W.toggle(!1)};function X(r){var c;if(e)return W||({instance:W}=n({setup(){const{state:e,toggle:t}=o();return()=>l(a,s(e,{"onUpdate:show":t}),null)}})),r=t({},K,i(c=r)?c:{message:c}),W.open(r),clearTimeout(q),r.duration>0&&(q=setTimeout(Q,r.duration)),W}const Y=""+new URL("name-icon-a81afba6.svg",import.meta.url).href,Z=""+new URL("identityCard-93fad6cd.svg",import.meta.url).href,ee=""+new URL("mobileIcon-d4a32f15.svg",import.meta.url).href,te=""+new URL("verifyCode-82321910.svg",import.meta.url).href;const ie=E({name:"ConferenceInfoPage",components:{MyPopupShow:B,FacialRecognition:z},data:()=>({conferenceInfo:{},enableVideo:!1,enableAudio:!1,ownerName:"",showModifyConferenceMdouleFlag:!1,title:"",desc:"",startTime:"",endTime:"",audioOnly:!1,audience:!1,advance:!1,allowTurnOnMic:!0,enablePassword:!1,pin:"",showTimes:!1,currentDate:"",currentTime:"",typeTime:0,activeTab:0,pickerTitle:"",loading:!1,password:"",outerHeight:document.documentElement.scrollHeight,showSelectTimesPicker:!1,selectTimesColumns:[{text:"15分钟",value:900},{text:"30分钟",value:1800},{text:"45分钟",value:2700},{text:"1小时",value:3600},{text:"2小时",value:7200},{text:"3小时",value:10800}],selectTimesValueColumns:[{text:"15分钟",value:"15分钟"},{text:"30分钟",value:"30分钟"},{text:"45分钟",value:"45分钟"},{text:"1小时",value:"1小时"},{text:"2小时",value:"2小时"},{text:"3小时",value:"3小时"}],selectTimesFieldValue:[],selectTimesFieldValueStr:"",showSelectMeetingPropertiesPicker:!1,selectMeetingPropertiesfieldValue:"",showSelectIcfPicker:!1,selectIcfFieldValue:"",showSelectInterviewPicker:!1,selectInterviewFieldValue:"",icfInfoId:"",visitId:"",showConfereeFlag:0,showAddconfereePoup:!1,addRoleFlag:1,confereeDoctorList:[],showConfereeDoctorPoup:!1,showAddTutelaLegitimaPoup:!1,legalGuardiansPhone:"",conferenceVisits:[],conferenceStatements:[],ySort:2,identityInformationFlag:!1,identityInformationLoading:!1,identityForm:{name:"",certNo:"",identityCardNo:""},identityConfigRes:{mode:1,certNo:"",name:"",identityCardNo:"",skipFaceIdentity:!1},businessType:-1,userInfo:{},facialRecognitionShowFlag:!1,joinConferenceLoadingFlag:!1,connectionStartFlag:0,connection:null,destroyConferenceBtnLoadingFlag:!1,selectMeetingPropertiescolumns:[],verifyTime:60}),onLoad(e){this.getOpenerEventChannel();let t=JSON.parse(e.conferenceInfo),i=t.conferenceId,n=t.password;if(t.conferenceInfo?this.conferenceInfo=t.conferenceInfo:this.getConferenceInfo(i,n),e.joinConferenceFlag&&this.joinConference("1"),location.href.includes("doctorui")){let e=location.origin+"/BackDoor/videotalk";location.origin.includes("http://localhost")&&(e="https://dct.test.trialdata.cn/BackDoor/videotalk");const t=(new J).withUrl(e).build();this.startConnect(t),t.onclose((()=>{setTimeout((()=>{this.startConnect(t)}),2e3)})),this.connection=t}},onShow(){j()},onUnload(){this.connection&&(this.connection.start().then((()=>{var e,t;this.connection.invoke("LeaveGroup",null==(t=null==(e=r)?void 0:e.conferenceInfo)?void 0:t.conferenceId)})).catch((e=>console.error(e.toString()))),this.connection.stop().catch((e=>console.error("停止SignalR连接时出错:",e))))},mounted(){const e=JSON.parse(sessionStorage.getItem("userInfoIM"));(null==e?void 0:e.studyId)&&(null==e?void 0:e.serialNo)&&!sessionStorage.getItem("conferenceSerialNo")&&(this.facialRecognitionShowFlag=!0),c.getConferenceStatements(e.patientId).then((t=>{this.conferenceStatements=t,(null==t?void 0:t.length)&&(this.selectMeetingPropertiescolumns[0]={text:"知情会议",value:"知情会议"}),c.getConferenceVisits(e.patientId).then((e=>{this.conferenceVisits=e,(null==e?void 0:e.length)&&(this.selectMeetingPropertiescolumns[this.selectMeetingPropertiescolumns.length]={text:"访视会议",value:"访视会议"})}))})).catch((()=>{c.getConferenceVisits(e.patientId).then((e=>{this.conferenceVisits=e,(null==e?void 0:e.length)&&this.selectMeetingPropertiescolumns.push({text:"访视会议",value:"访视会议"})}))})),this.ownerName=d.getUserDisplayName(this.conferenceInfo.owner);let t=0;location.href.includes("doctorui")?t=11:location.href.includes("patientui")&&(t=12),this.userInfo=e,(null==e?void 0:e.studyId)&&t?h.getIdentityVerificationResult({studyId:e.studyId,configType:12===t?1:2,triggerEvent:2}).then((e=>{var i;e.skipFaceIdentity=e.skipVerification,(null==e?void 0:e.identityCardNo)&&(e.certNo=null==e?void 0:e.identityCardNo),this.identityForm=U(e),this.identityConfigRes=U(e),(null==(i=this.$refs)?void 0:i.facialRecognitionRef)&&(this.$refs.facialRecognitionRef.skipFaceIdentity=e.skipFaceIdentity),this.businessType=t})).catch((()=>{this.businessType=t})):this.businessType=t,setTimeout((()=>{this.outerHeight=document.documentElement.scrollHeight}),0)},methods:{getVerifycode(){if(this.verifyTime/1<60)return;const{mobile:e}=this.identityForm;if(!/^[1][3-9][0-9]{9}$/.test(e)||!e)return void Notify({type:"danger",message:"请输入正确的手机号"});this.verifyTime=59,h.postIdentityVerificationSentVerifyCode({mobile:this.identityForm.mobile});const t=setInterval((()=>{this.verifyTime--,this.verifyTime/1==0&&(this.verifyTime=60,clearInterval(t))}),1e3)},startConnect(e){e.start().then((()=>{this.connectionStartFlag=1})).catch((()=>{setTimeout((()=>{this.startConnect(e)}),2e3)}))},handleSaveIdentityInformation(){var e;if(1==this.identityConfigRes.mode){const e=/^[1][3-9][0-9]{9}$/,{mobile:t}=this.identityForm;if(!e.test(t)||!t)return void Notify({type:"danger",message:"请输入正确的手机号"});if(!this.identityForm.verifyCode)return;this.identityInformationLoading=!0,h.postVerifyMobile(this.userInfo.studyId,{...this.identityForm,bizType:location.href.includes("doctorui")?11:12}).then((()=>{this.joinConferenceFun(),this.identityInformationLoading=!1})).catch((e=>{var t;f({title:(null==(t=null==e?void 0:e.data)?void 0:t.message)||"验证码错误",icon:"none"}),this.identityInformationLoading=!1}))}else this.identityForm.name?this.identityForm.certNo?2==this.identityConfigRes.mode&&h.postFacialIdentityApply(this.userInfo.studyId,{businessType:this.businessType,code:this.userInfo.code,conferenceId:this.conferenceInfo.conferenceId,name:this.identityConfigRes.certNo&&this.identityConfigRes.name?"":this.identityForm.name,certNo:this.identityConfigRes.certNo&&this.identityConfigRes.name?"":this.identityForm.certNo,patientId:!location.href.includes("doctorui")||location.href.includes("my/myPage")||location.href.includes("/home")?"":null==(e=this.userInfo)?void 0:e.patientId}).then((e=>{sessionStorage.removeItem("conferenceSerialNo"),window.parent.postMessage(`certUr=${e.certUrl}`,"*")})).catch((e=>{f({title:e.message||"核验失败",icon:"none"})})):f({title:"请填写身份证号",icon:"none"}):f({title:"请填写姓名",icon:"none"})},modifyConference(){var e,t,i,n,o,l,s,a,r;if(this.title=null==(e=null==this?void 0:this.conferenceInfo)?void 0:e.conferenceTitle,this.start&&(this.startTime=this.start.slice(0,-3)),null==(t=this.conferenceInfo)?void 0:t.endTime){const e=(null==(i=this.conferenceInfo)?void 0:i.endTime)-(null==(n=this.conferenceInfo)?void 0:n.startTime);this.selectTimesColumns.map((t=>{t.value===e&&(this.selectTimesFieldValueStr=t.text,this.selectTimesFieldValue=[t.text])}))}if((null==(o=this.conferenceInfo)?void 0:o.visitId)?(this.selectMeetingPropertiesfieldValue="访视会议",this.conferenceVisits.map((e=>{e.visitTemplateId===this.conferenceInfo.visitId&&(this.selectInterviewFieldValue=e.visitName)})),this.visitId=this.conferenceInfo.visitId):(null==(l=this.conferenceInfo)?void 0:l.icfStatementId)&&(this.selectMeetingPropertiesfieldValue="知情会议",this.conferenceStatements.map((e=>{e.icfInfoId===this.conferenceInfo.icfStatementId&&(this.selectIcfFieldValue=e.icfInfoName)})),this.icfInfoId=null==(s=this.conferenceInfo)?void 0:s.icfStatementId),null==(r=null==(a=this.conferenceInfo)?void 0:a.users)?void 0:r.length){const{users:e}=this.conferenceInfo;e.map(((e,t)=>{e.ySort=t+1})),this.confereeDoctorList=e}this.showModifyConferenceMdouleFlag=!0},destroyConferenceBtn(){if(this.destroyConferenceBtnLoadingFlag)return;const e={roomId:this.conferenceInfo.conferenceId,advance:!0};this.destroyConferenceBtnLoadingFlag=!0,c.destroyConference(e).then((()=>{var e;f({title:"取消会议成功",icon:"none"}),(null==(e=this.conferenceInfo)?void 0:e.enableWaitingRoom)&&this.connectionStartFlag?this.connection.invoke("SendMessage",this.conferenceInfo.conferenceId,9,this.conferenceInfo.conferenceId+"").then((()=>{this.goBack(),this.destroyConferenceBtnLoadingFlag=!1})).catch((()=>{this.goBack(),this.destroyConferenceBtnLoadingFlag=!1})):(this.goBack(),this.destroyConferenceBtnLoadingFlag=!1)})).catch((()=>{f({title:"取消会议失败",icon:"none"}),this.destroyConferenceBtnLoadingFlag=!1}))},getConferenceInfo(e,t){h.queryConferenceInfo(e,t).then((e=>{this.conferenceInfo=e})).catch((()=>{this.goBack()}))},goBack(){u({url:"/pages/voip/conference/ConferencePortalPage"})},favConference(){h.favConference(this.conferenceInfo.conferenceId).then((e=>{this.$refs.favButton.title="已收藏",this.$refs.favButton.disabled=!0})).catch((e=>{console.error("favConference error",e)}))},joinConference(e){"1"===e||!this.identityConfigRes.skipFaceIdentity&&this.businessType?this.identityInformationFlag=!0:this.joinConferenceFun()},joinConferenceFun(){if(this.conferenceInfo.enableWaitingRoom&&this.conferenceInfo.owner!==d.getUserId())this.joinConferenceLoadingFlag||(this.joinConferenceLoadingFlag=!0,h.postOperateConference({conferenceId:this.conferenceInfo.id,operateType:10}).then((()=>{u({url:"/pages/voip/conference/WaitingRoom?conferenceInfo="+JSON.stringify(this.conferenceInfo)+`&enableVideo=${this.enableVideo}&&enableAudio=${this.enableAudio}`}),this.joinConferenceLoadingFlag=!1})).catch((()=>{this.joinConferenceLoadingFlag=!1})));else{const e=this.conferenceInfo;let t=!this.enableVideo&&!this.enableAudio;!this.joinConferenceLoadingFlag&&e.conferenceId&&(this.joinConferenceLoadingFlag=!0,h.dctJoinConference({conferenceId:e.id}).then((()=>{m.joinConference(e.conferenceId,!1,e.pin,e.owner,e.conferenceTitle,"",t,e.advance,!this.enableAudio,!this.enableVideo),r.conferenceInfo=e,this.joinConferenceLoadingFlag=!1})).catch((e=>{var t,i,n,o;this.joinConferenceLoadingFlag=!1,((null==(t=null==e?void 0:e.data)?void 0:t.detail)||(null==(i=null==e?void 0:e.data)?void 0:i.message))&&f({title:(null==(n=null==e?void 0:e.data)?void 0:n.detail)||(null==(o=null==e?void 0:e.data)?void 0:o.message),icon:"none"})})))}},handleSaveAddTutelaLegitima(){this.confereeDoctorList.find((e=>this.legalGuardiansPhone===e.mobile))?this.$notify({text:"手机号不可重复",type:"warn"}):/^[1][3-9][0-9]{9}$/.test(this.legalGuardiansPhone)&&(this.ySort+=1,this.confereeDoctorList.push({mobile:this.legalGuardiansPhone,roleName:2===this.addRoleFlag?"监护人":"公正见证人",userName:"",userType:this.addRoleFlag-1,directionFlag:"left",ySort:this.ySort}),this.showAddTutelaLegitimaPoup=!1)},handleSaveConfereeDoctor(){this.confereeDoctorList.forEach((e=>{"right"===(null==e?void 0:e.directionFlag)&&e.checked&&(e.directionFlag="left",this.ySort+=1,e.ySort=this.ySort)})),this.showConfereeDoctorPoup=!1},handleSaveAddconferee(){if(this.addRoleFlag>1)this.legalGuardiansPhone="",this.showAddTutelaLegitimaPoup=!0;else{if(!this.confereeDoctorList.find((e=>"right"===(null==e?void 0:e.directionFlag))))return void this.$notify({text:"没有可选择的研究人员",type:"warn"});this.showConfereeDoctorPoup=!0}this.showAddconfereePoup=!1},handleAddconferee(){this.showAddconfereePoup=!0;const e=JSON.parse(sessionStorage.getItem("userInfoIM"));(null==e?void 0:e.patientId)&&c.getDoctorConferenceUser(null==e?void 0:e.patientId).then((e=>{var t;if(null==e?void 0:e.length){let i=e.filter((e=>this.conferenceInfo.users.every((t=>(t.mobile===(null==e?void 0:e.mobile)&&(t.directionFlag="left"),t.mobile!==(null==e?void 0:e.mobile))))));(null==(t=this.confereeDoctorList)?void 0:t.length)&&(i=i.filter((e=>this.confereeDoctorList.every((t=>t.mobile!==(null==e?void 0:e.mobile)))))),this.confereeDoctorList=this.confereeDoctorList.concat(i.map((e=>(e.ySort=999,e.directionFlag="right",e))))}}))},pickerTimeGroup(){this.showSelectTimesPicker=!0},onSelectTimesConfirm({selectedOptions:e}){this.showSelectTimesPicker=!1,this.selectTimesFieldValueStr=e[0].text,this.selectTimesFieldValue=[e[0].text]},filterStartTime:(e,t)=>"minute"===e?t.filter((e=>"00"===e.value||"05"===e.value||"10"===e.value||"15"===e.value||"20"===e.value||"25"===e.value||"30"===e.value||"35"===e.value||"40"===e.value||"45"===e.value||"50"===e.value||"55"===e.value)):t,onSelectInterviewConfirm({selectedOptions:e}){this.showSelectInterviewPicker=!1,this.selectInterviewFieldValue=e[0].visitName,this.visitId=e[0].visitTemplateId,this.selectIcfFieldValue=""},onSelectIcfConfirm({selectedOptions:e}){this.showSelectIcfPicker=!1,this.selectIcfFieldValue=e[0].icfInfoName,this.icfInfoId=e[0].icfInfoId,this.selectInterviewFieldValue=""},onSelectMeetingPropertiesConfirm({selectedOptions:e}){this.showSelectMeetingPropertiesPicker=!1,this.selectMeetingPropertiesfieldValue=e[0].text,"知情会议"===this.selectMeetingPropertiesfieldValue?this.selectInterviewFieldValue="":this.selectIcfFieldValue=""},onConfirm(){1===this.typeTime?this.startTime=this.currentDate.join("-")+" "+this.currentTime.join(":"):2===this.typeTime&&(this.endTime=this.currentDate.join("-")+" "+this.currentTime.join(":")),this.showTimes=!1},pickerGroup(e){if(this.activeTab=0,this.typeTime=e,1===e)if(this.pickerTitle="开始时间",this.startTime)this.currentDate=this.startTime.split(" ")[0].split("-"),this.currentTime=this.startTime.split(" ")[1].split(":");else{const e=A(new Date,"{y}-{m}-{d} {h}:{i}");this.currentDate=e.split(" ")[0].split("-");const t=e.split(" ")[1].split(":"),i=Number(t[1]),n=Number(t[0]);i&&(i/5==0||(i<5?t[1]="05":i<10?t[1]="10":i<15?t[1]="15":i<20?t[1]="20":i<25?t[1]="25":i<30?t[1]="30":i<35?t[1]="35":i<40?t[1]="40":i<45?t[1]="45":i<50?t[1]="50":i>55&&(t[1]="00",t[0]=n+1<9?`0${n+1}`:`${n+1}`))),this.currentTime=t}this.showTimes=!0},orderConference(){if(this.enablePassword&&!this.password)return void this.$notify({text:"请输入会议密码",type:"warn"});if(!this.startTime)return void this.$notify({text:"请选择开始时间",type:"warn"});if(!this.selectTimesFieldValueStr)return void this.$notify({text:"请选择时长",type:"warn"});if(!this.selectMeetingPropertiesfieldValue)return void this.$notify({text:"请选择会议属性",type:"warn"});if("知情会议"===this.selectMeetingPropertiesfieldValue&&!this.selectIcfFieldValue)return void this.$notify({text:"请选择知情",type:"warn"});if("访视会议"===this.selectMeetingPropertiesfieldValue&&!this.selectInterviewFieldValue)return void this.$notify({text:"请选择访视",type:"warn"});const e=U(this.conferenceInfo);e.conferenceTitle=this.title,this.password&&this.enablePassword&&(e.password=this.password),e.pin=""+Math.ceil((1+1e5*Math.random())/10),e.owner=d.getUserId()||p("userId"),e.managers="",e.startTime=Math.ceil(new Date(this.startTime).getTime()/1e3),e.start=A(new Date(this.startTime),"{y}-{m}-{d} {h}:{i}");let t=0;this.selectTimesColumns.map((e=>{e.text===this.selectTimesFieldValueStr&&(t=e.value)}));let i=1e3*(e.startTime+t);e.endTime=Math.ceil(i/1e3),e.end=A(new Date(i),"{y}-{m}-{d} {h}:{i}"),e.audience=this.audience,e.allowSwitchMode=this.allowTurnOnMic,e.advance=this.advance,"知情会议"===this.selectMeetingPropertiesfieldValue?e.icfStatementId=this.icfInfoId:e.visitId=this.visitId,e.conferenceType="知情会议"===this.selectMeetingPropertiesfieldValue?1:2,e.users=this.confereeDoctorList.filter((e=>"right"!==e.directionFlag));const n=JSON.parse(sessionStorage.getItem("userInfoIM"));e.dctPatientId=n.patientId,this.loading||(this.loading=!0,h.postUpdateConference(e).then((()=>{X({type:"success",message:"修改会议成功"}),this.goBack(),this.loading=!1})).catch((()=>{this.loading=!1})))}},computed:{start(){if(this.conferenceInfo.startTime)return A(new Date(1e3*this.conferenceInfo.startTime),"{y}-{m}-{d} {h}:{i}:{s}")},end(){return this.conferenceInfo.endTime?A(new Date(1e3*this.conferenceInfo.endTime),"{y}-{m}-{d} {h}:{i}:{s}"):"-"},enableDestroy(){var e;return!(null==(e=this.conferenceInfo)?void 0:e.isComplete)&&this.conferenceInfo.owner===d.getUserId()&&1!==this.conferenceInfo.conferenceStatus}}},[["render",function(e,t,i,n,o,s){var a,r,c,d;const h=V,f=P,u=N,m=M,p=_("van-button"),j=D,U=_("van-icon"),A=_("van-picker"),B=_("van-popup"),z=_("MyPopupShow"),E=_("van-field"),J=_("van-form"),q=_("van-date-picker"),W=_("van-time-picker"),K=_("van-picker-group"),Q=_("van-cell-group"),X=_("FacialRecognition");return g(),v("div",null,[y(I("div",null,[I("div",{class:"nav-title"},[I("div",{style:{width:"18px",height:"18px",overflow:"hidden"},onClick:t[0]||(t[0]=(...e)=>s.goBack&&s.goBack(...e))},[l(h,{style:{width:"100%",height:"100%"},src:H,mode:""})]),I("div",null,"会议详情"),I("div",{style:{width:"18px",height:"18px",overflow:"hidden"}})]),I("div",{class:"conference-info-container"},[I("div",{class:"item-container"},[I("div",{class:"item"},[I("p",{class:"title"},"会议主题"),I("p",{class:"desc"},C(o.conferenceInfo.conferenceTitle),1)]),I("div",{class:"item"},[I("p",{class:"title"},"发起人"),I("p",{class:"desc"},C(o.conferenceInfo.createUserName),1)]),I("div",{class:"item"},[I("p",{class:"title"},"会议号"),I("p",{class:"desc"},C(o.conferenceInfo.conferenceId),1)])]),I("div",{class:"item-container"},[I("div",{class:"item"},[I("p",{class:"title"},"开始时间"),I("p",{class:"desc"},C(s.start),1)]),I("div",{class:"item"},[I("p",{class:"title"},"结束时间"),I("p",{class:"desc"},C(s.end),1)])]),(new Date).getTime()<1e3*o.conferenceInfo.endTime&&!(null==(a=o.conferenceInfo)?void 0:a.isComplete)?(g(),v("div",{key:0,class:"item-container"},[l(m,{class:"item",onChange:t[1]||(t[1]=e=>o.enableVideo=!o.enableVideo)},{default:x((()=>[l(f,null,{default:x((()=>[R(" 开启视频 ")])),_:1}),l(u,{disabled:o.audience,checked:o.enableVideo},null,8,["disabled","checked"])])),_:1}),l(m,{class:"item",onChange:t[2]||(t[2]=e=>o.enableAudio=!o.enableAudio),style:{display:"flex","flex-direction":"row","justify-content":"space-between",padding:"12px 20px","border-spacing":"20px"}},{default:x((()=>[l(f,null,{default:x((()=>[R(" 开启音频 ")])),_:1}),l(u,{disabled:o.audience,checked:o.enableAudio},null,8,["disabled","checked"])])),_:1})])):T("",!0),I("div",{class:"action-container"},[s.enableDestroy?(g(),b(p,{key:0,style:{width:"50%","margin-right":"10px"},type:"danger",class:"destroy",onClick:s.destroyConferenceBtn},{default:x((()=>[R(" 取消会议 ")])),_:1},8,["onClick"])):T("",!0),s.enableDestroy&&(new Date).getTime()<=1e3*o.conferenceInfo.startTime?(g(),b(p,{key:1,style:{width:"50%","margin-right":"10px"},type:"primary",plain:"",onClick:s.modifyConference},{default:x((()=>[R(" 修改会议 ")])),_:1},8,["onClick"])):T("",!0),(null==(r=o.conferenceInfo)?void 0:r.isComplete)||-1===o.businessType?-1!==o.businessType?(g(),b(p,{key:3,style:{width:"100%"},disabled:""},{default:x((()=>[R(" 会议已结束 ")])),_:1})):T("",!0):(g(),b(p,{key:2,type:"primary",style:k({width:s.enableDestroy?"50%":"100%"}),onClick:s.joinConference},{default:x((()=>[R(" 加入会议 ")])),_:1},8,["style","onClick"]))])])],512),[[w,!o.facialRecognitionShowFlag&&!o.showModifyConferenceMdouleFlag&&!o.identityInformationFlag]]),y(I("div",{class:"modifyConferenceMdoule"},[y(I("div",null,[I("div",{style:k({height:o.outerHeight?o.outerHeight-44+"px":"calc(100vh - 44px)",overflow:"auto"})},[I("div",{class:"nav-title"},[I("div",{style:{width:"18px",height:"18px",overflow:"hidden"},onClick:t[3]||(t[3]=e=>o.showModifyConferenceMdouleFlag=!1)},[l(h,{style:{width:"100%",height:"100%"},src:H,mode:""})]),I("div",null,"修改会议预定"),I("div",{style:{width:"18px",height:"18px",overflow:"hidden"}})]),I("div",{class:"order-conference-container"},[l(f,null,{default:x((()=>[I("span",{style:{"white-space":"nowrap","margin-right":"10px"}},"会议标题"),l(j,{modelValue:o.title,"onUpdate:modelValue":t[4]||(t[4]=e=>o.title=e),class:"text-input tips-box",placeholder:"请输入"},null,8,["modelValue"])])),_:1}),T("",!0),l(f,null,{default:x((()=>[R(" 开始时间 "),I("div",{class:L({"color-e3e3e3":!o.startTime}),style:{flex:"1","text-align":"right"},onClick:t[6]||(t[6]=e=>s.pickerGroup(1))},C(o.startTime||"请选择"),3),l(U,{style:{"font-size":"18px","margin-left":"10px"},name:"arrow"})])),_:1}),l(f,null,{default:x((()=>[R(" 时长 "),I("div",{style:{flex:"1","text-align":"right"},class:L({"color-e3e3e3":!o.selectTimesFieldValueStr}),onClick:t[7]||(t[7]=(...e)=>s.pickerTimeGroup&&s.pickerTimeGroup(...e))},C(o.selectTimesFieldValueStr||"请选择"),3),l(U,{style:{"font-size":"18px","margin-left":"10px"},name:"arrow"})])),_:1}),l(f,null,{default:x((()=>[I("div",null,"参会人员"),I("div",{class:"centerflex-h",onClick:t[8]||(t[8]=()=>{o.showConfereeFlag=1})},[I("div",null,C(o.confereeDoctorList.filter((e=>"right"!==e.directionFlag)).length||2),1),l(U,{style:{"font-size":"18px","margin-left":"10px"},name:"arrow"})])])),_:1}),l(f,null,{default:x((()=>[I("div",null,"会议属性"),I("div",{class:"centerflex-h",onClick:t[9]||(t[9]=()=>{o.showSelectMeetingPropertiesPicker=!0})},[I("div",{class:L({"color-e3e3e3":!o.selectMeetingPropertiesfieldValue})},C(o.selectMeetingPropertiesfieldValue||"请选择"),3),l(U,{style:{"font-size":"18px","margin-left":"10px"},name:"arrow"})])])),_:1}),"知情会议"===o.selectMeetingPropertiesfieldValue?(g(),b(f,{key:1},{default:x((()=>[I("div",null,"关联知情"),I("div",{class:"centerflex-h",onClick:t[10]||(t[10]=()=>{o.showSelectIcfPicker=!0})},[I("div",{style:{"max-width":"260px"},class:L({"color-e3e3e3":!o.selectIcfFieldValue})},C(o.selectIcfFieldValue||"请选择"),3),l(U,{style:{"font-size":"18px","margin-left":"10px"},name:"arrow"})])])),_:1})):"访视会议"===o.selectMeetingPropertiesfieldValue?(g(),b(f,{key:2},{default:x((()=>[I("div",null,"关联访视"),I("div",{class:"centerflex-h",onClick:t[11]||(t[11]=()=>{o.showSelectInterviewPicker=!0})},[I("div",{style:{"max-width":"260px"},class:L({"color-e3e3e3":!o.selectInterviewFieldValue})},C(o.selectInterviewFieldValue||"请选择"),3),l(U,{style:{"font-size":"18px","margin-left":"10px"},name:"arrow"})])])),_:1})):T("",!0)])],4),l(p,{style:{width:"100%","border-radius":"0"},type:"primary",loading:o.loading,"loading-text":"确定修改",onClick:s.orderConference},{default:x((()=>[R("确定修改")])),_:1},8,["loading","onClick"])],512),[[w,!o.showConfereeFlag]]),l(B,{show:o.showSelectTimesPicker,"onUpdate:show":t[14]||(t[14]=e=>o.showSelectTimesPicker=e),round:"",position:"bottom"},{default:x((()=>[l(A,{modelValue:o.selectTimesFieldValue,"onUpdate:modelValue":t[12]||(t[12]=e=>o.selectTimesFieldValue=e),columns:o.selectTimesValueColumns,onCancel:t[13]||(t[13]=e=>o.showSelectTimesPicker=!1),onConfirm:s.onSelectTimesConfirm},null,8,["modelValue","columns","onConfirm"])])),_:1},8,["show"]),y(I("div",null,[I("div",{style:k({height:o.outerHeight?o.outerHeight-44+"px":"calc(100vh - 44px)",padding:"0 15px",overflow:"auto"})},[I("div",{class:"nav-title"},[I("div",{style:{width:"18px",height:"18px",overflow:"hidden"},onClick:t[15]||(t[15]=e=>o.showConfereeFlag=0)},[l(h,{style:{width:"100%",height:"100%"},src:H,mode:""})]),I("div",null,"参会人员"),I("div",{style:{width:"18px",height:"18px",overflow:"hidden"}})]),I("div",{class:"bt-hr"}),(g(!0),v(F,null,S(o.confereeDoctorList.sort(((e,t)=>e.ySort-t.ySort)),((e,t)=>(g(),v("div",{key:t},[t<2||t>1&&"right"!==(null==e?void 0:e.directionFlag)?(g(),v("div",{key:0,class:"centerflex-h bt-hr",style:{padding:"15px 0"}},[(null==e?void 0:e.avatarUrl)?(g(),b(h,{key:0,style:{width:"48px",height:"48px","margin-right":"15px","border-radius":"50%"},src:e.avatarUrl,mode:""},null,8,["src"])):2===(null==e?void 0:e.userType)?(g(),v("div",{key:1,style:{width:"48px",height:"48px","margin-right":"15px",color:"#fff","background-color":"#41b592","border-radius":"50%","font-size":"12px",padding:"5px 0px","box-sizing":"border-box"}},[I("div",{class:"centerflex no-wrap w-full"},"公正"),I("div",{class:"centerflex no-wrap w-full"},"见证人")])):1===(null==e?void 0:e.userType)?(g(),v("div",{key:2,style:{width:"48px",height:"48px","margin-right":"15px","font-size":"12px",color:"#fff","background-color":"#5860da","border-radius":"50%","box-sizing":"border-box"},class:"centerflex"},[I("div",{class:"no-wrap"},"监护人")])):(g(),b(h,{key:3,style:{width:"48px",height:"48px","margin-right":"15px"},src:$,mode:""})),I("div",{class:"flex-1"},[I("div",{style:{"font-size":"15px","margin-bottom":"8px"},class:"flex justify-between"},[(null==e?void 0:e.userName)?(g(),v("div",{key:0},C(e.userName),1)):(g(),v("div",{key:1}," ")),t&&4!==(null==e?void 0:e.userType)?(g(),v("div",{key:2,style:{color:"#d9001b"},onClick:()=>{e.directionFlag="right",0===(null==e?void 0:e.userType)?e.checked=!1:e.mobile=""}}," 移除 ",8,["onClick"])):T("",!0)]),I("div",{style:{"font-size":"12px",color:"#9a9a9a"}},[t?T("",!0):(g(),v("span",{key:0},"我，主持人，")),(null==e?void 0:e.roleName)?(g(),v("span",{key:1},C(e.roleName.replace(/,/g,"、")),1)):T("",!0)])])])):T("",!0)])))),128))],4),l(p,{style:{width:"100%","border-radius":"0"},type:"primary",onClick:s.handleAddconferee},{default:x((()=>[R("添加参会人员")])),_:1},8,["onClick"]),l(z,{myPopupShow:o.showAddconfereePoup,title:"请选择",cancelText:"取消",saveText:"确认",handleSave:s.handleSaveAddconferee,handleCancel:()=>{o.showAddconfereePoup=!1}},{bodyslot:x((()=>[I("div",{class:"centerflex",style:k([{"border-radius":"4px",padding:"10px 0",width:"90%","box-shadow":"0 1px 12px rgba(0, 0, 0, 0.07)",margin:"15px auto"},1===o.addRoleFlag?{background:"#E9EFFC",color:"#5860da"}:{}]),onClick:t[16]||(t[16]=()=>{o.addRoleFlag=1})}," 研究人员 ",4),I("div",{class:"centerflex",style:k([{"border-radius":"4px",padding:"10px 0",width:"90%","box-shadow":"0 1px 12px rgba(0, 0, 0, 0.07)",margin:"15px auto"},2===o.addRoleFlag?{background:"#E9EFFC",color:"#5860da"}:{}]),onClick:t[17]||(t[17]=()=>{o.addRoleFlag=2})}," 监护人 ",4),I("div",{class:"centerflex",style:k([{"border-radius":"4px",padding:"10px 0",width:"90%","box-shadow":"0 1px 12px rgba(0, 0, 0, 0.07)",margin:"15px auto"},3===o.addRoleFlag?{background:"#E9EFFC",color:"#5860da"}:{}]),onClick:t[18]||(t[18]=()=>{o.addRoleFlag=3})}," 公正见证人 ",4)])),_:1},8,["myPopupShow","handleSave","handleCancel"]),l(z,{myPopupShow:o.showAddTutelaLegitimaPoup,title:2===o.addRoleFlag?"监护人":"公正见证人",cancelText:"取消",saveText:"确认",handleSave:s.handleSaveAddTutelaLegitima,handleCancel:()=>{o.showAddTutelaLegitimaPoup=!1}},{bodyslot:x((()=>[I("div",{style:{padding:"0 16px"}}," 该手机号将用于用户注册登录，以及接收会议通知 "),l(J,{class:"participant-form"},{default:x((()=>[l(E,{modelValue:o.legalGuardiansPhone,"onUpdate:modelValue":t[19]||(t[19]=e=>o.legalGuardiansPhone=e),modelModifiers:{trim:!0},name:"legalGuardiansPhone",rules:[{required:!0,message:"请输入"},{validator:e=>/^[1][3-9][0-9]{9}$/.test(e),message:"手机格式错误",trigger:"onBlur"}],label:"手机号",maxlength:"11",placeholder:"请输入"},null,8,["modelValue","rules"])])),_:1})])),_:1},8,["myPopupShow","title","handleSave","handleCancel"]),l(z,{myPopupShow:o.showConfereeDoctorPoup,title:"研究人员",cancelText:"取消",saveText:"确认",handleSave:s.handleSaveConfereeDoctor,handleCancel:()=>{o.showConfereeDoctorPoup=!1}},{bodyslot:x((()=>[I("div",{style:k({maxHeight:o.outerHeight-240+"px",overflow:"auto"})},[(g(!0),v(F,null,S(o.confereeDoctorList,((e,t)=>(g(),v("div",{key:t},["right"===(null==e?void 0:e.directionFlag)&&0===(null==e?void 0:e.userType)?(g(),v("div",{key:0,class:"centerflex-h justify-between",style:{padding:"10px"}},[I("div",null,C(e.userName)+"，"+C(e.roleName.replace(/,/g,"、")),1),y(I("img",{style:{width:"15px"},src:O,alt:"",onClick:()=>{e.checked=!(null==e?void 0:e.checked)}},null,8,["onClick"]),[[w,!(null==e?void 0:e.checked)]]),y(I("img",{style:{width:"15px"},src:G,alt:"",onClick:()=>{e.checked=!(null==e?void 0:e.checked)}},null,8,["onClick"]),[[w,null==e?void 0:e.checked]])])):T("",!0)])))),128))],4)])),_:1},8,["myPopupShow","handleSave","handleCancel"])],512),[[w,1===o.showConfereeFlag]]),l(B,{show:o.showSelectMeetingPropertiesPicker,"onUpdate:show":t[21]||(t[21]=e=>o.showSelectMeetingPropertiesPicker=e),round:"",position:"bottom"},{default:x((()=>[l(A,{columns:o.selectMeetingPropertiescolumns,onCancel:t[20]||(t[20]=e=>o.showSelectMeetingPropertiesPicker=!1),onConfirm:s.onSelectMeetingPropertiesConfirm},null,8,["columns","onConfirm"])])),_:1},8,["show"]),l(B,{show:o.showSelectIcfPicker,"onUpdate:show":t[23]||(t[23]=e=>o.showSelectIcfPicker=e),round:"",position:"bottom"},{default:x((()=>[l(A,{columns:o.conferenceStatements,"columns-field-names":{text:"icfInfoName",value:"icfInfoId"},onCancel:t[22]||(t[22]=e=>o.showSelectIcfPicker=!1),onConfirm:s.onSelectIcfConfirm},null,8,["columns","onConfirm"])])),_:1},8,["show"]),l(B,{show:o.showSelectInterviewPicker,"onUpdate:show":t[25]||(t[25]=e=>o.showSelectInterviewPicker=e),round:"",position:"bottom"},{default:x((()=>[l(A,{columns:o.conferenceVisits,"columns-field-names":{text:"visitName",value:"visitTemplateId"},onCancel:t[24]||(t[24]=e=>o.showSelectInterviewPicker=!1),onConfirm:s.onSelectInterviewConfirm},null,8,["columns","onConfirm"])])),_:1},8,["show"]),l(B,{show:o.showTimes,"onUpdate:show":t[30]||(t[30]=e=>o.showTimes=e),position:"bottom"},{default:x((()=>[l(K,{title:o.pickerTitle,tabs:["选择日期","选择时间"],"next-step-text":"下一步",onConfirm:s.onConfirm,onCancel:t[28]||(t[28]=e=>o.showTimes=!1),"active-tab":o.activeTab,"onUpdate:activeTab":t[29]||(t[29]=e=>o.activeTab=e)},{default:x((()=>[l(q,{modelValue:o.currentDate,"onUpdate:modelValue":t[26]||(t[26]=e=>o.currentDate=e)},null,8,["modelValue"]),l(W,{modelValue:o.currentTime,"onUpdate:modelValue":t[27]||(t[27]=e=>o.currentTime=e),filter:s.filterStartTime},null,8,["modelValue","filter"])])),_:1},8,["title","onConfirm","active-tab"])])),_:1},8,["show"])],512),[[w,o.showModifyConferenceMdouleFlag]]),y(I("div",null,[I("div",{style:k({height:o.outerHeight?o.outerHeight-44+"px":"calc(100vh - 44px)",overflow:"auto"})},[I("div",{class:"nav-title"},[I("div",{style:{width:"18px",height:"18px",overflow:"hidden"},onClick:t[31]||(t[31]=e=>o.identityInformationFlag=!1)},[l(h,{style:{width:"100%",height:"100%"},src:H,mode:""})]),I("div",null,"身份确认"),I("div",{style:{width:"18px",height:"18px",overflow:"hidden"}})]),I("div",null,[I("div",{style:{padding:"40px 20px","font-size":"15px",color:"#3e3e3e"}}," 根据临床研究相关法规及指导原则要求，本次操作需核验您的身份，我们将对个人信息严格保密。 "),I("div",{style:{"text-align":"center","font-weight":"bold",padding:"0px 20px 40px 20px","font-size":"20px",color:"#333333"}}," 您是"+C((null==(c=o.identityConfigRes)?void 0:c.identity)||""),1),2==(null==(d=o.identityConfigRes)?void 0:d.mode)?(g(),b(Q,{key:0,inset:"",class:"form-modules-body"},{default:x((()=>{var e;return[I("div",{class:"mobile-inputs"},[I("img",{src:Y,alt:""}),l(E,{modelValue:o.identityForm.name,"onUpdate:modelValue":t[32]||(t[32]=e=>o.identityForm.name=e),modelModifiers:{trim:!0},disabled:!!(null==(e=o.identityConfigRes)?void 0:e.name),placeholder:"姓名",maxlength:"20",rules:[{required:!0,message:"请填写"}]},null,8,["modelValue","disabled"])]),I("div",{class:"mobile-inputs"},[I("img",{src:Z,alt:""}),l(E,{modelValue:o.identityForm.certNo,"onUpdate:modelValue":t[33]||(t[33]=e=>o.identityForm.certNo=e),modelModifiers:{trim:!0},disabled:!!o.identityConfigRes.certNo,placeholder:"身份证号",maxlength:"20",rules:[{required:!0,message:"请填写"}]},null,8,["modelValue","disabled"])])]})),_:1})):T("",!0),l(J,{class:"form-modules"},{default:x((()=>[1==o.identityConfigRes.mode?(g(),b(Q,{key:0,inset:"",class:"form-modules-body"},{default:x((()=>[I("div",{class:"mobile-inputs"},[I("img",{src:ee,alt:""}),l(E,{modelValue:o.identityForm.mobile,"onUpdate:modelValue":t[34]||(t[34]=e=>o.identityForm.mobile=e),modelModifiers:{trim:!0},disabled:!!o.identityConfigRes.mobile,placeholder:"手机号",rules:[{validator:e=>/^[1][3-9][0-9]{9}$/.test(e),message:"请输入正确的手机号",trigger:"onBlur"}]},null,8,["modelValue","disabled","rules"])]),I("div",{class:"mobile-inputs"},[I("img",{src:te,alt:""}),l(E,{modelValue:o.identityForm.verifyCode,"onUpdate:modelValue":t[35]||(t[35]=e=>o.identityForm.verifyCode=e),modelModifiers:{trim:!0},placeholder:"验证码",rules:[{required:!0,message:"请写填验证码"}]},null,8,["modelValue"])]),I("div",{class:"getverify",onClick:t[36]||(t[36]=(...e)=>s.getVerifycode&&s.getVerifycode(...e))}," |  "+C(o.verifyTime>59?"获取验证码":o.verifyTime),1)])),_:1})):T("",!0)])),_:1})])],4),l(p,{style:{width:"100%","border-radius":"0"},type:"primary",loading:o.identityInformationLoading,"loading-text":"确定",onClick:s.handleSaveIdentityInformation},{default:x((()=>[R("确定")])),_:1},8,["loading","onClick"])],512),[[w,o.identityInformationFlag]]),o.facialRecognitionShowFlag?(g(),b(X,{key:0,joinConferenceFun:s.joinConferenceFun,ref:"facialRecognitionRef"},null,8,["joinConferenceFun"])):T("",!0)])}],["__scopeId","data-v-33c51f65"]]);export{ie as default};
