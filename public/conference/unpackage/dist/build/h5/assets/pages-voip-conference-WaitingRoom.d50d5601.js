import{w as e,c as n,K as o,J as t,b as i,o as s,d as r,e as a,n as c,t as l,g as d,F as h,h as f,j as g,f as u,q as p,E as v,v as m}from"./index-15d7e8a5.js";import{d as x}from"./debounce.3401e956.js";import{C as y,_ as I}from"./chat-icon.2d9995d8.js";import{M as w,_ as b}from"./men.d710d635.js";import{_ as C}from"./_plugin-vue_export-helper.1b428a4d.js";import{H as k}from"./HubConnectionBuilder.f43695dc.js";import"./MyPopupShow.vue_vue_type_script_lang.dace47fb.js";const F=""+new URL("leave-icon-6c3c8226.svg",import.meta.url).href;const j=C({name:"WaitingRoom",components:{ChatRoom:y,MyPopupShow:w},data:()=>({user:{userId:""},conferenceInfo:{},outerHeight:document.documentElement.scrollHeight,waitingRoomObj:{users:[]},showChatModuleFlag:!1,backLoadingFlag:!1,joinConferenceLoadingFlag:!1,getConferenceWaittingUsersInterval:null,connection:null,enableVideo:!1,enableAudio:!1,joinConferenceLeaveWaitingRoomFlag:!1,destroyFlag:0}),onLoad(n){this.getOpenerEventChannel();const o=JSON.parse(n.conferenceInfo);this.enableVideo="true"===(null==n?void 0:n.enableVideo),this.enableAudio="true"===(null==n?void 0:n.enableAudio);let t=o.conferenceId,i=o.password;o?this.conferenceInfo=o:t&&this.getConferenceInfo(t,i),setTimeout((()=>{this.outerHeight=document.documentElement.scrollHeight,this.getConferenceWaittingUsersFun()}),0);let s=location.origin.replace("gwc","dct")+"/BackDoor/videotalk";location.origin.includes("http://localhost")&&(s="https://dct.test.trialdata.cn/BackDoor/videotalk");const r=(new k).withUrl(s).build();this.startConnect(r,n),r.on("ReceiveMessage",(async(n,o)=>{if(o){const t=JSON.parse(o);4===n||7===n||6===n&&(null==t?void 0:t.userId)!==e.getUserId()?this.getConferenceWaittingUsersFun():5===n?(this.getConferenceWaittingUsersFun(),(null==t?void 0:t.userId)&&e.getUserId()===(null==t?void 0:t.userId)&&this.joinConference()):8!==n&&9!==n||(this.destroyFlag=n)}})),r.onclose((()=>{setTimeout((()=>{this.startConnect(r,n)}),2e3)})),this.connection=r},mounted(){this.getConferenceWaittingUsersFun()},onUnload(){this.conferenceInfo.id&&!this.joinConferenceLeaveWaitingRoomFlag&&n.postOperateConference({conferenceId:this.conferenceInfo.id,operateType:11}),this.connection&&this.connection.start().then((()=>{var e,n;this.connection.invoke("LeaveGroup",null==(n=null==(e=o)?void 0:e.conferenceInfo)?void 0:n.conferenceId).then((()=>{this.connection.stop()}))})).catch((e=>console.error(e.toString())))},onShow(){x()},methods:{startConnect(n,o){n.start().then((()=>{var t;if(this.connectionStartFlag=1,n.invoke("JoinGroup",null==(t=this.conferenceInfo)?void 0:t.conferenceId),!(null==o?void 0:o.noSend)){const n=JSON.stringify({userId:e.getUserId()});this.sendMessage(6,n)}})).catch((e=>{setTimeout((()=>{this.startConnect(n,o)}),2e3)}))},async sendMessage(e=6,n,o){return new Promise(((o,t)=>{var i;if(this.connectionStartFlag){const s=null==(i=this.conferenceInfo)?void 0:i.conferenceId;this.connection.invoke("SendMessage",s,e,n).then((()=>{o()})).catch((e=>t(e)))}}))},getConferenceWaittingUsersFun(){this.conferenceInfo.id&&n.getConferenceWaittingUsers({conferenceId:this.conferenceInfo.id}).then((n=>{var o;this.conferenceInfo=n,this.waitingRoomObj=n,(null==(o=null==n?void 0:n.users)?void 0:o.length)&&n.users.map((n=>{n.userId===e.getUserId()&&(this.user=n,this.user.identity=n.userType,this.user.patientId=this.conferenceInfo.dctPatientId)}))})).catch((()=>{}))},goChat(){this.showChatModuleFlag=!0,this.$refs.ChatModuleRef&&(this.$refs.ChatModuleRef.showPop=!0)},getConferenceInfo(e,o){n.queryConferenceInfo(e,o).then((e=>{this.conferenceInfo=e})).catch((()=>{this.goBack()}))},async joinConference(){try{this.joinConferenceLeaveWaitingRoomFlag=!0;const n=this.conferenceInfo;if(n.conferenceId&&!this.joinConferenceLoadingFlag){this.joinConferenceLoadingFlag=!0;let i=!this.enableVideo&&!this.enableAudio;await this.sendMessage(7,JSON.stringify({userId:e.getUserId()}),0),await t.joinConference(n.conferenceId,!1,n.pin,n.owner,n.conferenceTitle,"",i,n.advance,!this.enableAudio,!this.enableVideo),o.conferenceInfo=n,this.joinConferenceLoadingFlag=!1}}catch{}},async goBack(){try{this.backLoadingFlag||(this.backLoadingFlag=!0,await this.sendMessage(7,JSON.stringify({userId:e.getUserId()}),0),i({url:"/pages/voip/conference/ConferencePortalPage"}),this.backLoadingFlag=!1)}catch(n){this.backLoadingFlag=!1}}}},[["render",function(e,n,o,t,i,x){var y,w,C,k,j,S,U;const M=v("chat-room"),L=v("MyPopupShow");return s(),r("div",null,[a("div",null,[a("div",{class:"nav-title"},[a("div",{style:{width:"18px",height:"18px",overflow:"hidden"}}),a("div",null,"等候室"),a("div",{style:{width:"18px",height:"18px",overflow:"hidden"}})]),a("div",{class:"waiting-room-container",style:c({height:i.outerHeight?i.outerHeight-44.5+"px":"calc(100vh - 44px)",overflow:"hidden"})},[a("div",{style:c({height:i.outerHeight?i.outerHeight-101.5+"px":"calc(100vh - 101px)",overflow:"auto"})},[a("div",{style:{padding:"20px 0",background:"#fff"}},[a("div",{class:"centerflex",style:{"margin-bottom":"20px","font-size":"20px",color:"333","font-weight":"bold"}},[0===(null==(y=i.conferenceInfo)?void 0:y.conferenceStatus)?(s(),r("div",{key:0}," 会议未开始，等待主持人进入 ")):(s(),r("div",{key:1},"请稍等，主持人即将邀请您入会"))]),a("div",{class:"centerflex",style:{"margin-bottom":"15px","font-size":"15px"}},l(null==(w=i.conferenceInfo)?void 0:w.conferenceTitle),1),0===(null==(C=i.conferenceInfo)?void 0:C.conferenceStatus)&&(null==(k=i.conferenceInfo)?void 0:k.start)&&i.conferenceInfo.startHourStr?(s(),r("div",{key:0,class:"centerflex",style:{"margin-bottom":"15px","font-size":"15px"}}," 会议开始时间："+l(i.conferenceInfo.start.split("T")[0])+"   "+l(i.conferenceInfo.startHourStr),1)):d("",!0)]),a("div",null,[a("div",{style:{padding:"20px 15px 10px",color:"#333","font-size":"14px"}}," 等候中 "),a("div",{style:{padding:"0 20px 15px 10px"}},[(s(!0),r(h,null,f(i.waitingRoomObj.users,((e,n)=>(s(),r("div",{key:n,class:"centerflex-h",style:{margin:"0 0 15px 0","font-size":"15px","font-weight":"bold"}},[2===(null==e?void 0:e.userType)?(s(),r("div",{key:0,style:{width:"48px",height:"48px","margin-right":"10px","font-size":"12px",color:"#fff","background-color":"#41b592","border-radius":"50%",padding:"5px 0px","box-sizing":"border-box"}},[a("div",{style:{width:"100%","white-space":"nowrap","text-align":"center"}},"公正"),a("div",{style:{width:"100%","white-space":"nowrap","text-align":"center"}},"见证人")])):1===(null==e?void 0:e.userType)?(s(),r("div",{key:1,style:{width:"48px",height:"48px","margin-right":"10px","font-size":"12px",color:"#fff","background-color":"#5860da","border-radius":"50%",padding:"5px 0px","box-sizing":"border-box"},class:"centerflex"},[a("div",{style:{"white-space":"nowrap"}},"监护人")])):(null==e?void 0:e.avatarUrl)?(s(),r("img",{key:2,src:e.avatarUrl,class:"avatar-img",alt:""},null,8,["src"])):(s(),r("img",{key:3,class:"avatar-img",src:b,mode:""})),(null==e?void 0:e.userName)?(s(),r("div",{key:4,style:{margin:"0 10px 0 0"}},l(e.userName),1)):d("",!0),m(" "+l(null==e?void 0:e.roleName),1)])))),128))])])],4),a("div",{class:"waiting-room-bottom-btns flex"},[a("div",{class:"centerflex-wrap",style:{padding:"10px 0"},onClick:n[0]||(n[0]=(...e)=>x.goChat&&x.goChat(...e))},[a("img",{style:{width:"24px","margin-bottom":"5px"},src:I,mode:""}),a("div",{class:"centerflex"},"聊天")]),a("div",{class:"centerflex-wrap",style:{padding:"10px 0"},onClick:n[1]||(n[1]=(...e)=>x.goBack&&x.goBack(...e))},[a("img",{style:{width:"23.5px","margin-bottom":"5px"},src:F,mode:""}),a("div",{class:"centerflex"},"离开")])])],4)]),(null==(j=i.conferenceInfo)?void 0:j.id)&&(null==(S=i.user)?void 0:S.userId)?(s(),g(M,{key:0,show:i.showChatModuleFlag,ref:"ChatModuleRef",user:i.user,conferenceId:i.conferenceInfo.id,connectionId:null==(U=i.conferenceInfo)?void 0:U.conferenceId,chatRoomType:1},null,8,["show","user","conferenceId","connectionId"])):d("",!0),u(L,{myPopupShow:i.destroyFlag,title:"提示",cancelText:"离开",handleCancel:x.goBack},{bodyslot:p((()=>[a("div",{style:{padding:"20px"}},[a("div",{style:{"text-align":"center"}},"主持人已"+l(9===i.destroyFlag?"销毁":"结束")+"当前会议",1)])])),_:1},8,["myPopupShow","handleCancel"])])}],["__scopeId","data-v-893a9d52"]]);export{j as default};
