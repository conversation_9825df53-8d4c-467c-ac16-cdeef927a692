class e extends Error{constructor(e,t){const o=new.target.prototype;super(`${e}: Status code '${t}'`),this.statusCode=t,this.__proto__=o}}class t extends Error{constructor(e="A timeout occurred."){const t=new.target.prototype;super(e),this.__proto__=t}}class o extends Error{constructor(e="An abort occurred."){const t=new.target.prototype;super(e),this.__proto__=t}}class n extends Error{constructor(e,t){const o=new.target.prototype;super(e),this.transport=t,this.errorType="UnsupportedTransportError",this.__proto__=o}}class s extends Error{constructor(e,t){const o=new.target.prototype;super(e),this.transport=t,this.errorType="DisabledTransportError",this.__proto__=o}}class r extends Error{constructor(e,t){const o=new.target.prototype;super(e),this.transport=t,this.errorType="FailedToStartTransportError",this.__proto__=o}}class i extends Error{constructor(e){const t=new.target.prototype;super(e),this.errorType="FailedToNegotiateWithServerError",this.__proto__=t}}class c extends Error{constructor(e,t){const o=new.target.prototype;super(e),this.innerErrors=t,this.__proto__=o}}class a{constructor(e,t,o){this.statusCode=e,this.statusText=t,this.content=o}}class l{get(e,t){return this.send({...t,method:"GET",url:e})}post(e,t){return this.send({...t,method:"POST",url:e})}delete(e,t){return this.send({...t,method:"DELETE",url:e})}getCookieString(e){return""}}var h,g;(g=h||(h={}))[g.Trace=0]="Trace",g[g.Debug=1]="Debug",g[g.Information=2]="Information",g[g.Warning=3]="Warning",g[g.Error=4]="Error",g[g.Critical=5]="Critical",g[g.None=6]="None";class u{constructor(){}log(e,t){}}u.instance=new u;class d{static isRequired(e,t){if(null==e)throw new Error(`The '${t}' argument is required.`)}static isNotEmpty(e,t){if(!e||e.match(/^\s*$/))throw new Error(`The '${t}' argument should not be empty.`)}static isIn(e,t,o){if(!(e in t))throw new Error(`Unknown ${o} value: ${e}.`)}}class _{static get isBrowser(){return!_.isNode&&"object"==typeof window&&"object"==typeof window.document}static get isWebWorker(){return!_.isNode&&"object"==typeof self&&"importScripts"in self}static get isReactNative(){return!_.isNode&&"object"==typeof window&&void 0===window.document}static get isNode(){return"undefined"!=typeof process&&process.release&&"node"===process.release.name}}function p(e,t){let o="";return f(e)?(o=`Binary data of length ${e.byteLength}`,t&&(o+=`. Content: '${function(e){const t=new Uint8Array(e);let o="";return t.forEach((e=>{o+=`0x${e<16?"0":""}${e.toString(16)} `})),o.substr(0,o.length-1)}(e)}'`)):"string"==typeof e&&(o=`String data of length ${e.length}`,t&&(o+=`. Content: '${e}'`)),o}function f(e){return e&&"undefined"!=typeof ArrayBuffer&&(e instanceof ArrayBuffer||e.constructor&&"ArrayBuffer"===e.constructor.name)}async function m(e,t,o,n,s,r){const i={},[c,a]=b();i[c]=a,e.log(h.Trace,`(${t} transport) sending data. ${p(s,r.logMessageContent)}.`);const l=f(s)?"arraybuffer":"text",g=await o.post(n,{content:s,headers:{...i,...r.headers},responseType:l,timeout:r.timeout,withCredentials:r.withCredentials});e.log(h.Trace,`(${t} transport) request complete. Response status: ${g.statusCode}.`)}class w{constructor(e,t){this._subject=e,this._observer=t}dispose(){const e=this._subject.observers.indexOf(this._observer);e>-1&&this._subject.observers.splice(e,1),0===this._subject.observers.length&&this._subject.cancelCallback&&this._subject.cancelCallback().catch((e=>{}))}}class v{constructor(e){this._minLevel=e,this.out=console}log(e,t){if(e>=this._minLevel){const o=`[${(new Date).toISOString()}] ${h[e]}: ${t}`;switch(e){case h.Critical:case h.Error:this.out.error(o);break;case h.Warning:this.out.warn(o);break;case h.Information:this.out.info(o);break;default:this.out.log(o)}}}}function b(){let e="X-SignalR-User-Agent";return _.isNode&&(e="User-Agent"),[e,y("8.0.0",S(),k(),C())]}function y(e,t,o,n){let s="Microsoft SignalR/";const r=e.split(".");return s+=`${r[0]}.${r[1]}`,s+=` (${e}; `,s+=t&&""!==t?`${t}; `:"Unknown OS; ",s+=`${o}`,s+=n?`; ${n}`:"; Unknown Runtime Version",s+=")",s}function S(){if(!_.isNode)return"";switch(process.platform){case"win32":return"Windows NT";case"darwin":return"macOS";case"linux":return"Linux";default:return process.platform}}function C(){if(_.isNode)return process.versions.node}function k(){return _.isNode?"NodeJS":"Browser"}function I(e){return e.stack?e.stack:e.message?e.message:`${e}`}class T extends l{constructor(e){super(),this._logger=e;const t={_fetchType:void 0,_jar:void 0};var o;o=t,("undefined"==typeof fetch||_.isNode)&&(o._jar=new(require("tough-cookie").CookieJar),"undefined"==typeof fetch?o._fetchType=require("node-fetch"):o._fetchType=fetch,o._fetchType=require("fetch-cookie")(o._fetchType,o._jar),1)?(this._fetchType=t._fetchType,this._jar=t._jar):this._fetchType=fetch.bind(function(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("could not find global")}()),this._abortControllerType=AbortController;const n={_abortControllerType:this._abortControllerType};(function(e){return"undefined"==typeof AbortController&&(e._abortControllerType=require("abort-controller"),!0)})(n)&&(this._abortControllerType=n._abortControllerType)}async send(n){if(n.abortSignal&&n.abortSignal.aborted)throw new o;if(!n.method)throw new Error("No method defined.");if(!n.url)throw new Error("No url defined.");const s=new this._abortControllerType;let r;n.abortSignal&&(n.abortSignal.onabort=()=>{s.abort(),r=new o});let i,c=null;if(n.timeout){const e=n.timeout;c=setTimeout((()=>{s.abort(),this._logger.log(h.Warning,"Timeout from HTTP request."),r=new t}),e)}""===n.content&&(n.content=void 0),n.content&&(n.headers=n.headers||{},f(n.content)?n.headers["Content-Type"]="application/octet-stream":n.headers["Content-Type"]="text/plain;charset=UTF-8");try{i=await this._fetchType(n.url,{body:n.content,cache:"no-cache",credentials:!0===n.withCredentials?"include":"same-origin",headers:{"X-Requested-With":"XMLHttpRequest",...n.headers},method:n.method,mode:"cors",redirect:"follow",signal:s.signal})}catch(u){if(r)throw r;throw this._logger.log(h.Warning,`Error from HTTP request. ${u}.`),u}finally{c&&clearTimeout(c),n.abortSignal&&(n.abortSignal.onabort=null)}if(!i.ok){const t=await E(i,"text");throw new e(t||i.statusText,i.status)}const l=E(i,n.responseType),g=await l;return new a(i.status,i.statusText,g)}getCookieString(e){let t="";return _.isNode&&this._jar&&this._jar.getCookies(e,((e,o)=>t=o.join("; "))),t}}function E(e,t){let o;switch(t){case"arraybuffer":o=e.arrayBuffer();break;case"text":default:o=e.text();break;case"blob":case"document":case"json":throw new Error(`${t} is not supported.`)}return o}class P extends l{constructor(e){super(),this._logger=e}send(n){return n.abortSignal&&n.abortSignal.aborted?Promise.reject(new o):n.method?n.url?new Promise(((s,r)=>{const i=new XMLHttpRequest;i.open(n.method,n.url,!0),i.withCredentials=void 0===n.withCredentials||n.withCredentials,i.setRequestHeader("X-Requested-With","XMLHttpRequest"),""===n.content&&(n.content=void 0),n.content&&(f(n.content)?i.setRequestHeader("Content-Type","application/octet-stream"):i.setRequestHeader("Content-Type","text/plain;charset=UTF-8"));const c=n.headers;c&&Object.keys(c).forEach((e=>{i.setRequestHeader(e,c[e])})),n.responseType&&(i.responseType=n.responseType),n.abortSignal&&(n.abortSignal.onabort=()=>{i.abort(),r(new o)}),n.timeout&&(i.timeout=n.timeout),i.onload=()=>{n.abortSignal&&(n.abortSignal.onabort=null),i.status>=200&&i.status<300?s(new a(i.status,i.statusText,i.response||i.responseText)):r(new e(i.response||i.responseText||i.statusText,i.status))},i.onerror=()=>{this._logger.log(h.Warning,`Error from HTTP request. ${i.status}: ${i.statusText}.`),r(new e(i.statusText,i.status))},i.ontimeout=()=>{this._logger.log(h.Warning,"Timeout from HTTP request."),r(new t)},i.send(n.content)})):Promise.reject(new Error("No url defined.")):Promise.reject(new Error("No method defined."))}}class $ extends l{constructor(e){if(super(),"undefined"!=typeof fetch||_.isNode)this._httpClient=new T(e);else{if("undefined"==typeof XMLHttpRequest)throw new Error("No usable HttpClient found.");this._httpClient=new P(e)}}send(e){return e.abortSignal&&e.abortSignal.aborted?Promise.reject(new o):e.method?e.url?this._httpClient.send(e):Promise.reject(new Error("No url defined.")):Promise.reject(new Error("No method defined."))}getCookieString(e){return this._httpClient.getCookieString(e)}}class R{static write(e){return`${e}${R.RecordSeparator}`}static parse(e){if(e[e.length-1]!==R.RecordSeparator)throw new Error("Message is incomplete.");const t=e.split(R.RecordSeparator);return t.pop(),t}}R.RecordSeparatorCode=30,R.RecordSeparator=String.fromCharCode(R.RecordSeparatorCode);class D{writeHandshakeRequest(e){return R.write(JSON.stringify(e))}parseHandshakeResponse(e){let t,o;if(f(e)){const n=new Uint8Array(e),s=n.indexOf(R.RecordSeparatorCode);if(-1===s)throw new Error("Message is incomplete.");const r=s+1;t=String.fromCharCode.apply(null,Array.prototype.slice.call(n.slice(0,r))),o=n.byteLength>r?n.slice(r).buffer:null}else{const n=e,s=n.indexOf(R.RecordSeparator);if(-1===s)throw new Error("Message is incomplete.");const r=s+1;t=n.substring(0,r),o=n.length>r?n.substring(r):null}const n=R.parse(t),s=JSON.parse(n[0]);if(s.type)throw new Error("Expected a handshake response from the server.");return[o,s]}}var M,q;(q=M||(M={}))[q.Invocation=1]="Invocation",q[q.StreamItem=2]="StreamItem",q[q.Completion=3]="Completion",q[q.StreamInvocation=4]="StreamInvocation",q[q.CancelInvocation=5]="CancelInvocation",q[q.Ping=6]="Ping",q[q.Close=7]="Close",q[q.Ack=8]="Ack",q[q.Sequence=9]="Sequence";class x{constructor(){this.observers=[]}next(e){for(const t of this.observers)t.next(e)}error(e){for(const t of this.observers)t.error&&t.error(e)}complete(){for(const e of this.observers)e.complete&&e.complete()}subscribe(e){return this.observers.push(e),new w(this,e)}}class H{constructor(e,t,o){this._bufferSize=1e5,this._messages=[],this._totalMessageCount=0,this._waitForSequenceMessage=!1,this._nextReceivingSequenceId=1,this._latestReceivedSequenceId=0,this._bufferedByteCount=0,this._reconnectInProgress=!1,this._protocol=e,this._connection=t,this._bufferSize=o}async _send(e){const t=this._protocol.writeMessage(e);let o=Promise.resolve();if(this._isInvocationMessage(e)){this._totalMessageCount++;let e=()=>{},n=()=>{};f(t)?this._bufferedByteCount+=t.byteLength:this._bufferedByteCount+=t.length,this._bufferedByteCount>=this._bufferSize&&(o=new Promise(((t,o)=>{e=t,n=o}))),this._messages.push(new A(t,this._totalMessageCount,e,n))}try{this._reconnectInProgress||await this._connection.send(t)}catch{this._disconnected()}await o}_ack(e){let t=-1;for(let o=0;o<this._messages.length;o++){const n=this._messages[o];if(n._id<=e.sequenceId)t=o,f(n._message)?this._bufferedByteCount-=n._message.byteLength:this._bufferedByteCount-=n._message.length,n._resolver();else{if(!(this._bufferedByteCount<this._bufferSize))break;n._resolver()}}-1!==t&&(this._messages=this._messages.slice(t+1))}_shouldProcessMessage(e){if(this._waitForSequenceMessage)return e.type===M.Sequence&&(this._waitForSequenceMessage=!1,!0);if(!this._isInvocationMessage(e))return!0;const t=this._nextReceivingSequenceId;return this._nextReceivingSequenceId++,t<=this._latestReceivedSequenceId?(t===this._latestReceivedSequenceId&&this._ackTimer(),!1):(this._latestReceivedSequenceId=t,this._ackTimer(),!0)}_resetSequence(e){e.sequenceId>this._nextReceivingSequenceId?this._connection.stop(new Error("Sequence ID greater than amount of messages we've received.")):this._nextReceivingSequenceId=e.sequenceId}_disconnected(){this._reconnectInProgress=!0,this._waitForSequenceMessage=!0}async _resend(){const e=0!==this._messages.length?this._messages[0]._id:this._totalMessageCount+1;await this._connection.send(this._protocol.writeMessage({type:M.Sequence,sequenceId:e}));const t=this._messages;for(const o of t)await this._connection.send(o._message);this._reconnectInProgress=!1}_dispose(e){null!=e||(e=new Error("Unable to reconnect to server."));for(const t of this._messages)t._rejector(e)}_isInvocationMessage(e){switch(e.type){case M.Invocation:case M.StreamItem:case M.Completion:case M.StreamInvocation:case M.CancelInvocation:return!0;case M.Close:case M.Sequence:case M.Ping:case M.Ack:return!1}}_ackTimer(){void 0===this._ackTimerHandle&&(this._ackTimerHandle=setTimeout((async()=>{try{this._reconnectInProgress||await this._connection.send(this._protocol.writeMessage({type:M.Ack,sequenceId:this._latestReceivedSequenceId}))}catch{}clearTimeout(this._ackTimerHandle),this._ackTimerHandle=void 0}),1e3))}}class A{constructor(e,t,o,n){this._message=e,this._id=t,this._resolver=o,this._rejector=n}}var N,W;(W=N||(N={})).Disconnected="Disconnected",W.Connecting="Connecting",W.Connected="Connected",W.Disconnecting="Disconnecting",W.Reconnecting="Reconnecting";class j{static create(e,t,o,n,s,r,i){return new j(e,t,o,n,s,r,i)}constructor(e,t,o,n,s,r,i){this._nextKeepAlive=0,this._freezeEventListener=()=>{this._logger.log(h.Warning,"The page is being frozen, this will likely lead to the connection being closed and messages being lost. For more information see the docs at https://learn.microsoft.com/aspnet/core/signalr/javascript-client#bsleep")},d.isRequired(e,"connection"),d.isRequired(t,"logger"),d.isRequired(o,"protocol"),this.serverTimeoutInMilliseconds=null!=s?s:3e4,this.keepAliveIntervalInMilliseconds=null!=r?r:15e3,this._statefulReconnectBufferSize=null!=i?i:1e5,this._logger=t,this._protocol=o,this.connection=e,this._reconnectPolicy=n,this._handshakeProtocol=new D,this.connection.onreceive=e=>this._processIncomingData(e),this.connection.onclose=e=>this._connectionClosed(e),this._callbacks={},this._methods={},this._closedCallbacks=[],this._reconnectingCallbacks=[],this._reconnectedCallbacks=[],this._invocationId=0,this._receivedHandshakeResponse=!1,this._connectionState=N.Disconnected,this._connectionStarted=!1,this._cachedPingMessage=this._protocol.writeMessage({type:M.Ping})}get state(){return this._connectionState}get connectionId(){return this.connection&&this.connection.connectionId||null}get baseUrl(){return this.connection.baseUrl||""}set baseUrl(e){if(this._connectionState!==N.Disconnected&&this._connectionState!==N.Reconnecting)throw new Error("The HubConnection must be in the Disconnected or Reconnecting state to change the url.");if(!e)throw new Error("The HubConnection url must be a valid url.");this.connection.baseUrl=e}start(){return this._startPromise=this._startWithStateTransitions(),this._startPromise}async _startWithStateTransitions(){if(this._connectionState!==N.Disconnected)return Promise.reject(new Error("Cannot start a HubConnection that is not in the 'Disconnected' state."));this._connectionState=N.Connecting,this._logger.log(h.Debug,"Starting HubConnection.");try{await this._startInternal(),_.isBrowser&&window.document.addEventListener("freeze",this._freezeEventListener),this._connectionState=N.Connected,this._connectionStarted=!0,this._logger.log(h.Debug,"HubConnection connected successfully.")}catch(e){return this._connectionState=N.Disconnected,this._logger.log(h.Debug,`HubConnection failed to start successfully because of error '${e}'.`),Promise.reject(e)}}async _startInternal(){this._stopDuringStartError=void 0,this._receivedHandshakeResponse=!1;const e=new Promise(((e,t)=>{this._handshakeResolver=e,this._handshakeRejecter=t}));await this.connection.start(this._protocol.transferFormat);try{let t=this._protocol.version;this.connection.features.reconnect||(t=1);const o={protocol:this._protocol.name,version:t};if(this._logger.log(h.Debug,"Sending handshake request."),await this._sendMessage(this._handshakeProtocol.writeHandshakeRequest(o)),this._logger.log(h.Information,`Using HubProtocol '${this._protocol.name}'.`),this._cleanupTimeout(),this._resetTimeoutPeriod(),this._resetKeepAliveInterval(),await e,this._stopDuringStartError)throw this._stopDuringStartError;(this.connection.features.reconnect||!1)&&(this._messageBuffer=new H(this._protocol,this.connection,this._statefulReconnectBufferSize),this.connection.features.disconnected=this._messageBuffer._disconnected.bind(this._messageBuffer),this.connection.features.resend=()=>{if(this._messageBuffer)return this._messageBuffer._resend()}),this.connection.features.inherentKeepAlive||await this._sendMessage(this._cachedPingMessage)}catch(t){throw this._logger.log(h.Debug,`Hub handshake failed with error '${t}' during start(). Stopping HubConnection.`),this._cleanupTimeout(),this._cleanupPingTimer(),await this.connection.stop(t),t}}async stop(){const e=this._startPromise;this.connection.features.reconnect=!1,this._stopPromise=this._stopInternal(),await this._stopPromise;try{await e}catch(t){}}_stopInternal(e){if(this._connectionState===N.Disconnected)return this._logger.log(h.Debug,`Call to HubConnection.stop(${e}) ignored because it is already in the disconnected state.`),Promise.resolve();if(this._connectionState===N.Disconnecting)return this._logger.log(h.Debug,`Call to HttpConnection.stop(${e}) ignored because the connection is already in the disconnecting state.`),this._stopPromise;const t=this._connectionState;return this._connectionState=N.Disconnecting,this._logger.log(h.Debug,"Stopping HubConnection."),this._reconnectDelayHandle?(this._logger.log(h.Debug,"Connection stopped during reconnect delay. Done reconnecting."),clearTimeout(this._reconnectDelayHandle),this._reconnectDelayHandle=void 0,this._completeClose(),Promise.resolve()):(t===N.Connected&&this._sendCloseMessage(),this._cleanupTimeout(),this._cleanupPingTimer(),this._stopDuringStartError=e||new o("The connection was stopped before the hub handshake could complete."),this.connection.stop(e))}async _sendCloseMessage(){try{await this._sendWithProtocol(this._createCloseMessage())}catch{}}stream(e,...t){const[o,n]=this._replaceStreamingParams(t),s=this._createStreamInvocation(e,t,n);let r;const i=new x;return i.cancelCallback=()=>{const e=this._createCancelInvocation(s.invocationId);return delete this._callbacks[s.invocationId],r.then((()=>this._sendWithProtocol(e)))},this._callbacks[s.invocationId]=(e,t)=>{t?i.error(t):e&&(e.type===M.Completion?e.error?i.error(new Error(e.error)):i.complete():i.next(e.item))},r=this._sendWithProtocol(s).catch((e=>{i.error(e),delete this._callbacks[s.invocationId]})),this._launchStreams(o,r),i}_sendMessage(e){return this._resetKeepAliveInterval(),this.connection.send(e)}_sendWithProtocol(e){return this._messageBuffer?this._messageBuffer._send(e):this._sendMessage(this._protocol.writeMessage(e))}send(e,...t){const[o,n]=this._replaceStreamingParams(t),s=this._sendWithProtocol(this._createInvocation(e,t,!0,n));return this._launchStreams(o,s),s}invoke(e,...t){const[o,n]=this._replaceStreamingParams(t),s=this._createInvocation(e,t,!1,n);return new Promise(((e,t)=>{this._callbacks[s.invocationId]=(o,n)=>{n?t(n):o&&(o.type===M.Completion?o.error?t(new Error(o.error)):e(o.result):t(new Error(`Unexpected message type: ${o.type}`)))};const n=this._sendWithProtocol(s).catch((e=>{t(e),delete this._callbacks[s.invocationId]}));this._launchStreams(o,n)}))}on(e,t){e&&t&&(e=e.toLowerCase(),this._methods[e]||(this._methods[e]=[]),-1===this._methods[e].indexOf(t)&&this._methods[e].push(t))}off(e,t){if(!e)return;e=e.toLowerCase();const o=this._methods[e];if(o)if(t){const n=o.indexOf(t);-1!==n&&(o.splice(n,1),0===o.length&&delete this._methods[e])}else delete this._methods[e]}onclose(e){e&&this._closedCallbacks.push(e)}onreconnecting(e){e&&this._reconnectingCallbacks.push(e)}onreconnected(e){e&&this._reconnectedCallbacks.push(e)}_processIncomingData(e){if(this._cleanupTimeout(),this._receivedHandshakeResponse||(e=this._processHandshakeResponse(e),this._receivedHandshakeResponse=!0),e){const o=this._protocol.parseMessages(e,this._logger);for(const e of o)if(!this._messageBuffer||this._messageBuffer._shouldProcessMessage(e))switch(e.type){case M.Invocation:this._invokeClientMethod(e);break;case M.StreamItem:case M.Completion:{const o=this._callbacks[e.invocationId];if(o){e.type===M.Completion&&delete this._callbacks[e.invocationId];try{o(e)}catch(t){this._logger.log(h.Error,`Stream callback threw error: ${I(t)}`)}}break}case M.Ping:break;case M.Close:{this._logger.log(h.Information,"Close message received from server.");const t=e.error?new Error("Server returned an error on close: "+e.error):void 0;!0===e.allowReconnect?this.connection.stop(t):this._stopPromise=this._stopInternal(t);break}case M.Ack:this._messageBuffer&&this._messageBuffer._ack(e);break;case M.Sequence:this._messageBuffer&&this._messageBuffer._resetSequence(e);break;default:this._logger.log(h.Warning,`Invalid message type: ${e.type}.`)}}this._resetTimeoutPeriod()}_processHandshakeResponse(e){let t,o;try{[o,t]=this._handshakeProtocol.parseHandshakeResponse(e)}catch(n){const e="Error parsing handshake response: "+n;this._logger.log(h.Error,e);const t=new Error(e);throw this._handshakeRejecter(t),t}if(t.error){const e="Server returned handshake error: "+t.error;this._logger.log(h.Error,e);const o=new Error(e);throw this._handshakeRejecter(o),o}return this._logger.log(h.Debug,"Server handshake complete."),this._handshakeResolver(),o}_resetKeepAliveInterval(){this.connection.features.inherentKeepAlive||(this._nextKeepAlive=(new Date).getTime()+this.keepAliveIntervalInMilliseconds,this._cleanupPingTimer())}_resetTimeoutPeriod(){if(!(this.connection.features&&this.connection.features.inherentKeepAlive||(this._timeoutHandle=setTimeout((()=>this.serverTimeout()),this.serverTimeoutInMilliseconds),void 0!==this._pingServerHandle))){let e=this._nextKeepAlive-(new Date).getTime();e<0&&(e=0),this._pingServerHandle=setTimeout((async()=>{if(this._connectionState===N.Connected)try{await this._sendMessage(this._cachedPingMessage)}catch{this._cleanupPingTimer()}}),e)}}serverTimeout(){this.connection.stop(new Error("Server timeout elapsed without receiving a message from the server."))}async _invokeClientMethod(e){const t=e.target.toLowerCase(),o=this._methods[t];if(!o)return this._logger.log(h.Warning,`No client method with the name '${t}' found.`),void(e.invocationId&&(this._logger.log(h.Warning,`No result given for '${t}' method and invocation ID '${e.invocationId}'.`),await this._sendWithProtocol(this._createCompletionMessage(e.invocationId,"Client didn't provide a result.",null))));const n=o.slice(),s=!!e.invocationId;let r,i,c;for(const l of n)try{const o=r;r=await l.apply(this,e.arguments),s&&r&&o&&(this._logger.log(h.Error,`Multiple results provided for '${t}'. Sending error to server.`),c=this._createCompletionMessage(e.invocationId,"Client provided multiple results.",null)),i=void 0}catch(a){i=a,this._logger.log(h.Error,`A callback for the method '${t}' threw error '${a}'.`)}c?await this._sendWithProtocol(c):s?(i?c=this._createCompletionMessage(e.invocationId,`${i}`,null):void 0!==r?c=this._createCompletionMessage(e.invocationId,null,r):(this._logger.log(h.Warning,`No result given for '${t}' method and invocation ID '${e.invocationId}'.`),c=this._createCompletionMessage(e.invocationId,"Client didn't provide a result.",null)),await this._sendWithProtocol(c)):r&&this._logger.log(h.Error,`Result given for '${t}' method but server is not expecting a result.`)}_connectionClosed(e){this._logger.log(h.Debug,`HubConnection.connectionClosed(${e}) called while in state ${this._connectionState}.`),this._stopDuringStartError=this._stopDuringStartError||e||new o("The underlying connection was closed before the hub handshake could complete."),this._handshakeResolver&&this._handshakeResolver(),this._cancelCallbacksWithError(e||new Error("Invocation canceled due to the underlying connection being closed.")),this._cleanupTimeout(),this._cleanupPingTimer(),this._connectionState===N.Disconnecting?this._completeClose(e):this._connectionState===N.Connected&&this._reconnectPolicy?this._reconnect(e):this._connectionState===N.Connected&&this._completeClose(e)}_completeClose(e){if(this._connectionStarted){this._connectionState=N.Disconnected,this._connectionStarted=!1,this._messageBuffer&&(this._messageBuffer._dispose(null!=e?e:new Error("Connection closed.")),this._messageBuffer=void 0),_.isBrowser&&window.document.removeEventListener("freeze",this._freezeEventListener);try{this._closedCallbacks.forEach((t=>t.apply(this,[e])))}catch(t){this._logger.log(h.Error,`An onclose callback called with error '${e}' threw error '${t}'.`)}}}async _reconnect(e){const t=Date.now();let o=0,n=void 0!==e?e:new Error("Attempting to reconnect due to a unknown error."),s=this._getNextRetryDelay(o++,0,n);if(null===s)return this._logger.log(h.Debug,"Connection not reconnecting because the IRetryPolicy returned null on the first reconnect attempt."),void this._completeClose(e);if(this._connectionState=N.Reconnecting,e?this._logger.log(h.Information,`Connection reconnecting because of error '${e}'.`):this._logger.log(h.Information,"Connection reconnecting."),0!==this._reconnectingCallbacks.length){try{this._reconnectingCallbacks.forEach((t=>t.apply(this,[e])))}catch(r){this._logger.log(h.Error,`An onreconnecting callback called with error '${e}' threw error '${r}'.`)}if(this._connectionState!==N.Reconnecting)return void this._logger.log(h.Debug,"Connection left the reconnecting state in onreconnecting callback. Done reconnecting.")}for(;null!==s;){if(this._logger.log(h.Information,`Reconnect attempt number ${o} will start in ${s} ms.`),await new Promise((e=>{this._reconnectDelayHandle=setTimeout(e,s)})),this._reconnectDelayHandle=void 0,this._connectionState!==N.Reconnecting)return void this._logger.log(h.Debug,"Connection left the reconnecting state during reconnect delay. Done reconnecting.");try{if(await this._startInternal(),this._connectionState=N.Connected,this._logger.log(h.Information,"HubConnection reconnected successfully."),0!==this._reconnectedCallbacks.length)try{this._reconnectedCallbacks.forEach((e=>e.apply(this,[this.connection.connectionId])))}catch(r){this._logger.log(h.Error,`An onreconnected callback called with connectionId '${this.connection.connectionId}; threw error '${r}'.`)}return}catch(r){if(this._logger.log(h.Information,`Reconnect attempt failed because of error '${r}'.`),this._connectionState!==N.Reconnecting)return this._logger.log(h.Debug,`Connection moved to the '${this._connectionState}' from the reconnecting state during reconnect attempt. Done reconnecting.`),void(this._connectionState===N.Disconnecting&&this._completeClose());n=r instanceof Error?r:new Error(r.toString()),s=this._getNextRetryDelay(o++,Date.now()-t,n)}}this._logger.log(h.Information,`Reconnect retries have been exhausted after ${Date.now()-t} ms and ${o} failed attempts. Connection disconnecting.`),this._completeClose()}_getNextRetryDelay(e,t,o){try{return this._reconnectPolicy.nextRetryDelayInMilliseconds({elapsedMilliseconds:t,previousRetryCount:e,retryReason:o})}catch(n){return this._logger.log(h.Error,`IRetryPolicy.nextRetryDelayInMilliseconds(${e}, ${t}) threw error '${n}'.`),null}}_cancelCallbacksWithError(e){const t=this._callbacks;this._callbacks={},Object.keys(t).forEach((o=>{const n=t[o];try{n(null,e)}catch(s){this._logger.log(h.Error,`Stream 'error' callback called with '${e}' threw error: ${I(s)}`)}}))}_cleanupPingTimer(){this._pingServerHandle&&(clearTimeout(this._pingServerHandle),this._pingServerHandle=void 0)}_cleanupTimeout(){this._timeoutHandle&&clearTimeout(this._timeoutHandle)}_createInvocation(e,t,o,n){if(o)return 0!==n.length?{arguments:t,streamIds:n,target:e,type:M.Invocation}:{arguments:t,target:e,type:M.Invocation};{const o=this._invocationId;return this._invocationId++,0!==n.length?{arguments:t,invocationId:o.toString(),streamIds:n,target:e,type:M.Invocation}:{arguments:t,invocationId:o.toString(),target:e,type:M.Invocation}}}_launchStreams(e,t){if(0!==e.length){t||(t=Promise.resolve());for(const o in e)e[o].subscribe({complete:()=>{t=t.then((()=>this._sendWithProtocol(this._createCompletionMessage(o))))},error:e=>{let n;n=e instanceof Error?e.message:e&&e.toString?e.toString():"Unknown error",t=t.then((()=>this._sendWithProtocol(this._createCompletionMessage(o,n))))},next:e=>{t=t.then((()=>this._sendWithProtocol(this._createStreamItemMessage(o,e))))}})}}_replaceStreamingParams(e){const t=[],o=[];for(let n=0;n<e.length;n++){const s=e[n];if(this._isObservable(s)){const r=this._invocationId;this._invocationId++,t[r]=s,o.push(r.toString()),e.splice(n,1)}}return[t,o]}_isObservable(e){return e&&e.subscribe&&"function"==typeof e.subscribe}_createStreamInvocation(e,t,o){const n=this._invocationId;return this._invocationId++,0!==o.length?{arguments:t,invocationId:n.toString(),streamIds:o,target:e,type:M.StreamInvocation}:{arguments:t,invocationId:n.toString(),target:e,type:M.StreamInvocation}}_createCancelInvocation(e){return{invocationId:e,type:M.CancelInvocation}}_createStreamItemMessage(e,t){return{invocationId:e,item:t,type:M.StreamItem}}_createCompletionMessage(e,t,o){return t?{error:t,invocationId:e,type:M.Completion}:{invocationId:e,result:o,type:M.Completion}}_createCloseMessage(){return{type:M.Close}}}const L=[0,2e3,1e4,3e4,null];class B{constructor(e){this._retryDelays=void 0!==e?[...e,null]:L}nextRetryDelayInMilliseconds(e){return this._retryDelays[e.previousRetryCount]}}class F{}F.Authorization="Authorization",F.Cookie="Cookie";class U extends l{constructor(e,t){super(),this._innerClient=e,this._accessTokenFactory=t}async send(e){let t=!0;this._accessTokenFactory&&(!this._accessToken||e.url&&e.url.indexOf("/negotiate?")>0)&&(t=!1,this._accessToken=await this._accessTokenFactory()),this._setAuthorizationHeader(e);const o=await this._innerClient.send(e);return t&&401===o.statusCode&&this._accessTokenFactory?(this._accessToken=await this._accessTokenFactory(),this._setAuthorizationHeader(e),await this._innerClient.send(e)):o}_setAuthorizationHeader(e){e.headers||(e.headers={}),this._accessToken?e.headers[F.Authorization]=`Bearer ${this._accessToken}`:this._accessTokenFactory&&e.headers[F.Authorization]&&delete e.headers[F.Authorization]}getCookieString(e){return this._innerClient.getCookieString(e)}}var O,z,K,X;(z=O||(O={}))[z.None=0]="None",z[z.WebSockets=1]="WebSockets",z[z.ServerSentEvents=2]="ServerSentEvents",z[z.LongPolling=4]="LongPolling",(X=K||(K={}))[X.Text=1]="Text",X[X.Binary=2]="Binary";class J{get pollAborted(){return this._pollAbort.aborted}constructor(e,t,o){this._httpClient=e,this._logger=t,this._pollAbort=new class{constructor(){this._isAborted=!1,this.onabort=null}abort(){this._isAborted||(this._isAborted=!0,this.onabort&&this.onabort())}get signal(){return this}get aborted(){return this._isAborted}},this._options=o,this._running=!1,this.onreceive=null,this.onclose=null}async connect(t,o){if(d.isRequired(t,"url"),d.isRequired(o,"transferFormat"),d.isIn(o,K,"transferFormat"),this._url=t,this._logger.log(h.Trace,"(LongPolling transport) Connecting."),o===K.Binary&&"undefined"!=typeof XMLHttpRequest&&"string"!=typeof(new XMLHttpRequest).responseType)throw new Error("Binary protocols over XmlHttpRequest not implementing advanced features are not supported.");const[n,s]=b(),r={[n]:s,...this._options.headers},i={abortSignal:this._pollAbort.signal,headers:r,timeout:1e5,withCredentials:this._options.withCredentials};o===K.Binary&&(i.responseType="arraybuffer");const c=`${t}&_=${Date.now()}`;this._logger.log(h.Trace,`(LongPolling transport) polling: ${c}.`);const a=await this._httpClient.get(c,i);200!==a.statusCode?(this._logger.log(h.Error,`(LongPolling transport) Unexpected response code: ${a.statusCode}.`),this._closeError=new e(a.statusText||"",a.statusCode),this._running=!1):this._running=!0,this._receiving=this._poll(this._url,i)}async _poll(o,n){try{for(;this._running;)try{const t=`${o}&_=${Date.now()}`;this._logger.log(h.Trace,`(LongPolling transport) polling: ${t}.`);const s=await this._httpClient.get(t,n);204===s.statusCode?(this._logger.log(h.Information,"(LongPolling transport) Poll terminated by server."),this._running=!1):200!==s.statusCode?(this._logger.log(h.Error,`(LongPolling transport) Unexpected response code: ${s.statusCode}.`),this._closeError=new e(s.statusText||"",s.statusCode),this._running=!1):s.content?(this._logger.log(h.Trace,`(LongPolling transport) data received. ${p(s.content,this._options.logMessageContent)}.`),this.onreceive&&this.onreceive(s.content)):this._logger.log(h.Trace,"(LongPolling transport) Poll timed out, reissuing.")}catch(s){this._running?s instanceof t?this._logger.log(h.Trace,"(LongPolling transport) Poll timed out, reissuing."):(this._closeError=s,this._running=!1):this._logger.log(h.Trace,`(LongPolling transport) Poll errored after shutdown: ${s.message}`)}}finally{this._logger.log(h.Trace,"(LongPolling transport) Polling complete."),this.pollAborted||this._raiseOnClose()}}async send(e){return this._running?m(this._logger,"LongPolling",this._httpClient,this._url,e,this._options):Promise.reject(new Error("Cannot send until the transport is connected"))}async stop(){this._logger.log(h.Trace,"(LongPolling transport) Stopping polling."),this._running=!1,this._pollAbort.abort();try{await this._receiving,this._logger.log(h.Trace,`(LongPolling transport) sending DELETE request to ${this._url}.`);const o={},[n,s]=b();o[n]=s;const r={headers:{...o,...this._options.headers},timeout:this._options.timeout,withCredentials:this._options.withCredentials};let i;try{await this._httpClient.delete(this._url,r)}catch(t){i=t}i?i instanceof e&&(404===i.statusCode?this._logger.log(h.Trace,"(LongPolling transport) A 404 response was returned from sending a DELETE request."):this._logger.log(h.Trace,`(LongPolling transport) Error sending a DELETE request: ${i}`)):this._logger.log(h.Trace,"(LongPolling transport) DELETE request accepted.")}finally{this._logger.log(h.Trace,"(LongPolling transport) Stop finished."),this._raiseOnClose()}}_raiseOnClose(){if(this.onclose){let e="(LongPolling transport) Firing onclose event.";this._closeError&&(e+=" Error: "+this._closeError),this._logger.log(h.Trace,e),this.onclose(this._closeError)}}}class V{constructor(e,t,o,n){this._httpClient=e,this._accessToken=t,this._logger=o,this._options=n,this.onreceive=null,this.onclose=null}async connect(e,t){return d.isRequired(e,"url"),d.isRequired(t,"transferFormat"),d.isIn(t,K,"transferFormat"),this._logger.log(h.Trace,"(SSE transport) Connecting."),this._url=e,this._accessToken&&(e+=(e.indexOf("?")<0?"?":"&")+`access_token=${encodeURIComponent(this._accessToken)}`),new Promise(((o,n)=>{let s,r=!1;if(t===K.Text){if(_.isBrowser||_.isWebWorker)s=new this._options.EventSource(e,{withCredentials:this._options.withCredentials});else{const t=this._httpClient.getCookieString(e),o={};o.Cookie=t;const[n,r]=b();o[n]=r,s=new this._options.EventSource(e,{withCredentials:this._options.withCredentials,headers:{...o,...this._options.headers}})}try{s.onmessage=e=>{if(this.onreceive)try{this._logger.log(h.Trace,`(SSE transport) data received. ${p(e.data,this._options.logMessageContent)}.`),this.onreceive(e.data)}catch(t){return void this._close(t)}},s.onerror=e=>{r?this._close():n(new Error("EventSource failed to connect. The connection could not be found on the server, either the connection ID is not present on the server, or a proxy is refusing/buffering the connection. If you have multiple servers check that sticky sessions are enabled."))},s.onopen=()=>{this._logger.log(h.Information,`SSE connected to ${this._url}`),this._eventSource=s,r=!0,o()}}catch(i){return void n(i)}}else n(new Error("The Server-Sent Events transport only supports the 'Text' transfer format"))}))}async send(e){return this._eventSource?m(this._logger,"SSE",this._httpClient,this._url,e,this._options):Promise.reject(new Error("Cannot send until the transport is connected"))}stop(){return this._close(),Promise.resolve()}_close(e){this._eventSource&&(this._eventSource.close(),this._eventSource=void 0,this.onclose&&this.onclose(e))}}class Q{constructor(e,t,o,n,s,r){this._logger=o,this._accessTokenFactory=t,this._logMessageContent=n,this._webSocketConstructor=s,this._httpClient=e,this.onreceive=null,this.onclose=null,this._headers=r}async connect(e,t){let o;return d.isRequired(e,"url"),d.isRequired(t,"transferFormat"),d.isIn(t,K,"transferFormat"),this._logger.log(h.Trace,"(WebSockets transport) Connecting."),this._accessTokenFactory&&(o=await this._accessTokenFactory()),new Promise(((n,s)=>{let r;e=e.replace(/^http/,"ws");const i=this._httpClient.getCookieString(e);let c=!1;if(_.isNode||_.isReactNative){const t={},[n,s]=b();t[n]=s,o&&(t[F.Authorization]=`Bearer ${o}`),i&&(t[F.Cookie]=i),r=new this._webSocketConstructor(e,void 0,{headers:{...t,...this._headers}})}else o&&(e+=(e.indexOf("?")<0?"?":"&")+`access_token=${encodeURIComponent(o)}`);r||(r=new this._webSocketConstructor(e)),t===K.Binary&&(r.binaryType="arraybuffer"),r.onopen=t=>{this._logger.log(h.Information,`WebSocket connected to ${e}.`),this._webSocket=r,c=!0,n()},r.onerror=e=>{let t=null;t="undefined"!=typeof ErrorEvent&&e instanceof ErrorEvent?e.error:"There was an error with the transport",this._logger.log(h.Information,`(WebSockets transport) ${t}.`)},r.onmessage=e=>{if(this._logger.log(h.Trace,`(WebSockets transport) data received. ${p(e.data,this._logMessageContent)}.`),this.onreceive)try{this.onreceive(e.data)}catch(t){return void this._close(t)}},r.onclose=e=>{if(c)this._close(e);else{let t=null;t="undefined"!=typeof ErrorEvent&&e instanceof ErrorEvent?e.error:"WebSocket failed to connect. The connection could not be found on the server, either the endpoint may not be a SignalR endpoint, the connection ID is not present on the server, or there is a proxy blocking WebSockets. If you have multiple servers check that sticky sessions are enabled.",s(new Error(t))}}}))}send(e){return this._webSocket&&this._webSocket.readyState===this._webSocketConstructor.OPEN?(this._logger.log(h.Trace,`(WebSockets transport) sending data. ${p(e,this._logMessageContent)}.`),this._webSocket.send(e),Promise.resolve()):Promise.reject("WebSocket is not in the OPEN state")}stop(){return this._webSocket&&this._close(void 0),Promise.resolve()}_close(e){this._webSocket&&(this._webSocket.onclose=()=>{},this._webSocket.onmessage=()=>{},this._webSocket.onerror=()=>{},this._webSocket.close(),this._webSocket=void 0),this._logger.log(h.Trace,"(WebSockets transport) socket closed."),this.onclose&&(!this._isCloseEvent(e)||!1!==e.wasClean&&1e3===e.code?e instanceof Error?this.onclose(e):this.onclose():this.onclose(new Error(`WebSocket closed with status code: ${e.code} (${e.reason||"no reason given"}).`)))}_isCloseEvent(e){return e&&"boolean"==typeof e.wasClean&&"number"==typeof e.code}}class G{constructor(e,t={}){var o;if(this._stopPromiseResolver=()=>{},this.features={},this._negotiateVersion=1,d.isRequired(e,"url"),this._logger=void 0===(o=t.logger)?new v(h.Information):null===o?u.instance:void 0!==o.log?o:new v(o),this.baseUrl=this._resolveUrl(e),(t=t||{}).logMessageContent=void 0!==t.logMessageContent&&t.logMessageContent,"boolean"!=typeof t.withCredentials&&void 0!==t.withCredentials)throw new Error("withCredentials option was not a 'boolean' or 'undefined' value");t.withCredentials=void 0===t.withCredentials||t.withCredentials,t.timeout=void 0===t.timeout?1e5:t.timeout;let n=null,s=null;_.isNode&&"undefined"!=typeof require&&(n=require("ws"),s=require("eventsource")),_.isNode||"undefined"==typeof WebSocket||t.WebSocket?_.isNode&&!t.WebSocket&&n&&(t.WebSocket=n):t.WebSocket=WebSocket,_.isNode||"undefined"==typeof EventSource||t.EventSource?_.isNode&&!t.EventSource&&void 0!==s&&(t.EventSource=s):t.EventSource=EventSource,this._httpClient=new U(t.httpClient||new $(this._logger),t.accessTokenFactory),this._connectionState="Disconnected",this._connectionStarted=!1,this._options=t,this.onreceive=null,this.onclose=null}async start(e){if(e=e||K.Binary,d.isIn(e,K,"transferFormat"),this._logger.log(h.Debug,`Starting connection with transfer format '${K[e]}'.`),"Disconnected"!==this._connectionState)return Promise.reject(new Error("Cannot start an HttpConnection that is not in the 'Disconnected' state."));if(this._connectionState="Connecting",this._startInternalPromise=this._startInternal(e),await this._startInternalPromise,"Disconnecting"===this._connectionState){const e="Failed to start the HttpConnection before stop() was called.";return this._logger.log(h.Error,e),await this._stopPromise,Promise.reject(new o(e))}if("Connected"!==this._connectionState){const e="HttpConnection.startInternal completed gracefully but didn't enter the connection into the connected state!";return this._logger.log(h.Error,e),Promise.reject(new o(e))}this._connectionStarted=!0}send(e){return"Connected"!==this._connectionState?Promise.reject(new Error("Cannot send data if the connection is not in the 'Connected' State.")):(this._sendQueue||(this._sendQueue=new Y(this.transport)),this._sendQueue.send(e))}async stop(e){return"Disconnected"===this._connectionState?(this._logger.log(h.Debug,`Call to HttpConnection.stop(${e}) ignored because the connection is already in the disconnected state.`),Promise.resolve()):"Disconnecting"===this._connectionState?(this._logger.log(h.Debug,`Call to HttpConnection.stop(${e}) ignored because the connection is already in the disconnecting state.`),this._stopPromise):(this._connectionState="Disconnecting",this._stopPromise=new Promise((e=>{this._stopPromiseResolver=e})),await this._stopInternal(e),void(await this._stopPromise))}async _stopInternal(e){this._stopError=e;try{await this._startInternalPromise}catch(t){}if(this.transport){try{await this.transport.stop()}catch(t){this._logger.log(h.Error,`HttpConnection.transport.stop() threw error '${t}'.`),this._stopConnection()}this.transport=void 0}else this._logger.log(h.Debug,"HttpConnection.transport is undefined in HttpConnection.stop() because start() failed.")}async _startInternal(e){let t=this.baseUrl;this._accessTokenFactory=this._options.accessTokenFactory,this._httpClient._accessTokenFactory=this._accessTokenFactory;try{if(this._options.skipNegotiation){if(this._options.transport!==O.WebSockets)throw new Error("Negotiation can only be skipped when using the WebSocket transport directly.");this.transport=this._constructTransport(O.WebSockets),await this._startTransport(t,e)}else{let n=null,s=0;do{if(n=await this._getNegotiationResponse(t),"Disconnecting"===this._connectionState||"Disconnected"===this._connectionState)throw new o("The connection was stopped during negotiation.");if(n.error)throw new Error(n.error);if(n.ProtocolVersion)throw new Error("Detected a connection attempt to an ASP.NET SignalR Server. This client only supports connecting to an ASP.NET Core SignalR Server. See https://aka.ms/signalr-core-differences for details.");if(n.url&&(t=n.url),n.accessToken){const e=n.accessToken;this._accessTokenFactory=()=>e,this._httpClient._accessToken=e,this._httpClient._accessTokenFactory=void 0}s++}while(n.url&&s<100);if(100===s&&n.url)throw new Error("Negotiate redirection limit exceeded.");await this._createTransport(t,this._options.transport,n,e)}this.transport instanceof J&&(this.features.inherentKeepAlive=!0),"Connecting"===this._connectionState&&(this._logger.log(h.Debug,"The HttpConnection connected successfully."),this._connectionState="Connected")}catch(n){return this._logger.log(h.Error,"Failed to start the connection: "+n),this._connectionState="Disconnected",this.transport=void 0,this._stopPromiseResolver(),Promise.reject(n)}}async _getNegotiationResponse(t){const o={},[n,s]=b();o[n]=s;const r=this._resolveNegotiateUrl(t);this._logger.log(h.Debug,`Sending negotiation request: ${r}.`);try{const e=await this._httpClient.post(r,{content:"",headers:{...o,...this._options.headers},timeout:this._options.timeout,withCredentials:this._options.withCredentials});if(200!==e.statusCode)return Promise.reject(new Error(`Unexpected status code returned from negotiate '${e.statusCode}'`));const t=JSON.parse(e.content);return(!t.negotiateVersion||t.negotiateVersion<1)&&(t.connectionToken=t.connectionId),t.useStatefulReconnect&&!0!==this._options._useStatefulReconnect?Promise.reject(new i("Client didn't negotiate Stateful Reconnect but the server did.")):t}catch(c){let t="Failed to complete negotiation with the server: "+c;return c instanceof e&&404===c.statusCode&&(t+=" Either this is not a SignalR endpoint or there is a proxy blocking the connection."),this._logger.log(h.Error,t),Promise.reject(new i(t))}}_createConnectUrl(e,t){return t?e+(-1===e.indexOf("?")?"?":"&")+`id=${t}`:e}async _createTransport(e,t,n,s){let i=this._createConnectUrl(e,n.connectionToken);if(this._isITransport(t))return this._logger.log(h.Debug,"Connection was provided an instance of ITransport, using that directly."),this.transport=t,await this._startTransport(i,s),void(this.connectionId=n.connectionId);const a=[],l=n.availableTransports||[];let g=n;for(const c of l){const n=this._resolveTransportOrError(c,t,s,!0===(null==g?void 0:g.useStatefulReconnect));if(n instanceof Error)a.push(`${c.transport} failed:`),a.push(n);else if(this._isITransport(n)){if(this.transport=n,!g){try{g=await this._getNegotiationResponse(e)}catch(u){return Promise.reject(u)}i=this._createConnectUrl(e,g.connectionToken)}try{return await this._startTransport(i,s),void(this.connectionId=g.connectionId)}catch(u){if(this._logger.log(h.Error,`Failed to start the transport '${c.transport}': ${u}`),g=void 0,a.push(new r(`${c.transport} failed: ${u}`,O[c.transport])),"Connecting"!==this._connectionState){const e="Failed to select transport before stop() was called.";return this._logger.log(h.Debug,e),Promise.reject(new o(e))}}}}return a.length>0?Promise.reject(new c(`Unable to connect to the server with any of the available transports. ${a.join(" ")}`,a)):Promise.reject(new Error("None of the transports supported by the client are supported by the server."))}_constructTransport(e){switch(e){case O.WebSockets:if(!this._options.WebSocket)throw new Error("'WebSocket' is not supported in your environment.");return new Q(this._httpClient,this._accessTokenFactory,this._logger,this._options.logMessageContent,this._options.WebSocket,this._options.headers||{});case O.ServerSentEvents:if(!this._options.EventSource)throw new Error("'EventSource' is not supported in your environment.");return new V(this._httpClient,this._httpClient._accessToken,this._logger,this._options);case O.LongPolling:return new J(this._httpClient,this._logger,this._options);default:throw new Error(`Unknown transport: ${e}.`)}}_startTransport(e,t){return this.transport.onreceive=this.onreceive,this.features.reconnect?this.transport.onclose=async o=>{let n=!1;if(this.features.reconnect){try{this.features.disconnected(),await this.transport.connect(e,t),await this.features.resend()}catch{n=!0}n&&this._stopConnection(o)}else this._stopConnection(o)}:this.transport.onclose=e=>this._stopConnection(e),this.transport.connect(e,t)}_resolveTransportOrError(e,t,o,r){const i=O[e.transport];if(null==i)return this._logger.log(h.Debug,`Skipping transport '${e.transport}' because it is not supported by this client.`),new Error(`Skipping transport '${e.transport}' because it is not supported by this client.`);if(!function(e,t){return!e||0!=(t&e)}(t,i))return this._logger.log(h.Debug,`Skipping transport '${O[i]}' because it was disabled by the client.`),new s(`'${O[i]}' is disabled by the client.`,i);if(!(e.transferFormats.map((e=>K[e])).indexOf(o)>=0))return this._logger.log(h.Debug,`Skipping transport '${O[i]}' because it does not support the requested transfer format '${K[o]}'.`),new Error(`'${O[i]}' does not support ${K[o]}.`);if(i===O.WebSockets&&!this._options.WebSocket||i===O.ServerSentEvents&&!this._options.EventSource)return this._logger.log(h.Debug,`Skipping transport '${O[i]}' because it is not supported in your environment.'`),new n(`'${O[i]}' is not supported in your environment.`,i);this._logger.log(h.Debug,`Selecting transport '${O[i]}'.`);try{return this.features.reconnect=i===O.WebSockets?r:void 0,this._constructTransport(i)}catch(c){return c}}_isITransport(e){return e&&"object"==typeof e&&"connect"in e}_stopConnection(e){if(this._logger.log(h.Debug,`HttpConnection.stopConnection(${e}) called while in state ${this._connectionState}.`),this.transport=void 0,e=this._stopError||e,this._stopError=void 0,"Disconnected"!==this._connectionState){if("Connecting"===this._connectionState)throw this._logger.log(h.Warning,`Call to HttpConnection.stopConnection(${e}) was ignored because the connection is still in the connecting state.`),new Error(`HttpConnection.stopConnection(${e}) was called while the connection is still in the connecting state.`);if("Disconnecting"===this._connectionState&&this._stopPromiseResolver(),e?this._logger.log(h.Error,`Connection disconnected with error '${e}'.`):this._logger.log(h.Information,"Connection disconnected."),this._sendQueue&&(this._sendQueue.stop().catch((e=>{this._logger.log(h.Error,`TransportSendQueue.stop() threw error '${e}'.`)})),this._sendQueue=void 0),this.connectionId=void 0,this._connectionState="Disconnected",this._connectionStarted){this._connectionStarted=!1;try{this.onclose&&this.onclose(e)}catch(t){this._logger.log(h.Error,`HttpConnection.onclose(${e}) threw error '${t}'.`)}}}else this._logger.log(h.Debug,`Call to HttpConnection.stopConnection(${e}) was ignored because the connection is already in the disconnected state.`)}_resolveUrl(e){if(0===e.lastIndexOf("https://",0)||0===e.lastIndexOf("http://",0))return e;if(!_.isBrowser)throw new Error(`Cannot resolve '${e}'.`);const t=window.document.createElement("a");return t.href=e,this._logger.log(h.Information,`Normalizing '${e}' to '${t.href}'.`),t.href}_resolveNegotiateUrl(e){const t=new URL(e);t.pathname.endsWith("/")?t.pathname+="negotiate":t.pathname+="/negotiate";const o=new URLSearchParams(t.searchParams);return o.has("negotiateVersion")||o.append("negotiateVersion",this._negotiateVersion.toString()),o.has("useStatefulReconnect")?"true"===o.get("useStatefulReconnect")&&(this._options._useStatefulReconnect=!0):!0===this._options._useStatefulReconnect&&o.append("useStatefulReconnect","true"),t.search=o.toString(),t.toString()}}class Y{constructor(e){this._transport=e,this._buffer=[],this._executing=!0,this._sendBufferedData=new Z,this._transportResult=new Z,this._sendLoopPromise=this._sendLoop()}send(e){return this._bufferData(e),this._transportResult||(this._transportResult=new Z),this._transportResult.promise}stop(){return this._executing=!1,this._sendBufferedData.resolve(),this._sendLoopPromise}_bufferData(e){if(this._buffer.length&&typeof this._buffer[0]!=typeof e)throw new Error(`Expected data to be of type ${typeof this._buffer} but was of type ${typeof e}`);this._buffer.push(e),this._sendBufferedData.resolve()}async _sendLoop(){for(;;){if(await this._sendBufferedData.promise,!this._executing){this._transportResult&&this._transportResult.reject("Connection stopped.");break}this._sendBufferedData=new Z;const t=this._transportResult;this._transportResult=void 0;const o="string"==typeof this._buffer[0]?this._buffer.join(""):Y._concatBuffers(this._buffer);this._buffer.length=0;try{await this._transport.send(o),t.resolve()}catch(e){t.reject(e)}}}static _concatBuffers(e){const t=e.map((e=>e.byteLength)).reduce(((e,t)=>e+t)),o=new Uint8Array(t);let n=0;for(const s of e)o.set(new Uint8Array(s),n),n+=s.byteLength;return o.buffer}}class Z{constructor(){this.promise=new Promise(((e,t)=>[this._resolver,this._rejecter]=[e,t]))}resolve(){this._resolver()}reject(e){this._rejecter(e)}}class ee{constructor(){this.name="json",this.version=2,this.transferFormat=K.Text}parseMessages(e,t){if("string"!=typeof e)throw new Error("Invalid input for JSON hub protocol. Expected a string.");if(!e)return[];null===t&&(t=u.instance);const o=R.parse(e),n=[];for(const s of o){const e=JSON.parse(s);if("number"!=typeof e.type)throw new Error("Invalid payload.");switch(e.type){case M.Invocation:this._isInvocationMessage(e);break;case M.StreamItem:this._isStreamItemMessage(e);break;case M.Completion:this._isCompletionMessage(e);break;case M.Ping:case M.Close:break;case M.Ack:this._isAckMessage(e);break;case M.Sequence:this._isSequenceMessage(e);break;default:t.log(h.Information,"Unknown message type '"+e.type+"' ignored.");continue}n.push(e)}return n}writeMessage(e){return R.write(JSON.stringify(e))}_isInvocationMessage(e){this._assertNotEmptyString(e.target,"Invalid payload for Invocation message."),void 0!==e.invocationId&&this._assertNotEmptyString(e.invocationId,"Invalid payload for Invocation message.")}_isStreamItemMessage(e){if(this._assertNotEmptyString(e.invocationId,"Invalid payload for StreamItem message."),void 0===e.item)throw new Error("Invalid payload for StreamItem message.")}_isCompletionMessage(e){if(e.result&&e.error)throw new Error("Invalid payload for Completion message.");!e.result&&e.error&&this._assertNotEmptyString(e.error,"Invalid payload for Completion message."),this._assertNotEmptyString(e.invocationId,"Invalid payload for Completion message.")}_isAckMessage(e){if("number"!=typeof e.sequenceId)throw new Error("Invalid SequenceId for Ack message.")}_isSequenceMessage(e){if("number"!=typeof e.sequenceId)throw new Error("Invalid SequenceId for Sequence message.")}_assertNotEmptyString(e,t){if("string"!=typeof e||""===e)throw new Error(t)}}const te={trace:h.Trace,debug:h.Debug,info:h.Information,information:h.Information,warn:h.Warning,warning:h.Warning,error:h.Error,critical:h.Critical,none:h.None};class oe{configureLogging(e){if(d.isRequired(e,"logging"),void 0!==e.log)this.logger=e;else if("string"==typeof e){const t=function(e){const t=te[e.toLowerCase()];if(void 0!==t)return t;throw new Error(`Unknown log level: ${e}`)}(e);this.logger=new v(t)}else this.logger=new v(e);return this}withUrl(e,t){return d.isRequired(e,"url"),d.isNotEmpty(e,"url"),this.url=e,this.httpConnectionOptions="object"==typeof t?{...this.httpConnectionOptions,...t}:{...this.httpConnectionOptions,transport:t},this}withHubProtocol(e){return d.isRequired(e,"protocol"),this.protocol=e,this}withAutomaticReconnect(e){if(this.reconnectPolicy)throw new Error("A reconnectPolicy has already been set.");return e?Array.isArray(e)?this.reconnectPolicy=new B(e):this.reconnectPolicy=e:this.reconnectPolicy=new B,this}withServerTimeout(e){return d.isRequired(e,"milliseconds"),this._serverTimeoutInMilliseconds=e,this}withKeepAliveInterval(e){return d.isRequired(e,"milliseconds"),this._keepAliveIntervalInMilliseconds=e,this}withStatefulReconnect(e){return void 0===this.httpConnectionOptions&&(this.httpConnectionOptions={}),this.httpConnectionOptions._useStatefulReconnect=!0,this._statefulReconnectBufferSize=null==e?void 0:e.bufferSize,this}build(){const e=this.httpConnectionOptions||{};if(void 0===e.logger&&(e.logger=this.logger),!this.url)throw new Error("The 'HubConnectionBuilder.withUrl' method must be called before building the connection.");const t=new G(this.url,e);return j.create(t,this.logger||u.instance,this.protocol||new ee,this.reconnectPolicy,this._serverTimeoutInMilliseconds,this._keepAliveIntervalInMilliseconds,this._statefulReconnectBufferSize)}}export{oe as H};
