import{Z as e,_ as o,$ as s,a0 as t,a1 as a,a2 as n,b as r,c as i,a as u}from"./index-15d7e8a5.js";const c=e({name:"FacialRecognition",props:{joinConferenceFun:{type:Function,default:()=>{}}},setup(e){o({message:"加载中...",forbidClick:!0,duration:3e5});const c=s(),l=t({errorObj:null,whetherPsaa:0,certName:"",iosShow:0,loading:!1,myTitle:"身份认证结果",conferenceInfo:{},skipFaceIdentity:!0,routerGo:o=>{1===o?setTimeout((()=>{e.joinConferenceFun()}),2500):setTimeout((()=>{l.routerBackFun()}),2500)},routerBackFun:()=>{c.query.conferenceInfo?r({url:"/pages/voip/conference/ConferenceInfoPage?conferenceInfo="+c.query.conferenceInfo}):r({url:"/pages/voip/conference/ConferencePortalPage"})},onLoad:async()=>{try{sessionStorage.setItem("conferenceSerialNo",1);let e={studyId:"",serialNo:""};if(sessionStorage.getItem("userInfoIM")){const o=JSON.parse(sessionStorage.getItem("userInfoIM"));(null==o?void 0:o.studyId)&&(null==o?void 0:o.serialNo)&&(e.studyId=o.studyId,e.serialNo=o.serialNo)}if((null==e?void 0:e.studyId)&&(null==e?void 0:e.serialNo)){const o=await i.getFacialIdentityResult(e.studyId,{businessId:e.serialNo});l.whetherPsaa=(null==o?void 0:o.isSuccess)?1:2,l.routerGo(l.whetherPsaa),u()}else setTimeout((()=>{l.whetherPsaa=this.skipFaceIdentity?1:2,l.routerGo(l.whetherPsaa),u()}),2e3)}catch(e){setTimeout((()=>{l.whetherPsaa=this.skipFaceIdentity?1:2,l.routerGo(l.whetherPsaa),u()}),300)}}});return a((()=>{l.onLoad()})),{...n(l)}}});export{c as _};
