import{C as o,l as e,w as n,s as t,m as i,p as l,o as s,j as a,q as c,u as d,f as u,v as h,I as r,x as p}from"./index-15d7e8a5.js";import{_ as f}from"./_plugin-vue_export-helper.1b428a4d.js";const _=f({name:"LoginPage",data:()=>({focus:!1,phone:"",code:""}),components:{},props:{},onShow(){console.log("login onShow"),o.APP_SERVER.indexOf("wildfirechat")>=0&&-1===o.IM_SERVER_HOST.indexOf("wildfirechat")||-1===o.APP_SERVER.indexOf("wildfirechat")&&o.IM_SERVER_HOST.indexOf("wildfirechat")>=0?console.error("!!!! 严重错误!!!! Config.APP_SERVER 和 Config.IM_SERVER_HOST要一起修改，不能一个用官方服务，一个用自己部署的服务"):o.IM_SERVER_HOST.indexOf(":")>=0?console.error("!!!! 严重错误!!!! Config.IM_SERVER_HOST 不能包含端口，只需要 HOST 即可"):o.IM_SERVER_HOST.indexOf("http")>=0&&console.error("!!!! 严重错误!!!! Config.IM_SERVER_HOST 不能包含http，只需要 HOST 即可")},methods:{bindPhoneInput:function(o){this.phone=o.detail.value},bindCodeInput:function(o){this.code=o.detail.value},bindLoginTap:function(o){console.log(this.phone),this.login(this.phone,this.code)},login(o,l){e.loginWithAuthCode(o,l).then((o=>{let e=o.userId,i=o.token;n.connect(e,i),t("userId",e),t("token",i),this.go2ConversationList()})).catch((o=>{console.log("login failed",o),i({title:o,icon:"none"})}))},bindAuthCodeTap:function(o){console.log(this.phone),this.authCode(this.phone)},authCode(o){e.requestAuthCode(o).then((o=>{i({title:"发送验证码成功",icon:"none"})})).catch((o=>{i({title:o,icon:"none"})}))},go2ConversationList(){l({url:"/pages/voip/conference/ConferencePortalPage",success:()=>{console.log("to conversation list success")},fail:o=>{console.log("to conversation list error",o)},complete:()=>{console.log("switch tab complete")}})}}},[["render",function(o,e,n,t,i,l){const f=d,_=r,g=p;return s(),a(f,{class:"page-body"},{default:c((()=>[u(f,{class:"login-type-title"},{default:c((()=>[h("验证码登录")])),_:1}),u(f,{class:"mobile-container"},{default:c((()=>[u(f,null,{default:c((()=>[h("请输入手机号")])),_:1}),u(f,null,{default:c((()=>[u(f,{class:"mobile-input-container"},{default:c((()=>[u(_,{class:"input",onInput:l.bindPhoneInput,type:"number",placeholder:"手机号"},null,8,["onInput"])])),_:1})])),_:1})])),_:1}),u(f,{class:"auth-code-container"},{default:c((()=>[u(f,null,{default:c((()=>[h("请输入验证码")])),_:1}),u(f,{class:"auth-code-input-container"},{default:c((()=>[u(_,{onInput:l.bindCodeInput,type:"number",placeholder:"验证码"},null,8,["onInput"]),u(g,{size:"mini",disabled:11!==i.phone.length,onClick:l.bindAuthCodeTap},{default:c((()=>[h("获取验证码")])),_:1},8,["disabled","onClick"])])),_:1})])),_:1}),u(g,{disabled:!(i.phone&&i.code),class:"confirm-button",onClick:l.bindLoginTap},{default:c((()=>[h("登录")])),_:1},8,["disabled","onClick"])])),_:1})}],["__scopeId","data-v-dd63bef1"]]);export{_ as default};
