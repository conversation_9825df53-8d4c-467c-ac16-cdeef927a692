
# 这个就是从dockerhub拿下来的, 主要是dockerhub经常被墙, 缓存下提高效率
# 如果要在本地编译, 记得pull 下package.trialdata.cn/trialdatadocker/library/nginxalpine:*******
# 然后tag成ipackage的地址
FROM ipackage.trialdata.cn/trialdatadocker/library/nginxalpine:******* as base


# 微信端我们主要用的是 h5, 所以换一个编译包
FROM ipackage.trialdata.cn/trialdatadocker/library/h5-builder:*******  AS build
WORKDIR  /usr/app
COPY . .

RUN rm -f package-lock.json
RUN rm -rf node_modules

RUN npm install -g pnpm@8.15.5 --force

RUN pnpm -v

# RUN npm install 
# 使用淘宝的 npm 镜像来安装依赖
# RUN npm install --registry=https://registry.npm.taobao.org
# 使用npmmirror镜像
RUN pnpm install --force --prefer-offline  --registry=https://registry.npmmirror.com
# --force
# RUN npm i
RUN pnpm run k8s

FROM base AS final
WORKDIR /app
# 这次把文件放到patientui下面, 是因为目前编译出来默认在patientui目录下, 这个需要调整.
COPY --from=build /usr/app/dist/prod /usr/share/nginx/html
COPY --from=build /usr/app/verinfo.txt /usr/share/nginx/html/verinfo.txt
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 启动应用跑的代码
CMD ["nginx", "-g", "daemon off;"]
# 使用envsubt 用环境变量替换后台api的参数
# CMD ["/bin/sh","-c",""""""]

 
