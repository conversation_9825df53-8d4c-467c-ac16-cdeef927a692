<!DOCTYPE html>
<html lang="zh-cn">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
  <!-- <script type="text/javascript">
      (function (c, l, a, r, i, t, y) {
        c[a] =
          c[a] ||
          function () {
            (c[a].q = c[a].q || []).push(arguments);
          };
        t = l.createElement(r);
        t.async = 1;
        t.src = "https://www.clarity.ms/tag/" + i;
        y = l.getElementsByTagName(r)[0];
        y.parentNode.insertBefore(t, y);
      })(window, document, "clarity", "script", "mxv7qcsm7u");
    </script> -->
  <title>用药情况填写</title>
  <style>
    /* 修正横向滚动，保证移动端无横向溢出 */
    html,
    body {
      width: 100vw;
      min-width: 0;
      max-width: 100vw;
      height: 812px;
      min-height: 812px;
      max-height: 812px;
      margin: 0;
      padding: 0;
      background: #f6f8fa;
      /* 防止内容被按钮遮挡 */
      overflow: hidden;
    }

    .navbar {
      width: 100vw;
      max-width: 100vw;
      min-width: 0;
      left: 0;
      transform: none;
      border-radius: 0;
      box-shadow: 0 2px 8px #b0b0b033;
    }

    .gdmt-container {
      width: 100vw;
      max-width: 100vw;
      min-width: 0;
      margin: 0;
      border-radius: 0;
      box-shadow: none;
      padding: 0 0 70px 0;
      background: #f6f8fa;
      overflow-x: hidden;
      box-sizing: border-box;
    }

    .card-group {
      flex-direction: column;
      gap: 14px;
      margin-bottom: 10px;
      width: 100%;
      min-width: 0;
      box-sizing: border-box;
      align-items: stretch;
    }

    .med-card {
      min-width: 0;
      max-width: 100%;
      width: 100%;
      box-sizing: border-box;
      border-radius: 12px;
      box-shadow: 0 2px 10px #b0b0b055;
      padding: 16px 8px 12px 8px;
      margin-bottom: 0;
      align-items: flex-start;
      background: #fff;
      /* 卡片高度自适应内容，移除最小高度限制 */
      min-height: unset !important;
      height: auto !important;
    }

    .card-title {
      font-size: 1.1rem;
      margin-bottom: 10px;
      padding-left: 2px;
    }

    .advice-item-group>div {
      padding-left: 2px;
    }

    .advice-item,
    .advice-value {
      font-size: 0.98rem;
      margin-right: 3px;
    }

    .advice-follow {
      font-size: 0.98rem;
      gap: 8px;
    }

    .submit-float-bar {
      left: 0 !important;
      right: 0 !important;
      max-width: 100vw !important;
      width: 100vw !important;
      border-radius: 0 !important;
      transform: none !important;
      padding-left: 0;
      padding-right: 0;
      box-shadow: 0 -2px 8px #bbb3;
      display: flex;
      justify-content: center;
    }

    .submit-btn {
      /* width: 375px !important;
        max-width: 375px !important; */
      min-width: 0;
      border-radius: 6px;
      font-size: 1.1rem;
      padding: 12px 0;
      margin-top: 0;
      box-sizing: border-box;
    }

    select,
    .dose-input,
    .freq-input {
      font-size: 1rem;
      padding: 6px 8px;
      border-radius: 6px;
      width: 100%;
      min-width: 0;
      margin-top: 4px;
      margin-bottom: 8px;
      box-sizing: border-box;
    }

    .options label {
      width: 100%;
      box-sizing: border-box;
      flex-direction: column;
      font-size: 1rem;
      padding: 6px 8px;
      border-radius: 6px;
      margin-bottom: 0;
    }

    /* 修正医嘱药物信息与遵循选项为纵向排列 */
    .advice-item-group {
      display: flex;
      flex-direction: column;
      gap: 2px;
      margin-bottom: 8px;
      align-items: flex-start;
      width: 100%;
    }

    .advice-item-group>div {
      display: flex;
      align-items: center;
      width: 100%;
    }

    .advice-item {
      font-weight: 500;
      color: #333;
      min-width: 0;
      flex-shrink: 0;
    }

    .advice-follow {
      margin-top: 2px;
      font-size: 15px;
      display: flex;
      align-items: flex-start;
      gap: 12px;
      font-weight: normal;
      justify-content: flex-start;
    }

    .advice-follow label {
      font-size: 15px;
      font-weight: normal;
      margin-right: 8px;
    }

    /* 医嘱部分利尿剂多药物横向展示 */
    .advice-item-group {
      display: flex;
      align-items: center;
      margin-bottom: 6px;
      gap: 18px;
      font-size: 16px;
    }

    .advice-item {
      font-weight: 500;
      color: #333;
    }

    .advice-value {
      color: #555;
      margin-left: 2px;
    }

    /* 悬浮提交按钮条，适配移动端底部 */
    .submit-float-bar {
      position: fixed;
      left: 0;
      right: 0;
      bottom: 0;
      background: #fff;
      box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
      padding: 12px 16px 10px 16px;
      box-sizing: border-box;
      z-index: 100;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .submit-float-bar .submit-btn {
      width: 100%;
      font-size: 18px;
      padding: 12px 0;
      border-radius: 6px;
      box-shadow: 0 2px 8px rgba(0, 123, 255, 0.08);
    }

    .dose-input[type=number],
    .freq-input[type=number] {
      -moz-appearance: textfield;
      appearance: textfield;
    }

    .dose-input[type=number]::-webkit-inner-spin-button,
    .dose-input[type=number]::-webkit-outer-spin-button,
    .freq-input[type=number]::-webkit-inner-spin-button,
    .freq-input[type=number]::-webkit-outer-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }

    .submit-float-bar {
      position: fixed;
      left: 0;
      bottom: 0;
      width: 100vw;
      left: 50%;
      transform: translateX(-50%);
      background: #f8f9faee;
      box-shadow: 0 -2px 12px #bbb3;
      padding: 12px 18px 18px 18px;
      z-index: 200;
      display: flex;
      justify-content: center;
    }

    .submit-float-bar .submit-btn {
      width: 100%;
      margin-top: 0;
      margin-bottom: 0;
    }

    .diuretic-item {
      margin-bottom: 12px;
      border-radius: 10px;
      padding: 10px 8px 8px 8px;
      transition: background 0.2s, border 0.2s;
      border: 2px solid #e0e0e0;
      background: #f7fafd;
    }

    .diuretic-item input[type="checkbox"]:checked~span,
    .diuretic-item input[type="checkbox"]:checked+span {
      font-weight: bold;
      color: #1a5d9a;
    }

    .diuretic-item input[type="checkbox"]:checked {
      accent-color: #1a5d9a;
    }

    .diuretic-item.selected {
      background: #e3f1ff;
      border: 2px solid #1a5d9a;
      box-shadow: 0 2px 8px #b0d4ff55;
    }

    .diuretic-item:not(.selected) {
      opacity: 0.7;
    }

    /* 顶部导航栏样式 */
    .navbar {
      width: 100%;
      background: #2a4d6c;
      height: 54px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      /* z-index: 100; */
      box-shadow: 0 2px 8px #b0b0b033;
    }

    .navbar-title {
      color: #fff;
      font-size: 1.5rem;
      font-weight: bold;
      letter-spacing: 2px;
      text-align: center;
    }

    /* 卡片式药物分组样式 */
    .card-group {
      display: flex;
      flex-wrap: wrap;
      gap: 24px 16px;
      justify-content: center;
      margin-bottom: 18px;
    }

    .med-card {
      background: #fff;
      border-radius: 16px;
      box-shadow: 0 2px 10px #b0b0b055;
      padding: 22px 18px 16px 18px;
      min-width: 300px;
      max-width: 420px;
      flex: 1 1 340px;
      margin-bottom: 0;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
    }

    .card-title {
      font-size: 1.3rem;
      font-weight: bold;
      color: #2a4d6c;
      margin-bottom: 14px;
      letter-spacing: 1px;
    }

    .card-title .hint {
      color: #ff9800;
      background: #fffbe6;
      font-size: 1.05rem;
      font-weight: normal;
      border-radius: 6px;
      padding: 1px 7px 1px 7px;
      margin-left: 6px;
      vertical-align: middle;
    }

    .med-card-module {
      padding: 0 20px 20px 20px;
    }

    .med-card select {
      margin-bottom: 10px;
    }

    .med-card .dose-fields {
      margin-top: 8px;
      margin-bottom: 0;
    }

    h2 {
      font-size: 2.2rem;
      color: #2a4d6c;
      margin-bottom: 24px;
      text-align: center;
      letter-spacing: 2px;
    }

    .section {
      margin-bottom: 28px;
    }

    label {
      font-size: 1.25rem;
      color: #222;
      display: block;
    }

    .checkbox-label {
      font-size: 1.1rem;
      margin-right: 12px;
      display: inline-block;
      margin-bottom: 8px;
      white-space: nowrap;
      max-width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      vertical-align: middle;
    }

    .diuretic-item {
      width: 100%;
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
    }

    .dose-fields {
      width: 100%;
      margin-left: 0;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      gap: 10px 18px;
      margin-top: 8px;
    }

    .dose-label {
      width: 100%;
      font-size: 1.1rem;
      margin-right: 8px;
      margin-bottom: 0;
      display: flex;
      align-items: center;
      white-space: nowrap;
    }

    .dose-input,
    .freq-input {
      font-size: 1.1rem;
      padding: 6px 8px;
      border: 1.5px solid #b0b0b0;
      border-radius: 6px;
      width: 90px;
      margin: 0 6px 0 6px;
      background: #fff;
    }

    .freq-input {
      width: 120px;
      min-width: 120px;
    }

    /* 自定义下拉框样式 */
    .custom-select {
      position: relative;
      display: inline-block;
      width: 120px;
      min-width: 120px;
    }

    /* 药物选择下拉框样式 */
    .custom-select.drug-select {
      width: 100%;
      min-width: 200px;
    }

    /* 频率选择下拉框样式 */
    .custom-select.freq-select {
      width: 120px;
      min-width: 120px;
    }

    .custom-select .select-trigger {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 12px;
      background: #fff;
      border: 1.5px solid #b0b0b0;
      border-radius: 8px;
      cursor: pointer;
      font-size: 1rem;
      color: #333;
      transition: all 0.3s ease;
      user-select: none;
      min-height: 20px;
    }

    .custom-select .select-trigger:hover {
      border-color: #007bff;
      box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
    }

    .custom-select.active .select-trigger {
      border-color: #007bff;
      box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
    }

    .custom-select .select-text {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .custom-select .select-text.placeholder {
      color: #999;
    }

    .custom-select .select-arrow {
      width: 0;
      height: 0;
      border-left: 5px solid transparent;
      border-right: 5px solid transparent;
      border-top: 6px solid #666;
      margin-left: 8px;
      transition: transform 0.3s ease;
    }

    .custom-select.active .select-arrow {
      transform: rotate(180deg);
    }

    .custom-select .select-options {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: #fff;
      border: 1px solid #ddd;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      z-index: 9999;
      max-height: 200px;
      overflow-y: auto;
      opacity: 0;
      visibility: hidden;
      transform: translateY(-10px);
      transition: all 0.3s ease;
    }

    .custom-select.active .select-options {
      opacity: 1 !important;
      visibility: visible !important;
      transform: translateY(0) !important;
      display: block !important;
    }

    .custom-select .select-option {
      padding: 10px 12px;
      cursor: pointer;
      transition: background-color 0.2s ease;
      font-size: 1rem;
      color: #333;
      border-bottom: 1px solid #f0f0f0;
    }

    .custom-select .select-option:last-child {
      border-bottom: none;
    }

    .custom-select .select-option:hover {
      background-color: #f8f9fa;
    }

    .custom-select .select-option.selected {
      background-color: #007bff;
      color: #fff;
    }

    .custom-select .select-option.selected:hover {
      background-color: #0056b3;
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
      .custom-select {
        width: 110px;
        min-width: 110px;
      }

      .custom-select.drug-select {
        width: 100%;
        min-width: 180px;
      }

      .custom-select .select-trigger {
        padding: 6px 10px;
        font-size: 0.9rem;
      }

      .custom-select .select-option {
        padding: 8px 10px;
        font-size: 0.9rem;
      }
    }

    /* 确保下拉框在med-card中正确显示 */
    .med-card .custom-select.drug-select {
      margin-bottom: 10px;
    }

    /* 确保下拉框选项不被容器裁剪 */
    .med-card {
      overflow: visible !important;
    }

    .card-group {
      overflow: visible !important;
    }

    select {
      font-size: 1.15rem;
      padding: 6px 10px;
      border-radius: 6px;
      border: 1.5px solid #b0b0b0;
      background: #fff;
      margin-top: 6px;
      margin-bottom: 6px;
    }

    .submit-btn {
      font-size: 1.3rem;
      background: #2a4d6c;
      color: #fff;
      border: none;
      border-radius: 8px;
      padding: 12px 0;
      width: 100%;
      margin-top: 18px;
      cursor: pointer;
      letter-spacing: 2px;
      transition: background 0.2s;
    }

    .submit-btn:hover {
      background: #1d3557;
    }

    .result {
      font-size: 1.2rem;
      color: #1b7e1b;
      margin-top: 18px;
      text-align: center;
    }

    body {
      font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
      background: #f6f8fa;
      margin: 0;
      padding: 0;
      padding-bottom: 70px;
    }

    .gdmt-container {
      max-width: 100%;
      background: #fff;
      height: calc(100vh - 79px);
      overflow: auto;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      padding: 24px 0px 16px 0px;
    }

    h2 {
      text-align: center;
      color: #1a237e;
      margin-bottom: 18px;
    }

    .section {
      margin-bottom: 18px;
    }

    label {
      font-weight: bold;
      display: block;
    }

    .options {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
    }

    .options label {
      font-weight: normal;
      background: #e3eafc;
      border-radius: 6px;
      padding: 6px 12px;
      cursor: pointer;
      transition: background 0.2s;
    }

    .options input[type="checkbox"]:checked+span {
      background: #1976d2;
      color: #fff;
    }

    select {
      width: 100%;
      padding: 8px;
      border-radius: 6px;
      border: 1px solid #b0bec5;
      font-size: 16px;
    }

    .submit-btn {
      width: 100%;
      background: #1976d2;
      color: #fff;
      border: none;
      border-radius: 6px;
      padding: 12px;
      font-size: 18px;
      margin-top: 12px;
      cursor: pointer;
      transition: background 0.2s;
    }

    .submit-btn:hover {
      background: #0d47a1;
    }

    .result {
      margin-top: 18px;
      color: #388e3c;
      text-align: center;
      font-size: 16px;
    }

    /* 医嘱部分标题栏 */
    .advice-title-bar {
      background: #f7f7fa;
      border-radius: 12px 12px 0 0;
      padding: 14px 16px 8px 16px;
      margin-bottom: 0;
    }

    .advice-section-title {
      font-size: 1.1rem;
      font-weight: 600;
      color: #333;
      letter-spacing: 0.5px;
      margin-bottom: 0;
    }

    /* 医嘱部分-利尿剂多药物分隔线 */
    .advice-item-group+.advice-item-group {
      border-top: 1px dashed #e0e0e0;
      margin-top: 10px;
      padding-top: 10px;
    }

    /* 恢复医嘱部分外层卡片样式 */
    .advice-title-bar,
    .advice-section-title {
      /* 还原为无特殊样式，使用med-card和card-title */
      all: unset;
    }

    /* 第二部分 剂量/频率字段样式调整 */
    .dose-label,
    .freq-label {
      font-weight: normal !important;
      font-size: 1rem !important;
      color: #333 !important;
    }

    .dose-input,
    .freq-input {
      font-size: 1rem !important;
    }

    .dose-unit {
      font-size: 1rem !important;
      font-weight: normal !important;
      color: #333 !important;
    }

    /*加载样式*/
    .popUpShowLoading,
    .onLoading {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 999;
      display: none;
    }

    .popUpShowLoading .box,
    .onLoading .box {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 80%;
      background: rgba(0, 0, 0, 0.7);
      border-radius: 0.66666667vw;
      box-shadow: 1px 1px 50px rgba(0, 0, 0, 0.3);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 32vw;
      height: 32vw;
      color: #fff;
      border-radius: 2vw;
    }

    .popUpShowLoading .box img,
    .onLoading .box img {
      width: 10.66666667vw;
      height: 10.66666667vw;
      margin-bottom: 1.33333333vw;
    }
  </style>
</head>

<body>
  <div class="navbar">
    <div class="navbar-title" style="position: absolute;left: 8vw;" onclick="previousPage()">&lt;</div>
    <span class="navbar-title">用药情况</span>
  </div>
  <div class="gdmt-container">
    <!-- 第一部分：医嘱遵循情况 -->
    <div id="adviceSection" style="display:block;">
      <form id="adviceForm">
        <div class="card-group">
          <div class="med-card-module">
            <div class="card-title">以下为医嘱信息，请确认您的实际用药情况</div>
            <div class="advice-demo">
              <div class="card-group">
                <div class="med-card">
                  <div class="card-title">利尿剂</div>
                  <!-- 会出现0-3种 -->
                  <div class="advice-item-group">
                    <div>
                      <span class="advice-item">呋塞米（襻利尿剂）</span>
                      <span class="advice-value">20mg</span>
                      <span class="advice-value">每日2次</span>
                    </div>
                    <div class="advice-follow">
                      是否遵循医嘱服用该药物？
                      <label><input type="radio" name="follow-diuretic-0" value="是">是</label>
                      <label><input type="radio" name="follow-diuretic-0" value="否">否</label>
                    </div>
                    <div class="advice-follow">
                      是否遵循医嘱剂量和频率？
                      <label><input type="radio" name="dosefreq-diuretic-0" value="是">是</label>
                      <label><input type="radio" name="dosefreq-diuretic-0" value="否">否</label>
                    </div>
                  </div>
                  <div class="advice-item-group">
                    <div>
                      <span class="advice-item">布美他尼（襻利尿剂）</span>
                      <span class="advice-value">12.5mg</span>
                      <span class="advice-value">每日1次</span>
                    </div>
                    <div class="advice-follow">
                      是否遵循医嘱服用该药物？
                      <label><input type="radio" name="follow-diuretic-1" value="是">是</label>
                      <label><input type="radio" name="follow-diuretic-1" value="否">否</label>
                    </div>
                    <div class="advice-follow">
                      是否遵循医嘱剂量和频率？
                      <label><input type="radio" name="dosefreq-diuretic-1" value="是">是</label>
                      <label><input type="radio" name="dosefreq-diuretic-1" value="否">否</label>
                    </div>
                  </div>
                  <div class="advice-item-group">
                    <div>
                      <span class="advice-item">托拉塞米（襻利尿剂）</span>
                      <span class="advice-value">12.5mg</span>
                      <span class="advice-value">每日1次</span>
                    </div>
                    <div class="advice-follow">
                      是否遵循医嘱服用该药物？
                      <label><input type="radio" name="follow-diuretic-1" value="是">是</label>
                      <label><input type="radio" name="follow-diuretic-1" value="否">否</label>
                    </div>
                    <div class="advice-follow">
                      是否遵循医嘱剂量和频率？
                      <label><input type="radio" name="dosefreq-diuretic-1" value="是">是</label>
                      <label><input type="radio" name="dosefreq-diuretic-1" value="否">否</label>
                    </div>
                  </div>
                  <div class="advice-item-group">
                    <div>
                      <span class="advice-item">氢氯噻嗪（噻嗪类利尿剂）</span>
                      <span class="advice-value">12.5mg</span>
                      <span class="advice-value">每日1次</span>
                    </div>
                    <div class="advice-follow">
                      是否遵循医嘱服用该药物？
                      <label><input type="radio" name="follow-diuretic-1" value="是">是</label>
                      <label><input type="radio" name="follow-diuretic-1" value="否">否</label>
                    </div>
                    <div class="advice-follow">
                      是否遵循医嘱剂量和频率？
                      <label><input type="radio" name="dosefreq-diuretic-1" value="是">是</label>
                      <label><input type="radio" name="dosefreq-diuretic-1" value="否">否</label>
                    </div>
                  </div>
                  <!-- 美托拉宗（噻嗪类利尿剂） -->
                   <div class="advice-item-group">
                    <div>
                      <span class="advice-item">美托拉宗（噻嗪类利尿剂）</span>
                      <span class="advice-value">12.5mg</span>
                      <span class="advice-value">每日1次</span>
                    </div>
                    <div class="advice-follow">
                      是否遵循医嘱服用该药物？
                      <label><input type="radio" name="follow-diuretic-1" value="是">是</label>
                      <label><input type="radio" name="follow-diuretic-1" value="否">否</label>
                    </div>
                    <div class="advice-follow">
                      是否遵循医嘱剂量和频率？
                      <label><input type="radio" name="dosefreq-diuretic-1" value="是">是</label>
                      <label><input type="radio" name="dosefreq-diuretic-1" value="否">否</label>
                    </div>
                  </div>
                  <div class="advice-item-group">
                    <div>
                      <span class="advice-item">吲达帕胺（噻嗪类利尿剂）</span>
                      <span class="advice-value">12.5mg</span>
                      <span class="advice-value">每日1次</span>
                    </div>
                    <div class="advice-follow">
                      是否遵循医嘱服用该药物？
                      <label><input type="radio" name="follow-diuretic-1" value="是">是</label>
                      <label><input type="radio" name="follow-diuretic-1" value="否">否</label>
                    </div>
                    <div class="advice-follow">
                      是否遵循医嘱剂量和频率？
                      <label><input type="radio" name="dosefreq-diuretic-1" value="是">是</label>
                      <label><input type="radio" name="dosefreq-diuretic-1" value="否">否</label>
                    </div>
                  </div>
                  <div class="advice-item-group">
                    <div>
                      <span class="advice-item">阿米洛利（保钾利尿剂）</span>
                      <span class="advice-value">12.5mg</span>
                      <span class="advice-value">每日1次</span>
                    </div>
                    <div class="advice-follow">
                      是否遵循医嘱服用该药物？
                      <label><input type="radio" name="follow-diuretic-1" value="是">是</label>
                      <label><input type="radio" name="follow-diuretic-1" value="否">否</label>
                    </div>
                    <div class="advice-follow">
                      是否遵循医嘱剂量和频率？
                      <label><input type="radio" name="dosefreq-diuretic-1" value="是">是</label>
                      <label><input type="radio" name="dosefreq-diuretic-1" value="否">否</label>
                    </div>
                  </div>
                  <div class="advice-item-group">
                    <div>
                      <span class="advice-item">氨苯蝶啶（保钾利尿剂）</span>
                      <span class="advice-value">12.5mg</span>
                      <span class="advice-value">每日1次</span>
                    </div>
                    <div class="advice-follow">
                      是否遵循医嘱服用该药物？
                      <label><input type="radio" name="follow-diuretic-1" value="是">是</label>
                      <label><input type="radio" name="follow-diuretic-1" value="否">否</label>
                    </div>
                    <div class="advice-follow">
                      是否遵循医嘱剂量和频率？
                      <label><input type="radio" name="dosefreq-diuretic-1" value="是">是</label>
                      <label><input type="radio" name="dosefreq-diuretic-1" value="否">否</label>
                    </div>
                  </div>
                  <div class="advice-item-group">
                    <div style="display: flex;flex-wrap: wrap;">
                      <span class="advice-item">托伐普坦（血管加压素V2受体拮抗剂）</span>
                      <span class="advice-value">12.5mg</span>
                      <span class="advice-value">每日1次</span>
                    </div>
                    <div class="advice-follow">
                      是否遵循医嘱服用该药物？
                      <label><input type="radio" name="follow-diuretic-1" value="是">是</label>
                      <label><input type="radio" name="follow-diuretic-1" value="否">否</label>
                    </div>
                    <div class="advice-follow">
                      是否遵循医嘱剂量和频率？
                      <label><input type="radio" name="dosefreq-diuretic-1" value="是">是</label>
                      <label><input type="radio" name="dosefreq-diuretic-1" value="否">否</label>
                    </div>
                  </div>
                </div>
                <div class="med-card">
                  <!-- 会出现0-1种 -->
                  <div class="card-title">β blockers</div>
                  <div class="advice-item-group">
                    <div>
                      <span class="advice-item">美托洛尔</span>
                      <span class="advice-value">25mg</span>
                      <span class="advice-value">每日1次</span>
                    </div>
                    <div class="advice-follow">
                      是否遵循医嘱服用该药物？
                      <label><input type="radio" name="follow-beta-blocker" value="是">是</label>
                      <label><input type="radio" name="follow-beta-blocker" value="否">否</label>
                    </div>
                    <div class="advice-follow">
                      是否遵循医嘱剂量和频率？
                      <label><input type="radio" name="dosefreq-beta-blocker" value="是">是</label>
                      <label><input type="radio" name="dosefreq-beta-blocker" value="否">否</label>
                    </div>
                  </div>
                </div>
                <div class="med-card">
                  <div class="card-title">ARNi/ACEi/ARB</div>
                  <div class="advice-item-group">
                    <div>
                      <span class="advice-item">沙库巴曲缬沙坦</span>
                      <span class="advice-value">50mg</span>
                      <span class="advice-value">每日2次</span>
                    </div>
                    <div class="advice-follow">
                      是否遵循医嘱服用该药物？
                      <label><input type="radio" name="follow-arni" value="是">是</label>
                      <label><input type="radio" name="follow-arni" value="否">否</label>
                    </div>
                    <div class="advice-follow">
                      是否遵循医嘱剂量和频率？
                      <label><input type="radio" name="dosefreq-arni" value="是">是</label>
                      <label><input type="radio" name="dosefreq-arni" value="否">否</label>
                    </div>
                  </div>
                </div>
                <div class="med-card">
                  <div class="card-title">MRA</div>
                  <div class="advice-item-group">
                    <div>
                      <span class="advice-item">螺内酯</span>
                      <span class="advice-value">20mg</span>
                      <span class="advice-value">每日1次</span>
                    </div>
                    <div class="advice-follow">
                      是否遵循医嘱服用该药物？
                      <label><input type="radio" name="follow-mra" value="是">是</label>
                      <label><input type="radio" name="follow-mra" value="否">否</label>
                    </div>
                    <div class="advice-follow">
                      是否遵循医嘱剂量和频率？
                      <label><input type="radio" name="dosefreq-mra" value="是">是</label>
                      <label><input type="radio" name="dosefreq-mra" value="否">否</label>
                    </div>
                  </div>
                </div>
                <div class="med-card">
                  <div class="card-title">SGLT2i</div>
                  <div class="advice-item-group">
                    <div>
                      <span class="advice-item">达格列净</span>
                      <span class="advice-value">10mg</span>
                      <span class="advice-value">每日1次</span>
                    </div>
                    <div class="advice-follow">
                      是否遵循医嘱服用该药物？
                      <label><input type="radio" name="follow-sglt2i" value="是">是</label>
                      <label><input type="radio" name="follow-sglt2i" value="否">否</label>
                    </div>
                    <div class="advice-follow">
                      是否遵循医嘱剂量和频率？
                      <label><input type="radio" name="dosefreq-sglt2i" value="是">是</label>
                      <label><input type="radio" name="dosefreq-sglt2i" value="否">否</label>
                    </div>
                  </div>
                </div>
                <div class="med-card">
                  <div class="card-title">维立西呱</div>
                  <div class="advice-item-group">
                    <div>
                      <span class="advice-item">维立西呱</span>
                      <span class="advice-value">5mg</span>
                      <span class="advice-value">每日1次</span>
                    </div>
                    <div class="advice-follow">
                      是否遵循医嘱服用该药物？
                      <label><input type="radio" name="follow-vericiguat" value="是">是</label>
                      <label><input type="radio" name="follow-vericiguat" value="否">否</label>
                    </div>
                    <div class="advice-follow">
                      是否遵循医嘱剂量和频率？
                      <label><input type="radio" name="dosefreq-vericiguat" value="是">是</label>
                      <label><input type="radio" name="dosefreq-vericiguat" value="否">否</label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="submit-float-bar">
          <button type="submit" class="submit-btn">下一步</button>
        </div>
      </form>
    </div>
    <!-- 第二部分：用药情况填写 -->
    <div id="mainFormWrap" style="display:none;">
      <form id="medicationForm" style="margin-bottom: 50px;">
        <div class="med-card-module card-group">
          <div class="med-card">
            <div class="card-title">利尿剂<span class="hint">可选0~3种</span></div>
            <div id="diuretics-options" class="options"></div>
            <div id="diuretics-extra"></div>
          </div>
          <div class="med-card">
            <div class="card-title">倍他受体阻滞剂</div>
            <div id="beta-blocker-container"></div>
            <div id="beta-blocker-extra"></div>
          </div>
          <div class="med-card">
            <div class="card-title">ARNI/ACEI/ARB</div>
            <div id="arni-acei-arb-container"></div>
            <div id="arni-acei-arb-extra"></div>
          </div>
          <div class="med-card">
            <div class="card-title">MRA</div>
            <div id="mra-container"></div>
            <div id="mra-extra"></div>
          </div>
          <div class="med-card">
            <div class="card-title">SGLT2i</div>
            <div id="sglt2i-container"></div>
            <div id="sglt2i-extra"></div>
          </div>
          <div class="med-card">
            <div class="card-title">维立西呱</div>
            <div id="vericiguat-container"></div>
            <div id="vericiguat-extra"></div>
          </div>
        </div>
      </form>
      <div class="submit-float-bar">
        <button type="submit" class="submit-btn">提交</button>
      </div>
      <div id="result" class="result"></div>
    </div>
  </div>

  <div class="onLoading">
    <div class="box" style="background: rgba(0, 0, 0, 0.8)">
      <img src="https://trialdata-share.oss-cn-hangzhou.aliyuncs.com/loading.gif" alt="" />
      <div>加载中...</div>
    </div>
  </div>

  <div class="popUpShowLoading">
    <div class="box" style="background: rgba(0, 0, 0, 0.8)">
      <img src="https://trialdata-share.oss-cn-hangzhou.aliyuncs.com/loading.gif" alt="" />
      <div>加载中...</div>
    </div>
  </div>
  <script type="text/javascript">
    /*获取元素  相当于jquery的 $('#id') $('.class')
      但只找出现的第一个元素
    示例： v('#scale') v('.header-box'),v('div')
    */
    function v(selector) {
      const element = document.querySelector(selector);
      return element;
    }
    /*获取多个元素  相当于jquery的 $('#id') $('.class')
    示例： vAll('#scale') vAll('.header-box'),vAll('div')
    */
    function vAll(selector) {
      const element = document.querySelectorAll(selector);
      return element;
    }
    /*  相当于vue中的 v-html
    el 元素或者'#id,.class,div'
    示例：vHtml('.vhtml', `<div>测试vhtml改变后的html</div>`)
     若是只传1个参数 vHtml('.vhtml') 则返回元素当前的Html
    */
    function vHtml(element, html) {
      if (typeof element === 'string') {
        element = v(element)
      }
      if (html) {
        element.innerHTML = html
      } else {
        return element.innerHTML
      }
    }
    /*  相当于vue中的 v-text
    el 元素或者'#id,.class,div'
    示例：vText('.vText', `测试vText改变后的Text`)
      若是只传1个参数 vText('.vText') 则返回元素当前的Text
    */
    function vText(element, text) {
      if (typeof element === 'string') {
        element = v(element)
      }
      if (text) {
        element.innerText = text
      } else {
        return element.innerText
      }
    }
    /*相当于 vue中的 v-modul
      element 元素或者'#id,.class,div' 得是input标签
      data 对象
      将要绑定property 对象中的key
      const inputData = {
        inputText: '初始值1'
      }
      vModel('.vModel',inputData,'inputText')
    */
    function vModel(element, data, property) {
      if (typeof element === 'string') {
        element = v(element)
      }
      element.value = data[property]; // 初始化输入框的值
      // 监听输入框的输入事件，更新数据
      element.addEventListener('input', function () {
        data[property] = element.value;
      });
      // 监听数据变化，更新输入框的值
      Object.defineProperty(data, property, {
        get: function () {
          return element.value;
        },
        set: function (newValue) {
          element.value = newValue;
        }
      });
    }
    /*vShow 相当于 vue中的 v-show
    element 元素或者 '#id,.class,div',
    flag: true 或 false ,
    displayType: 如：block flex等
    示例：
    vShow('.vShow',true) vShow('.vShow',false)
    vShow('.vShow',true,'flex')
    */
    function vShow(element, flag, displayType) {
      if (typeof element === 'string') {
        element = v(element)
      }
      if (flag) {
        // 如果条件满足，将元素添加到文档中
        element.style.display = ''; // 将样式 display 值设为空字符串
        if (displayType)
          element.style.display = displayType;
      } else {
        // 如果条件不满足，将元素从文档中移除
        element.style.display = 'none';
      }
    }
    /*vOn 相当于 vue中的 @ v-on
    element 元素 或者 '#id,.class,div',
    eventName 事件名 如cilik,
    fun 回调函数
    示例： vOn('.vShow','click',clickName)
    */
    function vOn(element, eventName, fun) {
      if (typeof element === 'string') {
        element = v(element)
      }
      if (eventName && fun) {
        // addEventListener 中的回调函数 this 指向调用者
        // onclick 中的函数 this 指向绑定元素
        element.addEventListener(eventName, fun);
      }
    }
    /* 移除事件
      vOnRemove('.vShow','click',clickName)
    */
    function vOnRemove(element, eventName, fun) {
      if (typeof element === 'string') {
        element = v(element)
      }
      if (eventName && fun) {
        element.removeEventListener(eventName, fun);
      }
    }
    /*vBind 相当于 vue中的 v-bind 
    element 元素 或者 '#id,.class,div',
    attributeName 名 如class style src value,
    val 要赋值的值
    示例：
    vBind('.vShow','class','red blue')
    vBind('.vShow','style',{ color: 'red','font-size': '30px'})
    vBind('.csimg','src','https://trialdata-share.oss-cn-hangzhou.aliyuncs.com/loading.gif')
    :*/
    function vBind(element, attributeName, val) {
      if (typeof element === 'string') {
        element = v(element)
      }
      if (element) {
        if (attributeName === 'class') {
          element.setAttribute('class', val);
        } else if (attributeName === 'style') {
          for (var key in val) {
            element.setAttribute('style', key + ':' + val[key] + ';');
          }
        } else {
          element[attributeName] = val
        }
      }
    }
    /*vAppend: 类似于v-slot append() - 在被选元素的结尾插入内容
    */
    function vAppend(element, val) {
      if (typeof element === 'string') {
        element = v(element)
      }
      element.innerHTML += val
    }
    /*vPrepend: prepend() - 在被选元素的开头插入内容
      注意：可能会影响原有的标签上的事件等
    */
    function vPrepend(element, val) {
      if (typeof element === 'string') {
        element = v(element)
      }
      const oldElementHTML = element.innerHTML
      element.innerHTML = val
      element.innerHTML += oldElementHTML
    }
    /*vRemove 移除某个元素*/
    function vRemove(element) {
      if (typeof element === 'string') {
        element = v(element)
      }
      element.parentNode.removeChild(element)
    }
    /**/
  </script>
  <script>
    // 创建自定义下拉框的函数
    function createCustomSelect(name, selectedValue, options, cssClass = '') {
      const selectedOption = options.find(opt => opt.value === selectedValue);
      const selectedText = selectedOption ? selectedOption.text : options[0].text;
      const isPlaceholder = !selectedValue || selectedValue === '';

      return `
        <div class="custom-select ${cssClass}" data-name="${name}">
          <input type="hidden" name="${name}" value="${selectedValue}">
          <div class="select-trigger">
            <span class="select-text ${isPlaceholder ? 'placeholder' : ''}">${selectedText}</span>
            <div class="select-arrow"></div>
          </div>
          <div class="select-options">
            ${options.map(option => `
              <div class="select-option ${option.value === selectedValue ? 'selected' : ''}"
                   data-value="${option.value}">${option.text}</div>
            `).join('')}
          </div>
        </div>
      `;
    }

    // 全局事件监听器标记
    let customSelectsInitialized = false;

    // 初始化自定义下拉框事件（只初始化一次）
    function initCustomSelects() {
      if (customSelectsInitialized) {
        return; // 如果已经初始化过，直接返回
      }

      customSelectsInitialized = true;

      // 使用事件委托，只绑定一次事件监听器
      document.addEventListener('click', function(e) {
        // 点击下拉框触发器
        const trigger = e.target.closest('.select-trigger');
        if (trigger) {
          e.preventDefault();
          e.stopPropagation();

          const customSelect = trigger.closest('.custom-select');
          if (!customSelect) return;

          const isActive = customSelect.classList.contains('active');

          // 关闭所有其他下拉框
          document.querySelectorAll('.custom-select.active').forEach(select => {
            if (select !== customSelect) {
              select.classList.remove('active');
            }
          });

          // 切换当前下拉框状态
          if (!isActive) {
            customSelect.classList.add('active');
            console.log('下拉框已激活:', customSelect.dataset.name);
          } else {
            customSelect.classList.remove('active');
            console.log('下拉框已关闭:', customSelect.dataset.name);
          }

          return; // 重要：阻止继续执行后面的代码
        }
        // 点击选项
        const option = e.target.closest('.select-option');
        if (option) {
          e.preventDefault();
          e.stopPropagation();

          const customSelect = option.closest('.custom-select');
          if (!customSelect) return;
          const trigger = customSelect.querySelector('.select-trigger');
          const selectText = trigger.querySelector('.select-text');
          const hiddenInput = customSelect.querySelector('input[type="hidden"]');

          // 更新显示文本
          const value = option.dataset.value;
          selectText.textContent = option.textContent;
          selectText.classList.toggle('placeholder', !value);

          // 更新隐藏输入框的值
          hiddenInput.value = value;

          // 更新选中状态
          customSelect.querySelectorAll('.select-option').forEach(opt => {
            opt.classList.remove('selected');
          });
          option.classList.add('selected');

          // 关闭下拉框
          customSelect.classList.remove('active');

          // 触发change事件（用于药物选择联动）
          const selectName = customSelect.dataset.name;
          if (selectName && (selectName === 'beta-blocker' || selectName === 'arni-acei-arb' ||
                            selectName === 'mra' || selectName === 'sglt2i' || selectName === 'vericiguat')) {
            // 使用setTimeout确保DOM更新完成后再执行
            setTimeout(() => {
              if (selectName === 'beta-blocker') {
                renderDoseFields('beta-blocker-extra', value);
              } else if (selectName === 'arni-acei-arb') {
                renderDoseFields('arni-acei-arb-extra', value);
              } else if (selectName === 'mra') {
                renderDoseFields('mra-extra', value);
              } else if (selectName === 'sglt2i') {
                renderDoseFields('sglt2i-extra', value);
              } else if (selectName === 'vericiguat') {
                renderDoseFields('vericiguat-extra', value);
              }
            }, 10);
          }
        }
        // 点击其他地方关闭所有下拉框
        else {
          document.querySelectorAll('.custom-select.active').forEach(select => {
            select.classList.remove('active');
          });
        }
      });
    }

    // 药物选项示例，可根据实际补充
    const DIURETICS = [
      "呋塞米（襻利尿剂）",
      "布美他尼（襻利尿剂）",
      "托拉塞米（襻利尿剂）",
      "氢氯噻嗪（噻嗪类利尿剂）",
      "美托拉宗（噻嗪类利尿剂）",
      "吲达帕胺（噻嗪类利尿剂）",
      "阿米洛利（保钾利尿剂）",
      "氨苯蝶啶（保钾利尿剂）",
      "托伐普坦（血管加压素V2受体拮抗剂）"
    ];
    const BETA_BLOCKERS = [
      "美托洛尔",
      "比索洛尔",
      "卡维地洛",
      "酒石酸美托洛尔",
      "琥珀酸美托洛尔"
    ];
    const ARNI_ACEI_ARB = [
      "沙库巴曲缬沙坦（ARNI）",
      "卡托普利（ACEI）",
      "依那普利（ACEI）",
      "福辛普利（ACEI）",
      "赖诺普利（ACEI）",
      "培哚普利（ACEI）",
      "雷米普利（ACEI）",
      "贝那普利（ACEI）",
      "坎地沙坦（ARB）",
      "缬沙坦（ARB）",
      "氯沙坦（ARB）",
      "厄贝沙坦（ARB）",
      "阿利沙坦（ARB）",
      "替米沙坦（ARB）",
      "美阿沙坦（ARB）",
    ];
    const MRA = ["螺内酯", "依普利酮", "非奈利酮"];
    const SGLT2I = ["达格列净", "恩格列净", "恒格列净", "艾托格列净"];
    const VERICIGUAT = ["维立西呱"];
    const minDate = "2024-01-01";
    const today = new Date();
    const yyyy = today.getFullYear();
    const mm = String(today.getMonth() + 1).padStart(2, '0');
    const dd = String(today.getDate()).padStart(2, '0');
    const todayStr = `${yyyy}-${mm}-${dd}`;

    // 渲染多选checkbox
    function renderDiuretics(selected = [], doses = {}) {
      const container = document.getElementById('diuretics-options');
      // 先收集当前页面上所有已输入的剂量和频率
      const currentDoses = { ...doses };
      DIURETICS.forEach(drug => {
        const doseInput = document.querySelector(`input[name="dose-${drug}"]`);
        const freqSelect = document.querySelector(`input[name="freq-${drug}"]`);
        const endDateInput = document.querySelector(`input[name="end-${drug}"]`);
        if (doseInput || freqSelect || endDateInput) {
          currentDoses[drug] = {
            dose: doseInput ? doseInput.value : (currentDoses[drug]?.dose || ''),
            freq: freqSelect ? freqSelect.value : (currentDoses[drug]?.freq || ''),
            endDate: endDateInput ? endDateInput.value : (currentDoses[drug]?.endDate || '')
          };
        }
      });
      container.innerHTML = '';
      let checkedCount = selected.length;
      DIURETICS.forEach(drug => {
        const id = `diuretic-${drug}`;
        const checked = selected.includes(drug) ? 'checked' : '';
        // 限制最多只能选3个
        const disabled = !checked && checkedCount >= 3 ? 'disabled' : '';
        const selectedClass = selected.includes(drug) ? 'selected' : '';
        const freqValue = currentDoses[drug]?.freq || '';
        const doseValue = currentDoses[drug]?.dose || '';
        let endDateValue = currentDoses[drug]?.endDate || todayStr;
        if (endDateValue < minDate) endDateValue = minDate;
        container.innerHTML += `
            <div class="diuretic-item ${selectedClass}">
                <label class="checkbox-label"><input type="checkbox" name="diuretics" value="${drug}" ${checked} ${disabled}><span>${drug}</span></label>
                <span class="dose-fields" style="display:${selected.includes(drug) ? 'inline-block' : 'none'}">
                    <label class="dose-label">单次剂量<input type="number" min="0" step="any" name="dose-${drug}" value="${doseValue}" class="dose-input"> mg</label>
                    <label class="dose-label">给药频率
                        ${createCustomSelect(`freq-${drug}`, freqValue, [
                            { value: '', text: '请选择' },
                            { value: '每日1次', text: '每日1次' },
                            { value: '每日2次', text: '每日2次' },
                            { value: '每日3次', text: '每日3次' },
                            { value: '每日4次', text: '每日4次' }
                        ], 'freq-select')}
                    </label>
                    <label class="dose-label">开始日期
                        <input
                         type="date"
                         min="2024-01-01"
                         name="start-${drug}"
                         class="date-input"
                    ></label>
                    <label class="dose-label">结束日期
                        <input
                            type="date"
                            name="end-${drug}"
                            class="date-input"
                            min="${minDate}"
                            max="${todayStr}"
                            value="${endDateValue}"
                        >
                    </label>
                </span>
            </div>`;
      });

      // 自定义下拉框已通过全局事件委托处理，无需重新初始化
    }
    // 渲染自定义下拉框
    function renderCustomSelect(containerId, selectId, options, selected) {
      const container = document.getElementById(containerId);
      // 创建选项数组，包含"未用药"默认项
      const selectOptions = [
        { value: '', text: '未用药' },
        ...options.map(opt => ({ value: opt, text: opt }))
      ];

      container.innerHTML = createCustomSelect(selectId, selected, selectOptions, 'drug-select');

      // 自定义下拉框已通过全局事件委托处理，无需重新初始化
    }

    // 获取自定义下拉框的值
    function getCustomSelectValue(selectId) {
      const hiddenInput = document.querySelector(`input[name="${selectId}"]`);
      return hiddenInput ? hiddenInput.value : '';
    }

    // 渲染下拉（保留兼容性，但现在使用自定义下拉框）
    function renderSelect(id, options, selected) {
      renderCustomSelect(id + '-container', id, options, selected);
    }

    function renderDoseFields(extraId, drug, dose = '', freq = '') {
      // 未用药时隐藏剂量和频率
      if (!drug) {
        document.getElementById(extraId).innerHTML = '';
        return;
      }
      document.getElementById(extraId).innerHTML = `
        <div class="dose-fields">
            <label class="dose-label">单次剂量<input type="number" min="0" step="any" name="dose-${drug}" value="${dose}" class="dose-input"> mg</label>
            <label class="dose-label">给药频率
                ${createCustomSelect(`freq-${drug}`, freq, [
                    { value: '', text: '请选择' },
                    { value: '每日1次', text: '每日1次' },
                    { value: '每日2次', text: '每日2次' },
                    { value: '每日3次', text: '每日3次' },
                    { value: '每日4次', text: '每日4次' }
                ], 'freq-select')}
            </label>
        </div>
        <div class="dose-fields">
            <label class="dose-label">开始日期<input type="date" name="start-${drug}" min="${minDate}" class="date-input"></label>
            <label class="dose-label">结束日期
             <input
                type="date"
                name="end-${drug}"
                class="date-input"
                min="${minDate}"
                max="${todayStr}"
                value="${todayStr}"
             ></label>
        </div>
    `;

    // 自定义下拉框已通过全局事件委托处理，无需重新初始化
    }
    // 读取上次访视数据
    function loadLastRecord() {
      try {
        return JSON.parse(localStorage.getItem('medicationRecord') || '{}');
      } catch {
        return {};
      }
    }
    // 保存本次访视数据
    function saveRecord(data) {
      localStorage.setItem('medicationRecord', JSON.stringify(data));
    }

    // 构造一组DEMO医嘱
    function getDemoRecord() {
      return {
        diuretics: ["呋塞米（襻利尿剂）", "氢氯噻嗪（噻嗪类利尿剂）"],
        diureticsDoses: {
          "呋塞米（襻利尿剂）": { dose: "20", freq: "每日2次" },
          "氢氯噻嗪（噻嗪类利尿剂）": { dose: "12.5", freq: "每日1次" }
        },
        betaBlocker: "",
        betaBlockerDose: "",
        betaBlockerFreq: "",
        arniAceiArb: "",
        arniAceiArbDose: "",
        arniAceiArbFreq: "",
        mra: "",
        mraDose: "",
        mraFreq: "",
        sglt2i: "",
        sglt2iDose: "",
        sglt2iFreq: "",
        vericiguat: "",
        vericiguatDose: "",
        vericiguatFreq: "",
        time: new Date().toISOString()
      };
    }


    // 初始化填写页（仅用药填写，无前置页）
    function initFormPage() {
      // 默认不选中任何药物
      renderDiuretics([], {});
      renderSelect('beta-blocker', BETA_BLOCKERS, '');
      renderSelect('arni-acei-arb', ARNI_ACEI_ARB, '');
      renderSelect('mra', MRA, '');
      renderSelect('sglt2i', SGLT2I, '');
      renderSelect('vericiguat', VERICIGUAT, '');
      renderDoseFields('beta-blocker-extra', '', '', '');
      renderDoseFields('arni-acei-arb-extra', '', '', '');
      renderDoseFields('mra-extra', '', '', '');
      renderDoseFields('sglt2i-extra', '', '', '');
      document.getElementById('vericiguat-extra').innerHTML = '';
    }

    // 页面初始化（先显示医嘱遵循情况）
    function initPage() {
      document.getElementById('adviceSection').style.display = 'block';
      document.getElementById('mainFormWrap').style.display = 'none';
      // 初始化用药填写页（第二部分），但不显示
      initFormPage();
    }
    // 表单提交
    function handleSubmit(e) {
      e.preventDefault();
      const diuretics = Array.from(document.querySelectorAll('input[name="diuretics"]:checked')).map(i => i.value);
      if (diuretics.length > 3) {
        alert('利尿剂最多可选3种');
        return;
      }
      // 利尿剂剂量
      const diureticsDoses = {};
      diuretics.forEach(drug => {
        const dose = document.querySelector(`input[name="dose-${drug}"]`)?.value || '';
        const freq = document.querySelector(`input[name="freq-${drug}"]`)?.value || '';
        diureticsDoses[drug] = { dose, freq };
      });
      // 其他药物
      const betaBlocker = getCustomSelectValue('beta-blocker');
      const betaBlockerDose = document.querySelector('input[name="dose-' + betaBlocker + '"]')?.value || '';
      const betaBlockerFreq = document.querySelector('input[name="freq-' + betaBlocker + '"]')?.value || '';
      const arniAceiArb = getCustomSelectValue('arni-acei-arb');
      const arniAceiArbDose = document.querySelector('input[name="dose-' + arniAceiArb + '"]')?.value || '';
      const arniAceiArbFreq = document.querySelector('input[name="freq-' + arniAceiArb + '"]')?.value || '';
      const mra = getCustomSelectValue('mra');
      const mraDose = document.querySelector('input[name="dose-' + mra + '"]')?.value || '';
      const mraFreq = document.querySelector('input[name="freq-' + mra + '"]')?.value || '';
      const sglt2i = getCustomSelectValue('sglt2i');
      const sglt2iDose = document.querySelector('input[name="dose-' + sglt2i + '"]')?.value || '';
      const sglt2iFreq = document.querySelector('input[name="freq-' + sglt2i + '"]')?.value || '';
      // 维立西呱
      const vericiguat = getCustomSelectValue('vericiguat');
      let vericiguatDose = '', vericiguatFreq = '';
      if (vericiguat) {
        vericiguatDose = document.querySelector('input[name="dose-' + vericiguat + '"]')?.value || '';
        vericiguatFreq = document.querySelector('input[name="freq-' + vericiguat + '"]')?.value || '';
      }
      const data = {
        diuretics,
        diureticsDoses,
        betaBlocker, betaBlockerDose, betaBlockerFreq,
        arniAceiArb, arniAceiArbDose, arniAceiArbFreq,
        mra, mraDose, mraFreq,
        sglt2i, sglt2iDose, sglt2iFreq,
        vericiguat, vericiguatDose, vericiguatFreq,
        time: new Date().toISOString()
      };
      saveRecord(data);
      document.getElementById('result').innerText = '提交成功！';
    }
    document.addEventListener('DOMContentLoaded', () => {
      // 先初始化自定义下拉框事件监听器
      initCustomSelects();
      // 然后初始化页面
      initPage();
      // 调试检查
      debugCustomSelects();
      // 第一部分"下一步"按钮交互
      document.getElementById('adviceForm').addEventListener('submit', function (e) {
        e.preventDefault();
        // 这里可做表单校验和数据收集，暂时直接切换
        document.getElementById('adviceSection').style.display = 'none';
        document.getElementById('mainFormWrap').style.display = 'block';
        // 滚动条设到顶部
        v('.gdmt-container').scrollTop = 0
        // 可在此处根据第一部分填写内容初始化第二部分
        // initFormPage(adviceData);
      });
      // 利尿剂checkbox显示剂量和限制选择数
      document.getElementById('diuretics-options').addEventListener('change', function (e) {
        const checked = Array.from(document.querySelectorAll('input[name="diuretics"]:checked')).map(i => i.value);
        renderDiuretics(checked);
      });
      // 下拉联动剂量已在自定义下拉框的点击事件中处理
      document.getElementById('medicationForm').addEventListener('submit', handleSubmit);
    });

    // 监听第一部分"是否遵循医嘱服用该药物"选项，控制"是否遵循医嘱剂量和频率"显示
    function setupAdviceFollowToggle() {
      const adviceSection = document.getElementById('adviceSection');
      if (!adviceSection) return;
      // 找到所有"是否遵循医嘱服用该药物"单选组
      const followGroups = adviceSection.querySelectorAll('.advice-item-group');
      followGroups.forEach((group, idx) => {
        const followRadios = group.querySelectorAll('input[type=radio][name^="follow-"]');
        const dosefreqDiv = group.querySelectorAll('.advice-follow')[1];
        if (!dosefreqDiv) return;
        // 初始隐藏
        dosefreqDiv.style.display = 'none';
        followRadios.forEach(radio => {
          radio.addEventListener('change', function () {
            if (this.value === '是') {
              dosefreqDiv.style.display = '';
            } else {
              dosefreqDiv.style.display = 'none';
              // 清除已选
              const inputs = dosefreqDiv.querySelectorAll('input[type=radio]');
              inputs.forEach(i => i.checked = false);
            }
          });
          // 页面初始化时根据当前选中项决定显示
          if (radio.checked && radio.value === '是') {
            dosefreqDiv.style.display = '';
          }
        });
      });
    }
    // 页面加载后初始化
    window.addEventListener('DOMContentLoaded', setupAdviceFollowToggle);
  </script>
  <script type="text/javascript">
    let fontSizeNum = 0;
    // let oldFontSize = 0
    window.onload = function () {
      var wjb = undefined;
      if (typeof WeixinJSBridge == "object") {
        wjb = WeixinJSBridge;
      }
      if (parent && typeof parent.WeixinJSBridge == "object") {
        wjb = parent.WeixinJSBridge;
      }
      if (typeof wjb == "object" && typeof wjb.invoke == "function") {
        handleFontSize();
      } else {
        document.addEventListener(
          "WeixinJSBridgeReady",
          handleFontSize,
          false
        );
      }
      function handleFontSize() {
        // 设置网页字体为默认大小
        if (typeof WeixinJSBridge == "object") {
          wjb = WeixinJSBridge;
        }
        if (parent && typeof parent.WeixinJSBridge == "object") {
          wjb = parent.WeixinJSBridge;
        }
        wjb.invoke("setFontSizeCallback", { fontSize: 0 });
        // 重写设置网页字体大小的事件
        wjb.on("menu:setfont", function (res) {
          fontSizeNum = res.fontSize;
          wjb.invoke("setFontSizeCallback", { fontSize: 0 });
        });
      }
    };

    window.parent.postMessage("pagehide", "*");

    var onLoading = document.querySelector(".onLoading");
    var popUpShowLoading = document.querySelector(".popUpShowLoading");

    var outerHeight;
    var distance; // 间距
    var questInfo = {}, // 记录得到的所有数据
      showLayout = false,
      questionView,
      quesitonData,
      quesitonDataListCopy, // 初始值
      quesitonDataTSList, // 暂存的数组
      isEditable = 1, // 默认可编辑
      IsInitial = false;

    async function initial() {
      if (IsInitial) return; // 如果已经初始化, 就不初始化了
      onLoading.style.display = "block";
      showLayout = true;
      setTimeout(() => {
        if (window.outerHeight > window.innerHeight) {
          outerHeight = window.innerHeight;
        } else if (window.outerHeight < window.innerHeight) {
          outerHeight = window.outerHeight;
        } else {
          outerHeight = window.outerHeight;
        }
      }, 0);
      // 接受客户端消息, 保存token, patientId, visitId等等相关信息
      window.removeEventListener(
        "message",
        async (e) => await handleQuestInfo(e)
      ); // 这样能移除嘛? 主要是有时候会有多次绑定的情况, 不行还使用jquery
      window.addEventListener(
        "message",
        async (e) => await handleQuestInfo(e)
      );
      //获取基本信息
      window.parent.postMessage("getQuestInfo", "*"); // 问父级要基础信息
      IsInitial = true;
    }
    initial(); // 从这里执行开始
    // 深拷贝对象
    function deepClone(source) {
      if (!source && typeof source !== "object") {
        throw new Error("error arguments", "deepClone");
      }
      const targetObj = source.constructor === Array ? [] : {};
      Object.keys(source).forEach((keys) => {
        if (source[keys] && typeof source[keys] === "object") {
          targetObj[keys] = deepClone(source[keys]);
        } else {
          targetObj[keys] = source[keys];
        }
      });
      return targetObj;
    }

    async function handleQuestInfo(event) {
      let questInfoFromParent = JSON.parse(event.data);
      questInfo = { ...questInfoFromParent };
      // 数据变动接口 // console.log(questInfo,questInfo.postDataChangeUrl);
      if (questInfo.getViewUrl) {
        // 如果有视图url则获取视图信息
        questionView = await getData(questInfo.getViewUrl);
      }
      if (questInfo.getDataUrl) {
        // await getData(questInfo.dctUrlApi+`api/Patient/xxx/${questId}/Data`);
        quesitonData = await getData(questInfo.getDataUrl);
        // 暂存修改
        if (quesitonData && quesitonData.questCrfItemes) {
          quesitonDataListCopy = deepClone(quesitonData.questCrfItemes);
          quesitonDataTSList = deepClone(quesitonData.questCrfItemes); // 可能取其他值
        } else {
          quesitonDataListCopy = [];
          quesitonDataTSList = [];
        }
      }
      isEditable = questInfo.isEditable / 1;

      topicAndAnswer(
        questionView.questCrfItemes,
        quesitonData.questCrfItemes
      );

      onLoading.style.display = "none";
      showLayout = false;
    }

    var contentText;

    // 题目遍历
    function topicAndAnswer(view, data) {
      // 第一题可以显示其余的都隐藏中间还要穿插其他题目
      view.forEach((item, index) => {
        // console.log(item.fieldLabel);
        // item.fieldLabel = item.fieldLabel.replace(/(<([^>]+)>)/gi, "");
        // console.log(item.fieldLabel,2);
        // if (index === 1) {
        //   item.fieldLabel = "2. <u>长距离</u>行走对您来说有困难吗？";
        // } else if (index === 2) {
        //   item.fieldLabel = "3. 户外<u>短距离</u>行走对您来说有困难吗？";
        // }
        data.forEach((ite, idx) => {
          if (item.dctCode === ite.dctCode) {
            if (item.crfFieldControl === 9) {
              item.fieldItems.forEach((it, ind) => {
                if (it.itemValue === ite.fieldValue) {
                  it.checkedShow = true;
                } else {
                  it.checkedShow = false;
                }
              });
            } else {
              item.fieldValue = ite.fieldValue;
            }
          }
        });
      });
      // 这里可以处理页面

      // 禁用
      if (isEditable === 0) {
        // var allInput = document.querySelectorAll('input[type="radio"]');
        // var allInputText = document.querySelectorAll('input[type="text"]');
        // var allInputDate = document.querySelectorAll('input[type="date"]');
        // for (let i = 0; i < allInput.length; i++) {
        //   allInput[i].disabled = true;
        // }
        // for (let i = 0; i < allInputText.length; i++) {
        //   allInputText[i].disabled = true;
        // }
        // for (let i = 0; i < allInputDate.length; i++) {
        //   allInputDate[i].disabled = true;
        // }
      }
    }

    function assessDateChange(time) {
      var assessDateText = document.querySelectorAll(".assessDateText");
      for (let i = 0; i < assessDateText.length; i++) {
        if (
          time.dataset.numberdate === assessDateText[i].dataset.numberdate
        ) {
          assessDateText[i].value = time.value;
          time.value = assessDateText[i].value;
          break;
        }
      }
    }

    function listAnswers(fieldItems, dctCode) {
      let set = "";
      for (let j = 0; j < fieldItems.length; j++) {
        if (fieldItems[j].checkedShow) {
          set += `
              <div class="content-topic-answer">
                <div class="content-topic-answer-mb">${fieldItems[j].itemName}</div>
                <label for="${dctCode}${j}">
                  <div>
                    <input id="${dctCode}${j}" checked type="radio" data-fieldValueStr="${fieldItems[j].itemName}" data-fieldValue="${fieldItems[j].itemValue}" name="${dctCode}" />
                  </div>
                </label>
              </div>
            `;
        } else {
          set += `
              <div class="content-topic-answer">
                <div class="content-topic-answer-mb">${fieldItems[j].itemName}</div>
                <label for="${dctCode}${j}">
                  <div>
                    <input id="${dctCode}${j}" type="radio" data-fieldValueStr="${fieldItems[j].itemName}" data-fieldValue="${fieldItems[j].itemValue}" name="${dctCode}" />
                  </div>
                </label>
              </div>
            `;
        }
      }
      return set;
    }

    function sendMessage() {
      var wjb = undefined;
      if (typeof WeixinJSBridge == "object") {
        wjb = WeixinJSBridge;
      }
      if (parent && typeof parent.WeixinJSBridge == "object") {
        wjb = parent.WeixinJSBridge;
      }
      if (typeof wjb == "object" && typeof wjb.invoke == "function") {
        resetFontSize();
      } else {
        document.addEventListener(
          "WeixinJSBridgeReady",
          resetFontSize,
          false
        );
      }

      function resetFontSize() {
        if (typeof wjb == "object" && typeof wjb.invoke == "function") {
          wjb.invoke("setFontSizeCallback", { fontSize: fontSizeNum });
          wjb.on("menu:setfont", function (res) {
            wjb.invoke("setFontSizeCallback", { fontSize: res.fontSize });
          });
        }
      }
      // 保存时通知父级
      window.parent.postMessage("saveQuestInfo", "*");
      window.parent.postMessage("goLastPageNew", "*");
    }

    function previousPage() {
      var wjb = undefined;
      if (typeof WeixinJSBridge == "object") {
        wjb = WeixinJSBridge;
      }
      if (parent && typeof parent.WeixinJSBridge == "object") {
        wjb = parent.WeixinJSBridge;
      }
      if (typeof wjb == "object" && typeof wjb.invoke == "function") {
        resetFontSize();
      } else {
        document.addEventListener(
          "WeixinJSBridgeReady",
          resetFontSize,
          false
        );
      }

      function resetFontSize() {
        if (typeof wjb == "object" && typeof wjb.invoke == "function") {
          wjb.invoke("setFontSizeCallback", { fontSize: fontSizeNum });
          wjb.on("menu:setfont", function (res) {
            wjb.invoke("setFontSizeCallback", { fontSize: res.fontSize });
          });
        }
      }

      window.parent.postMessage("previousPage", "*");
    }
    // 保存
    async function SaveData(num = 0) {
      try {
        if (num === 0) {
          popUpShowLoading.style.display = "block";
        }
        let questData = getQuestData();
        for (let x of quesitonData.questCrfItemes) {
          if (questData[x.dctCode] && questData[x.dctCode].fieldValue) {
            x.fieldValue = questData[x.dctCode].fieldValue;
            x.fieldValueStr = questData[x.dctCode].fieldValueStr;
          }
        }
        if (num === 0) {
          // 已提交先走数据变动
          if (quesitonData.isSubmit) {
            let res = await putData(questInfo.postDataChangeUrl, quesitonData)
            if (res?.questCrfItemes.length && res?.questCrfItemes.some((e) => e?.isDisplay)) {
              // 显示数据变动
              window.parent.postMessage(`dataChangeModuleObj=${JSON.stringify(res)}`, "*");
              popUpShowLoading.style.display = "none";
              return
            }
          }
          quesitonData.isStaging = false;
          let z = await putData(questInfo.putUrl, quesitonData);
          popUpShowLoading.style.display = "none";
          // 提交成功后
          sendMessage(); // 保存数据提示保存成功
        } else if (num === 1) {
          // 暂存写到这里
          if (questionView && !questionView.isSubmit) {
            let showAll = whetherChange(
              quesitonDataTSList,
              quesitonData.questCrfItemes
            );
            if (showAll) {
              quesitonData.isStaging = true;
              let z = await putData(questInfo.putUrl, quesitonData); // 这里放开
              quesitonDataTSList = deepClone(quesitonData.questCrfItemes);
            }
          }
        }
      } catch {
        popUpShowLoading.style.display = "none";
      }
    }

    function getQuestData() {
      let z = {};
      let topicAnsweEvery = document.querySelectorAll(
        ".topic-answe-every-radio"
      );
      for (let i = 0; i < topicAnsweEvery.length; i++) {
        let inputPitch = topicAnsweEvery[i].querySelectorAll(
          'input[type="radio"]'
        );
        for (let j = 0; j < inputPitch.length; j++) {
          if (inputPitch[j].checked) {
            let dctCode = inputPitch[j].name;
            let dctVal = inputPitch[j].dataset.fieldvalue;
            let dctValStr = inputPitch[j].dataset.fieldvaluestr;
            z[dctCode] = { fieldValue: dctVal, fieldValueStr: dctValStr };
          }
        }
      }
      let contentOther = document.querySelectorAll(".content-other");
      for (let k = 0; k < contentOther.length; k++) {
        let inputPitchText =
          contentOther[k].querySelectorAll('input[type="text"]');
        for (let j = 0; j < inputPitchText.length; j++) {
          if (inputPitchText[j].value) {
            z[inputPitchText[j].name] = {
              fieldValue: inputPitchText[j].value,
              fieldValueStr: inputPitchText[j].value,
            };
          } else {
            z[inputPitchText[j].name] = { fieldValue: "", fieldValueStr: "" };
          }
        }
      }


      // 获取最后一个值
      healthV = document.querySelector(".health-value");
      if (healthV.dataset.value) {
        let dctCode = healthV.dataset.dctcode
        let dctVal = healthV.dataset.value
        z[dctCode] = { fieldValue: dctVal, fieldValueStr: dctVal }
      } else {
        let dctCode = healthV.dataset.dctcode
        z[dctCode] = { fieldValue: '', fieldValueStr: '' }
      }
      // 文本和日期
      // console.log(z);
      return z;
    }

    // post接口
    async function postData(url = "", data = {}) {
      // Default options are marked with *
      const response = await fetch(url, {
        method: "post", // *GET, POST, PUT, DELETE, etc.
        mode: "cors", // no-cors, *cors, same-origin
        cache: "no-cache", // *default, no-cache, reload, force-cache, only-if-cached
        credentials: "omit", // include, *same-origin, omit
        headers: {
          "Content-Type": "application/json",
          TrialAuth: questInfo.token,
          // 'Content-Type': 'application/x-www-form-urlencoded',
        },
        redirect: "follow", // manual, *follow, error
        referrerPolicy: "no-referrer", // no-referrer, *no-referrer-when-downgrade, origin, origin-when-cross-origin, same-origin, strict-origin, strict-origin-when-cross-origin, unsafe-url
        body: JSON.stringify(data), // body data type must match "Content-Type" header
      });
      return response.json(); // parses JSON response into native JavaScript objects
    }

    // 提交接口
    async function putData(url = "", data = {}) {
      // Default options are marked with *
      const response = await fetch(url, {
        method: "PUT", // *GET, POST, PUT, DELETE, etc.
        mode: "cors", // no-cors, *cors, same-origin
        cache: "no-cache", // *default, no-cache, reload, force-cache, only-if-cached
        credentials: "omit", // include, *same-origin, omit
        headers: {
          "Content-Type": "application/json",
          TrialAuth: questInfo.token,
          // 'Content-Type': 'application/x-www-form-urlencoded',
        },
        redirect: "follow", // manual, *follow, error
        referrerPolicy: "no-referrer", // no-referrer, *no-referrer-when-downgrade, origin, origin-when-cross-origin, same-origin, strict-origin, strict-origin-when-cross-origin, unsafe-url
        body: JSON.stringify(data), // body data type must match "Content-Type" header
      });
      return response.json(); // parses JSON response into native JavaScript objects
    }

    // 获取数据
    async function getData(url = "") {
      // Default options are marked with *
      const response = await fetch(url, {
        method: "GET", // *GET, POST, PUT, DELETE, etc.
        mode: "cors", // no-cors, *cors, same-origin
        cache: "no-cache", // *default, no-cache, reload, force-cache, only-if-cached
        credentials: "omit", // include, *same-origin, omit
        headers: {
          "Content-Type": "application/json",
          TrialAuth: questInfo.token,
          // 'Content-Type': 'application/x-www-form-urlencoded',
        },
        redirect: "follow", // manual, *follow, error
        referrerPolicy: "no-referrer", // no-referrer, *no-referrer-when-downgrade, origin, origin-when-cross-origin, same-origin, strict-origin, strict-origin-when-cross-origin, unsafe-url
      });
      return response.json(); // parses JSON response into native JavaScript objects
    }

    // 调试：检查自定义下拉框是否正确生成
    function debugCustomSelects() {
      setTimeout(() => {
        const customSelects = document.querySelectorAll('.custom-select');
        console.log('找到自定义下拉框数量:', customSelects.length);
        customSelects.forEach((select, index) => {
          const options = select.querySelectorAll('.select-option');
          console.log(`下拉框 ${index + 1} 选项数量:`, options.length);
        });
      }, 1000);
    }
  </script>
</body>

</html>