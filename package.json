{"name": "edct", "version": "0.1.0", "private": true, "scripts": {"dev": "vite --mode development", "build": "vuedx-typecheck . && vite build --mode production", "preview": "vite preview", "test": "jest ./test", "k8s": "vite build --mode production --outDir dist/prod ", "eslint": "npx eslint . --ext .js,.jsx,.ts,.tsx --fix", "stylelint": "npx stylelint **/*.{css,vue} --fix"}, "dependencies": {"@fingerprintjs/fingerprintjs": "3.3.3", "@microsoft/signalr": "^8.0.0", "@trialdata/common-fun-css": "^1.0.5", "axios": "^0.21.4", "core-js": "3.30.2", "detectrtc": "^1.4.1", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^3.3.1", "events": "^3.3.0", "image-mosaic": "^1.0.4", "less": "^4.2.0", "long": "^5.2.3", "nprogress": "0.2.0", "pdfh5": "1.4.3", "pdfjs-dist": "2.4.456", "prettier": "^2.2.1", "recorder-core": "^1.3.24040900", "screenfull": "^5.1.0", "setimmediate": "1.0.5", "signature_pad": "^4.1.6", "vant": "3.6.11", "video.js": "7.21.4", "viewerjs": "1.10.2", "vite-plugin-vue-setup-extend": "^0.4.0", "vue": "3.3.4", "vue-i18n": "^9.3.0-beta.17", "vue-router": "4.2.2", "vue3-lazy": "^1.0.0-alpha.1", "vuex": "4.1.0"}, "license": "MIT", "devDependencies": {"@babel/preset-env": "^7.12.11", "@testing-library/jest-dom": "^5.11.8", "@types/jest": "^26.0.20", "@types/node": "^14.14.20", "@types/nprogress": "^0.2.0", "@typescript-eslint/eslint-plugin": "^4.21.0", "@typescript-eslint/parser": "^4.21.0", "@vitejs/plugin-vue": "^1.1.4", "@vue/compiler-sfc": "^3.0.11", "@vue/test-utils": "^2.0.0-beta.13", "@vuedx/typecheck": "^0.6.0", "@vuedx/typescript-plugin-vue": "^0.6.0", "autoprefixer": "^10.4.13", "babel-eslint": "^10.1.0", "babel-jest": "^26.6.3", "crypto-js": "^4.0.0", "default-passive-events": "^2.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^7.8.0", "jest": "^26.6.3", "lint-staged": "^9.5.0", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "path-to-regexp": "^6.2.0", "postcss": "^8.4.18", "postcss-import": "^14.0.1", "postcss-nested": "^5.0.3", "postcss-simple-vars": "^6.0.2", "sass": "^1.32.8", "stylelint": "^13.8.0", "stylelint-config-standard": "^20.0.0", "tailwindcss": "^3.2.3", "ts-jest": "^26.4.4", "typescript": "^4.2.3", "vite": "^2.1.2", "vite-plugin-svg-icons": "^0.4.0", "vue-jest": "^5.0.0-alpha.7"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "eslintIgnore": ["node_modules", "dist"]}