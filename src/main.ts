import { createApp } from 'vue'
import App from './App.vue'
// import { Field, CellGroup, Form, Button, Uploader,Slider,Tabbar } from 'vant';
import 'vant/lib/index.css'
import Vant from 'vant'
import initStorePersistence from './store/persistence'
import store from './store/index'
import { router } from './route/index'
import 'video.js/dist/video-js.css'
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import '@trialdata/common-fun-css/index.css'
import '@/style/common.less';
import '@/style/tailwind.less'
import { maintainedFun } from '@/utils/debounce'
import { Toast } from 'vant'

const lowNetSpeedInfo = '网络信号较弱，请等待或稍后再试';
const noNetInfo = '无法连接到网络，请检查网络设置';

const routerBackFun = (num = 1) => {
    // 判断是否为app，如果是app消息，则post给app
    if (store.state.appKey?.toLowerCase() === 'app' && window.location.href.includes('navigateback=1')) {
        return new Promise((resolve, reject) => {
            const msgData = {
                data: {
                    action: 'navigateBack',
                    payload: ''
                }
            }
            // 如果是iOS
            if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.jumpToNative) {
                window.webkit.messageHandlers.jumpToNative.postMessage(msgData);
            }
            // 如果是非iOS环境(即uniapp、安卓)
            uni.postMessage(msgData);
        });
    }

    const routerInn = [...store.state.routerInn]
    if (typeof num !== 'number') {
        num = 1
    }
    if (num > store.state.routerInn.length) {
        if (store.state.routerInn.length) {
            num = store.state.routerInn.length - 1
        } else {
            // 将来做没路由时的兜底跳转
            return
        }
    }
    // 清除当前路由栈，回到上一栈
    if (store.state.routerInn?.length && store.state.routerInn[store.state.routerInn.length - num].backPath) {
        const routerInnBack = { ...store.state.routerInn[store.state.routerInn.length - num] }
        if (num) {
            routerInn.length -= num
        }
        store.dispatch('setRouterInn', routerInn)
        router.replace({
            path: routerInnBack.backPath,
            query: routerInnBack.backQuery
        })
    }
}
const routerGoFun = (type, path = '', query, backPath, backQuery = '') => {
    // backQuery 不传形参时 默认值 undefined
    if (type === 'routerInnPush') {
        const routerInn = [...store.state.routerInn]
        routerInn.push({
            backQuery,
            backPath,
            path,
            query
        })
        store.dispatch('setRouterInn', routerInn)
    }
    // else if (type === 'replace') {
    //     // 
    // }
    router.replace({
        path,
        query
    });
}

const setActiveSite = async (res, activeStudyId, activeSiteId) => {
    // console.log(res, activeStudyId, activeSiteId);
    let studyInfo = {
        avatarUrl: '',
        studyName: '',
        siteName: ''
    }
    res.studies.forEach(p => {
        p.sites.forEach(s => {
            if (p.studyId === activeStudyId && s.siteId === activeSiteId) {
                if (s.isActive != 1) {
                    s.isActive = 1;
                }
                studyInfo = {
                    avatarUrl: p.avatarUrl,
                    studyName: p.studyName,
                    siteName: s.siteName
                }
            } else {
                if (s.isActive === 1) {
                    s.isActive = 0;
                }
            }
        });
    });
    // 切换热门项目
    if (activeStudyId && activeSiteId) {
        store.dispatch('setSiteIdStudyIds', {
            siteId: activeSiteId,
            studyId: activeStudyId,
            ...studyInfo
        })
    }
}

NProgress.configure({
    easing: 'ease',  // 动画方式    
    speed: 500,  // 递增进度条的速度    
    showSpinner: false, // 是否显示加载ico    
    trickleSpeed: 200, // 自动递增间隔    
    minimum: 0.3 // 初始化时的最小百分比
})
initStorePersistence(store) // 缓存vuex

const app = createApp(App)
app.config.globalProperties.$routerBackFun = routerBackFun
app.config.globalProperties.$routerGoFun = routerGoFun
// 自定义 v-toupper-case 指令将HTML的值自动转为大写
// app.directive('toupper-case',  function(el, binding){
//     // js查看binding.value身上有没有toupperCase属性object.hasOwnProperty()
//     // console.log(binding.value.hasOwnProperty('toUpperCase'));
//     el.innerHTML = binding.value.toUpperCase()
// })

app.
    use(Vant).
    // use(VueVideoPlayer).
    // use(Notify).
    use(store).
    use(router).mount('#app')

// 10秒后提示网络信号较弱
const timeoutTimer = setTimeout(() => {
    Toast.clear();
    Toast({
        message: lowNetSpeedInfo,
        duration: 0,
        forbidClick: false,
    });
}, 10000);


// 当路由进入前
router.beforeEach(async (to, form) => {
    // 每次切换页面时，调用进度条
    NProgress.start();
    // 这个一定要加，没有next()页面不会跳转的
    return true
});
router.onError((error, to) => {
    Toast(noNetInfo);
});
router.afterEach((to) => {
    // 在即将进入新的页面组件前，关闭掉进度条
    if (
        to.fullPath !== "/home" &&
        to.fullPath !== "/maintained" &&
        to.fullPath !== "/sweepCodePrompt"
    ) {

        maintainedFun(to.fullPath);
    }

    NProgress.done();
    clearTimeout(timeoutTimer);
});
