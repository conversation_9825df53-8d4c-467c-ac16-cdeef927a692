import {
  createRouter,
  createWebHashHistory,
  RouteRecordRaw
} from "vue-router";
// createWebHashHistory,createWebHistory

import store from '@/store'

const patientToken = sessionStorage.getItem('patientToken')

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/home',
  },
  {
    path: '/404',
    name: 'Page404',
    component: () => import('@/views/404.vue'),
  },
  {
    path: '/home',
    name: 'Home',
    component: () => import('@/views/Home/index.vue'),
    beforeEnter: () => { // 路由守卫,通过审核后可以进入
      if (patientToken || store.state.patientToken) {
        if (patientToken && patientToken !== 'undefined') {
          store.dispatch('setToken', patientToken);
        }
        return true
      } else {
        return false
      }
    }
  },
  // 提示扫码页:sweepCodePrompt
  {
    path: '/sweepCodePrompt',
    name: 'SweepCodePrompt',
    component: () => import('@/views/sweepCodePrompt/index.vue'),
  },
  // 结束研究的状态ProjectEnd
  {
    path: '/projectEnd',
    name: 'ProjectEnd',
    component: () => import('@/views/sweepCodePrompt/ProjectEnd.vue'),
  },
  // 欢迎加入项目WelcomeToJoinUs
  {
    path: '/welcomeToJoinUs',
    name: 'WelcomeToJoinUs',
    component: () => import('@/views/welcomeToJoinUs/index.vue'),
  },
  // 条款 - 协议 PrivacyAgreementTermsOfService
  {
    path: '/privacyagreementtermsofservice',
    name: 'PrivacyAgreementTermsOfService',
    component: () => import('@/views/welcomeToJoinUs/PrivacyAgreementTermsOfService.vue'),
  },
  // 绑定手机号BindingMobile
  {
    path: '/bindingmobile',
    name: 'BindingMobile',
    component: () => import('@/views/welcomeToJoinUs/BindingMobile.vue'),
  },
  /*受试者信息登记 */
  {
    path: '/informationRegister',
    name: 'InformationRegister',
    component: () => import('@/views/welcomeToJoinUs/InformationRegister.vue'),
  },
  // 知情同意书-Informed
  {
    path: '/informed',
    name: 'Informed',
    component: () => import('@/views/welcomeToJoinUs/Informed.vue'),
  },
  // 知情同意书-Informed InformedVideo
  {
    path: '/informedVideo',
    name: 'InformedVideo',
    component: () => import('@/views/welcomeToJoinUs/InformedVideo.vue'),
  },
  // 待发起签署
  {
    path: '/readyForSignature',
    name: 'ReadyForSignature.vue',
    component: () => import('@/views/welcomeToJoinUs/ReadyForSignature.vue'),
  },
  // 待讲解知情同意书
  {
    path: '/waitExplainInformed',
    name: 'WaitExplainInformed',
    component: () => import('@/views/welcomeToJoinUs/WaitExplainInformed.vue'),
  },
  // 待同意知情同意书
  {
    path: '/waitInformed',
    name: 'WaitInformed',
    component: () => import('@/views/welcomeToJoinUs/WaitInformed.vue'),
  },
  // 知情问卷
  {
    path: '/informedQuestionnaire',
    name: 'InformedQuestionnaire',
    component: () => import('@/views/welcomeToJoinUs/InformedQuestionnaire.vue'),
  },
  // 录制个人声明recordPersonalStatement
  {
    path: '/recordPersonalStatement',
    name: 'RecordPersonalStatement',
    component: () => import('@/views/welcomeToJoinUs/RecordPersonalStatement.vue'),
  },
  // 签署电子知情同意SignInformed
  {
    path: '/signInformed',
    name: 'SignInformed',
    component: () => import('@/views/welcomeToJoinUs/SignInformed.vue'),
  },
  // 签署知情同意
  {
    path: '/signInformedFaDaDa',
    name: 'SignInformedFaDaDa',
    component: () => import('@/views/welcomeToJoinUs/SignInformedFaDaDa.vue'),
  },
  // 播放videoPlay
  {
    path: '/videoPlay',
    name: 'VideoPlay',
    component: () => import('@/views/welcomeToJoinUs/VideoPlay.vue'),
  },
  // 登记信息
  {
    path: '/registerInfo',
    name: 'RegisterInfo',
    component: () => import('@/views/welcomeToJoinUs/RegisterInfo.vue')
  },
  // 问卷调查
  {
    path: '/questionnaire',
    name: 'Questionnaire',
    component: () => import('@/views/Questionnaire/index.vue'),
  },
  // 列表问卷编辑-或只读
  {
    path: '/editQuestionnairetList',
    name: 'EditQuestionnairetList',
    component: () => import('@/views/Questionnaire/EditQuestionnairetList.vue'),
  },
  // 审核-auit
  {
    path: '/audit',
    name: 'Audit',
    component: () => import('@/views/audit/index.vue'),
  },
  // (我的日常)-->日历myCalendar
  {
    path: '/mycalendar',
    name: 'MyCalendar',
    component: () => import('@/views/myDaily/index.vue'),
  },
  // 服药日志TakeMedicineLog 
  {
    path: '/takemedicineLog',
    name: 'TakeMedicineLog',
    component: () => import('@/views/myDaily/TakeMedicineLog.vue'),
  },
  {
    path: '/newTakemedicineLog',
    name: 'NewTakeMedicineLog',
    component: () => import('@/views/myDaily/NewTakeMedicineLog/index.vue'),
  },
  {
    path: '/takemedicineLogQuestionnaire',
    name: 'TakemedicineLogQuestionnaire',
    component: () => import('@/views/myDaily/NewTakeMedicineLog/TakemedicineLogQuestionnaire.vue'),
  },
  // 选择服药
  {
    path: '/takeMedicineLogList',
    name: 'TakeMedicineLogList',
    component: () => import('@/views/myDaily/TakeMedicineLogList.vue'),
  },
  // 编辑 服药日志
  {
    path: '/edittakemedicineLog',
    name: 'EditTakeMedicineLog',
    component: () => import('@/views/myDaily/EditTakeMedicineLog.vue'),
  },
  // 不适记录
  {
    path: '/discomfort',
    name: 'Discomfort',
    component: () => import('@/views/myDaily/Discomfort.vue'),
  },
  // 添加编辑-不适记录|合并用药
  {
    path: '/editdiscomfort',
    name: 'EditDiscomfort',
    component: () => import('@/views/myDaily/EditDiscomfort.vue'),
  },
  // 合并用药
  {
    path: '/drugcombination',
    name: 'DrugCombination',
    component: () => import('@/views/myDaily/DrugCombination.vue'),
  },
  /* 补偿/报销 */
  {
    path: '/compensationReimbursement',
    name: 'CompensationReimbursement',
    component: () => import('@/views/Home/compensationReimbursement/index.vue'),
    redirect: '/compensationReimbursement/compensationReimbursementPage',
    children: [
      {
        path: 'compensationReimbursementPage',
        name: 'CompensationReimbursementPage',
        component: () => import('@/views/Home/compensationReimbursement/CompensationReimbursementPage.vue'),
      },
      // 详情
      {
        path: 'costDetails',
        name: 'CostDetails',
        component: () => import('@/views/Home/compensationReimbursement/CostDetails.vue'),
      },
      // {
      //   path: 'costBreakdown',
      //   name: 'CostBreakdown',
      //   component: () => import('@/views/Home/compensationReimbursement/CostBreakdown.vue'),
      // }
    ]
  },
  {
    path: '/digitalSignature',
    name: 'DigitalSignature',
    component: () => import('@/views/Home/digitalSignature/index.vue'),
    redirect: '/digitalSignature/digitalSignaturePage',
    children: [
      {
        path: 'digitalSignaturePage',
        name: 'DigitalSignaturePage',
        component: () => import('@/views/Home/digitalSignature/DigitalSignaturePage.vue'),
      },
      // 详情
      {
        path: 'digitalSignatureDetails',
        name: 'DigitalSignatureDetails',
        component: () => import('@/views/Home/digitalSignature/DigitalSignatureDetails.vue'),
      },
      // 签名
      {
        path: 'signatureConfirmation',
        name: 'SignatureConfirmation',
        component: () => import('@/views/Home/digitalSignature/SignatureConfirmation.vue'),
      },
      // {
      //   path: 'costBreakdown',
      //   name: 'CostBreakdown',
      //   component: () => import('@/views/Home/compensationReimbursement/CostBreakdown.vue'),
      // }
    ]
  },
  // {
  //   path: '/editdrugcombination',
  //   name: 'EditDrugCombination',
  //   component: () => import('@/views/myDaily/EditDrugCombination.vue'),
  // },
  // 下单寄送PlaceAnOrder
  {
    path: '/placeanorder',
    name: 'PlaceAnOrder',
    component: () => import('@/views/myDaily/PlaceAnOrder.vue'),
  },
  // 快件运单协议 waybillAgreement
  {
    path: '/waybillAgreement',
    name: 'WaybillAgreement',
    component: () => import('@/views/myDaily/WaybillAgreement.vue')
  },
  // 人脸识别
  {
    path: '/facialRecognition',
    name: 'FacialRecognition',
    component: () => import('@/views/facialRecognition/index.vue')
  },
  // 身份核验
  {
    path: '/identityVerification',
    name: 'IdentityVerification',
    component: () => import('@/views/facialRecognition/IdentityVerification.vue')
  },
  // 我的
  {
    path: '/my',
    name: 'my',
    component: () => import('@/views/my/index.vue'),
    redirect: '/my/myPage',
    children: [
      {
        path: 'myPage',
        name: 'MyPage',
        component: () => import('@/views/my/MyPage.vue'),
      },
      // 绑定家属
      {
        path: 'bindFamily',
        name: 'BindFamily',
        component: () => import('@/views/my/BindFamily.vue'),
      },
      // 领取礼品
      {
        path: 'gift',
        name: 'Gift',
        component: () => import('@/views/dateLog/Gift.vue')
      },
      // 常见问题
      {
        path: 'commonproblem',
        name: 'CommonProblem',
        component: () => import('@/views/dateLog/CommonProblem.vue')
      },
      {
        path: 'feedback',
        name: 'Feedback',
        component: () => import('@/views/my/Feedback.vue'),
      },
      // 修改手机号
      {
        path: 'editmobile',
        name: 'EditMobile',
        component: () => import('@/views/welcomeToJoinUs/EditMobile.vue'),
      },
      // 申请药物
      {
        path: 'drugapplication',
        name: 'DrugApplication',
        component: () => import('@/views/dateLog/DrugApplication.vue')
      },
      // 我的地址
      {
        path: 'myaddress',
        name: 'MyAddress',
        component: () => import('@/views/my/MyAddress.vue')
      },
      // 编辑地址
      {
        path: 'editaddress',
        name: 'EditAddress',
        component: () => import('@/views/my/EditAddress.vue')
      },
      // 我的订单MyOrder
      {
        path: 'myorder',
        name: 'MyOrder',
        component: () => import('@/views/my/MyOrder.vue')
      },
      // 修改订单
      {
        path: 'editorder',
        name: 'EditOrder',
        component: () => import('@/views/my/EditOrder.vue')
      },
      // 订单详情
      {
        path: 'orderDetails',
        name: 'OrderDetails',
        component: () => import('@/views/my/OrderDetails.vue')
      },
      // 运单详情LogisticsDetails
      {
        path: 'logisticsdetails',
        name: 'LogisticsDetails',
        component: () => import('@/views/my/LogisticsDetails.vue')
      },
      // 我的余额
      {
        path: 'myBalance',
        name: 'MyBalance',
        component: () => import('@/views/my/myBalance/index.vue')
      },
      // 余额明细
      {
        path: 'balanceDetailList',
        name: 'BalanceDetailList',
        component: () => import('@/views/my/myBalance/BalanceDetailList.vue')
      },
      // 余额详情
      {
        path: 'balanceDetail',
        name: 'BalanceDetail',
        component: () => import('@/views/my/myBalance/BalanceDetail.vue')
      },
      // 确认银行卡
      {
        path: 'confirmBankCardInformation',
        name: 'ConfirmBankCardInformation',
        component: () => import('@/views/my/myBalance/ConfirmBankCardInformation.vue')
      },
    ]
  },
  // 项目时间轴-->随访
  {
    path: '/datelog',
    name: 'DateLog',
    component: () => import('@/views/dateLog/index.vue')
  },
  // 互动沟通
  {
    path: '/interactivecommunication',
    name: 'InteractiveCommunication',
    component: () => import('@/views/Home/InteractiveCommunication.vue')
  },
  // 在线客服
  {
    path: '/onlinecustomerservice',
    name: 'OnlineCustomerService',
    component: () => import('@/views/dateLog/OnlineCustomerService.vue')
  },
  // 访视任务interview(暂时移除)
  {
    path: '/interview',
    name: 'Interview',
    component: () => import('@/views/interview/index.vue')
  },
  // 拍照上传任务 UploadTheTestSheetTask
  {
    path: '/uploadthetestsheetTask',
    name: 'UploadTheTestSheetTask',
    component: () => import('@/views/interview/UploadTheTestSheetTask.vue')
  },
  // 上传任务 UploadTheTestSheet
  // {
  //   path: '/uploadthetestsheet',
  //   name: 'UploadTheTestSheet',
  //   component: () => import('@/views/interview/UploadTheTestSheet.vue')
  // },
  // 问卷任务
  {
    path: '/questionnairestaskcale',
    name: 'QuestionnairesTask',
    component: () => import('@/views/interview/QuestionnairesTask.vue')
  },
  // 其他任务
  {
    path: '/other',
    // name: 'OtherTask',
    component: () => import('@/views/interview/other/index.vue'),
    redirect: '/other/otherTask',
    children: [
      {
        path: 'otherTask',
        name: 'OtherTask',
        component: () => import('@/views/interview/other/OtherTask.vue'),
      },
      // 任务详情
      {
        path: 'otherTaskDetails',
        name: 'OtherTaskDetails',
        component: () => import('@/views/interview/other/OtherTaskDetails.vue')
      },
      // 任务问卷
      {
        path: 'otherQuestionnaires',
        name: 'OtherQuestionnaires',
        component: () => import('@/views/interview/other/OtherQuestionnaires.vue')
      },
    ]
  },
  // 随访内容(问卷)FollowQuestionnaires
  {
    path: '/followquestionnaires',
    name: 'FollowQuestionnaires',
    component: () => import('@/views/interview/FollowQuestionnaires.vue')
  },
  // 随访内容(Url问卷)FollowUrlQuestionnaires
  {
    path: '/followurlquestionnaires',
    name: 'FollowUrlQuestionnaires',
    component: () => import('@/views/interview/FollowUrlQuestionnaires.vue')
  },
  // 暂未开放 TemporarilyNotOpened
  {
    path: '/temporarilynotopened',
    name: 'TemporarilyNotOpened',
    component: () => import('@/views/Home/TemporarilyNotOpened.vue')
  },
  // 知情同意列表
  {
    path: '/informedList',
    name: 'InformedList',
    component: () => import('@/views/InformedList/index.vue')
  },
  // 我的知情同意预览
  {
    path: '/informedPreview',
    name: 'InformedPreview',
    component: () => import('@/views/InformedList/InformedPreview.vue')
  },
  // 我的知情列表
  {
    path: '/informedDetails',
    name: 'InformedDetails',
    component: () => import('@/views/InformedList/InformedDetails.vue'),
    // props(router) {
    //   return router.query
    // }
    // props: (router) => router.query
  },
  {
    path: '/informedDetailsVideoPlayList',
    name: 'InformedDetailsVideoPlayList',
    component: () => import('@/views/InformedList/VideoPlayList.vue'),
  },
  {
    path: '/icfList',
    name: 'IcfList',
    component: () => import('@/views/InformedList/IcfList.vue'),
  },
  {
    path: '/followReminder',
    name: 'FollowCalendar',
    component: () => import('@/views/Home/FollowReminder.vue'),
  },
  {
    path: '/learningMaterials',
    name: 'LearningMaterials',
    component: () => import('@/views/learningMaterials/index.vue'),
  },
  {
    path: '/learningMaterialsInfo',
    name: 'LearningMaterialsInfo',
    component: () => import('@/views/learningMaterials/LearningMaterialsInfo.vue'),
  },
  // 维护页
  {
    path: '/maintained',
    name: 'Maintained',
    component: () => import('@/views/Home/Maintained.vue')
  },
]

export const router = createRouter({
  // 路由的history模式，共有三种模式，
  // createWebHistory、createWebHashHistory、createMemoryHistory
  history: createWebHashHistory(),// history: createWebHistory(),
  // 路由配置
  routes,
  // 是否严格匹配路由
  strict: true,
  // 路由跳转完成后，页面滚动行为
  scrollBehavior: () => ({ left: 0, top: 0 }),
})
