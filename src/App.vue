<template>
  <!-- <div style="font-size: 0.15rem;word-break: break-all;
        word-wrap: break-word;">{{  myLocationHref }}</div> -->
  <RouterView></RouterView>
  <MyPopupShow
    :myPopupShow="outLoginFlag"
    title="温馨提示"
    texts="您已连续操作两小时，为确保信息安全，请点击左上角关闭后再打开使用"
    myPopupShowClass="z-9999"
    mypopupTextsClass="text-red-400 my-2"
  />
  <!-- 会议 -->
  <iframe
    v-if="fireConferenceObj?.src"
    ref="fireConferenceRef"
    id="fireConferenceId"
    class="absolute bg-white border-none app-transition"
    :class="[
      !fireConferenceObj?.smallScreenFlag
        ? 'w-100vw h-100vh'
        : 'w-120px h-150px',
    ]"
    :style="`
      z-index: 9999;
      top: ${fireConferenceObj?.smallScreenFlag ? touchTop : 0}px;
      left: ${fireConferenceObj?.smallScreenFlag ? touchLeft : 0}px;
      border-radius: ${fireConferenceObj?.smallScreenFlag ? 10 : 0}px;
    `"
    :src="fireConferenceObj.src"
  />
  <!-- 蒙层 -->
  <div
    v-if="fireConferenceObj?.src && fireConferenceObj?.smallScreenFlag"
    id="maskLayerIframe"
    class="absolute ft-12-rem text-white w-120px h-150px bg-black overflow-hidden"
    :style="`
      z-index: 99999;
      top: ${touchTop}px;
      left: ${touchLeft}px;
      border-radius: 10px;
    `"
    @touchstart.stop="touchstart"
    @touchmove.stop="touchmove"
    @touchend.stop="touchend"
    @click="openFireConference"
  >
    <div style="height: 126px" />
    <div style="height: 24px" class="centerflex bg-theme">进入全屏</div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent, reactive, onMounted,
  toRefs, getCurrentInstance, provide, watchEffect,
  nextTick, ref, watch
} from 'vue';
import { getOpenId, patientLoginLog, getPatientStatus, getIdentityVerificationResult } from '@/api/user';
import { useStore } from "vuex";
import { getAllQueryString } from '@trialdata/common-fun-css/index'
import MyPopupShow from "@/components/MyPopupShow.vue";
import { Toast } from 'vant';
import { maintainedFun, returnPatientStatusUrl } from '@/utils/debounce';
import { useRouter } from 'vue-router';
import { GetOpenIdInterface } from '@/types/home'
import {
  getFacialIdentityResult
} from '@/api/user'

export default defineComponent({
  name: "App",
  components: {
    MyPopupShow
  },
  setup() {
    const urls = window.location.href.split('#')[1]
    const myLocation = { ...window.location }
    const proxy: any = getCurrentInstance()?.proxy
    const store = useStore();
    const router = useRouter()
    const request = getAllQueryString();
    const code = request['code'];
    const appKey = request['AppKey'] || request['appkey'];
    store.dispatch('setPatientCode', code)
    // const code = sessionStorage.getItem("code");
    let url = '/';
    // isFamilyMember true 为家属
    /*patientStatus
      0 = 未知, 1 = 未关注, 2 = 未扫码, 3 = 待更新手机号, 4 = 待完成知情同意书, 
      5 = 待提交入排问卷, 6 = 待审核, 7 = 审核失败, 8 = 审核信息不足,
      9 = 审核成功, 10 = 成功入组, 11 = 中途脱落, 
      12 = 重新入组, 13 = 完成试验随访中, 14 = 结局发生 15 结束研究
      16 = 筛选失败, 17 = 待随机, 
      18 = 已经完成知情同意书, 19 = 待录制个人声明, 20 = 待患者短息签字, 
      21 = 待审核个人声明, 22 = 需重录制个人声明, 23 = 个人声明审核成功, 
      24 = 待医生短息签字 25 = 个人声明审核失败, 26 = 待解答知情同意疑问
      27 = 待观看视频, 28 = 已观看视频, 29 = 待讲解知情同意, 
      30 = 再次知情同意, 31 = 待绑定微信信息 32 = 已绑定微信信息,
      33 = 筛选期, 34 = 待入组, 37 = 已剔除
      patientICFStatus
      0 = 未知, 1 = 知情同意创建, 2 = 知情流程结束, 3 = 个人信息已绑定, 
      101 = 待观看视频, 102 = 观看视频开始, 103 = 观看视频结束, 104 = 观看视频完成, 
      201 = 待阅读文档, 202 = 阅读文档开始, 203 = 阅读文档结束, 204 = 阅读文档完成,
      401 = 待讲解知情, 402 = 知情讲解时间, 403 = 已讲解知情, 
      801 = 待填写知情问卷, 802 = 知情问卷填写, 803 = 知情问卷提交,
      1001 = 待审核知情问卷, 1002 = 知情问卷需完善, 1003 = 知情问卷审核失败, 1004 = 知情问卷审核成功,
      2001 = 待录制声明视频, 2002 = 已录制声明视频, 4001 = 待受试者签字,
      4002 = 受试者已签字, 4003 = 待审核个人声明, 4004 = 声明需完善, 4005 = 声明审核失败, 
      4006 = 声明审核成功, 4007 = 待研究者签字, 4008 = 研究者已签字, 4009 = 退回重签, 4010 = 受试者拒签, 4011 = 研究者拒签
      (61 = 待身份认证 62 = 身份认证成功 63 = 身份认证失败) - 绑定身份证后的
      */
    /*
      avatarUrl: "" //头像 url
      expiredTime: "0001-01-01T00:00:00" //登陆的失效时间
      inGroupDay: "2021-01-09" //入组日期
      patientNum: "001-001" /病例编号
      patientStatus: 1 //入组受试者状态
      token: ""
     */
    const expiredTime = sessionStorage.getItem('expiredTime')
    const patientToken = sessionStorage.getItem('patientToken')
    const date = new Date().getTime()
    if (appKey) {
      sessionStorage.setItem("appKey", appKey);
      store.dispatch('setAppKey', appKey);
    }

    const getOpen = async () => {
      // 判断是否来源于app
      if (window.location.href.toLowerCase().includes('appkey=app') && !window.location.href.toLowerCase().includes('appkey=patient') && !window.location.href.toLowerCase().includes('appkey=apppatient')) {
        const hash = window.location.hash + '';
        // 去除哈希前的'#'符号
        const hashContent = hash.slice(1);
        // 解析哈希中的查询参数
        const hashParams = new URLSearchParams(hashContent);
        const queryCode = hashParams.get('code');
        sessionStorage.setItem("code", queryCode);
        sessionStorage.setItem("appKey", 'APP');
      }
      if (code != undefined) {
        sessionStorage.setItem("code", code);
      }
      await getOpenId(sessionStorage.getItem("code")).then(async (rest) => {
        const res = rest as GetOpenIdInterface
        state.resLoginInfo = res
        store.dispatch('setUserInformation', res);
        let tmpHomeList = store?.state?.homeList;
        tmpHomeList.hasInteractiveCommunication = res.hasInteractiveCommunication
        store.dispatch('setHomeList', tmpHomeList);
        if (res.token) {
          store.dispatch('setToken', res.token);
          sessionStorage.setItem('patientToken', res.token)
        }
        sessionStorage.setItem('expiredTime', res.expiredTime)
        switch (res.patientStatus) {
          case 0:
          case 1:
          case 2:
            url = '/sweepCodePrompt'; // 扫码
            break;
          case 3:
            url = '/welcomeToJoinUs'; // 欢迎加入
            if (res?.isFamilyMember) {
              url = '/registerInfo' // 家属登记信息页面
            } else if (res?.withoutICF && res?.patientICFStatus_Other) {
              url = returnPatientStatusUrl(res)
            }
            break;
          case 4:
            url = returnPatientStatusUrl(res)
            break;
          case 5:
            url = '/questionnaire'; // 入排问卷
            break;
          case 6:
          case 7:
          case 8:
          case 9:
            url = '/audit'; // 审核成功
            break;
          case 10:
            url = '/'; // 入组成功
            break;
          case 11:
            url = '/audit'; // 中途脱落
            break;
          case 12:
            url = '/welcomeToJoinUs'; // 重新入组
            break;
          case 13:
            url = '/projectEnd'; // 完成试验随访中
            break;
          case 14:
            url = '/projectEnd'; //结局发生
            break;
          case 15:
            url = '/projectEnd' // 结束研究
            break;
          case 16:
            url = '/audit' // 筛选失败
            break;
          case 17:
            url = '/' // 待随机audit
            break;
          case 18:
            url = '/informed' // 已完成知情同意
            break;
          case 19:
            url = '/recordPersonalStatement' // 待录制个人声明
            break;
          case 20:
            url = '/signInformed' // 待患者签字
            break;
          case 21:
          case 22:
          case 23:
          case 24:
          case 25:
            url = '/audit' // 24 = 待医生短信签字
            break;
          case 26:
            url = '/waitInformed' // 26 = 待解答知情同意疑问
            break;
          case 27:
          case 28:
            url = '/informedVideo' // 等等观看视频
            break;
          case 29:
            url = '/waitExplainInformed' // 待讲解知情同意
          case 31:
            url = '/informationRegister' // 绑定受试者
            break;
          case 32:
            url = '/' // 已绑定微信信息
            break;
          case 33:
            url = '/' // 筛选期
            break;
          case 34:
            url = '/' // 待入组
            // case 35:
            // url = '/readyForSignature' // 待发起签署
            break;
          case 37:
            url = '/audit' // 筛选失败
            break;
        }
        // APP特殊处理
        if (appKey?.toLowerCase() === 'app') {
          switch (res.patientStatus) {
            case 33:
              url = 'welcomeToJoinUs'; // app一开始已经采集用户手机&身份信息，重新筛选后应该直接跳转项目介绍页
              if (res?.isFamilyMember) {
                url = '/registerInfo' // 家属登记信息页面
              } else if (res?.withoutICF && res?.patientICFStatus_Other) {
                url = returnPatientStatusUrl(res)
              }
              break;
          }
        }
        // 埋点
        patientLoginLog()
        const str = urls?.includes('informedPreview?ICFStatementId=') ? urls : ''
        if (str) {
          // 预览pdf下载页 不做跳转了
          return
        }
        // memberStatus家属状态
        if (res?.memberStatus === 2) {
          router.replace('/sweepCodePrompt')
          return
        }
        const token = sessionStorage.getItem('patientToken')
        const notice: any = maintainedFun('')
        if (notice?.maintainedFlag) {
          return
        }
        // 判断是否身份核验 =>  身份核验单独页面
        // router传参 verificationType 1 首次访问 2数据签署
        const identityVerificationResult = await getIdentityVerificationResult({
          studyId: res?.dctStudyId,
          configType: 1,
          triggerEvent: 1,
        })
        // 如果是多版本的副流程 （消息模板）》有知情id > 取状态跳转
        const ICFStatementId = request['icfStatementId'] || request['ICFStatementID']
        const continueStatusObj = {
          ICFStatementId,
          res,
          token,
          urls,
          url,
          myLocation
        }
        if (!identityVerificationResult?.skipVerification && !window.location.href.includes('frommsglist=1')) {
          // 判断是否是app，如果是app，特殊处理
          // 获取哈希值
          const hash = myLocation.hash + '';
          // 去除哈希前的'#'符号
          const hashContent = hash.slice(1);
          // 解析哈希中的查询参数
          const hashParams = new URLSearchParams(hashContent);
          const appKey = hashParams.get('AppKey') || hashParams.get('appkey');
          if (appKey) {
            store.dispatch('setAppKey', appKey)
          }
          continueStatusObj.myLocation.hash = encodeURIComponent(continueStatusObj.myLocation.hash)
          continueStatusObj.urls = continueStatusObj.urls ? encodeURIComponent(continueStatusObj.urls) : ''
          continueStatusObj.url = continueStatusObj.url ? encodeURIComponent(continueStatusObj.url) : ''
          localStorage.setItem('continueStatusObj', JSON.stringify(continueStatusObj))
          store.dispatch('setContinueStatusObj', continueStatusObj)
          proxy.$routerGoFun('replace', '/identityVerification', { verificationType: res.isFamilyMember ? '19' : '16', ...identityVerificationResult })
          return
        }
        // 继续流转
        state.continueStatus(continueStatusObj)
      })
        .catch(() => {
          if (appKey?.toLowerCase() === 'app') {
            const timeoutMsg = {
              data: {
                action: 'reLogin',
                payload: ''
              }
            };
            // 如果是iOS
            if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.jumpToNative) {
              window.webkit.messageHandlers.jumpToNative.postMessage(timeoutMsg);
            }
            // 如果是非iOS环境(即uniapp、安卓)
            uni.postMessage(timeoutMsg);
            return;
          }
          Toast.clear()
          store.dispatch('setToken', '');
          proxy.$routerGoFun('replace', '/sweepCodePrompt')
        })
    }

    if (expiredTime && patientToken && patientToken !== 'undefined') {
      // token已过期
      if (date > new Date(expiredTime).getTime()) {
        getOpen()
      }
    } else {
      getOpen()
    }

    const state = reactive({
      // myLocationHref: myLocation.href,
      resLoginInfo: null,
      fireConferenceRef: null,
      fireConferenceObj: {
        // https://dct.test.trialdata.cn/doctorui/conference/unpackage/dist/build/h5/index.html#/?userInfo={%22token%22:%22edf11d1a-404d-47f5-a5c4-3e651968edb1%22,%22patientId%22:%2200061e82-8061-613e-0000-000000000000%22,%22icfStatementID%22:%2200061e82-8061-6168-0000-000000000001%22,%22makeReservation%22:true,%22host%22:%22dct.test.trialdata.cn%22,%22pathname%22:%22/doctorui/%22,%22code%22:%22031bbcGa1G20UH0i3fIa1Rie8N3bbcG6%22,%22studyId%22:%220005d5e9-f91a-8028-0000-000000000000%22,%22siteId%22:%220006024b-f02a-26a7-0000-000000000003%22,%22stateTabChangeFalg%22:1,%22documentTitle%22:%22Investigator%20Portal%22,%22patientName%22:%22%E6%AE%B7%E4%BF%8A%22,%22patientNo%22:%22Hope%22,%22backUrl%22:%22/patientDetails%22}
        src: '', // 退出时直接清空src 即可
        smallScreenFlag: false, // 是否小屏模式
        addEventListenerFlag: 0, // 监听次数
      },
      startclientX: 0,
      startclientY: 0,
      touchTop: 0,
      touchLeft: 0,
      endTop: 0,
      endLeft: 0,
      touchstart: (e) => {
        // 记录拖拽元素初始位置
        state.startclientX = e.changedTouches[0].clientX;
        state.startclientY = e.changedTouches[0].clientY;
      },
      touchmove: (e) => {
        e.preventDefault(); // 阻止页面滚动
        if (e.targetTouches.length === 1) { // 一根手指
          let touch = e.targetTouches[0]
          let newLeft = state.endLeft + (touch.clientX - state.startclientX);
          let newTop = state.endTop + (touch.clientY - state.startclientY);
          // 获取div的宽度和高度
          let divWidth = 100;
          let divHeight = 120;
          // 计算边界限制
          let maxLeft = window.innerWidth - divWidth;
          let maxTop = window.innerHeight - divHeight;
          // 更新left和top值，同时确保不会超出边界
          state.touchLeft = Math.min(Math.max(newLeft, 0), maxLeft);
          state.touchTop = Math.min(Math.max(newTop, 0), maxTop);
        }
      },
      touchend: () => {
        const divWidth = 120; // div的宽度
        const windowWidth = window.innerWidth;
        const halfWidth = (windowWidth / 2) - 60;
        // 判断div应该吸附到左边还是右边
        if (state.touchLeft < halfWidth) {
          // 吸附到左边
          state.touchLeft = 0;
        } else {
          // 吸附到右边
          state.touchLeft = windowWidth - divWidth;
        }
        state.endTop = state.touchTop
        state.endLeft = state.touchLeft
      },
      // 点击放大
      openFireConference: () => {
        if (state.fireConferenceObj.src && state.fireConferenceRef?.style?.width !== '100vw') {
          state.fireConferenceObj.smallScreenFlag = false
        }
      },
      // 关闭会议
      closeFireConference: () => {
        state.fireConferenceObj.addEventListenerFlag -= 1
        window.removeEventListener('message', state.handleFireConferenceMessage);
        state.fireConferenceObj.src = ''
      },
      // 接收
      handleFireConferenceMessage: (e) => {
        if (e.data === 'back') {
          state.touchTop = 0
          state.touchLeft = 0
          state.endLeft = 0
          state.closeFireConference()
        } else if (e.data === 'smallScreen') {
          state.touchLeft = window.innerWidth - 120;
          state.touchTop = 10;
          state.endLeft = window.innerWidth - 120;
          // console.log('11smallScreenFlag');
          state.fireConferenceObj.smallScreenFlag = true
        } else if (e.data && typeof e.data === 'string' && e.data.includes('certUr=')) {
          location.replace(e.data.split('certUr=')[1])
        }
      },
      outLoginFlag: false,
      showOutDiolog: () => {
        // 暂时注释，后面如果出现稽查再放开
        // // 两小时弹出
        // const myLogingOutTime = sessionStorage.getItem('patientuiLogingOutTime')
        // if (!myLogingOutTime || myLogingOutTime === 'null') {
        //   requestAnimationFrame(state.showOutDiolog);
        //   sessionStorage.setItem('patientuiLogingOutTime', Date.parse(Date()) + '')
        // } else if ((Date.parse(Date()) - Number(myLogingOutTime)) > 7200000 &&
        //   !state?.outLoginFlag) {
        //   state.outLoginFlag = true
        // } else if (!state?.outLoginFlag) {
        //   // 未达到2小时，继续检查
        //   requestAnimationFrame(state.showOutDiolog);
        // }
      },
      // 继续流转
      continueStatus: ({
        ICFStatementId,
        res,
        token,
        urls,
        url,
        myLocation
      }) => {
        // 继续流转
        if (ICFStatementId) {
          const { userInformation } = store.state
          if (ICFStatementId) {
            userInformation.icfStementId = ICFStatementId
            userInformation.icfStatementID = ICFStatementId
          }
          store.dispatch('setUserInformation', userInformation)
          // 获取状态请求
          getPatientStatus({ ICFStatementId })
            .then((res: any) => {
              proxy.$routerGoFun('replace', returnPatientStatusUrl(res) || url)
            })
          return
        }
        // 消息模板直接进野火
        if (myLocation.hash.includes('Conference=true')) {
          sessionStorage.removeItem('conferenceIMNum')
          // 获取哈希值
          const hash = myLocation.hash + '';
          // 去除哈希前的'#'符号
          const hashContent = hash.slice(1);
          // 解析哈希中的查询参数
          const hashParams = new URLSearchParams(hashContent);
          const serialNo = hashParams.get('serialNo');
          const conferenceId = hashParams.get('conferenceId');
          const { dctPatientId, icfStementId, patientNum, dctStudyId } = store.state.userInformation;
          let info = {
            token: res.token,
            patientId: dctPatientId,
            icfStatementID: icfStementId,
            makeReservation: false,
            host: location.host,
            pathname: location.pathname,
            code: sessionStorage.getItem("code"),
            documentTitle: document.title,
            patientName: '',
            patientNo: patientNum,
            studyId: dctStudyId || '',
            serialNo: serialNo || '',
            conferenceId: conferenceId || '',
          }
          // 消息模板时 - 进到会议列表 不进到详情
          // if (res.patientICFStatus === 401 && res.patientStatus === 4) {
          //   info.backUrl = '/waitExplainInformed'
          // } else {
          //   info.backUrl = url
          // }
          const userInfo = JSON.stringify(info)
          let path = `https://${location.host}/patientui/conference/unpackage/dist/build/h5/index.html#/?userInfo=${userInfo}`
          // path = `http://localhost:5173/?userInfo=${userInfo}`
          state.fireConferenceObj.src = path
          // location.replace(path)
          // return
        }
        // 消息模板跳转互动沟通 状态在知情 有3个页面会有
        if (urls.includes('interactivecommunication')) {
          // 使用异步回调函数获取最新状态
          if (store.state?.patientToken) {
            const routerInn = [...store.state.routerInn]
            routerInn.push({
              backQuery: null,
              backPath: url,
              path: urls,
              query: null
            })
            store.dispatch('setRouterInn', routerInn)
            router.replace(urls)
            return
          }
        }
        if (urls !== '/informedList' &&
          (res?.patientStatus === 16 ||
            res?.patientStatus === 37 ||
            res?.patientStatus === 15 ||
            res?.patientStatus === 14 ||
            res?.patientStatus === 13 ||
            res?.patientStatus === 11 ||
            res?.patientStatus === 7)) {
          // console.log('myLocation.hash',myLocation.hash,res?.patientStatus);
          if ((res?.patientStatus === 15 ||
            res?.patientStatus === 14 ||
            res?.patientStatus === 13 ||
            res?.patientStatus === 11 ||
            res?.patientStatus === 7) && myLocation.hash.includes('unplannedWindow=1')) {
            // 这里不用阻止计划外的问卷
            const routerInn = [...store.state.routerInn]
            routerInn.push({
              backQuery: null,
              backPath: url,
              path: urls,
              query: null
            })
            store.dispatch('setRouterInn', routerInn)
            router.replace(urls)
            return
          } else {
            proxy.$routerGoFun('replace', url)
            return
          }
        }
        // 主流程
        if (urls === '/informedList' && res.token && token && token !== 'null' && token !== 'undefined') {
          proxy.$routerGoFun('replace', urls)
        } else if (store.state?.patientToken) {
          proxy.$routerGoFun('replace', url)
        }
        // 筛选期 成功入组 待入组
        if ((res.patientStatus === 10 || res.patientStatus == 33 || res.patientStatus == 34) && urls && url !== '/informedList') {
          if ((urls !== '/' && urls !== '/home') && urls !== '/my' && urls !== '/datelog') {
            setTimeout(() => {
              // 使用异步回调函数获取最新状态
              if (store.state?.patientToken) {
                // proxy.$routerGoFun('routerInnPush', urls, '', '/')
                const routerInn = [...store.state.routerInn]
                routerInn.push({
                  backQuery: null,
                  backPath: '/',
                  path: urls,
                  query: null
                })
                store.dispatch('setRouterInn', routerInn)
                router.replace(urls)
              }
            }, 500)
          }
        }
      }
    })

    watchEffect(() => {
      if (state.fireConferenceObj.src && state.fireConferenceObj.addEventListenerFlag === 0) {
        nextTick(() => {
          state.fireConferenceObj.addEventListenerFlag += 1
          // state.fireConferenceRef.contentWindow.addEventListener('message', state.handleFireConferenceMessage);
          window.addEventListener('message', state.handleFireConferenceMessage);
        })
      }
    })

    provide('fireConferenceObj', state.fireConferenceObj)

    onMounted(() => {
      sessionStorage.removeItem('conferenceIMNum');
      // 获取哈希值
      const hash = myLocation.hash + '';
      // 去除哈希前的'#'符号
      const hashContent = hash.slice(1);
      // 解析哈希中的查询参数
      const hashParams = new URLSearchParams(hashContent);
      const serialNo = hashParams.get('serialNo');
      const bgStudyId = hashParams.get('bgStudyId');
      const appKey = hashParams.get('appkey') || hashParams.get('AppKey');
      const conferenceId = hashParams.get('conferenceId');
      if (myLocation.hash.includes('FacialRecognition=true')) {
        if (appKey?.toLowerCase() === 'app' && conferenceId && conferenceId != '') {
          getOpenId(sessionStorage.getItem("code")).then(async (rest) => {
            const res = rest as GetOpenIdInterface
            state.resLoginInfo = res
            store.dispatch('setUserInformation', res);
            let tmpHomeList = store?.state?.homeList;
            tmpHomeList.hasInteractiveCommunication = res.hasInteractiveCommunication
            store.dispatch('setHomeList', tmpHomeList);
            if (res.token) {
              store.dispatch('setToken', res.token);
              sessionStorage.setItem('patientToken', res.token)
            }
            sessionStorage.setItem('expiredTime', res.expiredTime)
            getFacialIdentityResult({
              studyId: bgStudyId,
              businessId: serialNo
            }).then((res: any) => {
              const msgData = {
                data: {
                  action: 'facialResult',
                  payload: {
                    isSuccess: res.isSuccess,
                    isComplete: res.isComplete,
                    isExpired: res.isExpired
                  }
                }
              }
              // 如果是iOS
              if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.jumpToNative) {
                window.webkit.messageHandlers.jumpToNative.postMessage(msgData);
              }
              // 如果是非iOS环境(即uniapp、安卓)
              uni.postMessage(msgData);
              return;
            })
          })
        }
      }

      // 禁用复制选中
      document.body.style.webkitUserSelect = "none";
      document.body.style.userSelect = "none";
      document.body.style.overscrollBehavior = 'none';
      document.body.addEventListener('scroll', function (event) {
        event._isScroller = true;
      }, { passive: true }
      );
      document.body.style.overflow = 'hidden';
      state.showOutDiolog()
    })

    return {
      ...toRefs(state),
    };
  },
});
</script>

<style lang="less" scoped>
:deep(.van-popup--bottom) {
  min-height: 3.08rem;
}
:deep(.van-button--primary) {
  background: var(--theme-bg-linear-gradient) !important;
  border: none;
}
.van-dialog {
  border-radius: 0.08rem !important;
}
:deep(.van-dialog) {
  border-radius: 0.08rem !important;
}
</style>