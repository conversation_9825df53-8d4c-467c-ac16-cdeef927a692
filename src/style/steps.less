// 更改删除按钮背景色
.van-button--danger {
    border: none;
    border-radius: 0.05rem;
    background: #e26375;
}

// 更改时间轴---
.van-steps {
    display: flex;
    margin: 0 0 0.2rem 0;
    :deep(.van-step__circle-container) {
        top: 0.4rem;
    }
    :deep(.van-step__line) {
        top: 0.4rem;
    }
    :deep(.van-step__title,.van-swipe-cell,
    .van-step__title--active) {
        width: 3.2rem;
        min-height: 0.63rem;
        box-sizing: border-box;
        border-top-left-radius: 0.07rem;
        border-top-right-radius: 0.07rem;
        background: #fff;
        box-shadow: 0 0.02rem 0.12rem rgba(0, 0, 0, 0.07); // 阴影
        overflow: hidden;
        //
        .status-module {
            display: flex;
            justify-content: space-between;
            padding: 0.1rem;

            .activeName {
                max-width: 2.5rem;
                margin: 0;
                word-break: break-all;
                word-wrap: break-word;
            }
        }
        h4 {
            color: var(--theme-color);
            /*强制换行*/
            word-break: break-all;
            word-wrap: break-word;
        }
        p {
            color: #767676;
            padding: 0 0.1rem 0.1rem;
            margin: 0;
        }
        .taskList-fieldValue-first p {
            padding-left: 0;
        }

        .active-status1 {
            color: #c3c3c3;
        }

        .active-status2 {
            color: #4889ef;
        }

        .active-status3 {
            color: #e26375;
        }

        .active-status4 {
            color: #41b592;
        }
    }
    // 控制删除按钮高度
    :deep(.van-swipe-cell__wrapper) { 
        min-height: 0.63rem;
    }

    //
    .taskList-module {
        display: flex;
        margin: 0.1rem;

        .taskList-fieldValue {
            display: flex;
        }

        img {
            width: 0.15rem;
            height: 0.15rem;
            margin: 0 0.1rem 0 0;
        }
    }

    // 去除边框
    :deep(.van-step--vertical) {
        border: none;
    }
    :deep(.van-step__circle) {
        width: 0.23rem;
        height: 0.23rem;
        transform: scale(0.5);
        background: url(@/assets/baby/dianIcon.svg);
    }
    :deep(.van-step--vertical:not(:last-child)::after) {
        border: none;
    }
    // 展开高亮
    :deep(.van-step__title,.van-swipe-cell__wrapper) {
        min-height: 0.63rem;
    }

    .active-open-back {
        // 到院-电话随访？
        .follow-reminder{
            margin: 0.1rem;
            color: var(--theme-color);
            img{
              width: 0.1645rem; 
              margin: 0 0.1rem 0 0; 
            }
        }
        .takenTime {
            margin-top: 0.1rem;
        }
        // 删除按钮背景色
        :deep(.van-swipe-cell__right) {
            // border-radius: 0.05rem 0.05rem 0 0.05rem;
            background: url(@/assets/baby/followupBack.svg) no-repeat 10% 50%;

            .van-button--danger {
                border-radius: 0.05rem 0.05rem 0 0.05rem;
            }
        }
        :deep(.van-step__title,.van-swipe-cell__wrapper) {
            min-height: 0.63rem;

            .active-back {
                background: url(@/assets/baby/followupBack.svg) no-repeat 10% 50%;
                min-height: 0.63rem;
                .status-module {
                    h4 {
                        color: #fff;
                    }

                    span {
                        color: #fff !important;
                    }
                }

                p {
                    color: #fff;
                }
            }
        }
    }
}