.questCrf-list{
    width: calc(100% - 0.4rem);
    min-height: 0.9rem;
    padding: 0.1rem;
    box-sizing: border-box;
    margin: 0.2rem 0 0.2rem 0.2rem;
    border-radius: 0.1rem;
    background: #fff;
    box-shadow: 0 0.02rem 0.12rem rgba(0, 0, 0, 0.07);
    .list-name{
        width: 100%;
        margin: 0 0 0.2rem 0;
        display: flex;
        justify-content: space-between;
        font-size: 0.15rem;
        .list-name-text{
            max-width: 88%;
        }
        img{
            height: 0.15rem;
        }
    }
    .questCrf-item{
        color: #333;
        p{
            white-space: pre-line;
            display: flex;
            justify-content: space-between;
            .none-fieldValueStr{
                color: #C3C3C3;
            }
            .unit{
                color: #9A9A9A;
            }
        }
    }
}
.questCrf-list-edit{
    margin: 0.2rem 0 0.1rem 0.2rem;
}
.add-delete-module{
    padding: 0 0.2rem;
    display: flex;
    justify-content: flex-end;
    margin: 0 0 0.1rem 0;
    img{
        width: 0.2253rem; 
        margin: 0 0 0 0.2rem;
    }
}
.br{
    width: 100%;
    height: 0.5px;
    margin: 0.02rem 0 0.1rem 0;
    background: #e2e2e2;
}
.questCrf-item-title {
    margin: 0 0 0.2rem 0;
}
:deep(.van-checkbox-group,
.van-checkbox-group--horizontal) {
    .van-checkbox,
    .van-checkbox--disabled {
        margin: 0 0 0.1rem 0;
    }
}