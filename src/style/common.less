:root {
  --theme-color: #5860da;
  --theme-bg-color: #5995FF;
  --theme-bg-linear-gradient: linear-gradient(180deg,
  #3FA1FC,
  #5860DA 100%);
}

.van-nav-bar__title,
.van-ellipsis {
  font-size: 0.15rem;
  font-weight: 700;
}

.van-icon {
  color: #000 !important;
}
/*toast的成功√icon*/
.van-badge__wrapper.van-icon.van-icon-success.van-toast__icon{
  color: #fff !important;
}

/*预览图片关闭按钮*/
.van-icon.van-icon-clear {
  color: #c8c9cc !important;
}

.van-dialog {
  min-height: 0;
}

// [aria-checked="true"] 选中的
div[aria-checked="true"] {
  background: #E9EFFC !important;
  border: var(--theme-color) solid 0.5px;
  box-sizing: border-box;

  .van-radio__label {
    color: var(--theme-color);
  }
}

.video-js {
  background: rgba(0, 0, 0, 0);
}

// 按钮
.btn-module {
  width: 100%;
  height: 90px;
  box-shadow: 0 0.02rem 0.12rem rgba(0, 0, 0, 0.07);
  background: #fff;

  .btn {
    width: 3.2rem;
  }
}

.isRequiredActive {
  display: inline-block;
  color: red;
  font-size: 0.25rem;
  line-height: 0.27rem;
}

.pd-w10 {
  padding: 0 0.1rem;
  box-sizing: border-box;
}

.none-warp-text-auto {
  // 单行文本溢出省略
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/*下一步按钮*/
.next-btn {
  background: #E9EFFC;
  color: var(--theme-color);
  border: var(--theme-color) solid 0.5px;
}

// 改写问卷 单选
.van-radio,
.van-checkbox {
  width: 100%;
  margin: 0 0 0.1rem 0;
  background: #ffffff;
  border-radius: 0.1rem;
  display: flex;
  justify-content: center;
  min-height: 0.42rem;
  box-shadow: 0 0.02rem 0.12rem rgba(0, 0, 0, 0.07);

  .van-radio__icon,
  .van-radio__icon--round {
    display: none;
  }

  .van-checkbox__icon {
    display: none;
  }
}

.nowrap {
  white-space: nowrap;
}

.clause {
  font-size: 0.13rem;
  color: #333;
  img {
    width: 0.25rem;
    height: 0.25rem;
  }

  input {
    width: 0.2rem;
    scale: 1.5;
  }

  .links {
    color: var(--theme-color);
    text-decoration: underline;
  }
}

.nocases {
  padding: 1rem 0 0 0;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  color: #9A9A9A;

  img {
    width: 1.36rem;
  }

  p {
    width: 100%;
    margin: 0.3rem 0 0 0;
    text-align: center;
  }
}

.nocases-pd2 {
  padding-top: 2rem;
}

.item-nos {
  margin: 0.1rem 0 0 0;
  color: #C3C3C3;
  text-align: center;
  font-size: 0.13rem;
}

.takeMedicineLogList-nav {
  padding: 0 0.1rem;
  height: 0.42rem;
  font-size: 0.12rem;
  display: flex;
  align-items: center;
  color: var(--theme-color);
  background: #C7C9EE;

  img {
    width: 0.1777rem;
    margin: 0 0.1rem 0 0;
  }
}

/*收件人信息*/
.placeanorder-module {
  width: 100%;
  padding: 0.1rem 0.1rem;
  margin: 0 0 0.1rem 0;
  box-sizing: border-box;
  background: #fff;
  border-radius: 0.1rem;

  .van-cell__title.van-field__label {
    white-space: nowrap;
  }

  .van-divider {
    margin: 0 0.2rem 0 0.16rem;
    width: 90%;
  }

  img {
    width: 0.35rem;
    height: 0.35rem;
    margin: 0 0.15rem 0 0;
  }

  .placeanorder-title {
    display: flex;
    align-items: center;
    margin: 0 0 0.1rem 0;
  }

  .van-cell-group,
  van-cell-group--inset {
    margin: 0;
  }

  h4 {
    min-width: 0.7rem;
  }

  p {
    margin: 0 0 0 0.1rem;
    word-break: break-all;
    word-wrap: break-word;
  }

}
/*通用底部按钮*/
.commmon-bottom-add-btn {
  height: 0.55rem;
  font-size: 0.18rem;
  color: #fff;
  background: var(--theme-bg-color);
}
/*通用卡片模块*/
.commmon-card-module {
  padding: 0.16rem;
  box-sizing: border-box;
  border-radius: 0.08rem;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 0.02rem 0.26rem rgba(232, 232, 242, 1);
}
/*通用流程轴*/
.common-date-navs-module {
  /*轴左侧线条*/
  .date-navs-items-left {
    width: 10%;
    position: relative;
    .date-bule-icon {
      position: relative;
      top: 0px;
      width: 0.21rem;
      height: 0.21rem;
      left: 50%;
      transform: translate(-50%, 0%);
      border-radius: 50%;
      z-index: 1;
    }
    .date-after {
      position: absolute;
      left: 50%;
      top: 0px;
      width: 2px;
      transform: translate(-50%, 0%);
      height: calc(100% + 0.2rem);
      background: #dee2e6;
    }
    img {
      top: 0.08rem;
      right: 0.05rem;
    }
  }
  /*轴右侧内容*/
  .date-navs-items-infos {
    width: 90%;
    padding: 0 0.1rem 0 0;
    box-sizing: border-box;
    font-size: 0.14rem;
    .approver-img {
      width: 0.34rem;
      height: 0.34rem;
      border-radius: 0.07rem;
      background:#B5D0FF;
    }
    /*三角*/
    .triangle-left {
      transform: translateY(0.05rem);
      width: 0;
      height: 0;
      border-top: 0.02rem solid transparent;
      border-bottom: 0.04rem solid transparent;
      border-right: 0.08rem solid #F5F5F5;
    }
  }
}

.facialRecognition {
  height: 100vh;
  .facialRecognition-body {
    height: calc(100vh - 46px - 0.6rem);
  }
  .facial-img {
    width: 1.5rem;
    height: 1.5rem;
  }
  .facial-btn {
    background: #FD3F1D;
    color: #fff;
  }
  .recognition-box {
    height: calc(100vh - 47px - 1rem);
    overflow: auto;
  }
  .recognition-img {
    width: 1.5rem;
    height: 1.5rem;
    margin: 0.9rem 0 0.4rem 0;
  }
  .recognition-line {
    line-height: 0.2rem;
  }
  .recognition-footer {
    color: #999999;
    padding-bottom: 0.4rem;
    padding-top: 0.2rem;
  }
}