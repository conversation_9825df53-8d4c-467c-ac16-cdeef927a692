@tailwind utilities;
@layer utilities {
  /* 定义过渡样式 */
  .app-transition {
    transition: width 0.3s ease-in-out, height 0.3s ease-in-out;
  }
  /* 强制换行 */
  .break-word {
    word-wrap: break-word;
  }
  /*页面加载loading 》 dct主题色*/ 
  #nprogress .bar {
    background: var(--theme-color);
  }
  .absolute-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .r-0 {
    right: 0;
  }
  .r-024rem {
    right: 0.24rem;
  }
  .t-0 {
    top: 0;
  }
  .t-012rem {
    top: 0.12rem;
  }
  .b-02-rem {
    bottom: 0.2rem;
  }
  .mb-0 {
    margin-bottom: 0px !important;
  }
  .mb-006-rem {
    margin-bottom: 0.06rem;
  }
  .mb-004rem {
    margin-bottom: 0.04rem;
  }
  .mb-01rem {
    margin-bottom: 0.1rem;
  }
  .mb-02rem {
    margin-bottom: 0.2rem;
  }
  .mb-015rem {
    margin-bottom: 0.15rem;
  }
  .mb-006rem {
    margin-bottom: 0.06rem;
  }
  .mb-02rem {
    margin-bottom: 0.2rem;
  }
  .mb-025rem {
    margin-bottom: 0.25rem;
  }
  .mb-03rem {
    margin-bottom: 0.3rem;
  }
  .ml-01rem {
    margin-left: 0.1rem;
  }
  .mr-006rem {
    margin-right: 0.06rem;
  }
  .mr-01rem {
    margin-right: 0.1rem;
  }
  .ml-015rem {
    margin-left: 0.15rem;
  }
  .ml-013rem {
    margin-left: 0.13rem;
  }
  .mx-015rem {
    margin-left: 0.15rem;
    margin-right: 0.15rem;
  }
  .mx-023rem {
    margin-left: 0.23rem;
    margin-right: 0.23rem;
  }
  .mb-10-px {
    margin-bottom: 10px;
  }
  .ml-16-px {
    margin-left: 16px;
  }
  .my-10-px {
    margin-top: 10px;
    margin-bottom: 10px;
  }
  .mt-5-px {
    margin-top: 5px;
  }
  .mt-015rem {
    margin-top: 0.15rem;
  }
  .mt-030rem {
    margin-top: 0.3rem;
  }
  .leading-02rem {
    line-height: 0.2rem;
  }
  b,strong {
    font-weight: bold;
  }
  em {
    font-style: italic;
  }
  .wrap1 {
    display: -webkit-box !important;
    word-break: break-all;
    word-wrap: break-word;
    overflow: hidden;
    /*…省略形式*/
    text-overflow: ellipsis;
    /*从上向下垂直排列子元素*/
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
  .wrap2 {
    display: -webkit-box !important;
    word-break: break-all;
    word-wrap: break-word;
    overflow: hidden;
    /*…省略形式*/
    text-overflow: ellipsis;
    /*从上向下垂直排列子元素*/
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  .wrap5 {
    display: -webkit-box !important;
    word-break: break-all;
    word-wrap: break-word;
    overflow: hidden;
    /*…省略形式*/
    text-overflow: ellipsis;
    /*从上向下垂直排列子元素*/
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 5;
  }
  /*常用按钮宽高*/
  .common-btn {
    width: 80%;
    height: 0.39rem;
  }
  .z-9999 {
    z-index: 9999;
  }
  .z-2 {
    z-index: 2;
  }
  .z--9 {
    z-index: -9;
  }
  .theme-color {
    color: #5860DA;
  }
  .theme-bg-btn-color {
    background: linear-gradient(180deg, #3FA1FC 0%, #5860DA 100%) !important;
  }
  .theme-bg-linear-gradient {
    background: var(--theme-bg-linear-gradient);
  }
  .mt-01-rem {
    margin-top: 0.1rem;
  }
  .mt-005rem {
    margin-top: 0.05rem;
  }
  .mt-02-rem {
    margin-top: 0.2rem;
  }
  .mt-014rem {
    margin-top: 0.14rem;
  }
  .mt-06-rem {
    margin-top: 0.6rem;
  }
  .mt-07-rem {
    margin-top: 0.7rem;
  }
  .mt-08-rem {
    margin-top: 0.8rem;
  }
  .mt-095-rem {
    margin-top: 0.95rem;
  }
  .mt-040-rem {
    margin-top: 0.4rem;
  }
  .mb-020-rem {
    margin-bottom: 0.2rem;
  }
  .mb-04-rem {
    margin-bottom: 0.4rem;
  }
  .pb-0-important {
    padding-bottom: 0 !important;
  }
  .pb-03rem {
    padding-bottom: 0.3rem;
  }
  .pb-004-rem {
    padding-bottom: 0.04rem;
  }
  .pb-006-rem {
    padding-bottom: 0.06rem;
  }
  .pr-01-rem {
    padding-right: 0.1rem;
  }
  .pr-02rem {
    padding-right: 0.2rem;
  }
  .p-004rem {
    padding: 0.04rem
  }
  .p-005rem {
    padding: 0.05rem
  }
  .p-014rem {
    padding: 0.14rem;
  }
  .p-01rem {
    padding: 0.1rem
  }
  .p-02rem {
    padding: 0.2rem
  }
  .p-014rem{
    padding: 0.14rem
  }
  .h-05px {
    height: 0.5px;
  }
  .h-048rem {
    height: 0.48rem;
  }
  .h-055rem {
    height: 0.55rem;
  }
  .h-065rem {
    height: 0.65rem;
  }
  .h-078rem {
    height: 0.78rem;
  }
  .h-100-vh {
    height: 100vh;
  }
  .h-100vh-80px {
    height: calc(100vh - 80px);
  }
  .h-83-vh {
    height: 83vh;
  }
  .h-49-vh {
    height: 49vh;
  }
  .h-100vh-46px {
    height: calc(100vh - 46px);
  }
  .h-100vh-50px {
    height: calc(100vh - 50px);
  }
  .h-100vh-46px-051rem {
    height: calc(100vh - 46px - 0.51rem);
  }
  .h-065rem{
    height: 0.65rem;
  }
  .ft-30-rem {
    font-size: 0.3rem;
  }
  .ft-24-rem {
    font-size: 0.24rem;
  }
  .ft-20-rem {
    font-size: 0.2rem;
  }
  .ft-22-rem {
    font-size: 0.22rem;
  }
  .ft-014-rem {
    font-size: 0.14rem;
  }
  .ft-015rem {
    font-size: 0.15rem;
  }
  .ft-18-rem {
    font-size: 0.18rem;
  }
  .ft-17-rem {
    font-size: 0.17rem;
  }
  .ft-18-rem {
    font-size: 0.18rem;
  }
  .ft-16-rem {
    font-size: 0.16rem;
  }
  .ft-15-rem {
    font-size: 0.15rem;
  }
  .ft-14-rem {
    font-size: 0.14rem;
  }
  .ft-13-rem {
    font-size: 0.13rem;
  }
  .ft-12-rem {
    font-size: 0.12rem;
  }
  .ft-11-rem {
    font-size: 0.11rem;
  }
  .ft-10-rem {
    font-size: 0.1rem;
  }
  .ft-09-rem {
    font-size: 0.09rem;
  }
  .py-01rem {
    padding-top: 0.1rem;
    padding-bottom: 0.1rem;
  }
  .py-012rem {
    padding-top: 0.12rem;
    padding-bottom: 0.12rem;
  }
  .py-015rem {
    padding-top: 0.15rem;
    padding-bottom: 0.15rem;
  }
  .pb-015rem {
    padding-bottom: 0.15rem;
  }
  .px-015rem {
    padding-left: 0.15rem;
    padding-right: 0.15rem;
  }
  .px-016rem {
    padding-left: 0.16rem;
    padding-right: 0.16rem;
  }
  .pt-01rem {
    padding-top: 0.1rem;
  }
  .pt-025rem {
    padding-top: 0.25rem;
  }
  .pt-02rem {
    padding-top: 0.2rem;
  }
  .pt-015rem {
    padding-top: 0.15rem;
  }
  .pt-018rem {
    padding-top: 0.18rem;
  }
  .pb-025rem {
    padding-bottom: 0.25rem;
  }
  .pb-01rem {
    padding-bottom: 0.1rem;
  }
  .pb-013rem {
    padding-bottom: 0.13rem;
  }
  .pb-005rem {
    padding-bottom: 0.05rem;
  }
  .pb-02rem {
    padding-bottom: 0.2rem;
  }
  .my-hr {
    height: 1px;
    background: #F5F5F5;
  }
  .m-02rem {
    margin: 0.2rem;
  }
  .my-01rem {
    margin-top: 0.1rem;
    margin-bottom: 0.1rem;
  }
  .my-02rem {
    margin-top: 0.2rem;
    margin-bottom: 0.2rem;
  }
  .font-color-fff {
    color: #fff;
  }
  .font-color-ADADAD {
    color: #ADADAD
  }
  .font-color-333{
    color: #333;
  }
  .font-color-666{
    color: #666;
  }
  .van-icon.font-color-666-important{
    color: #666 !important;
  }
  .font-color-E02020 {
    color: #E02020;
  }
  .font-color-72AE32 {
    color: #72AE32;
  }
  .font-color-5995FF {
    color: #5995FF;
  }
  .font-color-7871FB {
    color: #7871FB;
  }
  .font-color-29A035 {
    color: #29A035;
  }
  .font-color-E60000 {
    color: #E60000;
  }
  .van-icon.font-color-5995FF {
    color: #5995FF !important;
  }
  .font-color-999 {
    color: #999;
  }
  .font-color-5860d9 {
    color: #5860d9;
  }
  .van-icon.font-color-5860d9 {
    color: #5860d9 !important;
  }
  .font-color-F94040 {
    color: #F94040;
  }
  .font-color-5490EC {
    color: #5490EC;
  }
  .font-color-0DBE88 {
    color: #0DBE88;
  }
  .bg-size-100 {
    background-size: 100% 100%;
  }
  .bg-4D4C4C {
    background: #4D4C4C;
  }
  .text-4D4C4C {
    color: #4D4C4C;
  }
  .bg-theme {
    background: var(--theme-color);
  }
  .bg-F7F7F7 {
    background: #F7F7F7;
  }
  .bg-F8F8F8 {
    background: #F8F8F8;
  }
  .bg-color-fff {
    background: #fff;
  }
  .bg-color-5490EC {
    background: #5490EC;
  }
  .bg-color-CBCDF3 {
    background: #CBCDF3;
  }
  .bg-color-5995FF {
    background: #5995FF;
  }
  .bg-color-dfdfdf {
    background: #dfdfdf;
  }
  .bg-color-F5F5F5 {
    background: #F5F5F5;
  }
  .bg-color-FFDC9F {
    background: #FFDC9F;
  }
  .bg-color-E0DFFF {
    background: #E0DFFF;
  }
  .bg-color-f7f7f7 {
    background: #f7f7f7;
  }
  .bg-color-F42F3E {
    background: #F42F3E;
  }
  .px-004rem {
    padding-left: 0.04rem;
    padding-right: 0.04rem;
  }
  .px-01rem {
    padding-left: 0.1rem;
    padding-right: 0.1rem;
  }
  .px-015rem {
    padding-left: 0.15rem;
    padding-right: 0.15rem;
  }
  .px-02rem {
    padding-left: 0.2rem;
    padding-right: 0.2rem;
  }
  .px-025rem {
    padding-left: 0.25rem;
    padding-right: 0.25rem;
  }
  .px-03rem {
    padding-left: 0.3rem;
    padding-right: 0.3rem;
  }
  .py-002rem {
    padding-top: 0.02rem;
    padding-bottom: 0.02rem;
  }
  .pl-014rem {
    padding-left: 0.14rem;
  }
  .pl-01rem {
    padding-left: 0.1rem;
  }
  .pl-016rem {
    padding-left: 0.16rem;
  }
  .pr-018rem {
    padding-right: 0.18rem;
  }
  .pr-016rem {
    padding-right: 0.16rem;
  }
  .py-008rem {
    padding-top: 0.08rem;
    padding-bottom: 0.08rem;
  }
  /* tailwind自带w-screen
    width: 100vw;
  */
  .w-100vw {
    width: 100vw;
  }
  .w-120px {
    width: 120px;
  }
  .w-045rem {
    width: 0.45rem;
  }
  .h-150px {
    height: 150px;
  }
  .w-042rem {
    width: 0.42rem;
  }
  .w-044rem {
    width: 0.44rem;
  }
  .w-04rem {
    width: 0.4rem;
  }
  .h-042rem {
    height: 0.42rem;
  }
  .h-100vh {
    height: 100vh;
  }
  .w-3-rem {
    width: 3rem;
  }
  .max-w-06rem {
    max-width: 0.6rem;
  }
  .max-w-08rem {
    max-width: 0.8rem;
  }
  .w-80-percent {
    width: 80%;
  }
  .w-90-percent {
    width: 90%;
  }
  .w-067rem {
    width: 0.67rem;
  }
  .w-148rem {
    width: 1.48rem;
  }
  .w-015rem {
    width: 0.15rem;
  }
  .w-0165rem {
    width: 0.165rem;
  }
  .w-02rem {
    width: 0.2rem;
  }
  .w-0215rem {
    width: 0.215rem;
  }
  .w-0175rem {
    width: 0.175rem;
  }
  .w-full028rem {
    width: calc(100% + 0.28rem);
  }
  .w-018rem {
    width: 0.18rem;
  }
  .w-008rem {
    width: 0.08rem;
  }
  .w-048rem {
    width: 0.48rem;
  }
  .w-096rem {
    width: 0.96rem;
  }
  .w-01rem {
    width: 0.1rem;
  }
  .h-01rem {
    height: 0.1rem;
  }
  .h-018rem {
    height: 0.18rem;
  }
  .h-019rem {
    height: 0.19rem;
  }
  .h-02rem {
    height: 0.2rem;
  }
  .h-035rem {
    height: 0.35rem;
  }
  .h-037rem {
    height: 0.37rem;
  }
  .h-04rem {
    height: 0.4rem;
  }
  .h-045rem {
    height: 0.45rem;
  }
  .h-051rem {
    height: 0.51rem;
  }
  .h-090rem {
    height: 0.90rem;
  }
  .h-05rem {
    height: 0.5rem;
  }
  .h-1-8rem {
    height: 1.8rem;
  }
  .max-w-3-1rem {
    max-width: 3.1rem;
  }
  .max-w-40-percent {
    max-width: 40%;
  }
  .max-h-055rem {
    max-height: 0.55rem;
  }
  .min-h-053rem {
    min-height: 0.53rem;
  }
  .min-w-240rem {
    min-width: 2.4rem;
  }
  /* .translate-y002rem {
    transform: translateY(0.02rem);
  } */
  .translate-y-005rem {
    transform: translateY(-0.05rem);
  }
  .translate-x014rem {
    transform: translateX(-0.14rem);
  }
  /*小阴影*/
  .withdraw-deposit-shadow {
    box-shadow: 0 0.02rem 0.13rem rgba(197, 197, 197, 0.50);
  }
  /*常用的卡片 阴影*/
  .common-shadow {
    box-shadow: 0 0.02rem 0.12rem rgba(0, 0, 0, 0.07);
  }
  /*常用的卡片 圆角*/
  .common-border-radius {
    border-radius: 0.06rem 0.06rem 0.1rem 0.1rem;
  }
  /*左圆角*/
  .withdraw-deposit-left-radius {
    border-radius: 0.8rem 0 0 0.8rem;
  }
  .radius-0025rem {
    border-radius: 0.025rem;
  }
  .radius-004rem {
    border-radius: 0.04rem;
  }
  .radius-005rem {
    border-radius: 0.05rem;
  }
  .radius-08rem {
    border-radius: 0.8rem;
  }
  .radius-50 {
    border-radius: 50%;
  }
  .font-color-555 {
    color: #555;
  }
  .font-color-E26375 {
    color: #E26375;
  }
  .font-color-C3C3C3 {
    color: #C3C3C3;
  }
  .font-color-999 {
    color: #999999;
  }
  .font-color-969799 {
    color: #969799;
  }
  .font-color-9A9A9A {
    color: #9A9A9A;
  }
  .font-color-429AF8 {
    color: #429AF8;
  }
  .font-color-F4A22E {
    color: #F4A22E;
  }
  /**/
  .border-E92626{
    border: 0.5px solid #E92626;
  }
  .border-bottom-F5F5F5 {
    border-bottom: 1px solid #F5F5F5;
  }
  .border-A6AAEB {
    border: 1px solid #A6AAEB;
  }
  .border-C3C3C3 {
    border: 1px solid #C3C3C3;
  }
  .tw-bg {
    background: linear-gradient(180deg,
    #FF798B,
    #DB4F62 100%);
  }
}
