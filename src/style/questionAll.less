// 上一题- 进度
.questionnaire-schedule {
    padding: 0.2rem 0.1rem 0.1rem;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.12rem;
    color: var(--theme-color);
    .questionnaire-schedule-gotop {
        :deep(.van-badge__wrapper,
        .van-icon,
        .van-icon-arrow-left) {
            color: var(--theme-color) !important;
            margin: 0.03rem 0 0 0;
        }
    }
    // 进度条背景
    .questionnaire-schedule-back {
        width: 2.44rem;
        height: 0.04rem;
        border-radius: 0.1rem;
        overflow: hidden;
        background: #EBF0FA;
        .questionnaire-schedule-in {
            width: 10%;
            height: 100%;
            border-radius: 0.1rem;
            overflow: hidden;
            background: linear-gradient(90deg,
            #3FA1FC 100%,
            #5860DA 100%);
        }
    }
    .questionnaire-schedule-need {
        width: 2rem;
    }
    // 需完善按钮
    .needImprove {
        padding: 0.02rem 0.07rem;
        white-space: nowrap;
        font-size: 0.1rem;
        color: var(--theme-color);
        border: 0.5px solid var(--theme-color);
        border-radius: 0.03rem;
    }
}
// 问卷内容区
.questionnaire-body {
    width: 100%;
    // height: 62vh;
    height: calc(100vh - 290px);
    box-sizing: border-box;
    .questionnaire-module {
        width: 100%;
        height: 100%;
        font-size: 0.13rem;
        box-sizing: border-box;
        overflow: auto;
        // 填写指南
        .fingerpost {
            padding: 0.2rem;
            box-sizing: border-box;
            margin: 0.2rem 0 0 0;
            color: #333;
            font-size: 0.15rem;
            display: -webkit-box;
            word-break: break-all;
            word-wrap: break-word;
        }
        .questionnaire-items {
            width: 100%;
            padding: 0.1rem;
            box-sizing: border-box;
            border-radius: 0.1rem;

            .questionnaire-items-title {
                margin: 0.1rem 0;
                display: flex;
                color: var(--theme-color);
                box-sizing: border-box;
                overflow: hidden;
                .date-and-time {
                    display: flex;
                }
            }
            // 上传照片
            .upload-img {
                padding: 0.1rem;
                height: 3.3rem;
                overflow: auto;
                box-sizing: border-box;
                border-radius: 0.1rem;
                background: #fff;
            }
        }
    }
    .btn {
        width: 80%;
        height: 0.39rem;
        margin: 0.2rem 10%;
    }
}