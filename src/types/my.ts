import { homeListInter } from '@/types/storeState';
import { requestFile } from "@/utils/axios";

export interface my {
    homeList: homeListInter;
    amount: number;
    getMyHomeDatas: Function;
    routerGo: Function
}

export interface EditAddress {
    myreg: RegExp;
    editAddressform: editAddressformInter;
    showAddress: boolean;
    cascaderValue: string;
    cityOptions: cityOptionsInter[];
    onFinish: Function;
    onSubmit: Function;
    successBack: Function;
    getEditAddressform: Function
}

export interface Feedback {
    fileList: any[];
    editAddressform: any;
    submitLoadingFlag: boolean;
    onSubmit: Function
}

interface editAddressformInter {
    addressId: string;
    name: string;
    mobile: string;
    cityValue: string;
    addressDetail: string;
    province: string;
    area: string;
    city: string;
    provinceCode: string;
    cityCode: string;
    areaCode: string;
}

interface cityOptionsInter {
    text: string;
    value: string;
    children: cityOptionsInter
}
/*BalanceDetail.vue*/
export interface getBankAccountInter {
    id: string;
    bank: string;
    cardNumber: string;
    openAccountBank: string;
    accountName: string;
    idCard: string;
    cardNumberCopy: string;
    bankAccountType: number;
}
export interface getPatientFinanceInter {
    id: string;
    amount: number;
    freezeAmount: number;
    withdrawalAmount: number
}

export interface getFinanceWithdrawalOrderItemInter {
    id: string;
    patientId: string;
    siteName: string;
    orderNo: string;
    amount: number;
    withdrawalType: number;
    withdrawalTypeName: string;
    bank: string;
    cardNumber: string;
    openAccountBank: string;
    accountName: string;
    idCard: string;
    status: number;
    statusName: string;
    payDate: string;
    payAccount: string;
    transactionReceipt: string;
    aprRuntimeId: number;
    currentRuntimeId: number;
    canOperateUserIds: string[];
    applyMan: string;
    applyManName: string;
    applyTime: string;
    attachmentEditViewModels: attachmentEditViewModels[];
    aprRuntimeLogViewModels: aprRuntimeLogViewModels[]
}
export interface attachmentEditViewModels {
    id: string;
    bindTableName: string;
    bindTableId: string;
    fileType: number;
    fileName: string;
    groupNumber: number;
    relativePath: string;
    fileUrl: string;
    thumbnailUrl: string;
    thirdCode: string;
    seqence: number
}
export interface aprRuntimeLogViewModels {
    detailName: string;
    detailCode: string;
    approver: string;
    requestDate: string;
    aprStatus: string;
    aprBizType: string;
    aprBizStatus: string;
    comment: string;
    remark: string
}
export interface balanceDetailStateInter {
    applyInfoObj: getFinanceWithdrawalOrderItemInter;
    bankInfoObj: getBankAccountInter;
    availableAmount: number;
    editFlag: boolean;
    saveWithdrawDeposit: Function;
    onLoad: Function;
}
/*BalanceDetailList.vue*/
export interface getFinanceChangeLogInter {
    items: financeChangeLogItemInter[];
    totalItemCount: number;
}

export interface financeChangeLogItemInter {
    id: string;
    amount: number;
    changeType: number;
    changeTypeName: string;
    orderNo: string;
    remark: string;
    createTime: string;
}

export interface balanceDetailListStateInter {
    myBalanceDetailListRef: any;
    balanceDetailList: financeChangeLogItemInter[];
    pageIndex: number;
    pageSize: number;
    totalItemCount: number;
    scrollChage: Function;
    onLoad: Function;
}
/*ConfirmBankCardInformation.vue*/
export interface getBankInfoListInter {
    bankCode: string;
    bankName: string;
}
export interface confirmBankCardInformationStateInter {
    outerHeight: number;
    submitLoadingFlag: boolean;
    bankAccountObj: getBankAccountInter;
    searchKey: '';
    selectBankBodyShow: number;
    bankInfoSubbranchList: any[];
    banksArr: getBankInfoListInter[];
    handleOpenAccountBank: Function;
    setBankInfo: Function;
    routerGo: Function;
    changeSearchKey: Function;
    saveConfirmBankInfo: Function;
    getBankInfoListFun: Function;
    getBankInfoSubbranchFun: Function;
    onLoad: Function;
    myPopupShow: boolean;
    loading: boolean;
    handleSave: Function;
    handleCancel: Function;
    bankAccountBlur: Function;
    accountVerify: Function;
    bankAccountMsg: Function;
    numBlank: Function;
    selectBankClick: Function;
    relationClick: Function;
    patientType: number;
    bankAccountList: any[];
}
/*MyBalance index.vue*/
export interface myBalanceStateInter {
    amount: number;
    availableAmount: number;
    myBalanceListRef: any;
    pageIndex: number;
    pageSize: number;
    totalItemCount: number;
    myBalanceList: any[];
    scrollChage: Function;
    routerGo: Function;
    onLoad: Function;
}
export interface getFinanceWithdrawalOrderInter {
    items: getFinanceWithdrawalOrderItemInter[];
    totalItemCount: number;
}
/*LogisticsDetails.vue*/
export interface logisticsDetailsStateInter {
    myLogisticsObj: getLogisticsOrderInter;
    // orderParams: {};
    orderParams: any;
    onLoad: Function;
}
export interface getLogisticsOrderInter {
    id: string;
    orderNo: string;
    waybillNo: string;
    creator: string;
    createTime: string;
    lastUpdator: string;
    lastUpdateTime: string;
    state: number;
    signTime: string;
    signUsername: string;
    waybillRecords: {
        id: string;
        waybillTime: string;
        logisticsInfo: string;
    }[];
    expectPickupTimeDisplay: string;
    createTimeDisplay: string;
    lastUpdateTimeDisplay: string;
    patientId: string;
    dctStudyId: string;
    dctSiteId: string;
    omsProjectId: string;
    customSubTaskId: string;
    goodsTypeName: string;
    logisticsProvider: string;
    shipperName: string;
    shipperPhoneNo: string;
    shipperProvinceCode: string;
    shipperProvinceName: string;
    shipperCityCode: string;
    shipperCityName: string;
    shipperAreaCode: string;
    shipperAreaName: string;
    shipperAddressDetail: string;
    rcptName: string;
    rcptPhoneNo: string;
    rcptProvinceCode: string;
    rcptProvinceName: string;
    rcptCityCode: string;
    rcptCityName: string;
    rcptAreaCode: string;
    rcptAreaName: string;
    rcptAddressDetail: string;
    shippingMethod: string;
    expectPickupTime: string;
    remark: string;
    channelName: string;
    materialOrderTraces: {
        areaCode: string;
        areaName: string;
        subLogisticsStatus: string;
        logisticsStatus: string;
        time: string;
        desc: string
    }[];
    thirdPlatformName: string;
}
/*MyOrder.vue*/
export interface OrderItem {
    id: string;
    orderType: number;
    orderNo: string;
    waybillNo: string;
    logisticsProviderLogo: string;
    logisticsProvider: string;
    orderState: string;
    createTime: string;
    createTimeDisplay: string;
    materials: Material[];
    materialOrderType: number;
    ruleName: string;
}

export interface Material {
    id: string;
    materialType: string;
    omsMaterialId: string;
    materialName: string;
    materialSpecs: string;
    lastUpdator: string;
    lastUpdatetime: string;
}

export interface getMyMaterialOrderInter {
    items: OrderItem[];
    totalItemCount: number;
}

export interface myOrderStateInter {
    noinformedImgSrc: string;
    myOrderListRef: any;
    myOrderList: OrderItem[];
    totalItemCount: number;
    pageIndex: number;
    pageSize: number;
    myOrderBack: Function;
    myOrderListScroll: Function;
    onLoad: Function;
    routerGo: Function;
}
/*OrderDetails.vue*/
export interface orderDetailsStateInter {
    routeOrderItem: any;
    orderDetailsObj: {
        id: string;
        orderNo: string;
        waybillNo: string;
        creator: string;
        createTime: string;
        createTimeDisplay: string;
        lastUpdator: string;
        lastUpdateTime: string;
        lastUpdateTimeDisplay: string;
        state: number;
        waybillRecords: any[];
        patientId: string;
        dctStudyId: string;
        dctSiteId: string;
        omsProjectId: string;
        customTaskId: string;
        goodsTypeName: string;
        logisticsProvider: string;
        shipperName: string;
        shipperPhoneNo: string;
        shipperProvinceCode: string;
        shipperProvinceName: string;
        shipperCityCode: string;
        shipperCityName: string;
        shipperAreaCode: string;
        shipperAreaName: string;
        shipperAddressDetail: string;
        rcptName: string;
        rcptPhoneNo: string;
        rcptProvinceCode: string;
        rcptProvinceName: string;
        rcptCityCode: string;
        rcptCityName: string;
        rcptAreaCode: string;
        rcptAreaName: string;
        rcptAddressDetail: string;
        shippingMethod: string;
        expectPickupTime: string;
        expectPickupTimeDisplay: string;
        remark: string;
        cityCollectValue: string;
        materials: any[];
        signTime?: string;
        orderStateDisplay?: string;
    };
    savePlaceAnOrderForm: Function;
    onLoad: Function;
}

// 上传用户系统使用反馈信息 SiteQrCode
export const SystemFeedbackInfoAsync = (data) => {
    return requestFile(
        `api/Doctor/SystemFeedbackInfoAsync`,
        data,
    );
};