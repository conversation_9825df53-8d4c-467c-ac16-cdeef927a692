import { disabledSaveFalgInter } from "@/types/quest";

/*PdfOpen.vue*/
// export interface PdfOpenStateInter {
//     pdfh5: any;
// }
export interface CommonColumnsInter {
    values: string[];
    defaultIndex: number;
}
/*CommonQuestion.vue*/
export interface CommonQuestionStateInter {
    refTypeShowFlag: boolean;
    rowChange: boolean;
    initialIsChange: boolean;
    isChange: boolean;
    myDeleteListPopupFlag: boolean;
    myDeleteListPopupObj: {
        questId: string;
        pDctCode: string;
        rowId: string;
    };
    handleSaveDeleteList: Function;
    handleCancelDeleteList: Function;
    saveTemporaryQuestionnaire: Function;
    clearStagingFun: Function;
    //
    timeColumns: CommonColumnsInter[];
    columnsTimeChange: Function;
    setTimeDefaultIndex: Function;
    saveTimeDefaultIndex: Function;
    setYearMonthColumns: Function;
    setYearMonthDefaultIndex: Function;
    yearMonthColumns: CommonColumnsInter[];
    yearMonthColumnsChange: Function;
    setSpecificDefaultIndex: Function;
    specificColumns: CommonColumnsInter[];
    specificColumnsChange: Function;
    //
    visitId: string;
    visDate: string;
    visitName: string;
    durgDataBeforehandlist: any;
    dctCodeItem: any;
    questItem: any;
    initialQuestionObj: any;
    oldQuestionObj: any;
    questionObj: any;
    myPreview: string[];
    questionDataObj: any;
    questionIndex: number;
    questionObjLength: number;
    dateIndex: number;
    yearMonthValue: Date;
    yearMonthIndex: number;
    editable: string;
    otherQuestionnaires: string;
    disabledSaveFalg: disabledSaveFalgInter;
    submitLoadingFlag: boolean;
    outerHeight: number;
    pickerYearMonth: Function;
    // confirmYearMonthValue: Function;
    showFieldDescription: Function;
    tab: Function;
    deleteListFun: Function;
    addListFun: Function;
    editListFun: Function;
    upListData: Function;
    needImprove: Function;
    getQuestionnaiData: Function;
    fzFun: Function;
    durgSpecialFieldTypeFz: Function;
    mutualExclusion: Function;
    refreshRelevanceAll: Function;
    invertSelectionFun: Function;
    showRelevance: Function;
    showRelevances: Function;
    keyUpNum: Function;
    pickerDay: Function;
    pickerDate: Function;
    saveQuestionnaire: Function;
}
/*.vue*/