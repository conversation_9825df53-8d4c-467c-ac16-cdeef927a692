import { CommonColumnsInter } from '@/types/components';
export interface questionObjInter {
    questId: string;
    questCrfType: number;
    crfName: string;
    crfGuideline: string;
    crfSubTitle: string;
    warningContent: string;
    finishStatus: number;
    questUrl: string;
    questCrfItemes: questCrfItemesInter[];
    publishDate: string;
}
/**/
export interface questCrfItemesInter {
    crfItemId: string;
    dctCode: string;
    isRequired: number;
    crfFieldType: number;
    crfFieldControl: number;
    refDctCode: string;
    refItemValue: string;
    refType: number;
    fieldItems: fieldItemsInter[];
    fieldLabel: string;
    fieldDescription: string;
    dctQuestUnit: string;
    maximumChoice: string;
    minimumChoice: string;
    isReadOnly: number;
    dctDateControlDefault: true;
    specialFieldType: number;
    dischargeStandard: number;
    children: string[];
    datas: datasInter[];
}
/**/
export interface fieldItemsInter {
    id: string;
    itemValue: string;
    itemName: string;
    itemContent: string;
    gaugeValue: number;
    mutualExclusion: true
}
/**/
export interface datasInter {
    fieldDataId: string;
    dctCode: string;
    fieldValue: string;
    fieldValueStr: string;
    rowNum: number
}
/*EditQuestionnairetList.vue*/
export interface EditQuestionnairetListStateInter {
    timeColumns: CommonColumnsInter[];
    columnsTimeChange: Function;
    setTimeDefaultIndex: Function;
    saveTimeDefaultIndex: Function;
    setYearMonthColumns: Function;
    setYearMonthDefaultIndex: Function;
    yearMonthColumns: CommonColumnsInter[];
    yearMonthColumnsChange: Function;
    setSpecificDefaultIndex: Function;
    specificColumns: CommonColumnsInter[];
    specificColumnsChange: Function;
    //
    questItem: any;
    questionObj: any;
    myPreview: string[];
    editFlag: string;
    questionDataObj: any;
    questionIndex: number;
    questionObjLength: number;
    dateIndex: number;
    yearMonthValue: Date;
    yearMonthIndex: number;
    disabledSaveFalg: disabledSaveFalgInter;
    submitLoadingFlag: boolean;
    outerHeight: number;
    pickerYearMonth: Function;
    showFieldDescription: Function;
    onLoad: Function;
    mutualExclusion: Function;
    refreshRelevanceAll: Function;
    invertSelectionFun: Function;
    showRelevance: Function;
    showRelevances: Function;
    keyUpNum: Function;
    pickerDay: Function;
    pickerDate: Function;
    saveQuestionnaire: Function;
}
export interface disabledSaveFalgInter {
    disabledSaveFalg: boolean;
}
/**/