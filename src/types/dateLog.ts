import { homeListInter } from '@/types/storeState';
  export interface commonProblemInter {
    commonProblemList: commonProblemList[];
  }
  export interface commonProblemList {
    seqNum: number;
    question: string;
    answer: string
  }

  export interface drugApplicationInter {
    drugApplicationList: drugApplicationListInter[];
    checkedItems: Function;
    addOrSubtract: Function;
    drugApplicationAdd: Function;
    showNeedImprove: boolean;
    reasons: reasons;
    confirmNeedImprove: Function;
  }
  interface reasons {
    suppymentReason: string
  }
  export interface drugApplicationListInter {
    checkedItem: boolean; // 自定义的
    id: string;
    drugName: string; 
    drugDisplayName: string;
    drugDescription: string;
    drugSpecifics: string;
    batchNumber: string;
    validateTill: string;
    storeNumber: number;
    recommendApplyNumber: number;
    maxApplyNumber: number;
    minApplyNumber: number;
    drugDoseUnit: string;
    scale: number;
    defaultDose: number; 
    maxDose: number;
    minDose: number;
    armName: string;
    medicationTips: string;
    manufacturer: string;
    numberOfTimesPerDay: number;
    isApplication: boolean;
    periodId: string;
    drugRemind: string;
    drugCategory: number; 
  }

  export interface giftInter {
    giftList: any[];
    checkedItems: Function;
    addOrSubtract: Function;
    giftAdd: Function;
  }
  
  export interface dateLogInter {
    dateLogList: any[];
    showOpenItem: Function;
    noinformedImgSrc: string;
    homeList: homeListInter;
    noinformedTexts: string;
    routerGoRemider: Function;
    routerGoFollowQuestion: Function;
  }
  
  export interface onlineCustomerServiceInter {
    sendOrAdd: Boolean;
    onlineCustomerServiceList: any[];
    sendMessagValue: string;
    addMyText: Function;
  }