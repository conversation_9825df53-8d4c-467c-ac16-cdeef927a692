export interface learningMaterialsInter {
  noinformedImgSrc: string;
  learningMaterialsList: TrainingItem[];
  learningMaterialsListRef: any;
  totalItemCount: number;
  pageIndex: number;
  pageSize: number;
  learningMaterialsListScroll: Function;
  onLoad: Function;
  routerGo: Function;
}
export interface TrainingItem {
  id: string;
  ruleId: string;
  studyId: string;
  userType: number;
  trainingType: number;
  trainingText: string;
  fileType: number;
  trianingTitle: string;
  weightedValue: number;
  isEnable: boolean;
  enableState: string;
  minimumTrainingTime: number;
  trainingFileId: string;
  trainingFileUrl: string;
  videoCoverId: string;
  videoCoverUrl: string;
  distributionRule: number;
  specificRules: string;
  distributionRuleString: string;
  operatorId: string;
  operatorName: string;
  lastUpdateTime: string;
  userTrainingRecords: UserTrainingRecord[];
  finishStatus: number;
}

interface UserTrainingRecord {
  id: string;
  trainingDocumentId: string;
  trainingSiteRuleId: string;
  userType: number;
  userId: string;
  startTime: string;
  endTime: string;
}
export interface getTrainingsInter {
  items: TrainingItem[];
  totalItemCount: number;
}

export interface learningMaterialsInfoInter {
  imgPreviewShow: boolean;
  learningMaterialsItem: any;
  TIMEOUT: any;
  learningMaterialsInfoVideoPlayerRef: any;
  learningMaterialsInfoIcfObj: any;
  recordId: string;
  onPause: Function;
  onEnded: Function;
  onPlaying: Function;
  getTrainingsEndTime: Function;
  getTrainingsStartTime: Function;
  imgClick: Function
}
export interface getTrainingsDetailsInter {
  trainingText: string;
}
export interface getTrainingsStartTimeInter {
  id: string;
  trainingDocumentId: string;
  trainingSiteRuleId: string;
  userType: number;
  userId: string;
  startTime: string;
  endTime: string;
}
