import { homeListInter } from '@/types/storeState';

export interface homeInter {
    conferenceFlag: boolean;
    homeList: homeListInter;
    showPopupFlag: boolean;
    popupShowFlag: boolean;
    showPopupText: string;
    trainingNotice: number;
    showPopup: Function;
    getMyHomeDatas: Function;
    routerGo: Function;
}

export interface InteractiveCommunication {
    sendOrAdd: boolean;
    interactiveCommunicationList: ChatMessageInter[];
    sendMessagValue: string;
    dataURLtoFile: Function;
    beforeRead: Function;
    afterRead: Function;
    sendImage: Function;
    addMyText: Function;
    avatarUrl: string;
    myPreview: any[];
    loadImgNum: number;
    endLoadImgNum: number;
    loadImg: Function;
    scrollAuto: Function;
    myCompress: Function;
    tab: Function;
    getPatientChattingRecord: Function;
}

export interface followReminderInter {
    visitPlanObj: getVisitPlanInter;
    saveFollow: Function;
    onLoad: Function
}

export interface getVisitPlanInter {
    patientId: string;
    groupStatus: number;
    patientStatus: number;
    patientNo: string;
    avatarUrl: string;
    sex: number;
    whetherToLayer: number;
    group: string;
    statusText: string;
    inGroup: string;
    visitId: string;
    visitName: string;
    visitRemindType: number;
    meetingDateTime: string;
    meetingStatus: number;
    remindContent: string;
    currentVisitText: string;
    timeText: string;
    dateWeekText: string;
    finishStatus: number;
    expectedTime: string;
    actualMeetingDateTime: string;
    visitBaseline: number;
    doctorEditable: number;
    hasExpectedDay: boolean;
}
/*互动沟通*/
export interface getPatientChattingRecordsInter {
    items: ChatMessageInter[];
    totalItemCount: number;
}
export interface ChatMessageInter {
    recordsId: string;
    windowsId: string;
    siteId: string;
    patientId: string;
    patNumber: string;
    doctorId: string;
    doctorName: string;
    avatarUrl: string;
    identity: number;
    chatTime: string;
    displayChatTime: string;
    contentType: number;
    content: string;
    contentUrl: string;
    thumbnail: string;
}
/*CompensationReimbursementPage.vue*/
export interface compensationReimbursementPageStateInter {
    noDatasImg: string;
    patientAuth: boolean;
    summaryObj: getCompensationAmountSummaryInter;
    compensationReimbursementList: GetPeriodicCompensationApplyItemInter[];
    compensationReimbursementListRef: any;
    pageIndex: number;
    pageSize: number;
    totalItemCount: number;
    scrollChage: Function;
    routerGo: Function;
    onLoad: Function;
}
export interface getCompensationAmountSummaryInter {
    applyAmount: number;
    dealedAmount: number;
    withdrawalAmount: number;
}
export interface GetPeriodicCompensationApplyAuthInter {
    doctorAuth: boolean;
    patientAuth: boolean;
}
/*--->*/
export interface FileAttachment {
    id: string;
    bindTableName: string;
    bindTableId: string;
    fileType: number;
    fileName: string;
    groupNumber: number;
    relativePath: string;
    fileUrl: string;
    thumbnailUrl: string;
    thirdCode: string;
    seqence: number;
}

interface ApplySubject {
    id: string;
    guId?: string;
    applyId: string;
    subjectId: string;
    subjectName: string;
    amount: string;
    remark: string;
    compensationConfigId: string;
    attachmentEditViewModels: FileAttachment[];
}

interface Visit {
    visitTemplateId: string;
    visitTemplateName: string;
}

interface RuntimeLogDetail {
    detailName: string;
    detailCode: string;
    approver: string;
    requestDate: string;
    aprStatus: string;
    aprBizType: string;
    aprBizStatus: string;
    comment: string;
    remark: string;
}

interface User {
    userId: string;
    userName: string;
}
export interface GetPeriodicCompensationApplyListInter {
    items: GetPeriodicCompensationApplyItemInter[];
    totalItemCount: number;
}

export interface GetPeriodicCompensationApplyItemInter {
    id: string;
    studyId: string;
    siteId: string;
    patientId: string;
    createTimeStr: string;
    applyNumber: string;
    patientNumber: string;
    siteName: string;
    relationVisitName: string;
    patientName: string;
    compensationAmount: number;
    status: number;
    statusStr: string;
    lastStatus: number;
    source: number;
    triggreType: number;
    relationVisitId: string;
    creator: string;
    creatorId: string;
    applyTime: string;
    applyTimeStr: string;
    createTime: string;
    lastUpdater: string;
    lastUpdateTime: string;
    lastUpdateTimeStr: string;
    applyStatus: number;
    applyMsg: string;
    realName: string;
    isDeleted: boolean;
    remark: string;
    nextApprovers: string;
    handledByMe: boolean;
    commitByMe: boolean;
    currentRuntimeId: number;
    applySubjects: ApplySubject[];
    visits: Visit[];
    runtimeLog: RuntimeLogDetail[];
    users: User[];
    subjects: string;
}
/*CostBreakdown.vue*/
export interface CostBreakdownStateInter {
    maxCount: number;
    maxSize: string;
    outerHeight: number;
    editFlag: boolean;
    showSubjectPopup: boolean;
    myPreview: [];
    operationFlag: string;
    operationItemObjIndex: number;
    defaultSubjectIndex: number;
    applySubjectsLength: number;
    operationCostBreakdownItem: ApplySubject;
    subjectOptions: {
        subjectId: string;
        subjectName: string;
    }[];
    amountUpNum: Function;
    onConfirmSubject: Function;
    handleImgPreview: Function;
    handleDeleteImg: Function;
    beforeRead: Function;
    afterRead: Function;
    setAttachmentImage: Function;
    setAttachmentSaveImage: Function;
    handleDelete: Function;
    saveCostBreakdown: Function;
    setDefaultIndexFun: Function;
    onLoad: Function;
}
/*通用的上传接口*/
export interface PostCommonFileInter {
    id: string;
    bindTableName: string;
    bindTableId: string;
    fileType: number;
    fileName: string;
    groupNumber: number;
    relativePath: string;
    fileUrl: string;
    thumbnailUrl: string;
    thirdCode: string;
    sequence: number;
}
/*CostDetails.vue*/
export interface CostDetailsStateInter {
    colorfff: string;
    showCostBreakdownFlag: boolean;
    myCostBreakdownRef: any;
    showInterviewPopup: boolean;
    defaultInterviewIndex: number;
    excessAmountPopupShow: boolean;
    excessAmountText: string;
    applyInfoObj: GetPeriodicCompensationApplyItemInter;
    editFlag: boolean;
    submitLoadingFlag: boolean;
    initCostBreakdownItem: any;
    //
    setDefaultInterviewIndexFun: Function;
    onConfirmInterview: Function;
    continueSave: Function;
    backCostBreakdown: Function;
    setCostBreakdown: Function;
    changeCostBreakdownItem: Function;
    costApplyForSave: Function;
    saveEnd: Function;
    onLoad: Function;
}

/*App.vue*/
export interface GetOpenIdInterface {
  token: string;
  expiredTime: string;
  patientStatus: number;
  patientICFStatus: number;
  patientNum: string;
  avatarUrl: string;
  inGroupDay: string;
  auditComments: string;
  finishLanguage: string;
  icfStementId: string;
  isFamilyMember: boolean;
  memberStatus: number;
}