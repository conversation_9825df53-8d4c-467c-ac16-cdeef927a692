export interface TakeMedicineLog {
    medicationsList: medicationsItemInter[];
    takemedicineLogScrollRef: any
    showOpenItem: Function;
    deleteTakeMedicineLog: Function;
    getMyTakeMedications: Function;
    routerGo: Function;
    totalItemCount: number; // 总条数
    pageIndex: number;
    pageSize: number;
    dates: datesInter
}
export interface getMedicationsInter {
    items: medicationsItemInter[];
    totalItemCount: number;
}
export interface medicationsItemInter {
    activeOpen: boolean;// 自定义
    id: string;
    patientId: string;
    takenTime: string;
    drugId: string;
    drugItem: drugItemInter;
    takenDose: number;
    medicationLogStatus: number;
    remark: string;
    takenTimeDT: Date;
}

export interface drugItemInter {
    id: string;
    drugName: string;
    drugDisplayName: string;
    drugDescription: string;
    drugSpecifics: string;
    batchNumber: string;
    validateTill: string;
    storeNumber: number;
    recommendApplyNumber: number;
    maxApplyNumber: number;
    minApplyNumber: number;
    drugDoseUnit: string;
    scale: number;
    defaultDose: number;
    maxDose: number;
    minDose: number;
    armName: string;
    medicationTips: string;
    manufacturer: string;
    numberOfTimesPerDay: number;
    isApplication: true;
    periodId: string;
    drugRemind: string;
    drugCategory: number
}
export interface Discomfort {
    discomfortList: any[];
    // showOpenItem: Function;
    deleteDiscomfort: Function;
    getAdverseEventsDatas: Function;
    routerGo: Function;
    dates: any;
    totalItemCount: number;
    discomfortScroll: any
}

export interface DrugCombination {
    CMsList: any[];
    // showOpenItem: Function;
    deleteDrugCombination: Function;
    getCMsDatas: Function;
    routerGo: Function;
    dates: any;
    totalItemCount: number;
    drugCombinationScroll: any
}
interface takeMedicineLogObj {
    id: string;
    drugName: string;
    drugDescription: string;
    drugSpecifics: string;
    batchNumber: string;
    validateTill: string;
    storeNumber: number;
    recommendApplyNumber: number;
    maxApplyNumber: number;
    minApplyNumber: number;
    drugDoseUnit: string;
    scale: number;
    defaultDose: number;
    maxDose: number;
    minDose: number;
    takenDose: number;
    armName: string;
    medicationTips: string;
    manufacturer: string;
    patientId: string;
    drugId: string;
    remark: string;
    takenTime: string;
    drugRemind: string;
}
export interface EditTakeMedicineLog {
    takeMedicineLogObj: takeMedicineLogObj;
    getMedications: Function;
    saveMedicationForm: Function;
    addOrSubtract: Function;
    showDatas: boolean;
    currentDate: any;
    minDate: any;
    maxDate: any;
    showIsLink: boolean;
    submitLoadingFlag: boolean;
    confirmValue: Function;
}

export interface EditDiscomfort {
    postQuestFile: Function;
    deleteQuestFile: Function;
    immediateWithdrawalFlag: boolean;
    routerBack: Function;
    EditDiscomfortRef: any;
    putCMData: Function;
    postCM: Function;
    getCMData: Function;
    getCM: Function;
    postAdverseEvent: Function;
    putAdverseEventData: Function;
    getAdverseEventData: Function;
    getAdverseEvent: Function;
    getQuestView: Function;
    getQuestData: Function;
    putQuestData: Function;
    myRequestId: string;
    myTitle: string;
    discomfortCMItem: any;
    questionOnload: Function;
}
/*不良事件清单Discomfort.vue*/
export interface getAdverseEventsInter {
    items: adverseEventsItemInter[];
    totalItemCount: number;
}
export interface adverseEventsItemInter {
    rowId: string;
    title: string;
    happenDate: string;
    logStatus: number;
    questDisplayType: number;
    questUrl: string;
    dtHappenDate: string;
}
export interface getDiscomfortViewDataInter {
    questId: string;
    rowId: string;
    aecmItems: aecmItemsInter[]
}
export interface aecmItemsInter {
    dctCode: string;
    fieldLabel: string;
    fieldValue: string;
    fieldValueStr: string;
    dctQuestUnit: string;
    maximumChoice: string;
    minimumChoice: string;
    isReadOnly: number;
}
/*NewTakeMedicineLog.vue*/
export interface getDurgRecordInter {
    items: durgRecordItemsInter[];
    totalItemCount: number;
}
export interface durgRecordItemsInter {
    // 自定义的key
    drugActive: boolean;
    id: string;
    dctPatientDrugPlanId: string;
    dctDrugRecordId: string;
    patientId: string;
    dctDrugId: string;
    drugName: string;
    drugDoseUnit: string;
    drugCategory: number;
    displayTime: string;
    drugTime: Date;
    actualDrugTime: string;
    patientQuestId: string;
    dosage: number;
    actualDosage: number;
    isFinished: number;
    finishedTime: Date;
    finishedTimeStr: string;
    isUserAdd: true;
    drugReminderText: string;
    isReadOnly: number;
    expireStr: string;
}
export interface newTakeMedicineLogStateInter {
    medicationsList: durgRecordItemsInter[];
    totalItemCount: number;
    pageIndex: number;
    pageSize: number;
    newTakemedicineLogScroll: any;
    dates: datesInter;
    filterDateMaxDate: Date;
    outerHeight: number;
    newTakemedicineLogFilterDateRef: any;
    myPopupShow: boolean;
    myPopupShowTexts: string;
    myPopupShowThatDayTexts: string;
    notFilledInOnThatDayFlag: boolean;
    handleSave: Function;
    handleCancel: Function;
    scrollChage: Function;
    getMyTakeMedications: Function;
    routerGo: Function;
}
export interface datesInter {
    dtBegin: string;
    dtEnd: string;
}
/*DrugCombination.vue*/
export interface getCMsInter {
    items: CMsItemsInter[];
    totalItemCount: number;
}
export interface CMsItemsInter {
    rowId: string;
    title: string;
    happenDate: string;
    logStatus: number;
    questDisplayType: number;
    questUrl: string;
    dtHappenDate: Date;
}
/*placeanorder.vue*/
export interface getMyAddressInter {
    addressId: string;
    name: string;
    mobile: string;
    province: string;
    city: string;
    area: string;
    provinceCode: string;
    cityCode: string;
    areaCode: string;
    addressDetail: string;
}