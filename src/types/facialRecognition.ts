export interface facialRecognitionInter {
  whetherPsaa: number;
  certName: string;
  iosShow: number;
  loading: boolean;
  handleRecognition: Function;
  routerGo: Function;
  result: Function;
  onLoad: Function;
  myTitle: string;
  verifyIOS: Function;
}

export interface facialRecognitionResponse {
  patientStatus: number;
  auditComments: string;
  patientICFStatus: number;
  icfAuditComments: string;
  message: string;
  bizId: string;
  certUrl: string;
  certName: string;
}