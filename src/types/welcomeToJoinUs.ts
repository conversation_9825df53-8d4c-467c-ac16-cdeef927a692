/* 诚挚邀请您加入项目页 */
export interface welcomeToJoinUsInter {
    checked: boolean;
    projectObj: getStudyIntroductionInter;
    routerGo: Function
}
/* 服务条款 隐私协议 */
export interface privacyAgreementInter {
    showTitleFlag: string;
    privacyObj: string;
    serviceLevelAgreementObj: string;
    routerBack: Function
}
/* 绑定手机号 */
export interface bindingMobileInter {
    verifyTime: number;
    form: bindingMobileFormInter;
    submitLoadingFlag: boolean;
    bindOrRegisterflag: boolean;
    getVerifycode: Function;
    routerGo: Function;
    bindIDcardLeft: boolean;
}
interface bindingMobileFormInter {
    mobile: string;
    verifyCode: string;
    identityCardNo: string;
    realName: string
}

export interface postVerifyCodeInter {
    patientStatus: number;
    auditComments: string;
    patientICFStatus: number;
    icfAuditComments: string;
    message: string;
    icfStatementID?: string;
    icfStementId?: string;
}
/*绑定受试者*/
export interface informationRegisterInter {
    checked: boolean;
    submitLoadingFlag: boolean;
    verifyTime: number;
    form: bindingMobileFormInter;
    onSubmit: Function;
    getVerifycode: Function;
    routerGo: Function
}
/*SignInformed.vue*/
export interface statementMobileObjInter {
    patientPhone: string;
    hasVideoStatement: boolean;
    patientSignatureType: number;
    signatureStrategy: number;
}
export interface postStatementVerifyCodeInter {
    patientStatus: number;
    auditComments: string;
    patientICFStatus: number;
    icfAuditComments: string;
    message: string
}
/*RecordPersonalStatement.vue*/
export interface getStatementInter {
    studyReciteText: string;
    statementFileId: string;
    videoUrl: string;
    videoCoverUrl: string;
    patientPhone: string;
    identityCardFileUrl: string
}
export interface postVideoComfirmInter {
    patientStatus: number;
    auditComments: string;
    patientICFStatus: number;
    icfAuditComments: string;
    message: string
}
export interface postIdentityCardInter {
    statementFileId: string;
    identityCardUrl: string;
}
/*InformedVideo.vue*/
export interface IcfObjInter {
    studyVideoUrl: string;
    studyVideoCoverUrl: string;
    studyVideoViewSeconds: number;
    isCompletedVideo: number;
}
export interface postStudyIntroduceVideoFinishInter {
    patientStatus: number;
    auditComments: string;
    patientICFStatus: number;
    icfAuditComments: string;
    message: string;
}
/*InformedQuestionnaire.vue*/
export interface commonQuestionnaireInter {
    outerHeight: number;
    questionObj: any; // 问卷总对象
    questionDataObj: any; // 问卷回答对象questCrfItemes
    questionIndex: number; // 当前回答问题的--下标
    questionObjLength: number; // 问题总长
    currentDate: Date; // new Date()
    yearMonthValue: Date;
    yearMonthIndex: number; // 年月下标
    dateIndex: number; // 日期下标
    minDate: Date;
    maxDate: Date;
    date: Date;
    editable: string;
    otherQuestionnaires: string; // 其他任务问卷
    // (将舍弃)
    questItem: any;
    visitId: string;
    dctCodeItem: any; // 获取到的图片列表
    disabledSaveFalg: any;
    visDate: string;
    visitName: string;
    myPreview: any[];
    // 服药特殊字段
    durgDataBeforehandlist: any;
    submitLoadingFlag: boolean;
    // 引导
    showFieldDescription: Function;
    // 图片预览
    tab: Function;
    // 只能输入数字
    keyUpNum: Function;
    // 点击选择时间
    pickerDay: Function;
    // 选择时间
    onConfirm: Function;
    // 日期2021-01-01
    confirmValue: Function;
    // 点击选择年月
    pickerYearMonth: Function;
    // 确定年、月
    confirmYearMonthValue: Function;
    // 点击选择日期
    pickerDate: Function;
    // 删除 新增 某个列表
    deleteListFun: Function;
    addListFun: Function;
    editListFun: Function;
    // 更新列表
    upListData: Function;
    // 需完善弹框
    needImprove: Function;
    // 提交问卷
    saveQuestionnaire: Function;
    // 特殊字段赋值
    durgSpecialFieldTypeFz: Function;
    // 初次获取数据
    getQuestionnaiData: Function;
    fzFun: Function;
    // 互斥
    mutualExclusion: Function;
    // 刷新 关联逻辑
    refreshRelevanceAll: Function;
    // 单选-反选
    invertSelectionFun: Function;
    // 是否关联显示
    showRelevance: Function;
    // 多选关联显示
    showRelevances: Function;
}
/*Informed.vue*/
export interface InformedObjInter {
    studyICF: string;
    studyKnowReadingTime: number;
    isAlreadyCompleteICF: number;
    studyICFUrl: string;
    goBack: number;
    explainICFType: number;
}
export interface postStudyIcfFinishjInter {
    patientStatus: number;
    auditComments: string;
    patientICFStatus: number;
    icfAuditComments: string;
    message: string;
    patientICFStatus_Other: number;
}
/*WelcomeToJoinUs.vue*/
export interface getStudyIntroductionInter {
    title: string;
    projectCode: string;
    rotationChartUrl: string[];
    projectContent: string;
}
/*audit.vue*/
export interface postSignComfirmInter {
    patientStatus: number;
    auditComments: string;
    patientICFStatus: number;
    icfAuditComments: string;
    message: string;
}
/*InformedList index.vue*/
export interface InformedListStateInter {
    formatDate: Function;
    noDatasImg: string;
    informedList: StatementResult[];
    routerGo: Function;
    nextClick: Function;
    loading: boolean;
    pageShow: number;
}
/*InformedDetails index.vue*/
export interface IcfObj {
	studyICF: string;
	studyKnowReadingTime: number;
	isAlreadyCompleteICF: number;
	studyICFUrl: string;
	goBack: number;
	explainICFType: number;
}

export interface StatementInfoObj {
	icfVersionNumber: string;
	icfVersionDate: string;
}

export interface VideoItem {
	videoCoverUrl: string;
	videoUrl: string;
}

export interface InformedDetailsInter {
	icfObj: IcfObj;
	statementInfoObj: StatementInfoObj;
	videoItem: VideoItem;
    routerGo: Function;
}
// interface Statement {
//     statementTemperatureID: string;
//     statementURL: string;
//     studyICF: string;
//     isPatientSigned: boolean;
//     isDoctorSigned: boolean;
// }

// export interface StatementResult {
//     statements: Statement[];
//     studyName: string;
// }
export interface PatientICFStatement {
	id: string;
	dctStudyICFInfoId: string;
	icfVersionNumber?: any;
	icfVersionDate?: any;
	effectiveDate?: any;
	isFirstICF: boolean;
	icfStatus: number;
	icfStatusName: string;
	status: string;
	dctPatientId: string;
	dctSiteId: string;
	patNumber: string;
	avatarUrl: string;
	patientStatus: number;
	inGroupDate: string;
	sex: string;
	group?: any;
	groupStatus: number;
	groupStatusName: string;
}

export interface StatementResult {
	dctStudyId: string;
	dctStudyName: string;
	patientICFStatements: PatientICFStatement[];
}

export interface getSummaryDetailInter {
    statementTemperatureID: string;
    statementURL: string;
    studyICF: string;
    isPatientSigned: boolean;
    isDoctorSigned: boolean;
}
/**/
export interface Form {
	mobile: string;
	verifyCode: string;
}

export interface StatementMobileObj {
	patientPhone: string;
	hasVideoStatement: boolean;
}

export interface SignInformedInter {
	patientSignatureType: number;
	touchstartFlag: boolean;
	signaturePad?: any;
	signatureCanvas?: any;
	outerWidth: number;
	outerHeight: any;
	cfrPartStr: string;
    signatureCfrStr: string;
	verifyTime: number;
	form: Form;
	disabledInput: boolean;
	statementMobileObj: StatementMobileObj;
	submitLoadingFlag: boolean;
    clearSignature: Function;
    getVerifycode: Function;
    routerGo: Function;
    onSubmit: Function;
    commonSubmit: Function;
    statementInfoObj: any;
}

export interface baseFileObjInter {
    id: string;
    bindTableName: string;
    bindTableId: string;
    fileType: number;
    fileName: string;
    groupNumber: number;
    relativePath: string;
    fileUrl: string;
    thumbnailUrl: string;
    thirdCode: string;
    seqence: number;
}