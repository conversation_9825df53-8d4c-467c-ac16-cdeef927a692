/*UploadTheTestSheetTask.vue*/
export interface uploadTheTestSheetTaskStateInter {
    questionnairesTaskList: questionnairesTaskItemInter[];
    uploadTheTestSheetTaskRef: any;
    pageIndex: number;
    pageSize: number;
    totalItemCount: number;
    routerGo: Function;
    scrollChage: Function;
    onLoad: Function;
}
export interface questionnairesTaskListInter {
    items: questionnairesTaskItemInter[];
    totalItemCount: number;
}
interface PatientQuest {
    questId: string;
    questDisplayType: number;
    questionUrl: string;
    questName: string;
    finishStatus: number;
    finishStatusText: string;
    sort: number;
    isSubmit?: boolean;
    isStaging?: boolean;
    isOffline?: boolean;
}

export interface questionnairesTaskItemInter {
    patientQuests: PatientQuest[];
    visitName: string;
}

/*QuestionnairesTask.vue*/
export interface questionnairesTaskStateInter {
    questionnairesTaskList: questionnairesTaskItemInter[];
    questionnairesTaskRef: any;
    pageIndex: number;
    pageSize: number;
    totalItemCount: number;
    routerGo: Function;
    scrollChage: Function;
    onLoad: Function;
}
/*OtherTask.vue*/
export interface otherTaskStateInter {
    otherTaskList: otherTaskList[];
    otherTaskRef: any;
    pageIndex: number;
    pageSize: number;
    totalItemCount: number;
    routerGo: Function;
    scrollChage: Function;
    onLoad: Function;
}
export interface getTasksInter {
    items: otherTaskList[];
    totalItemCount: number;
}

interface otherTaskList {
    id: string;
    taskName: string;
    taskDescription: string;
    patientId: string;
    finishedCount: number;
    totalCount: number;
    status: number;
    isExpired: boolean;
    statusText: string;
    publishDate: string;
}
/*OtherTaskDetails.vue*/
export interface otherTaskDetailsStateInter {
    otherTaskDetailsObj: getTaskDetailsInter;
    subTasksDetailsObj: getTaskSubTasksDetailsInter[];
    routerGo: Function;
    onLoad: Function;
}
export interface getTaskSubTasksDetailsInter {
    id: string;
    isStaging?: boolean;
    isSubmit?: boolean;
    dctPatientCustomTaskId: string;
    subtaskType: number;
    specificItems: string;
    subtaskName: string;
    specificItemList: string[];
    isFinished: boolean;
    finishedTime: string;
    associatedNumber: string;
}
export interface getTaskDetailsInter {
    id: string;
    taskName: string;
    taskDescription: string;
    patientId: string;
    finishedCount: number;
    totalCount: number;
    status: number;
    isExpired: boolean;
    statusText: string;
    publishDate: string
}
