
// 用来约束store的state对象的类型接口
export interface IState {
  routerInn: any[];
  patientToken: string;
  auditFlag: boolean;
  userInformation: userInformationInter;
  homeList: homeListInter;
  taskText: string;
  getOldQuestDataFlag: number;
  oldQuestData: any;
  oldQuestDataIndex: number;
  storePlaceAnOrderObj: {
    expectPickupTimeDisplay: string;
    remark: string;
  };
  patientCode: string;
  continueStatusObj: Record<string, any>;
  initialQuestionObj: any;
  tableListArr: any[];
  tableListAllList: any[];
  initialListArr: any[];
  initialAllListArr: any[];
  mainQuestIsChange: boolean;
  getQuestionnaiDataObj: any;
}

interface userInformationInter {
  token: string;
  expiredTime: string;
  patientStatus: number;
  patientNum: string;
  avatarUrl: string;
  inGroupDay: string;
  auditComments: string;
  finishLanguage: string;
  icfStatementID?: string;
  icfStementId?: string;
  isFamilyMember: boolean;
  memberStatus: number;
  dctStudyId?: string;
}

export interface homeListInter {
  dctPatientId: string;
  dctSiteId: string;
  dctStudyId: string;
  displayTraining: boolean;
  patNumber: string;
  avatarUrl: string;
  memberAvatarUrl: string;
  greeting: string;
  currentStatusText: string;
  patientTrialStatusText: string;
  patientStatus: number;
  visits: visitsInter[];
  frequencyFeature: number[];
  currentVisitId: string;
  currentVisitText: string;
  totalTask: number;
  completedTask: number;
  questionTaskStatus: number;
  pictureTaskStatus: number;
  otherTaskStatus: number;
  visitMeetingList: any[];
  dailyFeature: number | string;
  inGroup: string;
  inGroupDay: string;
  medicationStatus: number;
  patientICFStatus: number;
  patientMenues: any[];
  studyName: string;
  unReadChattingRecordsNum: number;
  needInGroupGuidModal: number;
  extendStatus?: number;
  hasInteractiveCommunication: number;
}

interface visitsInter {
  visitId: string;
  avatarUrl: string;
  visitName: string;
  beginDate: string;
  endDate: string;
  order: number;
  isCurrent: number;
  isCenter: number;
  editable: number;
  finishStatus: number;
  quests: questsInter[]
}

interface questsInter {
  questId: string;
  edcCrfId: number;
  questName: string;
  questDescribe: string;
  questDisplayType: number;
}
