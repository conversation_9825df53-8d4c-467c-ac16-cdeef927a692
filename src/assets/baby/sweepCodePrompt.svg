<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="750" height="1213" viewBox="0 0 750 1213">
  <defs>
    <linearGradient id="linear-gradient" x1="0.5" y1="-0.102" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#adbfed" stop-opacity="0.2"/>
      <stop offset="1" stop-color="#fff"/>
    </linearGradient>
    <filter id="路径_5922" x="1" y="97" width="749" height="775" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="15" result="blur"/>
      <feFlood flood-color="#36ace5" flood-opacity="0.071"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-2" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#d0d2ef"/>
      <stop offset="1" stop-color="#e1e2f0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#c1dbfe"/>
      <stop offset="1" stop-color="#fff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#9cb8f4" stop-opacity="0.451"/>
      <stop offset="1" stop-color="#dfebfd" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" x1="0.5" x2="0.145" y2="0.945" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#a3bcff"/>
      <stop offset="1" stop-color="#7caaf2"/>
    </linearGradient>
  </defs>
  <g id="组_5037" data-name="组 5037" transform="translate(0 -121)">
    <rect id="矩形_2630" data-name="矩形 2630" width="750" height="1213" transform="translate(0 121)" fill="url(#linear-gradient)"/>
    <g transform="matrix(1, 0, 0, 1, 0, 121)" filter="url(#路径_5922)">
      <path id="路径_5922-2" data-name="路径 5922" d="M10,0H649a10,10,0,0,1,10,10V675a10,10,0,0,1-10,10H10A10,10,0,0,1,0,675V10A10,10,0,0,1,10,0Z" transform="translate(46 139)" fill="#fff"/>
    </g>
    <text id="请联系中心负责医生后_扫描中心二维码加入" data-name="请联系中心负责医生后
扫描中心二维码加入" transform="translate(52 391)" fill="#5860da" font-size="33" font-family="PingFang-SC-Medium, PingFang SC" font-weight="500"><tspan x="159" y="35">请联系中心负责医生后</tspan><tspan x="175.5" y="75">扫描中心二维码加入</tspan></text>
    <circle id="椭圆_1" data-name="椭圆 1" cx="25.5" cy="25.5" r="25.5" transform="translate(271 176)" fill="url(#linear-gradient-2)"/>
    <circle id="椭圆_6463" data-name="椭圆 6463" cx="14.5" cy="14.5" r="14.5" transform="translate(384 202)" opacity="0.996" fill="url(#linear-gradient-2)"/>
    <g id="组_5003" data-name="组 5003">
      <g id="_2.0" data-name="2.0" transform="translate(163.047 567.263)">
        <g id="空页面" transform="translate(-3 -27.941)">
          <g id="服务器错误" transform="translate(0 0)">
            <rect id="矩形备份-2" width="431" height="256" transform="translate(-0.047 -0.322)" fill="none"/>
            <path id="椭圆形" d="M427.812,190.785C377.7,128.608,297.546,92,211.475,92A272.759,272.759,0,0,0,3.236,188.257Z" transform="translate(2.275 64.63)" fill-rule="evenodd" fill="url(#linear-gradient-4)"/>
            <g id="_3.通用组件_彩色版_盆栽03备份-4" data-name="3.通用组件/彩色版/盆栽03备份-4" transform="translate(103.894 156.661)">
              <g id="分组" transform="translate(0 0)">
                <g id="Fill-1" transform="translate(6.406)">
                  <path id="路径_5436" data-name="路径 5436" d="M5.1,19.742H0V2.5A2.51,2.51,0,0,1,2.535.018h.027A2.51,2.51,0,0,1,5.1,2.5Z" transform="translate(-0.001 -0.018)" fill="#bae637" fill-rule="evenodd"/>
                </g>
                <path id="Fill-3" d="M10.35,18.792H5.24v-9A2.449,2.449,0,0,1,7.718,7.37h.153A2.449,2.449,0,0,1,10.35,9.789Z" transform="translate(3.685 5.152)" fill="#389e0d" fill-rule="evenodd"/>
                <path id="Fill-5" d="M6.324,16.639H1.18V6.851A2.459,2.459,0,0,1,3.665,4.418h.174A2.459,2.459,0,0,1,6.324,6.851Z" transform="translate(0.83 3.077)" fill="#7cb305" fill-rule="evenodd"/>
                <g id="Fill-7" transform="translate(0 19.142)">
                  <path id="路径_5437" data-name="路径 5437" d="M14.338,14.887H.958A.947.947,0,0,1,0,13.951v-13A.947.947,0,0,1,.958.015H14.338A.947.947,0,0,1,15.3.951v13a.947.947,0,0,1-.958.936" transform="translate(0 -0.015)" fill="#3b68b8" fill-rule="evenodd"/>
                </g>
              </g>
            </g>
            <g id="_3.通用组件_彩色版_盆栽03备份-5" data-name="3.通用组件/彩色版/盆栽03备份-5" transform="translate(338.676 161.768)">
              <g id="分组-2" data-name="分组" transform="translate(0 0)">
                <g id="Fill-1-2" data-name="Fill-1" transform="translate(6.006)">
                  <path id="路径_5438" data-name="路径 5438" d="M4.777,18.544H0V2.351A2.355,2.355,0,0,1,2.377.017H2.4A2.355,2.355,0,0,1,4.777,2.351Z" transform="translate(-0.001 -0.017)" fill="#bae637" fill-rule="evenodd"/>
                </g>
                <path id="Fill-3-2" data-name="Fill-3" d="M9.7,17.652H4.912V9.2A2.3,2.3,0,0,1,7.236,6.923h.144A2.3,2.3,0,0,1,9.7,9.2Z" transform="translate(3.454 4.84)" fill="#389e0d" fill-rule="evenodd"/>
                <path id="Fill-5-2" data-name="Fill-5" d="M5.928,15.629H1.106V6.435A2.308,2.308,0,0,1,3.436,4.15H3.6A2.308,2.308,0,0,1,5.928,6.435Z" transform="translate(0.778 2.89)" fill="#7cb305" fill-rule="evenodd"/>
                <g id="Fill-7-2" data-name="Fill-7" transform="translate(0 17.98)">
                  <path id="路径_5439" data-name="路径 5439" d="M13.442,13.984H.9A.889.889,0,0,1,0,13.1V.893A.889.889,0,0,1,.9.014H13.442a.889.889,0,0,1,.9.879V13.1a.889.889,0,0,1-.9.879" transform="translate(0 -0.014)" fill="#3b68b8" fill-rule="evenodd"/>
                </g>
              </g>
            </g>
            <g id="_3.通用组件_彩色版_盆栽03备份-6" data-name="3.通用组件/彩色版/盆栽03备份-6" transform="translate(286.136 178.81)">
              <g id="分组-3" data-name="分组" transform="translate(0 0)">
                <g id="Fill-1-3" data-name="Fill-1" transform="translate(8.675)">
                  <path id="路径_5440" data-name="路径 5440" d="M6.9,25.035H0V3.173A3.3,3.3,0,0,1,3.433.023h.036A3.3,3.3,0,0,1,6.9,3.173Z" transform="translate(-0.002 -0.023)" fill="#bae637" fill-rule="evenodd"/>
                </g>
                <path id="Fill-3-3" data-name="Fill-3" d="M14.015,23.83H7.1V12.414a3.221,3.221,0,0,1,3.356-3.067h.208a3.221,3.221,0,0,1,3.356,3.067Z" transform="translate(4.99 6.534)" fill="#389e0d" fill-rule="evenodd"/>
                <path id="Fill-5-3" data-name="Fill-5" d="M8.563,21.1H1.6V8.688A3.234,3.234,0,0,1,4.962,5.6H5.2A3.234,3.234,0,0,1,8.563,8.688Z" transform="translate(1.124 3.901)" fill="#7cb305" fill-rule="evenodd"/>
                <g id="Fill-7-3" data-name="Fill-7" transform="translate(0 24.274)">
                  <path id="路径_5441" data-name="路径 5441" d="M19.416,18.879H1.3A1.246,1.246,0,0,1,0,17.692V1.206A1.246,1.246,0,0,1,1.3.019h18.12a1.246,1.246,0,0,1,1.3,1.187V17.692a1.246,1.246,0,0,1-1.3,1.187" transform="translate(0 -0.019)" fill="#3b68b8" fill-rule="evenodd"/>
                </g>
              </g>
            </g>
            <path id="路径-46" d="M110.367,101.776,93.432,146.227h117.2l-21.139-44.451Z" transform="translate(65.7 71.505)" fill-rule="evenodd" fill="url(#linear-gradient-5)"/>
            <g id="椭圆形-2" data-name="椭圆形" transform="translate(147.891 51.07)">
              <ellipse id="椭圆_6367" data-name="椭圆 6367" cx="67.5" cy="67" rx="67.5" ry="67" transform="translate(0.062 -0.392)"/>
              <ellipse id="椭圆_6368" data-name="椭圆 6368" cx="67.5" cy="67" rx="67.5" ry="67" transform="translate(0.062 -0.392)" fill="url(#linear-gradient-6)"/>
            </g>
            <g id="路径-45" transform="translate(206.086 78.284)">
              <path id="路径_5442" data-name="路径 5442" d="M121,46h19.709l-2.838,55.652h-14.8Z" transform="translate(-121 -46)"/>
              <path id="路径_5443" data-name="路径 5443" d="M121,46h19.709l-2.838,55.652h-14.8Z" transform="translate(-121 -46)" fill="#fff"/>
            </g>
            <g id="椭圆形-3" data-name="椭圆形" transform="translate(206.236 139.586)">
              <circle id="椭圆_6369" data-name="椭圆 6369" cx="9.368" cy="9.368" r="9.368"/>
              <circle id="椭圆_6370" data-name="椭圆 6370" cx="9.368" cy="9.368" r="9.368" fill="#fff"/>
            </g>
          </g>
        </g>
      </g>
    </g>
    <text id="扫码提示" transform="translate(311 209)" fill="#5860da" font-size="32" font-family="PingFangSC-Semibold, PingFang SC" font-weight="600"><tspan x="0" y="0">扫码提示</tspan></text>
  </g>
</svg>
