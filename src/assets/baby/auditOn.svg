<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="415.257" height="246" viewBox="0 0 415.257 246">
  <defs>
    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#c1dbfe"/>
      <stop offset="1" stop-color="#fff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#b4cef5"/>
      <stop offset="1" stop-color="#97b9f3"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#95b3ff"/>
      <stop offset="1" stop-color="#608ded"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="0.97" y1="0.106" x2="0.03" y2="0.894" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#8bacfb"/>
      <stop offset="1" stop-color="#5f87da"/>
    </linearGradient>
  </defs>
  <g id="_2.0" data-name="2.0" transform="translate(4.027 20.048)">
    <g id="空页面" transform="translate(-4.067 -19.662)">
      <g id="搜索结果空" transform="translate(0 0)">
        <rect id="矩形备份" width="415" height="246" transform="translate(0.04 -0.386)" fill="none"/>
        <path id="椭圆形" d="M412.672,184.084C364.44,124.237,287.286,89,204.439,89A262.542,262.542,0,0,0,4,181.651Z" transform="translate(2.625 56.566)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
        <g id="_3.通用组件_彩色版_盆栽03备份-2" data-name="3.通用组件/彩色版/盆栽03备份-2" transform="translate(80.346 152.464)">
          <g id="分组" transform="translate(0 0)">
            <g id="Fill-1" transform="translate(9.634 0)">
              <path id="路径_5432" data-name="路径 5432" d="M7.664,30.064H0V3.812A3.8,3.8,0,0,1,3.813.028h.04A3.8,3.8,0,0,1,7.664,3.812Z" transform="translate(-0.002 -0.028)" fill="#bae637" fill-rule="evenodd"/>
            </g>
            <path id="Fill-3" d="M15.872,29.052H8.188V15.344a3.705,3.705,0,0,1,3.727-3.684h.231a3.705,3.705,0,0,1,3.727,3.684Z" transform="translate(5.235 7.409)" fill="#389e0d" fill-rule="evenodd"/>
            <path id="Fill-5" d="M9.58,25.6H1.844v-14.9A3.721,3.721,0,0,1,5.58,6.99h.262a3.721,3.721,0,0,1,3.737,3.7Z" transform="translate(1.179 4.423)" fill="#7cb305" fill-rule="evenodd"/>
            <g id="Fill-7" transform="translate(0 29.148)">
              <path id="路径_5433" data-name="路径 5433" d="M21.564,22.671H1.44A1.433,1.433,0,0,1,0,21.246V1.449A1.433,1.433,0,0,1,1.44.024H21.564A1.433,1.433,0,0,1,23,1.449v19.8a1.433,1.433,0,0,1-1.44,1.425" transform="translate(0 -0.024)" fill="#3b68b8" fill-rule="evenodd"/>
            </g>
          </g>
        </g>
        <g id="_3.通用组件_彩色版_盆栽03备份-3" data-name="3.通用组件/彩色版/盆栽03备份-3" transform="translate(127.691 135.757)">
          <g id="分组-2" data-name="分组" transform="translate(0 0)">
            <g id="Fill-1-2" data-name="Fill-1" transform="translate(5.781)">
              <path id="路径_5434" data-name="路径 5434" d="M4.6,17.85H0V2.263A2.267,2.267,0,0,1,2.288.017h.024A2.267,2.267,0,0,1,4.6,2.263Z" transform="translate(-0.001 -0.017)" fill="#bae637" fill-rule="evenodd"/>
            </g>
            <path id="Fill-3-2" data-name="Fill-3" d="M9.523,17.25H4.912V9.11A2.212,2.212,0,0,1,7.149,6.923h.138A2.212,2.212,0,0,1,9.523,9.11Z" transform="translate(3.141 4.399)" fill="#389e0d" fill-rule="evenodd"/>
            <path id="Fill-5-2" data-name="Fill-5" d="M5.748,15.2H1.106V6.35a2.221,2.221,0,0,1,2.242-2.2h.157a2.221,2.221,0,0,1,2.242,2.2Z" transform="translate(0.707 2.626)" fill="#7cb305" fill-rule="evenodd"/>
            <g id="Fill-7-2" data-name="Fill-7" transform="translate(0 17.307)">
              <path id="路径_5435" data-name="路径 5435" d="M12.939,13.461H.864A.855.855,0,0,1,0,12.615V.861A.855.855,0,0,1,.864.014H12.939A.855.855,0,0,1,13.8.861V12.615a.855.855,0,0,1-.864.846" transform="translate(0 -0.014)" fill="#3b68b8" fill-rule="evenodd"/>
            </g>
          </g>
        </g>
        <g id="编组-3" transform="translate(143.309 32.448)">
          <path id="路径-41" d="M9.375,0h98.61q12.868,3.635,12.868,27.989v99.883H39.921A19.673,19.673,0,0,1,20.249,108.2V27.989h0q0-11.576-1.461-16.463T9.375,0Z" transform="translate(5.379 0)" fill-rule="evenodd" fill="url(#linear-gradient-2)"/>
          <path id="路径-42" d="M49.375,62H151.017a11.476,11.476,0,0,1,11.476,11.476V116.1H65.54V81.548a16.637,16.637,0,0,0-3.554-10.174Q58.706,67.407,49.375,62Z" transform="translate(30.955 39.642)" fill-rule="evenodd" fill="url(#linear-gradient-3)"/>
          <path id="路径-43" d="M30.375,88.87a103.661,103.661,0,0,1,8.076-16.115C43.47,64.489,54.2,61,60.021,61s10.788,4.284,14.71,9.646q3.921,5.363,3.016,18.223Z" transform="translate(18.807 39.003)" fill="#5f87da" fill-rule="evenodd"/>
          <path id="路径-44" d="M.4,36.066H26.28q1.44-22.736-3.494-29.652T15.442.039Q5.669,3.607,2.906,11.2T.4,36.066Z" transform="translate(-0.375 0)" fill="#5f87da" fill-rule="evenodd"/>
          <line id="路径-36" x2="60.657" transform="translate(45.903 30.329)" fill="none" stroke="#fff" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/>
          <line id="路径-36备份-3" x2="37.706" transform="translate(45.903 48.362)" fill="none" stroke="#fff" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/>
          <line id="路径-36备份-4" x2="37.706" transform="translate(45.903 63.116)" fill="none" stroke="#fff" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/>
          <path id="矩形" d="M24.051,64.651l3.089,2.031a1.639,1.639,0,0,1,.469,2.27l-.006.009L18.858,82.074a1.639,1.639,0,0,1-2.265.46L13.5,80.5a1.639,1.639,0,0,1-.469-2.27l.006-.009,8.746-13.112A1.639,1.639,0,0,1,24.051,64.651Z" transform="translate(7.547 41.164)" fill-rule="evenodd" fill="url(#linear-gradient-4)"/>
          <g id="椭圆形-2" data-name="椭圆形" transform="translate(24.591 80.33)">
            <ellipse id="椭圆_6365" data-name="椭圆 6365" cx="15.574" cy="14.754" rx="15.574" ry="14.754" transform="translate(0 0)"/>
            <ellipse id="椭圆_6366" data-name="椭圆 6366" cx="15.574" cy="14.754" rx="15.574" ry="14.754" transform="translate(0 0)" fill="none" stroke="#d7e7ff" stroke-width="3"/>
          </g>
        </g>
      </g>
    </g>
  </g>
</svg>
