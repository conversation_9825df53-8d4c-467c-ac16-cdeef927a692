<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="312" height="312" viewBox="0 0 312 312">
  <defs>
    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1.057" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff" stop-opacity="0"/>
      <stop offset="0.455" stop-color="#fff" stop-opacity="0.455"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-9" x1="0.5" y1="0.006" x2="0.5" y2="0.93" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffc58d"/>
      <stop offset="1" stop-color="#fe581b"/>
    </linearGradient>
    <linearGradient id="linear-gradient-10" x1="0.3" y1="0.145" x2="0.67" y2="0.879" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffef7c"/>
      <stop offset="1" stop-color="#ffcd35"/>
    </linearGradient>
    <linearGradient id="linear-gradient-11" x1="0.74" y1="0.555" x2="0.202" y2="0.458" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffdd58"/>
      <stop offset="1" stop-color="#ffaf35"/>
    </linearGradient>
    <linearGradient id="linear-gradient-12" x1="0.562" y1="0.711" x2="0.667" y2="0.913" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffef7c" stop-opacity="0"/>
      <stop offset="1" stop-color="#ffa780"/>
    </linearGradient>
    <linearGradient id="linear-gradient-13" x1="0.58" y1="0.283" x2="0.58" y2="0.974" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffef7c" stop-opacity="0"/>
      <stop offset="1" stop-color="#ffaf00"/>
    </linearGradient>
    <linearGradient id="linear-gradient-14" x1="0.74" y1="0.873" x2="0.202" y2="0.214" xlink:href="#linear-gradient-10"/>
    <linearGradient id="linear-gradient-15" x1="0.349" y1="0.504" x2="0.306" y2="0.439" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffef7c" stop-opacity="0"/>
      <stop offset="1" stop-color="#fffada"/>
    </linearGradient>
    <linearGradient id="linear-gradient-16" x1="0.239" y1="0.859" x2="0.239" y2="-0.11" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff290"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-17" x1="0.554" y1="0.877" x2="0.554" y2="1.026" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="1" stop-color="#ffe3c6"/>
    </linearGradient>
    <linearGradient id="linear-gradient-18" x1="0.509" y1="0.877" x2="0.48" y2="1" xlink:href="#linear-gradient-17"/>
    <linearGradient id="linear-gradient-19" x1="0.5" x2="0.5" y2="0.977" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff7d4"/>
      <stop offset="1" stop-color="#fa8812"/>
    </linearGradient>
  </defs>
  <g id="组_5238" data-name="组 5238" transform="translate(-921 -2452)">
    <g id="组_5237" data-name="组 5237">
      <g id="组_5047" data-name="组 5047" transform="translate(702 2104)">
        <g id="APP" transform="translate(219 340)">
          <g id="切图" transform="translate(0 8)">
            <g id="编组-4">
              <g id="编组-2" opacity="0.67">
                <g id="_-mockplus-" data-name="-mockplus-" transform="translate(144.63)">
                  <g id="矩形">
                    <path id="路径_9075" data-name="路径 9075" d="M0,0H22.741V312H0Z" fill-rule="evenodd" fill="url(#linear-gradient)"/>
                  </g>
                </g>
                <g id="_-mockplus--2" data-name="-mockplus-" transform="translate(84.14 14.649)">
                  <g id="矩形备份-7" transform="translate(0 8.85) rotate(-23)">
                    <path id="矩形-2" data-name="矩形" d="M0,0H22.65V312.071H0Z" fill-rule="evenodd" fill="url(#linear-gradient)"/>
                  </g>
                </g>
                <g id="_-mockplus--3" data-name="-mockplus-" transform="translate(35.664 38.167)">
                  <g id="矩形备份-2" transform="translate(220.668) rotate(45)">
                    <path id="矩形-3" data-name="矩形" d="M0,0H22.65V312.071H0Z" fill-rule="evenodd" fill="url(#linear-gradient)"/>
                  </g>
                </g>
                <g id="_-mockplus--4" data-name="-mockplus-" transform="translate(2.436 89.94)">
                  <g id="矩形备份-8" transform="matrix(0.375, 0.927, -0.927, 0.375, 289.346, 0)">
                    <path id="矩形-4" data-name="矩形" d="M0,0H22.65V312.071H0Z" fill-rule="evenodd" fill="url(#linear-gradient)"/>
                  </g>
                </g>
                <g id="_-mockplus--5" data-name="-mockplus-" transform="translate(6.092 87.561)">
                  <g id="矩形备份-4" transform="translate(296.111 121.935) rotate(113)">
                    <path id="矩形-5" data-name="矩形" d="M0,0H22.65V312.071H0Z" fill-rule="evenodd" fill="url(#linear-gradient)"/>
                  </g>
                </g>
                <g id="_-mockplus--6" data-name="-mockplus-" transform="translate(0 144.63)">
                  <g id="矩形备份" transform="translate(0 22.741) rotate(-90)">
                    <path id="矩形-6" data-name="矩形" d="M0,0H22.741V312H0Z" fill-rule="evenodd" fill="url(#linear-gradient)"/>
                  </g>
                </g>
                <g id="_-mockplus--7" data-name="-mockplus-" transform="translate(35.665 38.167)">
                  <g id="矩形备份-3" transform="translate(0 16.016) rotate(-45)">
                    <path id="矩形-7" data-name="矩形" d="M0,0H22.65V312.071H0Z" fill-rule="evenodd" fill="url(#linear-gradient)"/>
                  </g>
                </g>
                <g id="_-mockplus--8" data-name="-mockplus-" transform="translate(86.328 0.408)">
                  <g id="矩形备份-5" transform="matrix(0.927, 0.375, -0.375, 0.927, 116.905, 0)">
                    <path id="矩形-8" data-name="矩形" d="M0,0H22.65V312.071H0Z" fill-rule="evenodd" fill="url(#linear-gradient)"/>
                  </g>
                </g>
              </g>
              <g id="_-mockplus--9" data-name="-mockplus-" transform="translate(87.132 61.854)">
                <g id="编组-10备份">
                  <g id="编组-10">
                    <rect id="矩形备份-9" width="109" height="25" rx="10.5" transform="translate(14.867 5.146)" fill="#dd8323"/>
                    <path id="矩形-9" data-name="矩形" d="M35,19h95.51V52.656a21.831,21.831,0,0,1-21.831,21.831H56.831A21.831,21.831,0,0,1,35,52.656Z" transform="translate(-13.887 -1.717)" fill-rule="evenodd" fill="url(#linear-gradient-9)"/>
                    <rect id="矩形-10" data-name="矩形" width="24" height="36" transform="translate(37.867 18.146)" fill="#fff" opacity="0.358"/>
                    <rect id="矩形备份-15" width="24" height="36" transform="translate(76.867 18.146)" fill="#fff" opacity="0.358"/>
                    <path id="多边形" d="M89.754,36.255,141.793,66.3a17.283,17.283,0,0,1,8.641,14.967v60.09a17.283,17.283,0,0,1-8.641,14.967L89.754,186.37a17.283,17.283,0,0,1-17.283,0l-52.04-30.045a17.283,17.283,0,0,1-8.641-14.967V81.267A17.283,17.283,0,0,1,20.431,66.3l52.04-30.045a17.283,17.283,0,0,1,17.283,0Z" transform="translate(-11.79 -3.067)" fill-rule="evenodd" fill="url(#linear-gradient-10)"/>
                    <path id="形状结合" d="M91.541,47.315l40.617,23.45A17.283,17.283,0,0,1,140.8,85.733v3.638A17.285,17.285,0,0,0,132.156,74.4l0,0L91.541,50.954a17.283,17.283,0,0,0-17.283,0h0L33.641,74.4A17.283,17.283,0,0,0,25,89.372h0V85.733a17.283,17.283,0,0,1,8.641-14.967l40.617-23.45A17.283,17.283,0,0,1,91.541,47.315Z" transform="translate(-12.984 -4.067)" fill-rule="evenodd" opacity="0.871" fill="url(#linear-gradient-11)"/>
                    <path id="多边形备份-2" d="M89.754,36.255,141.793,66.3a17.283,17.283,0,0,1,8.641,14.967v60.09a17.283,17.283,0,0,1-8.641,14.967L89.754,186.37a17.283,17.283,0,0,1-17.283,0l-52.04-30.045a17.283,17.283,0,0,1-8.641-14.967V81.267A17.283,17.283,0,0,1,20.431,66.3l52.04-30.045a17.283,17.283,0,0,1,17.283,0Z" transform="translate(-11.79 -3.067)" fill-rule="evenodd" fill="url(#linear-gradient-12)"/>
                    <path id="形状结合-2" data-name="形状结合" d="M89.964,36.315,142,66.361a17.283,17.283,0,0,1,8.641,14.967v60.09A17.283,17.283,0,0,1,142,156.385L89.96,186.43a17.283,17.283,0,0,1-17.283,0l-52.04-30.045A17.283,17.283,0,0,1,12,141.418V81.328a17.283,17.283,0,0,1,8.641-14.967l52.04-30.045A17.283,17.283,0,0,1,89.964,36.315Zm0,5.458a17.283,17.283,0,0,0-17.283,0h0L25.368,69.089a17.283,17.283,0,0,0-8.641,14.967h0v54.632a17.283,17.283,0,0,0,8.641,14.967h0l47.313,27.316a17.283,17.283,0,0,0,17.283,0h0l47.313-27.316a17.283,17.283,0,0,0,8.641-14.967h0V84.057a17.283,17.283,0,0,0-8.641-14.967h0Z" transform="translate(-11.809 -3.073)" fill-rule="evenodd" fill="url(#linear-gradient-13)"/>
                    <path id="多边形备份" d="M91.389,51.255l40.617,23.45a17.283,17.283,0,0,1,8.641,14.967v46.9a17.283,17.283,0,0,1-8.641,14.967l-40.617,23.45a17.283,17.283,0,0,1-17.283,0L33.489,151.54a17.283,17.283,0,0,1-8.641-14.967v-46.9a17.283,17.283,0,0,1,8.641-14.967l40.617-23.45a17.283,17.283,0,0,1,17.283,0Z" transform="translate(-12.97 -4.423)" fill-rule="evenodd" fill="url(#linear-gradient-14)"/>
                    <path id="形状结合-3" data-name="形状结合" d="M91.541,50.315l40.617,23.45A17.283,17.283,0,0,1,140.8,88.733v4.548a17.283,17.283,0,0,0-8.641-14.967h0L91.541,54.864a17.283,17.283,0,0,0-17.283,0h0L33.641,78.314A17.283,17.283,0,0,0,25,93.281h0V88.733a17.283,17.283,0,0,1,8.641-14.967l40.617-23.45a17.283,17.283,0,0,1,17.283,0Z" transform="translate(-12.984 -4.338)" fill-rule="evenodd" fill="url(#linear-gradient-15)"/>
                    <path id="形状结合-4" data-name="形状结合" d="M25.908,88.361v46.063a17.283,17.283,0,0,0,8.641,14.967h0l40.617,23.45a17.283,17.283,0,0,0,17.283,0h0l40.617-23.45a17.282,17.282,0,0,0,7.732-9.435v.835a17.283,17.283,0,0,1-8.641,14.967L91.54,179.208a17.283,17.283,0,0,1-17.283,0L33.64,155.758A17.283,17.283,0,0,1,25,140.791V93.89a17.272,17.272,0,0,1,.909-5.532Z" transform="translate(-12.984 -7.986)" fill-rule="evenodd" opacity="0.59" fill="url(#linear-gradient-16)"/>
                    <g id="编组" transform="translate(27.48 67.312)">
                      <rect id="矩形-11" data-name="矩形" width="73" height="74" transform="translate(0.388 -0.166)" fill="rgba(255,255,255,0.01)"/>
                      <rect id="矩形-12" data-name="矩形" width="73" height="74" transform="translate(0.388 -0.166)" fill="rgba(255,255,255,0.01)"/>
                      <g id="路径" transform="translate(28.198 10.006)">
                        <path id="路径_9076" data-name="路径 9076" d="M50.5,30.42V19.32A8.34,8.34,0,0,0,42.141,11L32.767,30.685A18.192,18.192,0,0,0,31,38.507v20.7a7.277,7.277,0,0,0,7.277,7.277H63.365a5.565,5.565,0,0,0,5.571-4.717L72.779,36.8a5.556,5.556,0,0,0-5.571-6.381Z" transform="translate(-31 -11)"/>
                        <path id="路径_9077" data-name="路径 9077" d="M50.5,30.42V19.32A8.34,8.34,0,0,0,42.141,11L32.767,30.685A18.192,18.192,0,0,0,31,38.507v20.7a7.277,7.277,0,0,0,7.277,7.277H63.365a5.565,5.565,0,0,0,5.571-4.717L72.779,36.8a5.556,5.556,0,0,0-5.571-6.381Z" transform="translate(-31 -11)" fill="url(#linear-gradient-17)"/>
                      </g>
                      <g id="路径-2" data-name="路径" transform="translate(12.735 34.566)">
                        <path id="路径_9078" data-name="路径 9078" d="M19.934,38h0C16.951,37.943,14.4,40.644,14,43.876V63.359a6.073,6.073,0,0,0,5.934,5.567,6.8,6.8,0,0,0,6.8-6.8V44.8A6.8,6.8,0,0,0,19.934,38Z" transform="translate(-14 -38)"/>
                        <path id="路径_9079" data-name="路径 9079" d="M19.934,38h0C16.951,37.943,14.4,40.644,14,43.876V63.359a6.073,6.073,0,0,0,5.934,5.567,6.8,6.8,0,0,0,6.8-6.8V44.8A6.8,6.8,0,0,0,19.934,38Z" transform="translate(-14 -38)" fill="#fff"/>
                        <path id="路径_9080" data-name="路径 9080" d="M19.934,38h0C16.951,37.943,14.4,40.644,14,43.876V63.359a6.073,6.073,0,0,0,5.934,5.567,6.8,6.8,0,0,0,6.8-6.8V44.8A6.8,6.8,0,0,0,19.934,38Z" transform="translate(-14 -38)" fill="url(#linear-gradient-18)"/>
                      </g>
                    </g>
                    <rect id="矩形-13" data-name="矩形" width="109" height="24" rx="10.5" transform="translate(14.867 0.146)" fill="url(#linear-gradient-19)"/>
                  </g>
                </g>
              </g>
            </g>
          </g>
        </g>
      </g>
    </g>
  </g>
</svg>
