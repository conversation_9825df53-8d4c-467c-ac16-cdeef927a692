<?xml version="1.0" encoding="UTF-8"?>
<svg width="46px" height="38px" viewBox="0 0 46 38" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon-账号绑定</title>
    <defs>
        <filter x="-8.6%" y="-32.2%" width="117.2%" height="164.3%" filterUnits="objectBoundingBox" id="filter-1">
            <feOffset dx="0" dy="11" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="10" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.345098039   0 0 0 0 0.376470588   0 0 0 0 0.854901961  0 0 0 0.0660699164 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="我的" transform="translate(-433.000000, -869.000000)">
            <rect fill="#FAFAFA" x="0" y="0" width="750" height="1624"></rect>
            <g id="编组" transform="translate(409.000000, 825.000000)" fill="#FFFFFF" fill-rule="nonzero">
                <rect id="矩形_2705-2" x="0.5" y="0" width="100" height="100" rx="12"></rect>
            </g>
            <g id="编组" filter="url(#filter-1)" transform="translate(30.000000, 825.000000)" fill="#FFFFFF" fill-rule="nonzero">
                <rect id="矩形_2708-2" x="0" y="0" width="691" height="185" rx="12"></rect>
            </g>
            <g id="icon-账号绑定" transform="translate(433.000000, 869.000000)" fill="#41B592" fill-rule="nonzero">
                <path d="M17,23.0207031 L17.1208333,23.0207031 L17,23.0207031 Z M30.5333333,8 L38.2666667,8 L38.2666667,11.75 L30.5333333,11.75 L30.5333333,8 Z M30.5333333,15.5 L38.2666667,15.5 L38.2666667,19.25 L30.5333333,19.25 L30.5333333,15.5 Z M41.740625,26.9492188 C41.486875,26.8789062 41.2270833,26.8320312 40.9552083,26.796875 C40.7075,26.7675781 40.4597917,26.75 40.2060417,26.75 L35.5358333,26.75 L31.6691667,30.5 L31.663125,30.5 L27.7964583,34.25 L24.7333333,34.25 C23.67,34.25 22.8,33.40625 22.8,32.375 C22.8,31.34375 23.67,30.5 24.7333333,30.5 L30.720625,30.5 L34.5872917,26.75 L24.7333333,26.75 C21.53125,26.75 18.9333333,29.2695312 18.9333333,32.375 C18.9333333,34.9648438 20.7397917,37.1445312 23.1927083,37.8007812 C23.4464583,37.8710938 23.70625,37.9179688 23.978125,37.953125 C24.2258333,37.9824219 24.4735417,38 24.7272917,38 L29.3975,38 L33.2641667,34.25 L33.2702083,34.25 L37.136875,30.5 L40.2,30.5 C41.2633333,30.5 42.1333333,31.34375 42.1333333,32.375 C42.1333333,33.40625 41.2633333,34.25 40.2,34.25 L34.2127083,34.25 L30.3460417,38 L40.2,38 C43.4020833,38 46,35.4804688 46,32.375 C46,29.7851562 44.1935417,27.6054688 41.740625,26.9492188 Z" id="Shape"></path>
                <path d="M0,4 L0,34 C-6.17636919e-16,36.209139 1.790861,38 4,38 L15.3333333,38 L15.3333333,38 L15.3333333,34.2 L7.834375,34.2 C7.7265625,33.618125 7.66666667,33.024375 7.66666667,32.41875 C7.66666667,29.8478125 8.67890625,27.43125 10.5057292,25.614375 C12.3085938,23.8271875 14.6984375,22.8296875 17.25,22.8 L26.809375,22.8 C25.7252604,21.7609375 24.4734375,20.8940625 23.0958333,20.25875 C24.3057292,18.916875 25.0364583,17.1415625 25.0364583,15.2 C25.0364583,11.0021875 21.6044271,7.6 17.3697917,7.6 C13.1351563,7.6 9.703125,11.0021875 9.703125,15.2 C9.703125,17.1415625 10.4398437,18.9109375 11.64375,20.25875 C7.03177083,22.39625 3.83333333,27.0334375 3.83333333,32.41875 C3.83333333,33.024375 3.87526042,33.618125 3.953125,34.2 L3.83333333,34.2 L3.83333333,3.8 L42.1666667,3.8 L42.1666667,22.8 L46,22.8 L46,4 C46,1.790861 44.209139,-4.05812251e-16 42,0 L4,0 C1.790861,4.05812251e-16 -2.705415e-16,1.790861 0,4 Z M17.3697917,11.4 C19.4841146,11.4 21.203125,13.1040625 21.203125,15.2 C21.203125,17.2959375 19.4841146,19 17.3697917,19 C15.2554688,19 13.5364583,17.2959375 13.5364583,15.2 C13.5364583,13.1040625 15.2554688,11.4 17.3697917,11.4 Z" id="Shape"></path>
            </g>
        </g>
    </g>
</svg>