<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="225.733" height="200.723" viewBox="0 0 225.733 200.723">
  <defs>
    <linearGradient id="linear-gradient" x1="0.5" y1="1" x2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ff5f18"/>
      <stop offset="0.07" stop-color="#ff691b"/>
      <stop offset="0.309" stop-color="#ff8625"/>
      <stop offset="0.547" stop-color="#ff9b2c"/>
      <stop offset="0.779" stop-color="#ffa830"/>
      <stop offset="1" stop-color="#ffac31"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="6.575" y1="-0.345" x2="6.214" y2="0.896" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#a7c1ff"/>
      <stop offset="0.027" stop-color="#adc5ff"/>
      <stop offset="0.187" stop-color="#cadaff"/>
      <stop offset="0.357" stop-color="#e2eaff"/>
      <stop offset="0.539" stop-color="#f2f6ff"/>
      <stop offset="0.74" stop-color="#fcfdff"/>
      <stop offset="1" stop-color="#fff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="22.184" y1="0.5" x2="23.184" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#2c49a2"/>
      <stop offset="1"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="0.001" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#a7c1ff"/>
      <stop offset="1" stop-color="#fff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="19.436" y1="0.5" x2="20.435" y2="0.5" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-6" x1="-0.4" y1="0.687" x2="1.242" y2="0.287" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-opacity="0.6"/>
      <stop offset="0.006" stop-color="#010102" stop-opacity="0.604"/>
      <stop offset="0.105" stop-color="#0c1329" stop-opacity="0.643"/>
      <stop offset="0.21" stop-color="#15224a" stop-opacity="0.682"/>
      <stop offset="0.323" stop-color="#1d2f65" stop-opacity="0.729"/>
      <stop offset="0.446" stop-color="#23397a" stop-opacity="0.78"/>
      <stop offset="0.583" stop-color="#284089" stop-opacity="0.835"/>
      <stop offset="0.746" stop-color="#2a4491" stop-opacity="0.898"/>
      <stop offset="1" stop-color="#2b4594"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" x1="-6.955" y1="-11.164" x2="-6.955" y2="-10.718" gradientUnits="objectBoundingBox">
      <stop offset="0"/>
      <stop offset="0.204" stop-color="#0a1124"/>
      <stop offset="0.579" stop-color="#1c2d60"/>
      <stop offset="0.855" stop-color="#273e86"/>
      <stop offset="1" stop-color="#2b4594"/>
    </linearGradient>
    <linearGradient id="linear-gradient-8" x1="0.5" y1="0" x2="0.5" y2="0.999" xlink:href="#linear-gradient-7"/>
    <linearGradient id="linear-gradient-9" x1="1" y1="0.5" x2="0" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="0.173" stop-color="#eaf1ff"/>
      <stop offset="0.529" stop-color="#c6d7ff"/>
      <stop offset="0.817" stop-color="#afc7ff"/>
      <stop offset="1" stop-color="#a7c1ff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-10" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-11" x1="0.292" y1="1" x2="0.292" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ff5f18"/>
      <stop offset="0.394" stop-color="#ff7b21"/>
      <stop offset="1" stop-color="#ffac31"/>
    </linearGradient>
    <linearGradient id="linear-gradient-12" x1="-4.086" y1="0.499" x2="-3.086" y2="0.499" xlink:href="#linear-gradient-7"/>
    <linearGradient id="linear-gradient-13" x1="-6.052" y1="0.5" x2="-5.052" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#2c49a2"/>
      <stop offset="0.063" stop-color="#2b479c"/>
      <stop offset="0.459" stop-color="#273c7a"/>
      <stop offset="0.785" stop-color="#253565"/>
      <stop offset="1" stop-color="#24325d"/>
    </linearGradient>
    <linearGradient id="linear-gradient-14" x1="-2.229" y1="0.499" x2="-1.228" y2="0.499" xlink:href="#linear-gradient-7"/>
    <linearGradient id="linear-gradient-15" x1="-3.946" y1="0" x2="-3.946" y2="1" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-16" x1="-2.113" y1="1" x2="-2.113" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ff5f18"/>
      <stop offset="0.154" stop-color="#ff701d"/>
      <stop offset="0.507" stop-color="#ff9028"/>
      <stop offset="0.802" stop-color="#ffa52f"/>
      <stop offset="1" stop-color="#ffac31"/>
    </linearGradient>
    <linearGradient id="linear-gradient-17" x1="-0.336" y1="1" x2="-0.336" y2="0.001" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ff5f18"/>
      <stop offset="0.484" stop-color="#ff8625"/>
      <stop offset="1" stop-color="#ffac31"/>
    </linearGradient>
    <linearGradient id="linear-gradient-18" x1="-0.001" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-11"/>
    <linearGradient id="linear-gradient-19" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-11"/>
  </defs>
  <g id="_20" data-name="20" transform="translate(-19.362 -76.505)">
    <g id="Layer_2" transform="translate(19.362 76.505)">
      <g id="组_4766" data-name="组 4766" transform="translate(11.965 111.323)">
        <path id="路径_5704" data-name="路径 5704" d="M287.778,1357.633H211.6a4.987,4.987,0,0,1-5.006-5.1l.535-30.242a5.2,5.2,0,0,1,5.185-5.1h76.177a4.987,4.987,0,0,1,5.006,5.1l-.535,30.242A5.2,5.2,0,0,1,287.778,1357.633Z" transform="translate(-206.593 -1317.2)" fill="#628cd2"/>
        <g id="组_4765" data-name="组 4765" transform="translate(7.538 5.123)">
          <g id="组_4743" data-name="组 4743">
            <g id="组_4739" data-name="组 4739" transform="translate(0.318)">
              <g id="组_4737" data-name="组 4737" transform="translate(0.229 0.332)">
                <path id="路径_5705" data-name="路径 5705" d="M280.349,1361.658a5.93,5.93,0,0,0-4.458-1.63,6.2,6.2,0,0,0-5.822,6.395,5.968,5.968,0,0,0,1.325,3.529Z" transform="translate(-270.065 -1360.016)" fill="#628cd2"/>
                <path id="路径_5706" data-name="路径 5706" d="M297.049,1383.272a6.008,6.008,0,0,0-1.057-3.172l-8.892,8.242a5.954,5.954,0,0,0,4.115,1.325A6.187,6.187,0,0,0,297.049,1383.272Z" transform="translate(-284.93 -1377.542)" fill="#628cd2"/>
              </g>
              <g id="组_4738" data-name="组 4738">
                <path id="路径_5707" data-name="路径 5707" d="M278.281,1359a5.748,5.748,0,0,0-4.344-1.58,6.029,6.029,0,0,0-5.669,6.229,5.867,5.867,0,0,0,1.287,3.439Z" transform="translate(-268.264 -1357.414)" fill="#fff"/>
                <path id="路径_5708" data-name="路径 5708" d="M294.581,1379.883a5.829,5.829,0,0,0-1.032-3.083l-8.65,8.013a5.79,5.79,0,0,0,4.013,1.287A6.008,6.008,0,0,0,294.581,1379.883Z" transform="translate(-282.781 -1374.33)" fill="#fff"/>
              </g>
            </g>
            <g id="组_4742" data-name="组 4742" transform="translate(0 17.719)">
              <g id="组_4740" data-name="组 4740" transform="translate(0.229 0.332)">
                <path id="路径_5709" data-name="路径 5709" d="M277.849,1500.757a5.932,5.932,0,0,0-4.458-1.63,6.2,6.2,0,0,0-5.822,6.395,5.967,5.967,0,0,0,1.325,3.529Z" transform="translate(-267.565 -1499.116)" fill="#628cd2"/>
                <path id="路径_5710" data-name="路径 5710" d="M294.649,1522.372a6.009,6.009,0,0,0-1.057-3.172l-8.892,8.242a5.954,5.954,0,0,0,4.115,1.325A6.205,6.205,0,0,0,294.649,1522.372Z" transform="translate(-282.517 -1516.642)" fill="#628cd2"/>
              </g>
              <g id="组_4741" data-name="组 4741">
                <path id="路径_5711" data-name="路径 5711" d="M275.781,1498.1a5.748,5.748,0,0,0-4.344-1.579,6.029,6.029,0,0,0-5.669,6.229,5.867,5.867,0,0,0,1.287,3.439Z" transform="translate(-265.764 -1496.514)" fill="#fff"/>
                <path id="路径_5712" data-name="路径 5712" d="M292.181,1518.983a5.829,5.829,0,0,0-1.032-3.083l-8.65,8.013a5.79,5.79,0,0,0,4.013,1.286A6.009,6.009,0,0,0,292.181,1518.983Z" transform="translate(-280.368 -1513.43)" fill="#fff"/>
              </g>
            </g>
          </g>
          <g id="组_4750" data-name="组 4750" transform="translate(19.719)">
            <g id="组_4746" data-name="组 4746" transform="translate(0.318)">
              <g id="组_4744" data-name="组 4744" transform="translate(0.229 0.332)">
                <path id="路径_5713" data-name="路径 5713" d="M435.149,1361.658a5.93,5.93,0,0,0-4.459-1.63,6.2,6.2,0,0,0-5.822,6.395,5.968,5.968,0,0,0,1.325,3.529Z" transform="translate(-424.865 -1360.016)" fill="#628cd2"/>
                <path id="路径_5714" data-name="路径 5714" d="M451.849,1383.272a6.008,6.008,0,0,0-1.057-3.172l-8.892,8.242a5.954,5.954,0,0,0,4.115,1.325A6.187,6.187,0,0,0,451.849,1383.272Z" transform="translate(-439.73 -1377.542)" fill="#628cd2"/>
              </g>
              <g id="组_4745" data-name="组 4745">
                <path id="路径_5715" data-name="路径 5715" d="M433.081,1359a5.748,5.748,0,0,0-4.344-1.58,6.029,6.029,0,0,0-5.669,6.229,5.866,5.866,0,0,0,1.287,3.439Z" transform="translate(-423.064 -1357.414)" fill="#fff"/>
                <path id="路径_5716" data-name="路径 5716" d="M449.381,1379.883a5.829,5.829,0,0,0-1.032-3.083l-8.65,8.013a5.79,5.79,0,0,0,4.013,1.287A6.017,6.017,0,0,0,449.381,1379.883Z" transform="translate(-437.581 -1374.33)" fill="#fff"/>
              </g>
            </g>
            <g id="组_4749" data-name="组 4749" transform="translate(0 17.719)">
              <g id="组_4747" data-name="组 4747" transform="translate(0.229 0.332)">
                <path id="路径_5717" data-name="路径 5717" d="M432.649,1500.757a5.932,5.932,0,0,0-4.458-1.63,6.2,6.2,0,0,0-5.822,6.395,5.967,5.967,0,0,0,1.325,3.529Z" transform="translate(-422.365 -1499.116)" fill="#628cd2"/>
                <path id="路径_5718" data-name="路径 5718" d="M449.349,1522.372a6.008,6.008,0,0,0-1.057-3.172l-8.892,8.242a5.954,5.954,0,0,0,4.115,1.325A6.187,6.187,0,0,0,449.349,1522.372Z" transform="translate(-437.23 -1516.642)" fill="#628cd2"/>
              </g>
              <g id="组_4748" data-name="组 4748">
                <path id="路径_5719" data-name="路径 5719" d="M430.581,1498.1a5.748,5.748,0,0,0-4.344-1.579,6.029,6.029,0,0,0-5.669,6.229,5.866,5.866,0,0,0,1.287,3.439Z" transform="translate(-420.564 -1496.514)" fill="#fff"/>
                <path id="路径_5720" data-name="路径 5720" d="M446.881,1518.983a5.829,5.829,0,0,0-1.032-3.083l-8.65,8.013a5.79,5.79,0,0,0,4.013,1.286A5.992,5.992,0,0,0,446.881,1518.983Z" transform="translate(-435.081 -1513.43)" fill="#fff"/>
              </g>
            </g>
          </g>
          <g id="组_4757" data-name="组 4757" transform="translate(39.439)">
            <g id="组_4753" data-name="组 4753" transform="translate(0.318)">
              <g id="组_4751" data-name="组 4751" transform="translate(0.229 0.332)">
                <path id="路径_5721" data-name="路径 5721" d="M589.949,1361.658a5.93,5.93,0,0,0-4.458-1.63,6.2,6.2,0,0,0-5.822,6.395,5.968,5.968,0,0,0,1.325,3.529Z" transform="translate(-579.665 -1360.016)" fill="#628cd2"/>
                <path id="路径_5722" data-name="路径 5722" d="M606.649,1383.272a6.008,6.008,0,0,0-1.057-3.172l-8.892,8.242a5.954,5.954,0,0,0,4.115,1.325A6.2,6.2,0,0,0,606.649,1383.272Z" transform="translate(-594.53 -1377.542)" fill="#628cd2"/>
              </g>
              <g id="组_4752" data-name="组 4752">
                <path id="路径_5723" data-name="路径 5723" d="M587.881,1359a5.748,5.748,0,0,0-4.344-1.58,6.029,6.029,0,0,0-5.669,6.229,5.866,5.866,0,0,0,1.287,3.439Z" transform="translate(-577.864 -1357.414)" fill="#fff"/>
                <path id="路径_5724" data-name="路径 5724" d="M604.181,1379.883a5.829,5.829,0,0,0-1.032-3.083l-8.65,8.013a5.79,5.79,0,0,0,4.013,1.287A6.017,6.017,0,0,0,604.181,1379.883Z" transform="translate(-592.381 -1374.33)" fill="#fff"/>
              </g>
            </g>
            <g id="组_4756" data-name="组 4756" transform="translate(0 17.719)">
              <g id="组_4754" data-name="组 4754" transform="translate(0.229 0.332)">
                <path id="路径_5725" data-name="路径 5725" d="M587.449,1500.757a5.932,5.932,0,0,0-4.458-1.63,6.2,6.2,0,0,0-5.822,6.395,5.968,5.968,0,0,0,1.325,3.529Z" transform="translate(-577.165 -1499.116)" fill="#628cd2"/>
                <path id="路径_5726" data-name="路径 5726" d="M604.149,1522.372a6.009,6.009,0,0,0-1.057-3.172l-8.892,8.242a5.954,5.954,0,0,0,4.115,1.325A6.187,6.187,0,0,0,604.149,1522.372Z" transform="translate(-592.03 -1516.642)" fill="#628cd2"/>
              </g>
              <g id="组_4755" data-name="组 4755">
                <path id="路径_5727" data-name="路径 5727" d="M585.381,1498.1a5.748,5.748,0,0,0-4.344-1.579,6.029,6.029,0,0,0-5.669,6.229,5.865,5.865,0,0,0,1.287,3.439Z" transform="translate(-575.364 -1496.514)" fill="#fff"/>
                <path id="路径_5728" data-name="路径 5728" d="M601.681,1518.983a5.83,5.83,0,0,0-1.032-3.083l-8.65,8.013a5.79,5.79,0,0,0,4.013,1.286A6.009,6.009,0,0,0,601.681,1518.983Z" transform="translate(-589.881 -1513.43)" fill="#fff"/>
              </g>
            </g>
          </g>
          <g id="组_4764" data-name="组 4764" transform="translate(59.158)">
            <g id="组_4760" data-name="组 4760" transform="translate(0.318)">
              <g id="组_4758" data-name="组 4758" transform="translate(0.229 0.332)">
                <path id="路径_5729" data-name="路径 5729" d="M744.749,1361.658a5.93,5.93,0,0,0-4.459-1.63,6.2,6.2,0,0,0-5.822,6.395,5.968,5.968,0,0,0,1.325,3.529Z" transform="translate(-734.465 -1360.016)" fill="#628cd2"/>
                <path id="路径_5730" data-name="路径 5730" d="M761.449,1383.272a6.008,6.008,0,0,0-1.057-3.172l-8.892,8.242a5.954,5.954,0,0,0,4.115,1.325A6.2,6.2,0,0,0,761.449,1383.272Z" transform="translate(-749.33 -1377.542)" fill="#628cd2"/>
              </g>
              <g id="组_4759" data-name="组 4759">
                <path id="路径_5731" data-name="路径 5731" d="M742.681,1359a5.748,5.748,0,0,0-4.344-1.58,6.029,6.029,0,0,0-5.669,6.229,5.867,5.867,0,0,0,1.287,3.439Z" transform="translate(-732.664 -1357.414)" fill="#fff"/>
                <path id="路径_5732" data-name="路径 5732" d="M758.981,1379.883a5.828,5.828,0,0,0-1.032-3.083l-8.65,8.013a5.79,5.79,0,0,0,4.013,1.287A6.017,6.017,0,0,0,758.981,1379.883Z" transform="translate(-747.181 -1374.33)" fill="#fff"/>
              </g>
            </g>
            <g id="组_4763" data-name="组 4763" transform="translate(0 17.719)">
              <g id="组_4761" data-name="组 4761" transform="translate(0.229 0.332)">
                <path id="路径_5733" data-name="路径 5733" d="M742.249,1500.757a5.932,5.932,0,0,0-4.458-1.63,6.2,6.2,0,0,0-5.822,6.395,5.967,5.967,0,0,0,1.325,3.529Z" transform="translate(-731.965 -1499.116)" fill="#628cd2"/>
                <path id="路径_5734" data-name="路径 5734" d="M758.949,1522.372a6.009,6.009,0,0,0-1.057-3.172L749,1527.442a5.954,5.954,0,0,0,4.115,1.325A6.2,6.2,0,0,0,758.949,1522.372Z" transform="translate(-746.83 -1516.642)" fill="#628cd2"/>
              </g>
              <g id="组_4762" data-name="组 4762">
                <path id="路径_5735" data-name="路径 5735" d="M740.181,1498.1a5.748,5.748,0,0,0-4.344-1.579,6.029,6.029,0,0,0-5.669,6.229,5.867,5.867,0,0,0,1.287,3.439Z" transform="translate(-730.164 -1496.514)" fill="#fff"/>
                <path id="路径_5736" data-name="路径 5736" d="M756.481,1518.983a5.828,5.828,0,0,0-1.032-3.083l-8.649,8.013a5.79,5.79,0,0,0,4.013,1.286A6.009,6.009,0,0,0,756.481,1518.983Z" transform="translate(-744.681 -1513.43)" fill="#fff"/>
              </g>
            </g>
          </g>
        </g>
        <path id="路径_5737" data-name="路径 5737" d="M322.466,1470.162h-74.7a.963.963,0,0,1-.968-.981h0a1,1,0,0,1,1.006-.981h74.7a.962.962,0,0,1,.968.981h0A1.012,1.012,0,0,1,322.466,1470.162Z" transform="translate(-241.676 -1448.965)" fill="#e0eaff"/>
      </g>
      <g id="组_4786" data-name="组 4786" transform="translate(39.315)">
        <g id="组_4767" data-name="组 4767" transform="translate(0 154.405)">
          <ellipse id="椭圆_6379" data-name="椭圆 6379" cx="54.636" cy="7.63" rx="54.636" ry="7.63" fill="#fff" opacity="0"/>
          <ellipse id="椭圆_6380" data-name="椭圆 6380" cx="52.076" cy="7.274" rx="52.076" ry="7.274" transform="translate(2.56 0.357)" fill="#fcfdff" opacity="0.056"/>
          <ellipse id="椭圆_6381" data-name="椭圆 6381" cx="49.528" cy="6.917" rx="49.528" ry="6.917" transform="translate(5.108 0.713)" fill="#f9fbfe" opacity="0.111"/>
          <ellipse id="椭圆_6382" data-name="椭圆 6382" cx="46.967" cy="6.56" rx="46.967" ry="6.56" transform="translate(7.669 1.07)" fill="#f5f8fe" opacity="0.167"/>
          <ellipse id="椭圆_6383" data-name="椭圆 6383" cx="44.407" cy="6.204" rx="44.407" ry="6.204" transform="translate(10.229 1.427)" fill="#f2f6fd" opacity="0.222"/>
          <ellipse id="椭圆_6384" data-name="椭圆 6384" cx="41.847" cy="5.847" rx="41.847" ry="5.847" transform="translate(12.79 1.783)" fill="#eff4fd" opacity="0.278"/>
          <ellipse id="椭圆_6385" data-name="椭圆 6385" cx="39.299" cy="5.49" rx="39.299" ry="5.49" transform="translate(15.337 2.14)" fill="#ecf2fc" opacity="0.333"/>
          <ellipse id="椭圆_6386" data-name="椭圆 6386" cx="36.738" cy="5.134" rx="36.738" ry="5.134" transform="translate(17.898 2.497)" fill="#e9f0fc" opacity="0.389"/>
          <ellipse id="椭圆_6387" data-name="椭圆 6387" cx="34.178" cy="4.777" rx="34.178" ry="4.777" transform="translate(20.458 2.853)" fill="#e6eefb" opacity="0.444"/>
          <ellipse id="椭圆_6388" data-name="椭圆 6388" cx="31.617" cy="4.42" rx="31.617" ry="4.42" transform="translate(23.019 3.21)" fill="#e3ecfb" opacity="0.5"/>
          <ellipse id="椭圆_6389" data-name="椭圆 6389" cx="29.07" cy="4.051" rx="29.07" ry="4.051" transform="translate(25.567 3.58)" fill="#dfe9fb" opacity="0.556"/>
          <ellipse id="椭圆_6390" data-name="椭圆 6390" cx="26.509" cy="3.694" rx="26.509" ry="3.694" transform="translate(28.127 3.936)" fill="#dce7fa" opacity="0.611"/>
          <ellipse id="椭圆_6391" data-name="椭圆 6391" cx="23.949" cy="3.338" rx="23.949" ry="3.338" transform="translate(30.687 4.293)" fill="#d9e5fa" opacity="0.667"/>
          <ellipse id="椭圆_6392" data-name="椭圆 6392" cx="21.388" cy="2.981" rx="21.388" ry="2.981" transform="translate(33.248 4.65)" fill="#d6e3f9" opacity="0.722"/>
          <ellipse id="椭圆_6393" data-name="椭圆 6393" cx="18.84" cy="2.624" rx="18.84" ry="2.624" transform="translate(35.796 5.006)" fill="#d3e1f9" opacity="0.778"/>
          <ellipse id="椭圆_6394" data-name="椭圆 6394" cx="16.28" cy="2.267" rx="16.28" ry="2.267" transform="translate(38.356 5.363)" fill="#d0dff8" opacity="0.833"/>
          <path id="路径_5738" data-name="路径 5738" d="M769.939,1702.21c0,1.057-6.14,1.911-13.719,1.911s-13.72-.854-13.72-1.911,6.14-1.911,13.72-1.911S769.939,1701.152,769.939,1702.21Z" transform="translate(-701.583 -1694.579)" fill="#ccdcf8" opacity="0.889"/>
          <path id="路径_5739" data-name="路径 5739" d="M784.918,1704.653c0,.866-4.994,1.554-11.159,1.554s-11.159-.7-11.159-1.554,4.994-1.554,11.159-1.554S784.918,1703.787,784.918,1704.653Z" transform="translate(-719.123 -1697.023)" fill="#c9daf7" opacity="0.944"/>
          <path id="路径_5740" data-name="路径 5740" d="M800,1707.1c0,.662-3.847,1.2-8.6,1.2s-8.6-.535-8.6-1.2,3.847-1.2,8.6-1.2S800,1706.433,800,1707.1Z" transform="translate(-736.75 -1699.465)" fill="#c6d8f7"/>
        </g>
        <g id="组_4785" data-name="组 4785" transform="translate(15.854)">
          <path id="路径_5741" data-name="路径 5741" d="M673.826,703.2l-92.9.293a6.4,6.4,0,0,1-6.5-7.5l17.758-129.157a8.953,8.953,0,0,1,8.56-7.541l92.9-.293a6.4,6.4,0,0,1,6.5,7.5L682.386,695.66A8.932,8.932,0,0,1,673.826,703.2Z" transform="translate(-570.713 -544.261)" fill="#1a317a"/>
          <path id="路径_5742" data-name="路径 5742" d="M645.226,663.114l-92.9.293a6.4,6.4,0,0,1-6.5-7.5l17.758-129.17a8.953,8.953,0,0,1,8.56-7.541l92.9-.293a6.4,6.4,0,0,1,6.5,7.5L653.786,655.56A8.965,8.965,0,0,1,645.226,663.114Z" transform="translate(-545.757 -509.269)" fill="#628cd2"/>
          <g id="组_4768" data-name="组 4768" transform="translate(8.757 19.949)">
            <path id="路径_5743" data-name="路径 5743" d="M705.263,725.656l-90.763.28L631.786,600.18l90.763-.28Z" transform="translate(-614.5 -599.9)" fill="#fff"/>
            <path id="路径_5744" data-name="路径 5744" d="M1117.23,1478.949h-18.751a2.449,2.449,0,0,1-2.484-2.891l.586-4.166a3.442,3.442,0,0,1,3.3-2.891h18.751a2.45,2.45,0,0,1,2.484,2.891l-.586,4.166A3.454,3.454,0,0,1,1117.23,1478.949Z" transform="translate(-1034.632 -1358.288)" fill="#c7d9f8"/>
            <path id="路径_5745" data-name="路径 5745" d="M656.9,1479.036h-7.643a1.683,1.683,0,0,1-1.707-1.987l.841-5.962a2.368,2.368,0,0,1,2.268-1.987H658.3a1.683,1.683,0,0,1,1.707,1.987l-.841,5.962A2.357,2.357,0,0,1,656.9,1479.036Z" transform="translate(-643.318 -1358.375)" fill="#c7d9f8"/>
          </g>
          <path id="路径_5746" data-name="路径 5746" d="M964.086,453.032l-6.739.025a8.4,8.4,0,0,0-8.56-9.758,11.777,11.777,0,0,0-11.248,9.822l-6.739.026a7.769,7.769,0,0,0-7.427,6.548l-.7,5.1a5.54,5.54,0,0,0,5.63,6.509l33.273-.1A7.77,7.77,0,0,0,969,464.65l.7-5.1A5.53,5.53,0,0,0,964.086,453.032Zm-17.223,4.229a3.546,3.546,0,0,1-3.605-4.166,4.979,4.979,0,0,1,4.752-4.191,3.546,3.546,0,0,1,3.6,4.166A4.968,4.968,0,0,1,946.863,457.261Z" transform="translate(-874.604 -443.3)" fill="#1a317a"/>
          <g id="组_4771" data-name="组 4771" transform="translate(26.43 37.592)">
            <g id="组_4770" data-name="组 4770">
              <g id="组_4769" data-name="组 4769" transform="translate(23.971)">
                <path id="路径_5747" data-name="路径 5747" d="M985.244,843.583H942.773a1.333,1.333,0,0,1-1.35-1.541h0a1.789,1.789,0,0,1,1.732-1.541h42.458a1.333,1.333,0,0,1,1.35,1.541h0A1.787,1.787,0,0,1,985.244,843.583Z" transform="translate(-941.411 -827.494)" fill="#c7d9f8"/>
                <path id="路径_5748" data-name="路径 5748" d="M985.835,791.483H949.173a1.333,1.333,0,0,1-1.35-1.541h0a1.789,1.789,0,0,1,1.732-1.541h36.662a1.333,1.333,0,0,1,1.35,1.541h0A1.789,1.789,0,0,1,985.835,791.483Z" transform="translate(-946.996 -782.031)" fill="#c7d9f8"/>
                <path id="路径_5749" data-name="路径 5749" d="M983.578,741.483H955.273a1.333,1.333,0,0,1-1.35-1.541h0a1.789,1.789,0,0,1,1.732-1.541H983.96a1.333,1.333,0,0,1,1.35,1.541h0A1.789,1.789,0,0,1,983.578,741.483Z" transform="translate(-952.319 -738.4)" fill="#c7d9f8"/>
              </g>
              <path id="路径_5750" data-name="路径 5750" d="M767.5,754.576H755.8a2.517,2.517,0,0,1-2.548-2.9l1.261-10.267a3.373,3.373,0,0,1,3.261-2.9h11.694a2.517,2.517,0,0,1,2.548,2.9l-1.261,10.267A3.373,3.373,0,0,1,767.5,754.576Z" transform="translate(-753.235 -738.487)" fill="#628cd2" opacity="0.75"/>
            </g>
          </g>
          <g id="组_4772" data-name="组 4772" transform="translate(49.36 59.133)">
            <path id="路径_5751" data-name="路径 5751" d="M975.836,909.309h-38.5a.782.782,0,0,1-.79-.9h0a1.05,1.05,0,0,1,1.006-.9h38.5a.782.782,0,0,1,.79.9h0A1.039,1.039,0,0,1,975.836,909.309Z" transform="translate(-936.122 -907.5)" fill="#c7d9f8"/>
            <path id="路径_5752" data-name="路径 5752" d="M956.74,935.809h-22.7a.782.782,0,0,1-.79-.9h0a1.05,1.05,0,0,1,1.006-.9h22.7a.782.782,0,0,1,.79.9h0A1.028,1.028,0,0,1,956.74,935.809Z" transform="translate(-933.242 -930.624)" fill="#c7d9f8"/>
          </g>
          <g id="组_4773" data-name="组 4773" transform="translate(48.023 70.101)">
            <path id="路径_5753" data-name="路径 5753" d="M941.542,995.409h-14.8a.782.782,0,0,1-.79-.9h0a1.049,1.049,0,0,1,1.006-.9h14.79a.782.782,0,0,1,.79.9h0A1.027,1.027,0,0,1,941.542,995.409Z" transform="translate(-925.535 -993.6)" fill="#c7d9f8"/>
            <path id="路径_5754" data-name="路径 5754" d="M946.227,1021.909H923.539a.782.782,0,0,1-.79-.9h0a1.05,1.05,0,0,1,1.006-.9h22.7a.782.782,0,0,1,.79.9h0A1.063,1.063,0,0,1,946.227,1021.909Z" transform="translate(-922.742 -1016.724)" fill="#c7d9f8"/>
          </g>
          <path id="路径_5755" data-name="路径 5755" d="M783.676,904.8H778.4a1.457,1.457,0,0,1-1.478-1.669l.573-4.637a1.844,1.844,0,0,1,1.783-1.592h5.286a1.457,1.457,0,0,1,1.478,1.669l-.573,4.637A1.867,1.867,0,0,1,783.676,904.8Zm-4.382-7.261a1.188,1.188,0,0,0-1.146,1.032l-.573,4.637a.888.888,0,0,0,.191.675.818.818,0,0,0,.637.268h5.274a1.188,1.188,0,0,0,1.146-1.032l.573-4.637a.888.888,0,0,0-.191-.675.818.818,0,0,0-.637-.267Z" transform="translate(-747.467 -839.117)" fill="#8da4fc"/>
          <path id="路径_5756" data-name="路径 5756" d="M793.3,894.995l-2.777-3.363a.661.661,0,0,1,.14-.917.644.644,0,0,1,.917.026l1.783,2.166,3.707-4.828a.681.681,0,0,1,.917-.191.614.614,0,0,1,.089.892Z" transform="translate(-759.227 -831.161)" fill="#ff5f18"/>
          <path id="路径_5757" data-name="路径 5757" d="M785.183,766.483l-4.484-5.427a1.08,1.08,0,0,1,.217-1.478,1.042,1.042,0,0,1,1.478.038l2.892,3.49,5.987-7.8a1.126,1.126,0,0,1,1.49-.318,1.01,1.01,0,0,1,.14,1.452Z" transform="translate(-750.593 -715.146)" fill="#1a317a"/>
          <g id="组_4774" data-name="组 4774" transform="translate(28.095 68.84)">
            <path id="路径_5758" data-name="路径 5758" d="M772.829,991.5h-5.07a1.416,1.416,0,0,1-1.44-1.63l.56-4.611a1.814,1.814,0,0,1,1.745-1.554h5.07a1.416,1.416,0,0,1,1.439,1.631l-.56,4.611A1.814,1.814,0,0,1,772.829,991.5Zm-4.191-7.172a1.16,1.16,0,0,0-1.121,1.006l-.561,4.611a.858.858,0,0,0,.191.662.834.834,0,0,0,.624.268h5.07a1.16,1.16,0,0,0,1.121-1.006l.56-4.611a.858.858,0,0,0-.191-.662.834.834,0,0,0-.624-.267Z" transform="translate(-766.308 -983.7)" fill="#8da4fc"/>
          </g>
          <path id="路径_5759" data-name="路径 5759" d="M781.4,979.594l-2.777-3.363a.661.661,0,0,1,.14-.917.644.644,0,0,1,.917.026l1.783,2.165,3.707-4.828a.681.681,0,0,1,.917-.191.614.614,0,0,1,.089.892Z" transform="translate(-748.843 -904.984)" fill="#ff5f18"/>
          <g id="组_4784" data-name="组 4784" transform="translate(20.621 83.171)">
            <g id="组_4777" data-name="组 4777">
              <g id="组_4776" data-name="组 4776">
                <g id="组_4775" data-name="组 4775" transform="translate(23.971 0)">
                  <path id="路径_5760" data-name="路径 5760" d="M946.624,1201.283H897.173a1.333,1.333,0,0,1-1.35-1.541h0a1.789,1.789,0,0,1,1.732-1.541h49.451a1.333,1.333,0,0,1,1.35,1.541h0A1.789,1.789,0,0,1,946.624,1201.283Z" transform="translate(-895.811 -1185.207)" fill="#c7d9f8"/>
                  <path id="路径_5761" data-name="路径 5761" d="M944.121,1149.27H903.37a1.218,1.218,0,0,1-1.236-1.4l.038-.268a1.627,1.627,0,0,1,1.58-1.4H944.5a1.218,1.218,0,0,1,1.236,1.4l-.038.268A1.637,1.637,0,0,1,944.121,1149.27Z" transform="translate(-901.321 -1139.831)" fill="#c7d9f8"/>
                  <path id="路径_5762" data-name="路径 5762" d="M964.97,1099.283h-55.2a1.333,1.333,0,0,1-1.35-1.541h0a1.79,1.79,0,0,1,1.733-1.541h55.2a1.333,1.333,0,0,1,1.35,1.541h0A1.811,1.811,0,0,1,964.97,1099.283Z" transform="translate(-906.806 -1096.2)" fill="#c7d9f8"/>
                </g>
                <path id="路径_5763" data-name="路径 5763" d="M721.9,1112.276H710.2a2.517,2.517,0,0,1-2.548-2.9l1.261-10.267a3.373,3.373,0,0,1,3.261-2.9h11.694a2.517,2.517,0,0,1,2.548,2.9l-1.261,10.267A3.373,3.373,0,0,1,721.9,1112.276Z" transform="translate(-707.635 -1096.2)" fill="#628cd2" opacity="0.75"/>
              </g>
            </g>
            <g id="组_4783" data-name="组 4783" transform="translate(1.666 19.017)">
              <g id="组_4778" data-name="组 4778" transform="translate(21.254 2.512)">
                <path id="路径_5764" data-name="路径 5764" d="M911.115,1267.009H891.739a.782.782,0,0,1-.79-.9h0a1.05,1.05,0,0,1,1.006-.9h19.375a.782.782,0,0,1,.79.9h0A1.05,1.05,0,0,1,911.115,1267.009Z" transform="translate(-890.511 -1265.2)" fill="#c7d9f8"/>
                <path id="路径_5765" data-name="路径 5765" d="M907.806,1293.509H888.3a.732.732,0,0,1-.739-.841l.013-.127a.982.982,0,0,1,.943-.841h19.5a.732.732,0,0,1,.739.841l-.013.127A.981.981,0,0,1,907.806,1293.509Z" transform="translate(-887.559 -1288.324)" fill="#c7d9f8"/>
              </g>
              <g id="组_4779" data-name="组 4779" transform="translate(19.927 13.493)">
                <path id="路径_5766" data-name="路径 5766" d="M891.816,1353.2H880.95a.675.675,0,0,1-.688-.777l.026-.242a.9.9,0,0,1,.879-.777h10.866a.674.674,0,0,1,.688.777l-.025.242A.925.925,0,0,1,891.816,1353.2Z" transform="translate(-879.86 -1351.4)" fill="#c7d9f8"/>
                <path id="路径_5767" data-name="路径 5767" d="M911.264,1379.609H877.939a.782.782,0,0,1-.79-.9h0a1.049,1.049,0,0,1,1.006-.9h33.337a.782.782,0,0,1,.79.9h0A1.052,1.052,0,0,1,911.264,1379.609Z" transform="translate(-877.142 -1374.437)" fill="#c7d9f8"/>
              </g>
              <g id="组_4782" data-name="组 4782">
                <g id="组_4780" data-name="组 4780" transform="translate(1.35)">
                  <path id="路径_5768" data-name="路径 5768" d="M738.076,1262.5H732.8a1.457,1.457,0,0,1-1.478-1.669l.573-4.637a1.844,1.844,0,0,1,1.783-1.592h5.274a1.457,1.457,0,0,1,1.478,1.669l-.573,4.637A1.844,1.844,0,0,1,738.076,1262.5Zm-4.382-7.261a1.188,1.188,0,0,0-1.146,1.032l-.573,4.637a.888.888,0,0,0,.191.675.818.818,0,0,0,.637.267h5.274a1.188,1.188,0,0,0,1.146-1.032l.573-4.637a.888.888,0,0,0-.191-.675.818.818,0,0,0-.637-.268Z" transform="translate(-731.314 -1253.438)" fill="#8da4fc"/>
                  <path id="路径_5769" data-name="路径 5769" d="M747.7,1252.694l-2.777-3.363a.661.661,0,0,1,.14-.917.644.644,0,0,1,.917.026l1.783,2.166,3.707-4.828a.681.681,0,0,1,.917-.191.614.614,0,0,1,.089.892Z" transform="translate(-743.073 -1245.482)" fill="#ff5f18"/>
                </g>
                <g id="组_4781" data-name="组 4781" transform="translate(0 10.955)">
                  <path id="路径_5770" data-name="路径 5770" d="M727.476,1348.6H722.2a1.457,1.457,0,0,1-1.478-1.669l.573-4.637a1.844,1.844,0,0,1,1.783-1.592h5.274a1.457,1.457,0,0,1,1.478,1.669l-.573,4.637A1.844,1.844,0,0,1,727.476,1348.6Zm-4.382-7.261a1.188,1.188,0,0,0-1.146,1.032l-.573,4.637a.889.889,0,0,0,.191.675.818.818,0,0,0,.637.268h5.274a1.188,1.188,0,0,0,1.146-1.032l.573-4.637a.889.889,0,0,0-.191-.675.819.819,0,0,0-.637-.267Z" transform="translate(-720.714 -1339.526)" fill="#8da4fc"/>
                  <path id="路径_5771" data-name="路径 5771" d="M737.2,1338.695l-2.777-3.363a.661.661,0,0,1,.14-.917.643.643,0,0,1,.917.026l1.784,2.166,3.707-4.828a.682.682,0,0,1,.917-.191.614.614,0,0,1,.089.892Z" transform="translate(-732.561 -1331.482)" fill="#ff5f18"/>
                </g>
              </g>
            </g>
          </g>
        </g>
      </g>
      <g id="组_4787" data-name="组 4787" transform="translate(31.328 84.05)">
        <path id="路径_5772" data-name="路径 5772" d="M446.06,1117.112h-6.382a1.479,1.479,0,0,1-1.478-1.478v-11.057a1.479,1.479,0,0,1,1.478-1.478h6.382a1.479,1.479,0,0,1,1.478,1.478v11.057A1.487,1.487,0,0,1,446.06,1117.112Z" transform="translate(-428.06 -1103.1)" fill="#1a317a"/>
        <path id="路径_5773" data-name="路径 5773" d="M385.428,1328.094H361.377a2.775,2.775,0,0,1-2.777-2.777v-42.573a7.043,7.043,0,0,1,7.044-7.045H381.16a7.043,7.043,0,0,1,7.044,7.045v42.573A2.767,2.767,0,0,1,385.428,1328.094Z" transform="translate(-358.6 -1253.713)" fill="url(#linear-gradient)"/>
        <path id="路径_5774" data-name="路径 5774" d="M419.03,1204.038H402.47a1.068,1.068,0,0,1-1.07-1.07v-11.9a1.068,1.068,0,0,1,1.07-1.07h16.573a1.068,1.068,0,0,1,1.07,1.07v11.9A1.079,1.079,0,0,1,419.03,1204.038Z" transform="translate(-395.948 -1178.93)" fill="#2c49a2"/>
        <path id="路径_5775" data-name="路径 5775" d="M419.362,1410.447H402.139a.739.739,0,0,1-.739-.739v-4.369a.739.739,0,0,1,.739-.739h17.223a.739.739,0,0,1,.739.739v4.369A.739.739,0,0,1,419.362,1410.447Z" transform="translate(-395.948 -1366.193)" fill="#e0eaff"/>
        <path id="路径_5776" data-name="路径 5776" d="M418.737,1485.022H402.763a1.365,1.365,0,0,1-1.363-1.363v-17.1a1.365,1.365,0,0,1,1.363-1.363h15.974a1.365,1.365,0,0,1,1.363,1.363v17.1A1.365,1.365,0,0,1,418.737,1485.022Z" transform="translate(-395.948 -1419.073)" fill="#e0eaff"/>
      </g>
      <g id="组_4803" data-name="组 4803" transform="translate(16.717 52.197)">
        <g id="组_4788" data-name="组 4788" transform="translate(0 125.864)">
          <ellipse id="椭圆_6395" data-name="椭圆 6395" cx="47.209" cy="6.586" rx="47.209" ry="6.586" fill="#fff" opacity="0"/>
          <ellipse id="椭圆_6396" data-name="椭圆 6396" cx="44.993" cy="6.28" rx="44.993" ry="6.28" transform="translate(2.217 0.306)" fill="#fcfdff" opacity="0.056"/>
          <ellipse id="椭圆_6397" data-name="椭圆 6397" cx="42.789" cy="5.974" rx="42.789" ry="5.974" transform="translate(4.42 0.611)" fill="#f9fbfe" opacity="0.111"/>
          <ellipse id="椭圆_6398" data-name="椭圆 6398" cx="40.573" cy="5.669" rx="40.573" ry="5.669" transform="translate(6.637 0.917)" fill="#f5f8fe" opacity="0.167"/>
          <ellipse id="椭圆_6399" data-name="椭圆 6399" cx="38.369" cy="5.35" rx="38.369" ry="5.35" transform="translate(8.841 1.236)" fill="#f2f6fd" opacity="0.222"/>
          <ellipse id="椭圆_6400" data-name="椭圆 6400" cx="36.152" cy="5.045" rx="36.152" ry="5.045" transform="translate(11.057 1.541)" fill="#eff4fd" opacity="0.278"/>
          <ellipse id="椭圆_6401" data-name="椭圆 6401" cx="33.949" cy="4.739" rx="33.949" ry="4.739" transform="translate(13.261 1.847)" fill="#ecf2fc" opacity="0.333"/>
          <ellipse id="椭圆_6402" data-name="椭圆 6402" cx="31.745" cy="4.433" rx="31.745" ry="4.433" transform="translate(15.465 2.153)" fill="#e9f0fc" opacity="0.389"/>
          <ellipse id="椭圆_6403" data-name="椭圆 6403" cx="29.528" cy="4.127" rx="29.528" ry="4.127" transform="translate(17.681 2.459)" fill="#e6eefb" opacity="0.444"/>
          <ellipse id="椭圆_6404" data-name="椭圆 6404" cx="27.324" cy="3.809" rx="27.324" ry="3.809" transform="translate(19.885 2.777)" fill="#e3ecfb" opacity="0.5"/>
          <ellipse id="椭圆_6405" data-name="椭圆 6405" cx="25.108" cy="3.503" rx="25.108" ry="3.503" transform="translate(22.102 3.083)" fill="#dfe9fb" opacity="0.556"/>
          <ellipse id="椭圆_6406" data-name="椭圆 6406" cx="22.904" cy="3.197" rx="22.904" ry="3.197" transform="translate(24.305 3.388)" fill="#dce7fa" opacity="0.611"/>
          <ellipse id="椭圆_6407" data-name="椭圆 6407" cx="20.688" cy="2.892" rx="20.688" ry="2.892" transform="translate(26.522 3.694)" fill="#d9e5fa" opacity="0.667"/>
          <ellipse id="椭圆_6408" data-name="椭圆 6408" cx="18.484" cy="2.586" rx="18.484" ry="2.586" transform="translate(28.726 4)" fill="#d6e3f9" opacity="0.722"/>
          <ellipse id="椭圆_6409" data-name="椭圆 6409" cx="16.267" cy="2.267" rx="16.267" ry="2.267" transform="translate(30.942 4.318)" fill="#d3e1f9" opacity="0.778"/>
          <ellipse id="椭圆_6410" data-name="椭圆 6410" cx="14.063" cy="1.962" rx="14.063" ry="1.962" transform="translate(33.146 4.624)" fill="#d0dff8" opacity="0.833"/>
          <path id="路径_5777" data-name="路径 5777" d="M545.194,1881.456c0,.917-5.312,1.656-11.847,1.656s-11.847-.739-11.847-1.656,5.312-1.656,11.847-1.656S545.194,1880.539,545.194,1881.456Z" transform="translate(-486.137 -1874.87)" fill="#ccdcf8" opacity="0.889"/>
          <path id="路径_5778" data-name="路径 5778" d="M558.086,1883.55c0,.739-4.318,1.35-9.643,1.35s-9.643-.6-9.643-1.35,4.318-1.35,9.643-1.35S558.086,1882.8,558.086,1883.55Z" transform="translate(-501.234 -1876.964)" fill="#c9daf7" opacity="0.944"/>
          <path id="路径_5779" data-name="路径 5779" d="M570.879,1885.73c0,.573-3.325,1.032-7.439,1.032S556,1886.3,556,1885.73s3.325-1.032,7.439-1.032S570.879,1885.157,570.879,1885.73Z" transform="translate(-516.243 -1879.144)" fill="#c6d8f7"/>
        </g>
        <g id="组_4802" data-name="组 4802" transform="translate(27.191)">
          <g id="组_4791" data-name="组 4791" transform="translate(21.56 20.915)">
            <g id="组_4790" data-name="组 4790">
              <g id="组_4789" data-name="组 4789">
                <path id="路径_5780" data-name="路径 5780" d="M923.8,1049.983l1.134-1.006a3.668,3.668,0,0,1,3.656-1.338l1.478.306a4.454,4.454,0,0,0,1.924-.038l2.637-.65a.253.253,0,0,1,.306.28l-.739,1.834a2.312,2.312,0,0,1-1.962,1.439l-2.994.229a2.662,2.662,0,0,0-.93.255,3.767,3.767,0,0,1-.714.28l-1.771.484-.1.331Z" transform="translate(-885.941 -1043.427)" fill="#fac4b6"/>
                <path id="路径_5781" data-name="路径 5781" d="M938.014,1050.064c1.223-.242,2.841.344,4.255,0,.471-.115,1.287-1.618,1.58-2.357a.767.767,0,0,1,.318-.407.257.257,0,0,1,.191.28l-.739,1.834a2.312,2.312,0,0,1-1.962,1.439l-2.994.229a2.664,2.664,0,0,0-.93.255,3.769,3.769,0,0,1-.713.28l-1.771.484-.1.331-.548-.688A13.647,13.647,0,0,1,938.014,1050.064Z" transform="translate(-895.365 -1043.471)" fill="#f5b3a6"/>
                <path id="路径_5782" data-name="路径 5782" d="M626.6,1017.794a5.807,5.807,0,0,1,4.484-.178c2.319.943,16.611,12.777,16.611,12.777l17.439-7.2,1.745,2.561s-14.153,12.8-18.649,11.732-16.331-9.274-16.331-9.274Z" transform="translate(-626.6 -1017.239)" fill="url(#linear-gradient-2)"/>
                <path id="路径_5783" data-name="路径 5783" d="M678.532,1080.754c1.5.968,10.038,8.815,14.993,8.917,2.2.038,16.649-9.936,17.592-10.471l.433.637s-14.153,12.8-18.649,11.732c-3.592-.854-11.885-6.28-15.057-8.407C677.717,1081.646,677.78,1080.27,678.532,1080.754Z" transform="translate(-671.271 -1071.307)" fill="#e0eaff"/>
              </g>
              <path id="路径_5784" data-name="路径 5784" d="M640.387,1029.776c-1.631-4.7-3.669-9.21-5.987-11.936.216-.051.446-.1.7-.14l.446.1a5.324,5.324,0,0,1,2.229,1.083c1.949,1.439,2.841,2.14,2.841,2.14l-.853,1.261,2.178,1.185a48.492,48.492,0,0,1,1.784,8.191,2.689,2.689,0,0,1,.025.318C642.451,1031.139,641.279,1030.375,640.387,1029.776Z" transform="translate(-633.406 -1017.641)" fill="#a7c1ff" opacity="0.5"/>
            </g>
            <path id="路径_5785" data-name="路径 5785" d="M647.139,1023.659l-.8,1.236,2.025,1.159a50.964,50.964,0,0,1,1.669,8.038c.255,2.713.624,10.356.777,13.8h-.127c-.662-5.185-3.019-20.751-8.242-26.713l-.038-.675a4.822,4.822,0,0,1,2.076,1.07C646.3,1022.971,647.139,1023.659,647.139,1023.659Z" transform="translate(-640.387 -1020.085)" fill="#fff"/>
          </g>
          <g id="组_4792" data-name="组 4792" transform="translate(6.908)">
            <path id="路径_5786" data-name="路径 5786" d="M518.587,858.689s4.535-1.35.739-4.93c-1.261-1.185-4.42-1.057-5.72,1.758s-.051,5.146-.242,6.739-1.019,1.108-1.452,3.185.484,1.745.433,3.72-1.248,2.42-.548,5.261,2.1,5.452,4.318,5.363,10.777-2.662,8.255-6.14-3.338-3.044-4.293-4.395-.1-3.338-1.35-4.777S515.632,858.2,518.587,858.689Z" transform="translate(-511.578 -853.052)" fill="url(#linear-gradient-3)"/>
            <path id="路径_5787" data-name="路径 5787" d="M563.145,875.675l1.325-2.025a1.378,1.378,0,0,0-.395-1.911h0a1.378,1.378,0,0,0-1.911.395l-1.325,2.025a1.378,1.378,0,0,0,.395,1.911h0A1.378,1.378,0,0,0,563.145,875.675Z" transform="translate(-554.365 -869.16)" fill="#2c49a2"/>
          </g>
          <path id="路径_5788" data-name="路径 5788" d="M662.718,1135.839l1.032,12.879a99.354,99.354,0,0,0,3.057,17.719l3.669,13.668-11.656.777-2.42-44.28,4.331-8.2Z" transform="translate(-631.044 -1093.324)" fill="url(#linear-gradient-4)"/>
          <g id="组_4794" data-name="组 4794" transform="translate(14.537 1.824)">
            <g id="组_4793" data-name="组 4793">
              <path id="路径_5789" data-name="路径 5789" d="M599.589,974.285l.28,5.694-6.369-.166.42-3.63a19.444,19.444,0,0,0,.038-4.217l-.089-.866Z" transform="translate(-590.693 -957.886)" fill="#fac4b6"/>
              <path id="路径_5790" data-name="路径 5790" d="M602.209,976.006A11.974,11.974,0,0,1,596.566,973c-.013-.382-.038-.764-.076-1.134L596.4,971l5.72,3.185Z" transform="translate(-593.223 -957.799)" fill="#f5b3a6"/>
              <path id="路径_5791" data-name="路径 5791" d="M585.736,874.3c-.166,4.267-1.439,10.357-4.293,10.165-3.694-.229-8.891-4.382-9.783-8.573-1.3-6.127,4.191-8.637,7.044-8.522S585.914,870.037,585.736,874.3Z" transform="translate(-571.464 -867.371)" fill="#fac4b6"/>
            </g>
            <path id="路径_5792" data-name="路径 5792" d="M578.7,867.474c2.854.115,7.21,2.65,7.045,6.917v.115a4.881,4.881,0,0,0-1.3-1.8c-3.287-2.586-6.025-.076-7.771,3.057-.65-.994-1.656-1.465-2.267-1.07s-.573,1.516.076,2.51a3,3,0,0,0,1.045,1.006,17.249,17.249,0,0,0-.841,2.79,10.489,10.489,0,0,1-3.032-5.019C570.36,869.856,575.85,867.36,578.7,867.474Z" transform="translate(-571.464 -867.458)" fill="url(#linear-gradient-5)"/>
          </g>
          <g id="组_4798" data-name="组 4798" transform="translate(0 21.147)">
            <g id="组_4796" data-name="组 4796">
              <path id="路径_5793" data-name="路径 5793" d="M590.876,1032.682c-.2-2.943-4.586-10.79-4.586-10.79l-2.255-2.6s.1,2.063-.688,2.331c-1.682.586-4.637-.739-5.236-2.433-.433-1.21-2.611,6.267-2.611,6.267l.255,21.223h2.56l.28,9.745s10.344,1.07,10.688-1.121A177.8,177.8,0,0,0,590.876,1032.682Z" transform="translate(-560.449 -1019.06)" fill="#2c49a2"/>
              <g id="组_4795" data-name="组 4795" transform="translate(0 35.718)">
                <path id="路径_5794" data-name="路径 5794" d="M510.537,1299.561c5.923,15.4,5.312,27.554,4.892,31.541-.166,1.618-1.4,6.8-27.082,32.369-.6.6-3.146-2.841-3.146-2.841s8.408-13.274,11.72-17.9a67.911,67.911,0,0,1,4.764-5.643,14.2,14.2,0,0,0,3.261-13.388c-2.115-7.707-4.739-18.93-3.21-22.611C502.652,1298.848,510.537,1299.561,510.537,1299.561Z" transform="translate(-481.652 -1299.453)" fill="#2c49a2"/>
                <path id="路径_5795" data-name="路径 5795" d="M497.443,1372.4c-3.07,4.28-10.5,15.962-11.592,17.681a.307.307,0,0,0-.051.191h0a.357.357,0,0,0,.076.229l.853,1.108h.013c2.675-4.153,9.936-15.274,13.579-19.083,1.427-1.49,2.917-3.2,4.28-4.828a24.449,24.449,0,0,0,5.72-15.541c.038-6.357-.484-14.344-3.108-17.541-1.223-1.491-3.185-1.427-5.4-.51h0c.051,5.044,2.013,13.236,3.669,19.261a14.2,14.2,0,0,1-3.261,13.388A67.9,67.9,0,0,0,497.443,1372.4Z" transform="translate(-482.176 -1329.12)" fill="url(#linear-gradient-6)"/>
                <path id="路径_5796" data-name="路径 5796" d="M461.75,1767a5.723,5.723,0,0,0,1.4,2.28,6.052,6.052,0,0,0,1.962,1.1,8.208,8.208,0,0,0,.115,4.217,22.883,22.883,0,0,0,2.6,4.917c.115.357-1.057,1.172-1.057,1.172s-8.548-8.6-9.337-10.1S461.75,1767,461.75,1767Z" transform="translate(-457.349 -1707.441)" fill="url(#linear-gradient-7)"/>
              </g>
            </g>
            <g id="组_4797" data-name="组 4797" transform="translate(9.451 35.558)">
              <path id="路径_5797" data-name="路径 5797" d="M534.072,1299.377s-2.866,5.4-2.5,10.191,7.108,24.407,7.414,26.063-1.49,6.5-.433,12.548,5.529,22.2,5.529,22.2l4.331-.408s1.414-23.872-.6-35.757c-.408-2.395,1.516-24.917-2.484-28.789C541.333,1301.53,539.536,1295.658,534.072,1299.377Z" transform="translate(-531.542 -1298.192)" fill="#2c49a2"/>
              <path id="路径_5798" data-name="路径 5798" d="M566.317,1371.627s1.414-23.872-.6-35.757c-.408-2.395,1.516-24.917-2.484-28.789-2.675-2.586-4.357-6.064-6.815-6.981a7.331,7.331,0,0,0-1.847.955s-2.866,5.4-2.5,10.191,7.108,24.407,7.414,26.063-1.491,6.5-.433,12.547c.981,5.618,4.9,19.936,5.465,21.962Z" transform="translate(-549.43 -1299.857)" fill="#628cd2"/>
              <path id="路径_5799" data-name="路径 5799" d="M620.889,1850.458a6.318,6.318,0,0,0,2.917.42,6.676,6.676,0,0,0,2.306-.879,9.1,9.1,0,0,0,3.643,2.866,25.437,25.437,0,0,0,5.987,1.274c.382.153.255,1.72.255,1.72s-13.286,1.172-15.121.777S620.889,1850.458,620.889,1850.458Z" transform="translate(-608.793 -1779.707)" fill="url(#linear-gradient-8)"/>
            </g>
          </g>
          <g id="组_4800" data-name="组 4800" transform="translate(6.171 20.617)">
            <g id="组_4799" data-name="组 4799">
              <path id="路径_5800" data-name="路径 5800" d="M523.675,1035.906l-1.4-11.21-4.9-9.8a1.977,1.977,0,0,1-.892,1.032,4.589,4.589,0,0,1-2.127.458,4.116,4.116,0,0,0-3.771,2.7c-.293.917-.726,6.178,0,8.306a35.788,35.788,0,0,1,2.025,8.93,23.891,23.891,0,0,1,.038,4.573,54.905,54.905,0,0,1-2.153,8.79c-1.185,3.427-3.108,7.6-3.962,13.86-.9,6.586-.726,20.942-.726,20.942l21.949-1.2-4.65-40.242-.153-.344Z" transform="translate(-505.788 -1014.9)" fill="url(#linear-gradient-9)"/>
              <path id="路径_5801" data-name="路径 5801" d="M590.653,1014.951l.038-.051a11.645,11.645,0,0,1,2.166,1.9,31.327,31.327,0,0,1,2.369,3.261l-.739,2.14,2.408.981s1.159,2.828.726,8.586c-.28,3.745-.841,8.458-1.2,11.287h-.446c.306-3.478.611-9.885-.675-14.955a37.4,37.4,0,0,0-5.5-12.178A3.887,3.887,0,0,0,590.653,1014.951Z" transform="translate(-579.098 -1014.9)" fill="#fff"/>
            </g>
            <path id="路径_5802" data-name="路径 5802" d="M544.443,1026.338l.65-.038h0s4.229,1.669,1.72,8.216c-1.159,3.019-3.847,8.267-3.847,8.267a25.637,25.637,0,0,1,.688,5.388,29.843,29.843,0,0,1-.408,4.828h-.739a27.52,27.52,0,0,0,.191-6.713,36.227,36.227,0,0,0-2.025-8.93c-.726-2.127-.293-7.388,0-8.306A4.107,4.107,0,0,1,544.443,1026.338Z" transform="translate(-535.882 -1024.848)" fill="#a7c1ff" opacity="0.5"/>
          </g>
          <g id="组_4801" data-name="组 4801" transform="translate(0.694 22.344)">
            <path id="路径_5803" data-name="路径 5803" d="M468.889,1053.644s1.452-2.56,3.159-5.6a20.193,20.193,0,0,1,1.121-1.694,59.319,59.319,0,0,0,6.038-12.7,3.96,3.96,0,0,0-1.529-4.713,3.424,3.424,0,0,0-4.331.675c-2.229,2.955-6.56,12.535-7.936,15.847a86.663,86.663,0,0,0-2.611,9.4,2.368,2.368,0,0,0,.14.9c.624,1.452,4.127,3.656,7.465,4.994,4.943,1.974,11.516,4.076,11.516,4.076a3.548,3.548,0,0,1,1.847-3.057Z" transform="translate(-462.8 -1028.454)" fill="#fff"/>
            <path id="路径_5804" data-name="路径 5804" d="M462.974,1055.911c.535,1.987,4.127,3.656,7.465,4.994,4.943,1.974,11.516,4.076,11.516,4.076a3.94,3.94,0,0,1,.268-1.287c-2-.7-15.885-5.643-17.325-7.885a2.1,2.1,0,0,1-.28-.981c.688-5.261,5.873-16.726,8.076-21.567,2.318-5.1,5.006-4.191,5.006-4.191a3.369,3.369,0,0,0-.866-.357,3.466,3.466,0,0,0-.6-.1,2.487,2.487,0,0,0-.306-.013,3.393,3.393,0,0,0-1.936.573,2.672,2.672,0,0,0-.611.586c-.14.178-.293.395-.446.624s-.318.484-.484.765c-.089.14-.166.28-.255.433-.255.446-.535.93-.8,1.439-.191.344-.369.7-.561,1.057s-.382.739-.573,1.121c-.293.573-.586,1.159-.866,1.758-.191.395-.382.79-.573,1.2s-.382.79-.561,1.185c-.28.586-.548,1.172-.8,1.733-.853,1.873-1.567,3.516-2,4.548-.815,1.974-1.758,4.9-2.446,7.21A7.213,7.213,0,0,0,462.974,1055.911Z" transform="translate(-462.821 -1028.581)" fill="url(#linear-gradient-10)"/>
            <path id="路径_5805" data-name="路径 5805" d="M601.277,1277.917a.777.777,0,0,1-.548-1.325c1.274-1.274,2.115-4.535,2.331-5.694a.772.772,0,1,1,1.516.293c-.038.191-.892,4.637-2.751,6.5A.762.762,0,0,1,601.277,1277.917Z" transform="translate(-582.959 -1239.466)" fill="#a7c1ff"/>
          </g>
        </g>
      </g>
      <g id="组_4812" data-name="组 4812" transform="translate(0 165.624)">
        <g id="组_4805" data-name="组 4805" transform="translate(11.326 0.346)">
          <g id="组_4804" data-name="组 4804">
            <path id="路径_5806" data-name="路径 5806" d="M218.176,1750.954l-.025.688a2.429,2.429,0,0,1-2.51,2.331l-9.822-.357a2.429,2.429,0,0,1-2.331-2.51l.025-.688a2.429,2.429,0,0,1,2.51-2.331l9.822.357A2.438,2.438,0,0,1,218.176,1750.954Z" transform="translate(-203.243 -1747.844)" fill="#fff"/>
            <path id="路径_5807" data-name="路径 5807" d="M214.067,1752.557h-.1l-9.822-.357a2.655,2.655,0,0,1-2.56-2.764l.025-.688a2.625,2.625,0,0,1,.853-1.847,2.672,2.672,0,0,1,1.911-.714l9.822.357a2.678,2.678,0,0,1,2.56,2.764l-.025.688a2.624,2.624,0,0,1-.854,1.847A2.653,2.653,0,0,1,214.067,1752.557Zm-9.809-5.885a2.2,2.2,0,0,0-2.178,2.1l-.025.688a2.179,2.179,0,0,0,2.1,2.255l9.821.357a2.171,2.171,0,0,0,2.255-2.1l.025-.688a2.14,2.14,0,0,0-.586-1.554,2.2,2.2,0,0,0-1.516-.7l-9.822-.357Z" transform="translate(-201.58 -1746.186)" fill="#1a317a"/>
          </g>
          <path id="路径_5808" data-name="路径 5808" d="M254,1754.729l1.834.064,3.885.14a2.429,2.429,0,0,0,2.51-2.331l.026-.688a2.429,2.429,0,0,0-2.331-2.51l-3.809-.14-1.911-.064Z" transform="translate(-247.322 -1748.816)" fill="#1a317a"/>
        </g>
        <g id="组_4808" data-name="组 4808">
          <g id="组_4806" data-name="组 4806">
            <path id="路径_5809" data-name="路径 5809" d="M129.437,1752.35l-.242.675a2.528,2.528,0,0,1-3.21,1.529l-9.63-3.414a2.528,2.528,0,0,1-1.529-3.21l.242-.675a2.528,2.528,0,0,1,3.21-1.529l9.63,3.414A2.528,2.528,0,0,1,129.437,1752.35Z" transform="translate(-114.427 -1745.314)" fill="#fff"/>
            <path id="路径_5810" data-name="路径 5810" d="M125.068,1753.1a2.651,2.651,0,0,1-.93-.166l-9.63-3.414a2.7,2.7,0,0,1-1.567-1.427,2.773,2.773,0,0,1-.115-2.115l.242-.675a2.7,2.7,0,0,1,1.427-1.567,2.773,2.773,0,0,1,2.115-.115l9.63,3.414a2.777,2.777,0,0,1,1.682,3.542h0l-.242.675A2.788,2.788,0,0,1,125.068,1753.1Zm-9.388-9.121a2.439,2.439,0,0,0-.981.216,2.221,2.221,0,0,0-1.159,1.287l-.242.675a2.27,2.27,0,0,0,.089,1.733,2.221,2.221,0,0,0,1.287,1.159l9.63,3.414a2.271,2.271,0,0,0,2.892-1.376l.242-.675h0a2.271,2.271,0,0,0-1.376-2.892l-9.63-3.414A2.167,2.167,0,0,0,115.68,1743.983Z" transform="translate(-112.669 -1743.469)" fill="#fa8b17"/>
          </g>
          <g id="组_4807" data-name="组 4807" transform="translate(5.953 1.838)">
            <path id="路径_5811" data-name="路径 5811" d="M159.4,1763.314l1.8.637,3.809,1.35a2.528,2.528,0,0,0,3.21-1.529l.242-.675a2.528,2.528,0,0,0-1.529-3.21l-3.732-1.325-1.873-.662Z" transform="translate(-159.4 -1757.9)" fill="#fa8b17"/>
          </g>
        </g>
        <g id="组_4811" data-name="组 4811" transform="translate(30.132 6.718)">
          <g id="组_4809" data-name="组 4809">
            <path id="路径_5812" data-name="路径 5812" d="M361.908,1799.036l.662.841a2.347,2.347,0,0,1-.408,3.286l-6.586,5.134a2.347,2.347,0,0,1-3.287-.408l-.662-.841a2.347,2.347,0,0,1,.408-3.286l6.586-5.134A2.347,2.347,0,0,1,361.908,1799.036Z" transform="translate(-350.89 -1797.89)" fill="#fff"/>
            <path id="路径_5813" data-name="路径 5813" d="M352.455,1807.34a2.593,2.593,0,0,1-2.038-.994l-.662-.841a2.576,2.576,0,0,1,.446-3.618l6.586-5.134a2.576,2.576,0,0,1,3.618.446l.662.841a2.576,2.576,0,0,1-.446,3.618l-6.586,5.134A2.559,2.559,0,0,1,352.455,1807.34Zm5.923-10.65a2.088,2.088,0,0,0-1.3.446l-6.586,5.134a2.107,2.107,0,0,0-.369,2.955l.662.841a2.107,2.107,0,0,0,2.955.369l6.586-5.134a2.107,2.107,0,0,0,.369-2.955l-.662-.841A2.08,2.08,0,0,0,358.379,1796.691Z" transform="translate(-349.208 -1796.207)" fill="#fa8b17"/>
          </g>
          <g id="组_4810" data-name="组 4810" transform="translate(3.884 0.212)">
            <path id="路径_5814" data-name="路径 5814" d="M383.229,1805.94l1.287-1.007,2.726-2.127a2.38,2.38,0,0,0,.153-3.312l-.433-.561a2.365,2.365,0,0,0-3.248-.663l-2.675,2.089L379.7,1801.4Z" transform="translate(-379.7 -1797.872)" fill="#fa8b17"/>
          </g>
        </g>
      </g>
      <g id="组_4817" data-name="组 4817" transform="translate(113.531 60.203)">
        <g id="组_4813" data-name="组 4813" transform="translate(0 101.833)">
          <ellipse id="椭圆_6411" data-name="椭圆 6411" cx="47.209" cy="6.586" rx="47.209" ry="6.586" fill="#fff" opacity="0"/>
          <ellipse id="椭圆_6412" data-name="椭圆 6412" cx="44.993" cy="6.28" rx="44.993" ry="6.28" transform="translate(2.217 0.306)" fill="#fcfdff" opacity="0.056"/>
          <ellipse id="椭圆_6413" data-name="椭圆 6413" cx="42.789" cy="5.974" rx="42.789" ry="5.974" transform="translate(4.42 0.611)" fill="#f9fbfe" opacity="0.111"/>
          <ellipse id="椭圆_6414" data-name="椭圆 6414" cx="40.573" cy="5.669" rx="40.573" ry="5.669" transform="translate(6.637 0.917)" fill="#f5f8fe" opacity="0.167"/>
          <ellipse id="椭圆_6415" data-name="椭圆 6415" cx="38.369" cy="5.35" rx="38.369" ry="5.35" transform="translate(8.841 1.236)" fill="#f2f6fd" opacity="0.222"/>
          <ellipse id="椭圆_6416" data-name="椭圆 6416" cx="36.152" cy="5.045" rx="36.152" ry="5.045" transform="translate(11.057 1.541)" fill="#eff4fd" opacity="0.278"/>
          <ellipse id="椭圆_6417" data-name="椭圆 6417" cx="33.949" cy="4.739" rx="33.949" ry="4.739" transform="translate(13.261 1.847)" fill="#ecf2fc" opacity="0.333"/>
          <ellipse id="椭圆_6418" data-name="椭圆 6418" cx="31.745" cy="4.433" rx="31.745" ry="4.433" transform="translate(15.465 2.153)" fill="#e9f0fc" opacity="0.389"/>
          <ellipse id="椭圆_6419" data-name="椭圆 6419" cx="29.528" cy="4.127" rx="29.528" ry="4.127" transform="translate(17.681 2.459)" fill="#e6eefb" opacity="0.444"/>
          <ellipse id="椭圆_6420" data-name="椭圆 6420" cx="27.324" cy="3.809" rx="27.324" ry="3.809" transform="translate(19.885 2.777)" fill="#e3ecfb" opacity="0.5"/>
          <ellipse id="椭圆_6421" data-name="椭圆 6421" cx="25.108" cy="3.503" rx="25.108" ry="3.503" transform="translate(22.102 3.083)" fill="#dfe9fb" opacity="0.556"/>
          <ellipse id="椭圆_6422" data-name="椭圆 6422" cx="22.904" cy="3.197" rx="22.904" ry="3.197" transform="translate(24.305 3.388)" fill="#dce7fa" opacity="0.611"/>
          <ellipse id="椭圆_6423" data-name="椭圆 6423" cx="20.688" cy="2.892" rx="20.688" ry="2.892" transform="translate(26.522 3.694)" fill="#d9e5fa" opacity="0.667"/>
          <ellipse id="椭圆_6424" data-name="椭圆 6424" cx="18.484" cy="2.586" rx="18.484" ry="2.586" transform="translate(28.726 4)" fill="#d6e3f9" opacity="0.722"/>
          <ellipse id="椭圆_6425" data-name="椭圆 6425" cx="16.267" cy="2.267" rx="16.267" ry="2.267" transform="translate(30.942 4.318)" fill="#d3e1f9" opacity="0.778"/>
          <ellipse id="椭圆_6426" data-name="椭圆 6426" cx="14.063" cy="1.962" rx="14.063" ry="1.962" transform="translate(33.146 4.624)" fill="#d0dff8" opacity="0.833"/>
          <path id="路径_5815" data-name="路径 5815" d="M1305.294,1755.656c0,.917-5.312,1.656-11.847,1.656s-11.847-.739-11.847-1.656,5.312-1.656,11.847-1.656S1305.294,1754.739,1305.294,1755.656Z" transform="translate(-1246.224 -1749.07)" fill="#ccdcf8" opacity="0.889"/>
          <path id="路径_5816" data-name="路径 5816" d="M1318.086,1757.75c0,.739-4.318,1.35-9.643,1.35s-9.643-.6-9.643-1.35,4.318-1.35,9.643-1.35S1318.086,1757.011,1318.086,1757.75Z" transform="translate(-1261.234 -1751.164)" fill="#c9daf7" opacity="0.944"/>
          <path id="路径_5817" data-name="路径 5817" d="M1330.979,1759.932c0,.573-3.325,1.032-7.439,1.032s-7.439-.459-7.439-1.032,3.325-1.032,7.439-1.032S1330.979,1759.359,1330.979,1759.932Z" transform="translate(-1276.33 -1753.346)" fill="#c6d8f7"/>
        </g>
        <g id="组_4816" data-name="组 4816" transform="translate(23.804)">
          <g id="组_4815" data-name="组 4815">
            <path id="路径_5818" data-name="路径 5818" d="M1285.025,1050.135h-50.713c-2.267,0-3.758-2.395-3.325-5.338l13.121-89.96c.433-2.955,2.611-5.337,4.879-5.337H1299.7c2.268,0,3.758,2.395,3.325,5.337L1289.9,1044.8C1289.47,1047.74,1287.292,1050.135,1285.025,1050.135Z" transform="translate(-1225.799 -945.22)" fill="#628cd2"/>
            <g id="组_4814" data-name="组 4814">
              <path id="路径_5819" data-name="路径 5819" d="M1248.125,1019.735h-50.712c-2.268,0-3.758-2.395-3.325-5.337l13.121-89.961c.433-2.955,2.612-5.337,4.879-5.337H1262.8c2.268,0,3.758,2.395,3.325,5.337L1253,1014.4C1252.571,1017.353,1250.379,1019.735,1248.125,1019.735Z" transform="translate(-1193.599 -918.692)" fill="#fff"/>
              <path id="路径_5820" data-name="路径 5820" d="M1245.294,1017.363h-50.713a3.451,3.451,0,0,1-2.637-1.223,5.713,5.713,0,0,1-1.1-4.586l13.121-89.96c.459-3.146,2.828-5.694,5.286-5.694h50.713a3.451,3.451,0,0,1,2.637,1.223,5.713,5.713,0,0,1,1.1,4.586l-13.121,89.96C1250.122,1014.8,1247.752,1017.363,1245.294,1017.363Zm-36.05-100.648c-2.064,0-4.076,2.242-4.471,4.994l-13.121,89.96a4.958,4.958,0,0,0,.9,3.923,2.612,2.612,0,0,0,2.013.943h50.713c2.064,0,4.076-2.242,4.471-4.994l13.121-89.961a4.959,4.959,0,0,0-.9-3.923,2.612,2.612,0,0,0-2.013-.943h-50.713Z" transform="translate(-1190.768 -915.9)" fill="#2c49a2"/>
            </g>
            <path id="路径_5821" data-name="路径 5821" d="M1282.787,1063.042H1232.1l12.089-82.942h50.7Z" transform="translate(-1226.835 -971.922)" fill="#e0eaff"/>
            <path id="路径_5822" data-name="路径 5822" d="M1525.481,950.179a.965.965,0,0,1-.8.879c-.37,0-.611-.4-.548-.879a.965.965,0,0,1,.8-.879C1525.3,949.3,1525.544,949.682,1525.481,950.179Z" transform="translate(-1481.656 -945.045)" fill="#1a317a"/>
            <path id="路径_5823" data-name="路径 5823" d="M1404.012,1664.688h-3.936c-.713,0-1.185-.764-1.044-1.694h0a1.85,1.85,0,0,1,1.541-1.694h3.936c.714,0,1.185.764,1.045,1.694h0A1.849,1.849,0,0,1,1404.012,1664.688Z" transform="translate(-1372.48 -1566.346)" fill="#1a317a"/>
          </g>
        </g>
      </g>
      <g id="组_4828" data-name="组 4828" transform="translate(149.519 73.273)">
        <path id="路径_5824" data-name="路径 5824" d="M1385.888,1571.69l-33.031-.293a1.611,1.611,0,0,1-1.618-1.924l.1-.675a2.274,2.274,0,0,1,2.191-1.9l33.031.293a1.611,1.611,0,0,1,1.618,1.924l-.1.675A2.274,2.274,0,0,1,1385.888,1571.69Z" transform="translate(-1342.96 -1497.041)" fill="#a7c1ff"/>
        <g id="组_4820" data-name="组 4820" transform="translate(12.368)">
          <path id="路径_5825" data-name="路径 5825" d="M1442.255,1031.481h-55.477a3.2,3.2,0,0,1-3.236-3.771l.765-5.439a4.489,4.489,0,0,1,4.293-3.771h55.477a3.2,3.2,0,0,1,3.236,3.771l-.764,5.439A4.468,4.468,0,0,1,1442.255,1031.481Z" transform="translate(-1383.505 -1018.5)" fill="url(#linear-gradient-11)"/>
          <g id="组_4818" data-name="组 4818" transform="translate(4.343 3.045)">
            <path id="路径_5826" data-name="路径 5826" d="M1422.555,1049.279H1417.6l.153-1.1c.471-.331.892-.65,1.261-.943s.713-.573,1.006-.841a5.18,5.18,0,0,0,.841-.917,1.786,1.786,0,0,0,.331-.815.832.832,0,0,0-.178-.726,1.01,1.01,0,0,0-.752-.255,2.045,2.045,0,0,0-.5.064,4.356,4.356,0,0,0-.484.166,2.139,2.139,0,0,0-.42.217c-.115.076-.2.127-.268.166h-.127l.2-1.478a6.107,6.107,0,0,1,.93-.28,5.756,5.756,0,0,1,1.185-.14,2.4,2.4,0,0,1,1.745.535,1.68,1.68,0,0,1,.408,1.5,2.889,2.889,0,0,1-.446,1.172,6.221,6.221,0,0,1-1.108,1.236c-.306.267-.612.51-.892.713s-.5.357-.624.446h2.866Z" transform="translate(-1417.6 -1042.4)" fill="#2c49a2"/>
            <path id="路径_5827" data-name="路径 5827" d="M1467.416,1048.585h-.917l-.229,1.592h-1.63l.229-1.592H1461.9l.178-1.261,3.465-3.923h1.694l-.561,3.962h.917Zm-2.369-1.21.318-2.293-2.038,2.293Z" transform="translate(-1456.256 -1043.272)" fill="#2c49a2"/>
            <path id="路径_5828" data-name="路径 5828" d="M1524.072,1048.188h-1.057l.242-1.681a2.216,2.216,0,0,0,.038-.408.69.69,0,0,0-.026-.306.3.3,0,0,0-.14-.166.527.527,0,0,0-.267-.051.667.667,0,0,0-.268.051q-.133.057-.306.153l-.331,2.407H1520.9l.662-4.688h1.057l-.242,1.681a2.73,2.73,0,0,1,.548-.344,1.392,1.392,0,0,1,.561-.127.864.864,0,0,1,.752.331,1.278,1.278,0,0,1,.14.943Z" transform="translate(-1507.741 -1043.36)" fill="#2c49a2"/>
          </g>
          <g id="组_4819" data-name="组 4819" transform="translate(24.283 4.025)">
            <path id="路径_5829" data-name="路径 5829" d="M1600.537,1052.164h-21.223a.876.876,0,0,1-.879-1.032h0a1.226,1.226,0,0,1,1.172-1.032h21.223a.875.875,0,0,1,.879,1.032h0A1.224,1.224,0,0,1,1600.537,1052.164Z" transform="translate(-1577.878 -1050.1)" fill="#e4eeff"/>
            <path id="路径_5830" data-name="路径 5830" d="M1607.319,1082.264h-32.305a.875.875,0,0,1-.879-1.032h0a1.225,1.225,0,0,1,1.172-1.032h32.305a.875.875,0,0,1,.879,1.032h0A1.213,1.213,0,0,1,1607.319,1082.264Z" transform="translate(-1574.125 -1076.366)" fill="#e4eeff"/>
          </g>
        </g>
        <g id="组_4827" data-name="组 4827" transform="translate(0 27.783)">
          <g id="组_4821" data-name="组 4821" transform="translate(1.618 1.503)">
            <path id="路径_5831" data-name="路径 5831" d="M1306.411,1248.4h34.649c2.255,0,3.885,1.376,3.643,3.07l-2.764,19.719c-.242,1.694-2.255,3.07-4.51,3.07h-25.516l-10.446,5.185c-.471.229-1.019-.076-.828-.471l2.344-4.713h-.2c-2.255,0-3.885-1.376-3.643-3.07l2.764-19.719C1302.143,1249.763,1304.169,1248.4,1306.411,1248.4Z" transform="translate(-1299.113 -1248.4)" fill="#628cd2"/>
          </g>
          <g id="组_4822" data-name="组 4822">
            <path id="路径_5832" data-name="路径 5832" d="M1293.711,1236.6h34.649c2.255,0,3.885,1.376,3.643,3.07l-2.764,19.719c-.242,1.694-2.255,3.07-4.51,3.07h-25.516l-10.446,5.185c-.471.229-1.019-.076-.828-.471l2.344-4.713h-.2c-2.255,0-3.885-1.376-3.643-3.07l2.764-19.719C1289.443,1237.976,1291.456,1236.6,1293.711,1236.6Z" transform="translate(-1286.413 -1236.6)" fill="#a7c1ff"/>
          </g>
          <g id="组_4825" data-name="组 4825" transform="translate(2.809 3.146)">
            <path id="路径_5833" data-name="路径 5833" d="M1324.658,1263.249h36.815a.981.981,0,0,0,.943-.828l.038-.293a.7.7,0,0,0-.713-.828h-36.815a.98.98,0,0,0-.943.828l-.038.293A.7.7,0,0,0,1324.658,1263.249Z" transform="translate(-1321.965 -1261.3)" fill="#fff"/>
            <g id="组_4824" data-name="组 4824" transform="translate(0 6.369)">
              <path id="路径_5834" data-name="路径 5834" d="M1317.938,1312.255h20.471a.5.5,0,0,0,.471-.407l.026-.14a.344.344,0,0,0-.357-.407h-20.471a.5.5,0,0,0-.471.407l-.025.14A.349.349,0,0,0,1317.938,1312.255Z" transform="translate(-1316.417 -1311.3)" fill="#628cd2"/>
              <path id="路径_5835" data-name="路径 5835" d="M1311.838,1356.055h20.471a.5.5,0,0,0,.471-.408l.026-.14a.344.344,0,0,0-.357-.408h-20.471a.5.5,0,0,0-.471.408l-.026.14A.355.355,0,0,0,1311.838,1356.055Z" transform="translate(-1311.094 -1349.521)" fill="#628cd2"/>
              <path id="路径_5836" data-name="路径 5836" d="M1308.887,1377.868h29.694a.571.571,0,0,0,.548-.484h0a.413.413,0,0,0-.42-.484h-29.694a.571.571,0,0,0-.547.484h0A.413.413,0,0,0,1308.887,1377.868Z" transform="translate(-1308.462 -1368.543)" fill="#628cd2"/>
              <g id="组_4823" data-name="组 4823" transform="translate(0.776 2.79)">
                <path id="路径_5837" data-name="路径 5837" d="M1401.452,1334.155h9.439a.5.5,0,0,0,.471-.408l.026-.14a.344.344,0,0,0-.357-.408h-9.452a.5.5,0,0,0-.471.408l-.025.14A.364.364,0,0,0,1401.452,1334.155Z" transform="translate(-1390.058 -1333.2)" fill="#628cd2"/>
                <path id="路径_5838" data-name="路径 5838" d="M1365.219,1334.155h3.286a.5.5,0,0,0,.471-.408l.025-.14a.344.344,0,0,0-.357-.408h-3.286a.5.5,0,0,0-.471.408l-.026.14A.349.349,0,0,0,1365.219,1334.155Z" transform="translate(-1358.449 -1333.2)" fill="#628cd2"/>
                <path id="路径_5839" data-name="路径 5839" d="M1314.919,1334.155h4.535a.5.5,0,0,0,.471-.408l.026-.14a.344.344,0,0,0-.357-.408h-4.535a.5.5,0,0,0-.471.408l-.025.14A.349.349,0,0,0,1314.919,1334.155Z" transform="translate(-1314.557 -1333.2)" fill="#628cd2"/>
              </g>
            </g>
          </g>
          <g id="组_4826" data-name="组 4826" transform="translate(30.444 7.185)">
            <path id="路径_5840" data-name="路径 5840" d="M1541.3,1300.9h3.07l1.248-7.9h-3.07Z" transform="translate(-1539.274 -1293)" fill="#fffefe"/>
            <path id="路径_5841" data-name="路径 5841" d="M1525.884,1311.9l-.484,3.083h7.885l.484-3.083Z" transform="translate(-1525.4 -1309.492)" fill="#fffefe"/>
          </g>
        </g>
      </g>
      <g id="组_4833" data-name="组 4833" transform="translate(123.008 97.744)">
        <g id="组_4829" data-name="组 4829" transform="translate(0 90.343)">
          <ellipse id="椭圆_6427" data-name="椭圆 6427" cx="45.26" cy="6.318" rx="45.26" ry="6.318" fill="#fff" opacity="0"/>
          <ellipse id="椭圆_6428" data-name="椭圆 6428" cx="43.146" cy="6.025" rx="43.146" ry="6.025" transform="translate(2.115 0.293)" fill="#fcfdff" opacity="0.056"/>
          <ellipse id="椭圆_6429" data-name="椭圆 6429" cx="41.031" cy="5.732" rx="41.031" ry="5.732" transform="translate(4.229 0.586)" fill="#f9fbfe" opacity="0.111"/>
          <ellipse id="椭圆_6430" data-name="椭圆 6430" cx="38.904" cy="5.427" rx="38.904" ry="5.427" transform="translate(6.357 0.892)" fill="#f5f8fe" opacity="0.167"/>
          <ellipse id="椭圆_6431" data-name="椭圆 6431" cx="36.789" cy="5.134" rx="36.789" ry="5.134" transform="translate(8.471 1.185)" fill="#f2f6fd" opacity="0.222"/>
          <ellipse id="椭圆_6432" data-name="椭圆 6432" cx="34.675" cy="4.841" rx="34.675" ry="4.841" transform="translate(10.586 1.478)" fill="#eff4fd" opacity="0.278"/>
          <ellipse id="椭圆_6433" data-name="椭圆 6433" cx="32.547" cy="4.548" rx="32.547" ry="4.548" transform="translate(12.713 1.771)" fill="#ecf2fc" opacity="0.333"/>
          <ellipse id="椭圆_6434" data-name="椭圆 6434" cx="30.433" cy="4.255" rx="30.433" ry="4.255" transform="translate(14.828 2.064)" fill="#e9f0fc" opacity="0.389"/>
          <ellipse id="椭圆_6435" data-name="椭圆 6435" cx="28.318" cy="3.949" rx="28.318" ry="3.949" transform="translate(16.942 2.369)" fill="#e6eefb" opacity="0.444"/>
          <ellipse id="椭圆_6436" data-name="椭圆 6436" cx="26.191" cy="3.656" rx="26.191" ry="3.656" transform="translate(19.07 2.662)" fill="#e3ecfb" opacity="0.5"/>
          <ellipse id="椭圆_6437" data-name="椭圆 6437" cx="24.076" cy="3.363" rx="24.076" ry="3.363" transform="translate(21.184 2.955)" fill="#dfe9fb" opacity="0.556"/>
          <ellipse id="椭圆_6438" data-name="椭圆 6438" cx="21.961" cy="3.07" rx="21.961" ry="3.07" transform="translate(23.299 3.248)" fill="#dce7fa" opacity="0.611"/>
          <ellipse id="椭圆_6439" data-name="椭圆 6439" cx="19.847" cy="2.764" rx="19.847" ry="2.764" transform="translate(25.414 3.554)" fill="#d9e5fa" opacity="0.667"/>
          <ellipse id="椭圆_6440" data-name="椭圆 6440" cx="17.719" cy="2.471" rx="17.719" ry="2.471" transform="translate(27.541 3.847)" fill="#d6e3f9" opacity="0.722"/>
          <ellipse id="椭圆_6441" data-name="椭圆 6441" cx="15.605" cy="2.178" rx="15.605" ry="2.178" transform="translate(29.656 4.14)" fill="#d3e1f9" opacity="0.778"/>
          <ellipse id="椭圆_6442" data-name="椭圆 6442" cx="13.49" cy="1.885" rx="13.49" ry="1.885" transform="translate(31.77 4.433)" fill="#d0dff8" opacity="0.833"/>
          <path id="路径_5842" data-name="路径 5842" d="M1367.126,1958.492c0,.879-5.083,1.592-11.363,1.592s-11.363-.713-11.363-1.592,5.083-1.592,11.363-1.592S1367.126,1957.626,1367.126,1958.492Z" transform="translate(-1310.502 -1952.174)" fill="#ccdcf8" opacity="0.889"/>
          <path id="路径_5843" data-name="路径 5843" d="M1379.5,1960.586c0,.713-4.14,1.287-9.248,1.287s-9.248-.573-9.248-1.287,4.14-1.287,9.248-1.287S1379.5,1959.873,1379.5,1960.586Z" transform="translate(-1324.988 -1954.268)" fill="#c9daf7" opacity="0.944"/>
          <path id="路径_5844" data-name="路径 5844" d="M1391.767,1962.594c0,.548-3.2.994-7.134.994s-7.134-.446-7.134-.994,3.2-.994,7.134-.994S1391.767,1962.046,1391.767,1962.594Z" transform="translate(-1339.386 -1956.275)" fill="#c6d8f7"/>
        </g>
        <g id="组_4832" data-name="组 4832" transform="translate(12.93)">
          <g id="组_4830" data-name="组 4830" transform="translate(11.681 1.11)">
            <path id="路径_5845" data-name="路径 5845" d="M1272.965,1654.3a1.459,1.459,0,0,1-.013-2.917l42.777-.191a3.34,3.34,0,0,0,2.866-1.822,2.512,2.512,0,0,0,.026-2.561,45.562,45.562,0,0,1-6.764-30.165l.662-5.822a1.462,1.462,0,0,1,2.9.331l-.662,5.822a42.593,42.593,0,0,0,6.306,28.229,5.365,5.365,0,0,1,.1,5.541,6.261,6.261,0,0,1-5.439,3.363l-42.764.191Z" transform="translate(-1271.5 -1559.82)" fill="#2c49a2"/>
            <path id="路径_5846" data-name="路径 5846" d="M1478.579,1271.946a16.113,16.113,0,0,1-12.293-5.338,16.276,16.276,0,0,1-3.758-13.172l2.522-18.8a21.636,21.636,0,0,1,9.923-15.1,1.466,1.466,0,0,1,1.579,2.471,18.633,18.633,0,0,0-8.586,13.006l-2.522,18.8a13.465,13.465,0,0,0,3.057,10.853,13.187,13.187,0,0,0,10.089,4.344h.1l1.822-.013c8.662-.051,16.662-7.146,17.822-15.821l2.522-18.8a13.354,13.354,0,0,0-3.72-11.541,1.459,1.459,0,0,1,2.038-2.089,16.265,16.265,0,0,1,4.573,14.025l-2.522,18.8c-1.35,10.051-10.637,18.293-20.7,18.356l-1.822.013Z" transform="translate(-1438.044 -1219.311)" fill="#2c49a2"/>
            <path id="路径_5847" data-name="路径 5847" d="M1470.413,1488.493a17.248,17.248,0,0,1-13.185-5.745,17.494,17.494,0,0,1-4.051-14.127,2.661,2.661,0,1,1,5.274.713,12.264,12.264,0,0,0,2.764,9.911,12,12,0,0,0,9.2,3.949h.1l1.822-.013c8.089-.051,15.554-6.688,16.649-14.777a2.661,2.661,0,0,1,5.274.713,22.847,22.847,0,0,1-21.885,19.388l-1.822.013A.274.274,0,0,0,1470.413,1488.493Z" transform="translate(-1429.878 -1434.661)" fill="#1a317a"/>
          </g>
          <path id="路径_5848" data-name="路径 5848" d="M1553.257,1216.689h0a1.457,1.457,0,0,1-1.465-1.707l.382-2.675a2.039,2.039,0,0,1,1.936-1.707h0a1.456,1.456,0,0,1,1.465,1.707l-.382,2.675A2.006,2.006,0,0,1,1553.257,1216.689Z" transform="translate(-1504.392 -1210.6)" fill="#1a317a"/>
          <path id="路径_5849" data-name="路径 5849" d="M1726.357,1220.889h0a1.456,1.456,0,0,1-1.465-1.707l.382-2.675a2.039,2.039,0,0,1,1.937-1.707h0a1.456,1.456,0,0,1,1.465,1.707l-.382,2.675A2.027,2.027,0,0,1,1726.357,1220.889Z" transform="translate(-1655.441 -1214.265)" fill="#1a317a"/>
          <g id="组_4831" data-name="组 4831" transform="translate(0 89.871)">
            <path id="路径_5850" data-name="路径 5850" d="M1180.118,1931.869l-.318,2.229.038.025c.268,1.236,4.382,2.217,9.554,2.217a30.168,30.168,0,0,0,7.847-.9h0c1.427-.42,2.306-.943,2.395-1.516l.28-1.949-4.153-.013a33.345,33.345,0,0,0-5.694-.458,35.845,35.845,0,0,0-5.516.408Zm13.49.064-6.879-.026c1.032-.1,2.153-.153,3.312-.153A28.146,28.146,0,0,1,1193.609,1931.933Z" transform="translate(-1179.8 -1929.538)" fill="#628cd2"/>
            <ellipse id="椭圆_6443" data-name="椭圆 6443" cx="9.388" cy="2.293" rx="9.388" ry="2.293" transform="translate(0.8 0.286) rotate(-0.512)" fill="#e0eaff"/>
            <path id="路径_5851" data-name="路径 5851" d="M1182.163,1918.52c.191-1.338,4.79-2.42,10.255-2.42s9.758,1.083,9.567,2.42-4.79,2.42-10.255,2.42S1181.985,1919.858,1182.163,1918.52Zm1.057,0c-.166,1.2,3.669,2.166,8.56,2.166s8.994-.968,9.159-2.166-3.669-2.166-8.56-2.166S1183.386,1917.323,1183.221,1918.52Z" transform="translate(-1181.858 -1916.1)" fill="#e0eaff"/>
          </g>
          <path id="路径_5852" data-name="路径 5852" d="M1572.818,1604.453h-4.433a1.581,1.581,0,0,1-1.605-1.86l.624-4.433a2.207,2.207,0,0,1,2.127-1.86h4.433a1.581,1.581,0,0,1,1.6,1.86l-.624,4.433A2.219,2.219,0,0,1,1572.818,1604.453Z" transform="translate(-1517.468 -1547.167)" fill="#1a317a"/>
        </g>
      </g>
      <g id="组_4842" data-name="组 4842" transform="translate(110.039 55.315)">
        <g id="组_4834" data-name="组 4834" transform="translate(28.682 22.152)">
          <path id="路径_5853" data-name="路径 5853" d="M1224.507,1076.617c.115-1.3-2.777-17.567-5.1-21.185s-5.325-4.726-5.567-2.841c-1.121,8.841,3.21,20.522,4.56,23.9a3.466,3.466,0,0,1,.191,1.936,92.279,92.279,0,0,0-1.261,10.777.683.683,0,0,1-.166.408l-1.248,1.427a.286.286,0,0,0,.025.471c.242.14.471.2.471.943a24.183,24.183,0,0,1-1.567,5.516.213.213,0,0,0,.255.28,12.905,12.905,0,0,0,3.261-1.274c.624-.56,2.586-4.127,2.42-5.822a2.474,2.474,0,0,0-.229-.8A44.792,44.792,0,0,0,1224.507,1076.617Z" transform="translate(-1212.13 -1051.652)" fill="#fac4b6"/>
          <path id="路径_5854" data-name="路径 5854" d="M1216.144,1116.422a6.056,6.056,0,0,0,.153-1.083c0-.739-.229-.8-.471-.943a.292.292,0,0,1-.026-.471l1.248-1.427a.64.64,0,0,0,.166-.408,92.3,92.3,0,0,1,1.261-10.777,3.469,3.469,0,0,0-.191-1.936c-1.236-3.07-4.943-13.044-4.739-21.477q.153.21.306.459c3.554,5.758,6.994,18.522,6.892,21.312s-2.191,12.764-3.325,14.216A11.674,11.674,0,0,0,1216.144,1116.422Z" transform="translate(-1212.022 -1074.526)" fill="#f5b3a6"/>
          <path id="路径_5855" data-name="路径 5855" d="M1213.46,1066.281c-.076-1.3-1.745-8.089-4.217-11.6s-4.891-3.72-7.235-2.777c-1.758.713,3.427,17.554,3.427,17.554S1213.536,1067.581,1213.46,1066.281Z" transform="translate(-1201.644 -1051.417)" fill="#fa8b17"/>
        </g>
        <g id="组_4835" data-name="组 4835" transform="translate(16.522 53.856)">
          <path id="路径_5856" data-name="路径 5856" d="M1118.277,1812.3s-2.267,2.637-4.458,3.006c-1.4.229-3.529.369-4.866.446a3.076,3.076,0,0,0-2.548,1.541c-.268.5-.357.994.127,1.325,1.236.828,6.535.663,8.5.994a63.082,63.082,0,0,0,6.841.306,1.884,1.884,0,0,0,1.771-1.185,3.806,3.806,0,0,0-.548-4.025A6.389,6.389,0,0,0,1118.277,1812.3Z" transform="translate(-1106.186 -1747.074)" fill="url(#linear-gradient-12)"/>
          <path id="路径_5857" data-name="路径 5857" d="M1113.577,1311.07l12.1-3.07a29.769,29.769,0,0,1,.968,6.229c.459,12.841-1.592,33.465-1.592,33.465s2.42,6.522,2.42,27.413c0,2.637-2.726,2.217-4.42,1.694a2.4,2.4,0,0,1-1.63-1.745c-1.083-4.586-6.369-33.172-10.522-59.617C1110.66,1313.847,1113.577,1311.07,1113.577,1311.07Z" transform="translate(-1110.289 -1307.019)" fill="#2c49a2"/>
          <path id="路径_5858" data-name="路径 5858" d="M1124.93,1303.765l1.656-3.465c-.382.739,1.376,9.044.535,14.242-1.809,11.261-1.325,15.057,1.019,25.783a268.484,268.484,0,0,0,4.79,29.961c-.28-.063-.548-.14-.79-.2a2.4,2.4,0,0,1-1.63-1.745c-.968-4.115-5.325-27.541-9.21-51.413Z" transform="translate(-1119.375 -1300.3)" fill="url(#linear-gradient-13)"/>
        </g>
        <g id="组_4836" data-name="组 4836" transform="translate(0 54.607)">
          <path id="路径_5859" data-name="路径 5859" d="M988.577,1839.1s-2.268,2.637-4.459,3.006c-1.4.229-3.529.37-4.866.446a3.077,3.077,0,0,0-2.548,1.541c-.268.5-.357.994.127,1.325,1.236.828,6.535.662,8.5.994a66.425,66.425,0,0,0,6.955.306,1.72,1.72,0,0,0,1.58-1.019,3.875,3.875,0,0,0-.471-4.2A6.4,6.4,0,0,0,988.577,1839.1Z" transform="translate(-976.486 -1771.211)" fill="url(#linear-gradient-14)"/>
          <path id="路径_5860" data-name="路径 5860" d="M1041.941,1375.753l-1.9-30.165s-2.318-19.286-.815-27.21c.688-3.618,2.764-12.178,2.764-12.178s10.166.586,10.675,1.452,2.841,4.458,0,16.828c-2.369,10.331-3.618,17.2-3.962,19.2a3.29,3.29,0,0,0,.013,1.172c.357,1.809,1.732,8.586,1.452,11.452-.752,7.669-2.65,20.522-2.65,20.522l-.051.013a6.114,6.114,0,0,1-5.529-1.083Z" transform="translate(-1030.793 -1306.2)" fill="#628cd2"/>
          <path id="路径_5861" data-name="路径 5861" d="M1041.344,1323.423c-.752,11.669,1.3,26,1.478,34.165.14,6.306-.306,14.891-.522,18.5-.153-.1-.306-.217-.459-.331l-1.9-30.165s-2.318-19.286-.815-27.21c.688-3.618,2.764-12.178,2.764-12.178s1.032.064,2.446.166l1.618,1.885C1045.968,1308.251,1042.1,1311.741,1041.344,1323.423Z" transform="translate(-1030.705 -1306.2)" fill="#2c49a2"/>
        </g>
        <g id="组_4839" data-name="组 4839" transform="translate(14.588)">
          <g id="组_4837" data-name="组 4837" transform="translate(4.815 11.207)">
            <path id="路径_5862" data-name="路径 5862" d="M1129,966.124l-.2,12.242s-.076,2.178,4.14,3.439c5.376,1.6,4.025-5.108,3.389-5.847-1.4-1.618-.255-10.458-.255-10.458Z" transform="translate(-1128.799 -965.5)" fill="#fac4b6"/>
          </g>
          <path id="路径_5863" data-name="路径 5863" d="M1129.527,966.124l7.07-.624s-.267,2.025-.382,4.318c-2.178,2.191-4.955,3.809-6.815,3.949Z" transform="translate(-1124.508 -954.293)" fill="#f5b3a6"/>
          <g id="组_4838" data-name="组 4838" transform="translate(0.163 0.716)">
            <path id="路径_5864" data-name="路径 5864" d="M1106.477,891.258c-.726,5.006-8.255,10.446-11.057,9.516-1.783-.586-3.07-4.318-3.134-9.376-.077-5.019,1.962-7.732,7.274-8.229C1102.859,882.863,1107.19,886.252,1106.477,891.258Z" transform="translate(-1092.284 -883.15)" fill="#fac4b6"/>
          </g>
          <path id="路径_5865" data-name="路径 5865" d="M1092.275,879.777a5.785,5.785,0,0,1,6.178-2.025.869.869,0,0,0,.51,0,5.518,5.518,0,0,1,1.86-.217c1.223.064,3.618,1.949,4.688,4.93s-1.006,10-2.854,11.732c-.548.51-2.726.026-3.733.191-2.153.344-1.987-.828-2.968-2.764l-.535-1.045a.311.311,0,0,1,.127-.42,1.729,1.729,0,0,0,.7-.688,1.419,1.419,0,0,0-.331-1.974,1.3,1.3,0,0,0-1.49.229.322.322,0,0,1-.5-.089l-1.592-3.146a.3.3,0,0,0-.14-.14C1091.728,884.108,1089.715,882.809,1092.275,879.777Z" transform="translate(-1091.002 -877.525)" fill="url(#linear-gradient-15)"/>
        </g>
        <g id="组_4840" data-name="组 4840" transform="translate(9.68 20.702)">
          <path id="路径_5866" data-name="路径 5866" d="M1052.6,1045.921a2.023,2.023,0,0,1,.9-1.682c2.064-1.337,7.35-4.369,12.675-4.191a30.638,30.638,0,0,1,10.012,1.911,2,2,0,0,1,1.223,1.388,21.529,21.529,0,0,1,.509,7.07c-.446,4.51-2.433,12.637-2.662,14.242s-.662,9.821.395,13.72-22.573,2.56-23.108,1.49,1.732-6.446.726-12.153C1052.681,1064.277,1052.6,1049.946,1052.6,1045.921Z" transform="translate(-1052.474 -1040.041)" fill="url(#linear-gradient-16)"/>
          <path id="路径_5867" data-name="路径 5867" d="M1052.554,1087.687c-.522-1.07,1.732-6.446.726-12.153-.713-4.013-.688-22.853-.688-22.853a29.793,29.793,0,0,1,7.083-3.681c1.083,1.2,2.267,3.822,1.1,9.325-1.9,8.93-3.783,14.9-4.815,20.063-.968,4.866-3.21,8.127,2.777,9.274,5.707,1.083,13.758,1.147,16.93-1.427C1076.554,1090.095,1053.089,1088.757,1052.554,1087.687Z" transform="translate(-1052.474 -1047.859)" fill="#ff5f18" opacity="0.55"/>
        </g>
        <g id="组_4841" data-name="组 4841" transform="translate(3.675 22.979)">
          <path id="路径_5868" data-name="路径 5868" d="M1015.282,1065.282c-1.452-.688-4.7.739-5.834,5.414-2.357,9.8-.854,14.216-1.325,15.592a49.789,49.789,0,0,0-1.465,11.337c.051,2.357.038,4.229.026,5.35-1.274-.153-1.529,6.1-1.236,6.879a12.465,12.465,0,0,0,2.293,2.637.212.212,0,0,0,.357-.127,24.364,24.364,0,0,1,1.223-5.6c.357-.65.586-.6.853-.611a.293.293,0,0,0,.242-.408s-.166-2.064-1.121-2.344c.866-2.038,2.331-6.637,3.032-8.764a35.431,35.431,0,0,0,1.248-6.509s1.567-4.662,3.261-12.306S1016.734,1065.97,1015.282,1065.282Z" transform="translate(-1005.338 -1064.201)" fill="#fac4b6"/>
          <path id="路径_5869" data-name="路径 5869" d="M1008.212,1111.491c-.051.344-.089.65-.115.866a.211.211,0,0,1-.357.127,13.612,13.612,0,0,1-2.293-2.637c-.293-.79-.038-7.044,1.236-6.879.013-1.121.026-2.994-.026-5.35a49.174,49.174,0,0,1,1.465-11.337c.459-1.376-1.032-5.783,1.325-15.592,1.07-4.446,4.051-5.949,5.592-5.5a4.07,4.07,0,0,0-1.783,2.166c-2.242,5.07-3.439,14.713-3.312,18.993s-2.408,17.707-2.408,17.707-2.191,2.764-1.3,4.981A4.894,4.894,0,0,0,1008.212,1111.491Z" transform="translate(-1005.338 -1064.194)" fill="#f5b3a6"/>
          <path id="路径_5870" data-name="路径 5870" d="M1035.121,1057.965c-6.981-1.006-7.821,13.834-7.821,13.834h0a9.966,9.966,0,0,0,6.777,2.153l1.338-.076s.65-1.885,1.21-4.331C1038.344,1061.914,1036.714,1058.194,1035.121,1057.965Z" transform="translate(-1024.502 -1057.916)" fill="#ffac31"/>
        </g>
      </g>
      <g id="组_4844" data-name="组 4844" transform="translate(58.36 24.802)">
        <path id="路径_5871" data-name="路径 5871" d="M594.217,663.684H580.5a1.168,1.168,0,0,1-1.185-1.376l1.924-13.732A1.633,1.633,0,0,1,582.8,647.2h13.72a1.168,1.168,0,0,1,1.185,1.376L595.784,662.3A1.623,1.623,0,0,1,594.217,663.684Z" transform="translate(-578.217 -646.028)" fill="#1a317a"/>
        <path id="路径_5872" data-name="路径 5872" d="M585.717,654.484H572a1.168,1.168,0,0,1-1.185-1.376l1.924-13.732A1.633,1.633,0,0,1,574.3,638h13.72a1.168,1.168,0,0,1,1.185,1.376l-1.924,13.72A1.634,1.634,0,0,1,585.717,654.484Z" transform="translate(-570.8 -638)" fill="#2c49a2"/>
        <g id="组_4843" data-name="组 4843" transform="translate(3.006 2.473)">
          <path id="路径_5873" data-name="路径 5873" d="M608.895,657.868c-1.363-1.044-3.465-.216-4.7,1.86-.65-2.076-2.522-2.917-4.178-1.86a5.2,5.2,0,0,0-1.834,5.682c.586,1.911,4.828,5.388,4.879,5.388.688-.318,4.943-3.465,6.064-5.388C610.372,661.473,610.258,658.925,608.895,657.868Z" transform="translate(-597.506 -657.411)" fill="url(#linear-gradient-17)"/>
          <path id="路径_5874" data-name="路径 5874" d="M599.279,691.582h0a.424.424,0,0,1-.42-.344l-.777-3.529c-.013-.025-.013-.025-.026-.025a.126.126,0,0,0-.115.076l-.9,1.8a.519.519,0,0,1-.459.293H594.6a.2.2,0,1,1,0-.408h1.974a.12.12,0,0,0,.1-.077l.9-1.8a.508.508,0,0,1,.51-.293.447.447,0,0,1,.369.344l.777,3.529c.013.026.013.026.038.038a.118.118,0,0,0,.115-.1l.344-1.261a.528.528,0,0,1,.5-.395h3.618a.2.2,0,0,1,0,.408h-3.618a.132.132,0,0,0-.115.1l-.344,1.261A.532.532,0,0,1,599.279,691.582Z" transform="translate(-594.4 -683.468)" fill="#fff"/>
        </g>
      </g>
      <g id="组_4845" data-name="组 4845" transform="translate(150.728 26.662)">
        <path id="路径_5875" data-name="路径 5875" d="M1321.1,665.058h4.841l1.974-12.458h-4.853Z" transform="translate(-1317.89 -652.6)" fill="url(#linear-gradient-18)"/>
        <path id="路径_5876" data-name="路径 5876" d="M1296.677,682.5l-.777,4.841h12.458l.764-4.841Z" transform="translate(-1295.9 -678.691)" fill="url(#linear-gradient-19)"/>
      </g>
    </g>
  </g>
</svg>
