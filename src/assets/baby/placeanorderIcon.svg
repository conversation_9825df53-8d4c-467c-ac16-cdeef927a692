<svg xmlns="http://www.w3.org/2000/svg" width="109.564" height="83.5" viewBox="0 0 109.564 83.5">
  <g id="送货_1_" data-name="送货 (1)" transform="translate(-29.5 -155.429)">
    <path id="路径_6028" data-name="路径 6028" d="M91.125,186.939H87.469a.469.469,0,0,1-.469-.469h0a.469.469,0,0,1,.469-.469h3.655a.469.469,0,0,1,.469.469h0A.485.485,0,0,1,91.125,186.939Z" transform="translate(-43.298 -23.021)" fill="#d8e3f3"/>
    <g id="组_4935" data-name="组 4935" transform="translate(38.021 155.429)">
      <g id="组_4922" data-name="组 4922" transform="translate(17.536 50.922)">
        <g id="组_4919" data-name="组 4919" transform="translate(2.64 2.663)">
          <rect id="矩形_2686" data-name="矩形 2686" width="0.148" height="16.4" transform="translate(15.167 4.365) rotate(67.238)" fill="#9eb6de"/>
          <rect id="矩形_2687" data-name="矩形 2687" width="16.4" height="0.148" transform="matrix(0.925, 0.379, -0.379, 0.925, 0.056, 4.435)" fill="#9eb6de"/>
          <rect id="矩形_2688" data-name="矩形 2688" width="0.148" height="16.4" transform="translate(10.682 0) rotate(22.452)" fill="#9eb6de"/>
          <rect id="矩形_2689" data-name="矩形 2689" width="16.4" height="0.148" transform="matrix(0.383, 0.924, -0.924, 0.383, 4.558, 0.008)" fill="#9eb6de"/>
        </g>
        <g id="组_4920" data-name="组 4920">
          <path id="路径_6054" data-name="路径 6054" d="M145.275,361.6a10.275,10.275,0,1,0,10.275,10.275A10.267,10.267,0,0,0,145.275,361.6Zm0,18.5a8.2,8.2,0,1,1,8.2-8.2A8.211,8.211,0,0,1,145.275,380.1Z" transform="translate(-135 -361.6)" fill="#174470"/>
        </g>
        <g id="组_4921" data-name="组 4921" transform="translate(2.05 2.075)">
          <rect id="矩形_2690" data-name="矩形 2690" width="0.321" height="16.4" transform="translate(8.052)" fill="#9eb6de"/>
          <rect id="矩形_2691" data-name="矩形 2691" width="16.4" height="0.321" transform="translate(0 8.052)" fill="#9eb6de"/>
          <rect id="矩形_2692" data-name="矩形 2692" width="16.4" height="0.321" transform="translate(2.521 2.29) rotate(45)" fill="#9eb6de"/>
          <rect id="矩形_2693" data-name="矩形 2693" width="0.321" height="16.4" transform="translate(13.908 2.29) rotate(45)" fill="#9eb6de"/>
        </g>
        <circle id="椭圆_6454" data-name="椭圆 6454" cx="1.062" cy="1.062" r="1.062" transform="translate(9.213 9.139)" fill="#174470"/>
      </g>
      <path id="路径_6055" data-name="路径 6055" d="M349.566,344.8c.1.3,1.754,4.273,1.754,4.273l-4.989,3.334-1.63-5.9Z" transform="translate(-275.37 -298.027)" fill="#174470"/>
      <path id="路径_6056" data-name="路径 6056" d="M89.406,319.8H80.984a.373.373,0,0,1-.346-.222l-1.284-2.445a.387.387,0,0,1,.272-.568h0a.414.414,0,0,1,.445.2l1.062,2.025a.418.418,0,0,0,.346.222h7.953a.39.39,0,0,1,.4.4h0A.428.428,0,0,1,89.406,319.8Z" transform="translate(-75.525 -276.753)" fill="#259e8d"/>
      <path id="路径_6057" data-name="路径 6057" d="M79.363,315.84H64.42a.416.416,0,0,1-.42-.42h0a.416.416,0,0,1,.42-.42H79.363a.416.416,0,0,1,.42.42h0A.433.433,0,0,1,79.363,315.84Z" transform="translate(-64 -275.588)" fill="#41b592"/>
      <g id="组_4925" data-name="组 4925" transform="translate(32.861)">
        <path id="路径_6058" data-name="路径 6058" d="M248.987,166.162c.42-.939.716-3.458-1.68-4.347s-6.274-1.309-7.706,2.865a6.587,6.587,0,0,0,1.26,6.842c.148.148-.049.321-.222.889a1.194,1.194,0,0,1-.741.741l3.137,1.778s1.7-2.445,1.9-2.643,1.309.222,1.63.2a9.232,9.232,0,0,0,1.655-3.063h0s.469-.494.617-.617c.124-.124-.049-.4-.222-.568s0-1.136,0-1.161A7.088,7.088,0,0,1,248.987,166.162Z" transform="translate(-228.77 -159.797)" fill="#f2a284"/>
        <path id="路径_6059" data-name="路径 6059" d="M303.1,236.595a5.407,5.407,0,0,0,2.47-.963c.864-.766,3.384-.124,3.8.469.445.593-.494,2-.939,1.9s.074-.4.222-.642c.148-.272.173-.519-.049-.716-.222-.173-.321.469-.864.741a3.731,3.731,0,0,1-2.3.321,4.09,4.09,0,0,0-1.852.074Z" transform="translate(-276.906 -215.558)" fill="#c83847"/>
        <path id="路径_6060" data-name="路径 6060" d="M238.179,218.015a4.967,4.967,0,0,0-.222,2.1c.049,1.951.815,3.977,2.915,4.619,3.977,1.26,9.83.469,11.09.2s2.939-.469,2.84-1.21.049-1.976-.815-2.025-8.793,1.68-10.719-1.062c-.79-1.136-1.284-2.816-2.371-3.73-.766-.642-1.8-.123-2.371.519A3.685,3.685,0,0,0,238.179,218.015Z" transform="translate(-227.842 -201.499)" fill="#41b592"/>
        <path id="路径_6061" data-name="路径 6061" d="M238,208.547s.815-1.013,1.655-.766,2.865,1.7,2.865,2.05a8.615,8.615,0,0,1-.37,1.457Z" transform="translate(-227.885 -194.822)" fill="#b8daab"/>
        <path id="路径_6062" data-name="路径 6062" d="M206.792,209.552s-2.667-.445-4.989,3.408a45.656,45.656,0,0,0-4.619,11.016c-.272,1.581-.148,3.359.469,3.754s9.139,1.581,10.621,1.26a1.9,1.9,0,0,0,1.482-1.8s2.865-11.608,2.2-13.658C211.263,211.479,208.422,209.775,206.792,209.552Z" transform="translate(-197.048 -196.173)" fill="#71bf86"/>
        <path id="路径_6063" data-name="路径 6063" d="M218.3,239.193s3.359-15.313,4.866-14.572c1.482.741,3.977,1.828,4.149,2.445.173.642-2.667,11.534-2.667,11.534l-.346.025s2.865-11.584,2.692-11.732-3.8-2.075-4.051-1.852a53.167,53.167,0,0,0-2.717,7.607c-.889,3.186-1.457,6.545-1.457,6.545Z" transform="translate(-213.051 -207.511)" fill="#41b592"/>
        <path id="路径_6064" data-name="路径 6064" d="M227.143,224.7a11.207,11.207,0,0,0,1.013,7.731,17.242,17.242,0,0,0,4.149,5.705l1.457-6.718Z" transform="translate(-219.498 -207.591)" fill="#41b592"/>
        <path id="路径_6065" data-name="路径 6065" d="M228.064,216.887a5.493,5.493,0,0,0-.222,2.3c.049,2.124.84,4.3,3.038,5.014,4.149,1.358,10.225.519,11.534.222,1.309-.272,3.063-.519,2.964-1.309-.124-.79.049-2.149-.84-2.2-.914-.074-9.139,1.8-11.139-1.136-.84-1.235-1.334-3.063-2.47-4.051-.79-.692-1.877-.148-2.47.568A1.915,1.915,0,0,0,228.064,216.887Z" transform="translate(-220.221 -200.593)" fill="#71bf86"/>
        <path id="路径_6066" data-name="路径 6066" d="M255.472,204.72l-2.272-.42,1.062,2.1Z" transform="translate(-239.331 -192.229)" fill="#c83847"/>
        <path id="路径_6067" data-name="路径 6067" d="M247,159.349c-.025.173-.148.222-.346.2a5.089,5.089,0,0,0-3.433-.124c-1.531.568-2,2.865-2.346,4.52a5.666,5.666,0,0,1-.445,1.655c-.543.716-2.667.864-3.779.3-1.111-.593-1.235-6.323.37-8.719,1.63-2.4,5.187-1.63,5.187-1.63A4.849,4.849,0,0,1,247,159.349Z" transform="translate(-226.241 -155.429)" fill="#41b592"/>
        <g id="组_4923" data-name="组 4923" transform="translate(17.317 10.283)">
          <path id="路径_6068" data-name="路径 6068" d="M268.109,197.663a1.75,1.75,0,0,1-.346-.025.754.754,0,0,1-.593-.469c-.025-.049,0-.1.049-.1.049-.025.1,0,.1.049h0a.521.521,0,0,0,.445.346,4.532,4.532,0,0,0,.642,0c.049,0,.1.025.1.074s-.025.1-.074.1A1.806,1.806,0,0,1,268.109,197.663Z" transform="translate(-267.161 -197.061)" fill="#c83847"/>
        </g>
        <ellipse id="椭圆_6455" data-name="椭圆 6455" cx="0.247" cy="0.395" rx="0.247" ry="0.395" transform="translate(18.685 6.588)" fill="#12315c"/>
        <path id="路径_6069" data-name="路径 6069" d="M270.553,178.926c.173-.222.469-.469.766-.3a.127.127,0,1,0,.123-.222.819.819,0,0,0-1.062.346c-.123.123.074.3.173.173Z" transform="translate(-252.238 -172.66)" fill="#12315c"/>
        <g id="组_4924" data-name="组 4924" transform="translate(14.635 3.611)" opacity="0.65">
          <path id="路径_6070" data-name="路径 6070" d="M262.549,173.3a7.351,7.351,0,0,1-.469,2.593c-.222.222-4.248.173-5.78-1.062.321-1.655.815-3.952,2.346-4.52a5.212,5.212,0,0,1,3.433.123A6.276,6.276,0,0,1,262.549,173.3Z" transform="translate(-256.3 -170.048)" fill="#fff"/>
        </g>
        <path id="路径_6071" data-name="路径 6071" d="M320.1,354.784s.741.766.864.963.642.889.914.84a9.839,9.839,0,0,0,2.42-1.161c.3-.3,2.1-.914,2.593-1.21.494-.321.445-1.161.074-1.334-.346-.2-1.433.42-1.877.568s-1.507.2-2.075.321a2.233,2.233,0,0,1-1.284.025c-.222-.123-.42-.247-.42-.247Z" transform="translate(-289.708 -304.084)" fill="#9eb6de"/>
        <path id="路径_6072" data-name="路径 6072" d="M231.849,280.1s5.508-.815,6.693,0,13.337,17.981,13.337,18.3-1.013,1.334-1.186,1.334a29.865,29.865,0,0,1-9.707-8.6c-3.532-5.211-3.236-5.409-4.125-5.286s-9.312.4-8.99-1.408C228.194,282.615,231.849,280.1,231.849,280.1Z" transform="translate(-220.253 -249.031)" fill="#12315c"/>
        <path id="路径_6073" data-name="路径 6073" d="M298.2,364.114a8.193,8.193,0,0,1,.667,1.062c.049.222.3,1.013.593,1.087a9.546,9.546,0,0,0,2.692-.37c.37-.2,2.272-.222,2.865-.37a.878.878,0,0,0,.519-1.26c-.272-.272-1.482-.025-1.976-.025-.469,0-1.482-.272-2.1-.321a2.3,2.3,0,0,1-1.235-.346,3.96,3.96,0,0,1-.321-.37Z" transform="translate(-273.217 -311.883)" fill="#9eb6de"/>
        <path id="路径_6074" data-name="路径 6074" d="M197.949,281.328s-2.272,7.854,3.112,7.854,12.374-1.186,12.547-.519a28.005,28.005,0,0,0,2.791,6.693c1.877,3.285,5.236,6.94,5.854,6.94s2.149-.864,1.976-1.186-7.163-18.3-8.645-19.265-3.977-1.408-8.571-.766A45.458,45.458,0,0,1,197.949,281.328Z" transform="translate(-197.368 -249.82)" fill="#1c3e95"/>
      </g>
      <g id="组_4929" data-name="组 4929" transform="translate(68.885 50.922)">
        <g id="组_4926" data-name="组 4926" transform="translate(2.664 2.669)">
          <rect id="矩形_2694" data-name="矩形 2694" width="0.148" height="16.4" transform="translate(15.157 4.375) rotate(67.238)" fill="#9eb6de"/>
          <rect id="矩形_2695" data-name="矩形 2695" width="16.4" height="0.148" transform="matrix(0.925, 0.379, -0.379, 0.925, 0.056, 4.436)" fill="#9eb6de"/>
          <rect id="矩形_2696" data-name="矩形 2696" width="0.148" height="16.4" transform="translate(10.681 0) rotate(22.452)" fill="#9eb6de"/>
          <rect id="矩形_2697" data-name="矩形 2697" width="16.4" height="0.148" transform="matrix(0.383, 0.924, -0.924, 0.383, 4.548, 0.02)" fill="#9eb6de"/>
        </g>
        <g id="组_4927" data-name="组 4927">
          <path id="路径_6075" data-name="路径 6075" d="M353.175,361.6a10.275,10.275,0,1,0,10.275,10.275A10.267,10.267,0,0,0,353.175,361.6Zm0,18.5a8.2,8.2,0,1,1,8.2-8.2A8.211,8.211,0,0,1,353.175,380.1Z" transform="translate(-342.9 -361.6)" fill="#174470"/>
        </g>
        <g id="组_4928" data-name="组 4928" transform="translate(2.075 2.075)">
          <rect id="矩形_2698" data-name="矩形 2698" width="0.321" height="16.4" transform="translate(8.027)" fill="#9eb6de"/>
          <rect id="矩形_2699" data-name="矩形 2699" width="16.4" height="0.321" transform="translate(0 8.052)" fill="#9eb6de"/>
          <rect id="矩形_2700" data-name="矩形 2700" width="16.4" height="0.321" transform="translate(2.516 2.302) rotate(45)" fill="#9eb6de"/>
          <rect id="矩形_2701" data-name="矩形 2701" width="0.321" height="16.4" transform="translate(13.885 2.285) rotate(45)" fill="#9eb6de"/>
        </g>
      </g>
      <path id="路径_6076" data-name="路径 6076" d="M360.5,370.086l5.78,7.434.593-.37-2.766-8.249Z" transform="translate(-287.268 -316.175)" fill="#9eb6de"/>
      <circle id="椭圆_6456" data-name="椭圆 6456" cx="1.062" cy="1.062" r="1.062" transform="translate(78.098 60.061)" fill="#174470"/>
      <path id="路径_6077" data-name="路径 6077" d="M143.866,317.37H115.635a1.223,1.223,0,0,1-1.235-1.235h0a1.223,1.223,0,0,1,1.235-1.235h28.231a1.223,1.223,0,0,1,1.235,1.235h0A1.223,1.223,0,0,1,143.866,317.37Z" transform="translate(-101.952 -275.512)" fill="#b8daab"/>
      <path id="路径_6078" data-name="路径 6078" d="M182.583,262.478c-1.161,1.334-5.73.642-7.237,3.137-1.482,2.495-3.186,7.755-5.113,7.978-1.9.222-52.633,0-52.633,0v-16.03h28.922s2.717,10.2,4.545,11.707,14.424.642,16.5-.642c1.7-1.037,3.26-4.1,3.433-7.78a12.432,12.432,0,0,0-.1-2.519c-.593-4.792-3.828-17.116-3.606-18.45s1.21-3.606,2.865-4.149a5.7,5.7,0,0,1,3.359,0,14.409,14.409,0,0,1,0,3.73c-.272.42-1.284.963-1.284,1.852,0,.914,5.952,10.423,7.978,13.757C182.262,258.452,183.768,261.169,182.583,262.478Z" transform="translate(-104.361 -215.705)" fill="#41b592"/>
      <ellipse id="椭圆_6457" data-name="椭圆 6457" cx="1.309" cy="1.902" rx="1.309" ry="1.902" transform="translate(67.873 20.048)" fill="#b8daab"/>
      <path id="路径_6079" data-name="路径 6079" d="M348.44,321.1s3.087,1.9,2.3,3.087c-.79,1.161-3.408-.049-3.927-1.531C346.266,321.151,347.575,320.484,348.44,321.1Z" transform="translate(-276.862 -280.009)" fill="#b8daab"/>
      <path id="路径_6080" data-name="路径 6080" d="M336.234,362.111a4.4,4.4,0,0,1-.568-3.408,16.148,16.148,0,0,1,6.1-7.187c4.792-3.112,11.312-.963,12.7.37a4.1,4.1,0,0,1,1.284,3.655.42.42,0,0,1-.148.247.489.489,0,0,1-.272.049c-3.557.025-6.94-.593-10.349.889-4.668,2.025-7.533,6.965-8.126,6.175C336.654,362.655,336.432,362.383,336.234,362.111Z" transform="translate(-268.46 -301.878)" fill="#b8daab"/>
      <g id="组_4930" data-name="组 4930" transform="translate(2.149 8.736)">
        <path id="路径_6081" data-name="路径 6081" d="M110.636,197.291H92.013A1,1,0,0,1,91,196.278v-.766a1,1,0,0,1,1.013-1.013h18.623a1,1,0,0,1,1.013,1.013v.766A1,1,0,0,1,110.636,197.291Z" transform="translate(-86.48 -193.586)" fill="#fcd88c"/>
        <rect id="矩形_2702" data-name="矩形 2702" width="17.61" height="5.903" transform="translate(5.829 3.705)" fill="#f19980"/>
        <path id="路径_6082" data-name="路径 6082" d="M126.474,210.834h-3.507a.664.664,0,0,1-.667-.667h0a.664.664,0,0,1,.667-.667h3.483a.664.664,0,0,1,.667.667h0A.629.629,0,0,1,126.474,210.834Z" transform="translate(-110.049 -204.881)" fill="#d85c3b"/>
        <path id="路径_6083" data-name="路径 6083" d="M128.714,200.754h0a.912.912,0,0,1-.914-.914v-8.126a.912.912,0,0,1,.914-.914h0a.912.912,0,0,1,.914.914v8.126A.9.9,0,0,1,128.714,200.754Z" transform="translate(-114.191 -190.8)" fill="#d85c3b"/>
        <path id="路径_6084" data-name="路径 6084" d="M101.178,250.668H74.305a1.6,1.6,0,0,1-1.605-1.605V231.205a1.6,1.6,0,0,1,1.605-1.605h26.872a1.6,1.6,0,0,1,1.605,1.605v17.857A1.6,1.6,0,0,1,101.178,250.668Z" transform="translate(-72.7 -220.017)" fill="#fcd88c"/>
        <path id="路径_6085" data-name="路径 6085" d="M87.133,247.211h-4.94a.6.6,0,0,1-.593-.593v-2.025a.6.6,0,0,1,.593-.593h4.94a.6.6,0,0,1,.593.593v2.025A.585.585,0,0,1,87.133,247.211Z" transform="translate(-79.402 -230.86)" fill="#f19980"/>
        <path id="路径_6086" data-name="路径 6086" d="M153.433,288.951h-9.04a.6.6,0,0,1-.593-.593v-4.866a.6.6,0,0,1,.593-.593h9.04a.6.6,0,0,1,.593.593v4.866A.618.618,0,0,1,153.433,288.951Z" transform="translate(-126.239 -260.152)" fill="#f19980"/>
        <path id="路径_6087" data-name="路径 6087" d="M154.673,290.141h-7.78a.6.6,0,0,1-.593-.593v-3.655a.6.6,0,0,1,.593-.593h7.78a.6.6,0,0,1,.593.593v3.655A.6.6,0,0,1,154.673,290.141Z" transform="translate(-128.122 -261.96)" fill="#fff"/>
        <path id="路径_6088" data-name="路径 6088" d="M86.737,280.688H82.094a.5.5,0,0,1-.494-.494h0a.5.5,0,0,1,.494-.494h4.643a.5.5,0,0,1,.494.494h0A.5.5,0,0,1,86.737,280.688Z" transform="translate(-79.402 -257.743)" fill="#f19980"/>
        <path id="路径_6089" data-name="路径 6089" d="M84.169,290.388H82.094a.5.5,0,0,1-.494-.494h0a.5.5,0,0,1,.494-.494h2.075a.5.5,0,0,1,.494.494h0A.5.5,0,0,1,84.169,290.388Z" transform="translate(-79.402 -265.047)" fill="#f19980"/>
        <path id="路径_6090" data-name="路径 6090" d="M156.649,244.688h-7.755a.5.5,0,0,1-.494-.494h0a.5.5,0,0,1,.494-.494h7.755a.5.5,0,0,1,.494.494h0A.5.5,0,0,1,156.649,244.688Z" transform="translate(-129.703 -230.634)" fill="#f19980"/>
        <path id="路径_6091" data-name="路径 6091" d="M152.748,254.388h-3.754a.5.5,0,0,1-.494-.494h0a.5.5,0,0,1,.494-.494h3.754a.5.5,0,0,1,.494.494h0A.5.5,0,0,1,152.748,254.388Z" transform="translate(-129.778 -237.938)" fill="#f19980"/>
        <path id="路径_6092" data-name="路径 6092" d="M128.714,252.577h0a.912.912,0,0,1-.914-.914V229.014a.912.912,0,0,1,.914-.914h0a.912.912,0,0,1,.914.914v22.649A.9.9,0,0,1,128.714,252.577Z" transform="translate(-114.191 -218.887)" fill="#f19980"/>
      </g>
      <path id="路径_6093" data-name="路径 6093" d="M318.058,244.074l2.89-.074a.218.218,0,0,1,.222.2v.074a.2.2,0,0,1-.2.2h-2.89a.2.2,0,0,1-.2-.2h0C317.861,244.148,317.935,244.074,318.058,244.074Z" transform="translate(-255.175 -222.124)" fill="#259e8d"/>
      <path id="路径_6094" data-name="路径 6094" d="M117.7,331.9h22.377a1.317,1.317,0,0,1,1.284,1.037l2.914,13.337H119.034Z" transform="translate(-104.437 -288.313)" fill="#259e8d"/>
      <path id="路径_6095" data-name="路径 6095" d="M140.292,378.018H114.605A1.6,1.6,0,0,1,113,376.413v-3.507a1.6,1.6,0,0,1,1.605-1.605h25.687a1.6,1.6,0,0,1,1.605,1.605v3.507A1.6,1.6,0,0,1,140.292,378.018Z" transform="translate(-100.898 -317.982)" fill="#b8daab"/>
      <g id="组_4933" data-name="组 4933" transform="translate(6.224 44.649)">
        <path id="路径_6096" data-name="路径 6096" d="M118.544,338.423V352.3a2.214,2.214,0,0,1-2.223,2.223h-24.8A2.214,2.214,0,0,1,89.3,352.3V338.423a2.214,2.214,0,0,1,2.223-2.223h24.8A2.214,2.214,0,0,1,118.544,338.423Z" transform="translate(-89.275 -336.2)" fill="#f19980"/>
        <path id="路径_6097" data-name="路径 6097" d="M118.468,340.548v2.1a4.88,4.88,0,0,1-4.866,4.89H94.066a4.88,4.88,0,0,1-4.866-4.89v-2.1a2.235,2.235,0,0,1,2.223-2.248h24.8A2.24,2.24,0,0,1,118.468,340.548Z" transform="translate(-89.2 -337.781)" fill="#d85c3b"/>
        <g id="组_4931" data-name="组 4931" transform="translate(1.531 15.758)">
          <path id="路径_6098" data-name="路径 6098" d="M97.969,400.79H95.8a.39.39,0,0,1-.4-.4h0a.39.39,0,0,1,.4-.4h2.149a.39.39,0,0,1,.4.4h0A.385.385,0,0,1,97.969,400.79Z" transform="translate(-95.4 -400)" fill="#d85c3b"/>
          <path id="路径_6099" data-name="路径 6099" d="M113.744,400.79H111.6a.39.39,0,0,1-.4-.4h0a.39.39,0,0,1,.4-.4h2.149a.39.39,0,0,1,.4.4h0A.39.39,0,0,1,113.744,400.79Z" transform="translate(-107.298 -400)" fill="#d85c3b"/>
          <path id="路径_6100" data-name="路径 6100" d="M129.444,400.79H127.3a.39.39,0,0,1-.4-.4h0a.39.39,0,0,1,.4-.4h2.149a.39.39,0,0,1,.4.4h0A.39.39,0,0,1,129.444,400.79Z" transform="translate(-119.12 -400)" fill="#d85c3b"/>
          <path id="路径_6101" data-name="路径 6101" d="M145.144,400.79H143a.39.39,0,0,1-.4-.4h0a.39.39,0,0,1,.4-.4h2.149a.39.39,0,0,1,.4.4h0A.39.39,0,0,1,145.144,400.79Z" transform="translate(-130.942 -400)" fill="#d85c3b"/>
          <path id="路径_6102" data-name="路径 6102" d="M160.944,400.79H158.8a.39.39,0,0,1-.4-.4h0a.39.39,0,0,1,.4-.4h2.149a.39.39,0,0,1,.4.4h0A.424.424,0,0,1,160.944,400.79Z" transform="translate(-142.84 -400)" fill="#d85c3b"/>
          <path id="路径_6103" data-name="路径 6103" d="M176.644,400.79H174.5a.39.39,0,0,1-.4-.4h0a.39.39,0,0,1,.4-.4h2.149a.39.39,0,0,1,.4.4h0A.39.39,0,0,1,176.644,400.79Z" transform="translate(-154.662 -400)" fill="#d85c3b"/>
          <path id="路径_6104" data-name="路径 6104" d="M192.344,400.79H190.2a.39.39,0,0,1-.4-.4h0a.39.39,0,0,1,.4-.4h2.149a.39.39,0,0,1,.4.4h0A.39.39,0,0,1,192.344,400.79Z" transform="translate(-166.484 -400)" fill="#d85c3b"/>
        </g>
        <path id="路径_6105" data-name="路径 6105" d="M118.468,338.423V340.5a4.859,4.859,0,0,1-4.866,4.866H94.066A4.859,4.859,0,0,1,89.2,340.5v-2.075a2.214,2.214,0,0,1,2.223-2.223h24.8A2.219,2.219,0,0,1,118.468,338.423Z" transform="translate(-89.2 -336.2)" fill="#fcd88c"/>
        <g id="组_4932" data-name="组 4932" transform="translate(0.469 2.248)">
          <path id="路径_6106" data-name="路径 6106" d="M203.17,347.5a.38.38,0,0,1-.37-.37v-1.457a.37.37,0,1,1,.741,0v1.457A.364.364,0,0,1,203.17,347.5Z" transform="translate(-175.211 -345.3)" fill="#f19980"/>
          <path id="路径_6107" data-name="路径 6107" d="M115.6,364.495h-2.939a.37.37,0,0,1,0-.741H115.6a.37.37,0,1,1,0,.741Zm-5.878,0h-2.939a.37.37,0,1,1,0-.741h2.939a.38.38,0,0,1,.37.371A.4.4,0,0,1,109.723,364.495Zm-5.9,0h-2.939a.37.37,0,0,1,0-.741h2.939a.38.38,0,0,1,.37.371A.4.4,0,0,1,103.82,364.495Zm-5.9-.123h-.074a5.106,5.106,0,0,1-2.717-1.556.367.367,0,0,1,.543-.494,4.446,4.446,0,0,0,2.322,1.309.355.355,0,0,1,.272.42A.33.33,0,0,1,97.917,364.371Zm20.624,0a.346.346,0,0,1-.346-.3.333.333,0,0,1,.272-.42,4.233,4.233,0,0,0,2.322-1.334.367.367,0,0,1,.543.494,5.27,5.27,0,0,1-2.717,1.556Z" transform="translate(-94.064 -358.023)" fill="#f19980"/>
          <path id="路径_6108" data-name="路径 6108" d="M91.47,347.5a.38.38,0,0,1-.37-.37v-1.457a.37.37,0,1,1,.741,0v1.457A.364.364,0,0,1,91.47,347.5Z" transform="translate(-91.1 -345.3)" fill="#f19980"/>
        </g>
        <path id="路径_6109" data-name="路径 6109" d="M144.395,372.13h-1.68a.822.822,0,0,1-.815-.815h0a.822.822,0,0,1,.815-.815h1.68a.822.822,0,0,1,.815.815h0A.822.822,0,0,1,144.395,372.13Z" transform="translate(-128.884 -362.028)" fill="#fff"/>
      </g>
      <g id="组_4934" data-name="组 4934" transform="translate(67.873 27.088)" opacity="0.39">
        <path id="路径_6110" data-name="路径 6110" d="M338.8,265.1c.617.79,8.3,12.992,7.681,13.041a9.737,9.737,0,0,1-4.273-1.877C341.2,275.35,338.8,265.1,338.8,265.1Z" transform="translate(-338.8 -265.1)" fill="#b8daab"/>
      </g>
      <path id="路径_6111" data-name="路径 6111" d="M171.272,328.31s-.321,6.792-1.754,8.175c-1.433,1.358-16.845,2.322-18.475,1.951-1.605-.37-5.607-12.547-5.607-12.547H117.7V325h28.922s2.717,10.2,4.545,11.707,14.424.642,16.5-.642c1.7-1.037,3.26-4.1,3.433-7.78Z" transform="translate(-104.437 -283.118)" fill="#259e8d"/>
    </g>
    <path id="路径_6124" data-name="路径 6124" d="M138.472,446.677H30.093a.6.6,0,0,1-.593-.593v-.692a.6.6,0,0,1,.593-.593H138.472a.6.6,0,0,1,.593.593v.692A.6.6,0,0,1,138.472,446.677Z" transform="translate(0 -217.9)" fill="#9eb6de"/>
    <path id="路径_6125" data-name="路径 6125" d="M121.6,467.277H69.093a.6.6,0,0,1-.593-.593v-.692a.6.6,0,0,1,.593-.593h52.485a.6.6,0,0,1,.593.593v.692A.58.58,0,0,1,121.6,467.277Z" transform="translate(-29.367 -233.412)" fill="#eff4fa"/>
    <path id="路径_6126" data-name="路径 6126" d="M338.615,467.277H309.693a.6.6,0,0,1-.593-.593v-.692a.6.6,0,0,1,.593-.593h28.922a.6.6,0,0,1,.593.593v.692A.618.618,0,0,1,338.615,467.277Z" transform="translate(-210.542 -233.412)" fill="#eff4fa"/>
    <path id="路径_6127" data-name="路径 6127" d="M233.515,487.777H204.593a.6.6,0,0,1-.593-.593v-.692a.6.6,0,0,1,.593-.593h28.922a.6.6,0,0,1,.593.593v.692A.585.585,0,0,1,233.515,487.777Z" transform="translate(-131.4 -248.848)" fill="#eff4fa"/>
    <path id="路径_6128" data-name="路径 6128" d="M298.3,237.334a3.251,3.251,0,0,0,1.8-.864c.889-.815,3.507-.124,3.952.519s-.519,2.174-.963,2.075c-.445-.124.074-.42.222-.716a.593.593,0,0,0-.049-.766c-.222-.2-.346.519-.914.79a3.85,3.85,0,0,1-2.4.346,3.17,3.17,0,0,0-1.556-.025Z" transform="translate(-202.409 -60.746)" fill="#f2a284"/>
    <circle id="椭圆_6458" data-name="椭圆 6458" cx="0.815" cy="0.815" r="0.815" transform="translate(100.114 176.786)" fill="#b8daab"/>
  </g>
</svg>
