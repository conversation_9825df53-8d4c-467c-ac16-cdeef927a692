<?xml version="1.0" encoding="UTF-8"?>
<svg width="69px" height="73px" viewBox="0 0 69 73" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 60.1 (88133) - https://sketch.com -->
    <title>icon-bc</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <linearGradient x1="50%" y1="-39.1963504%" x2="50%" y2="178.871545%" id="linearGradient-1">
            <stop stop-color="#CCDDFF" offset="0%"></stop>
            <stop stop-color="#C7DDFA" offset="41.7099956%"></stop>
            <stop stop-color="#6C98FF" offset="100%"></stop>
        </linearGradient>
        <rect id="path-2" x="0" y="0" width="12" height="4" rx="2"></rect>
        <rect id="path-4" x="0" y="0" width="36" height="4" rx="2"></rect>
        <rect id="path-6" x="0" y="0" width="27" height="4" rx="2"></rect>
        <rect id="path-8" x="0" y="0" width="17" height="4" rx="2"></rect>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="首页-补偿icon" transform="translate(-634.000000, -1374.000000)">
            <g id="icon-bc" transform="translate(634.000000, 1374.000000)">
                <path d="M59,1 C63.418278,1 67,4.581722 67,9 L67.0009786,33.3280339 C63.982975,30.6698138 59.9596114,29.047429 55.5416667,29.047429 C46.1758378,29.047429 38.5833333,36.3387422 38.5833333,45.3330329 C38.5833333,50.449195 41.0399636,55.0143417 44.8822133,57.9999488 L20,57.9996137 C15.581722,57.9996137 12,54.4178917 12,49.9996137 L12,9 C12,4.581722 15.581722,1 20,1 L59,1 Z" id="形状结合" fill="#A3BEFE"></path>
                <path d="M56,4 C60.418278,4 64,7.581722 64,12 L64.0009384,31.6421876 C61.4674818,30.17613 58.5024102,29.3332857 55.3333317,29.3332857 C46.0123158,29.3332857 38.4561394,36.6246346 38.4561394,45.6189694 C38.4561394,52.7329179 43.1831376,58.7815114 49.7716113,60.9996575 L20,60.9998928 C15.581722,60.9998928 12,57.4181708 12,52.9998928 L12,12 C12,7.581722 15.581722,4 20,4 L56,4 Z" id="形状结合" fill="url(#linearGradient-1)"></path>
                <g id="矩形备份-18" transform="translate(22.000000, 45.000000)">
                    <mask id="mask-3" fill="white">
                        <use xlink:href="#path-2"></use>
                    </mask>
                    <use id="蒙版" fill="#EDF3FC" xlink:href="#path-2"></use>
                    <rect fill="#FFFFFF" mask="url(#mask-3)" x="-1" y="1" width="12" height="4" rx="2"></rect>
                </g>
                <path d="M54.9915429,32 C51.2316269,32.0460862 47.6452224,33.4943694 45.0214622,36.0261774 C42.3977019,38.5579854 40.9515605,41.9658775 41.0012389,45.5 C40.9515605,49.0341225 42.3977019,52.4420146 45.0214622,54.9738226 C47.6452224,57.5056306 51.2316269,58.9539138 54.9915429,59 C58.754409,58.9580986 62.3451534,57.5116542 64.972561,54.9793886 C67.5999687,52.447123 69.0484246,49.0368436 68.9987639,45.5 C69.0484246,41.9631564 67.5999687,38.5528769 64.972561,36.0206114 C62.3451534,33.4883458 58.754409,32.0419014 54.9915429,32" id="路径备份" fill="#EC6464" fill-rule="nonzero"></path>
                <path d="M59.6369958,46.2931197 L59.6369958,47.537229 L56.3699583,47.537229 L56.3699583,49.2450518 L59.6369958,49.2450518 L59.6369958,50.5004712 L56.3699583,50.5004712 L56.3699583,53 L54.6801113,53 L54.6801113,50.5004712 L51.2878999,50.5004712 L51.2878999,49.2450518 L54.6801113,49.2450518 L54.6801113,47.537229 L51.2878999,47.537229 L51.2878999,46.2931197 L54.1168289,46.2931197 L51,41 L52.8901252,41 C53.0653686,41.2827521 55.4561892,45.8520264 55.4937413,45.9198869 L58.2100139,41 L60,41 L56.8331015,46.2931197 L59.6369958,46.2931197" id="路径备份-2" fill="#FFFFFF" fill-rule="nonzero"></path>
                <path d="M56.4071146,53 L54.56917,53 L54.56917,50.3616236 L51.3557312,50.3616236 L51.3557312,48.7785978 L54.56917,48.7785978 L54.56917,47.1715867 L51.3557312,47.1715867 L51.3557312,45.6125461 L53.9525692,45.6125461 L51,40 L53.0632411,40 C53.2173913,40.2878229 54.972332,44.0415129 55.4347826,45.0129151 L58.0316206,40.0359779 L60,40.0359779 L59.9051383,40.2158672 L56.9525692,45.648524 L59.5256917,45.648524 L59.5256917,47.1715867 L56.4071146,47.1715867 L56.4071146,48.7426199 L59.5019763,48.7426199 L59.5019763,50.3256457 L56.4071146,50.3256457 L56.4071146,53 Z M54.8991292,53 L56.1008708,53 L56.1008708,50.4372856 L58.7865022,50.4372856 L58.7865022,49.3820502 L56.1008708,49.3820502 L56.1008708,47.3991354 L58.7865022,47.3991354 L58.7865022,46.3554961 L56.4143687,46.3554961 L56.4979681,46.1931522 L59.1,40.9285714 L57.8460087,40.9285714 L55.4634252,46.1351722 L55.3798258,45.9032524 C55.3798258,45.9032524 53.5197387,41.554755 53.2898403,40.9285714 L51.9,40.9285714 L54.5020319,46.3554961 L52.0671988,46.3554961 L52.0671988,47.3991354 L54.8991292,47.3991354 L54.8991292,49.3820502 L52.0671988,49.3820502 L52.0671988,50.4372856 L54.8991292,50.4372856 L54.8991292,53 Z" id="形状备份" fill="#FFFFFF" fill-rule="nonzero"></path>
                <g id="矩形备份-15" transform="translate(22.000000, 12.000000)">
                    <mask id="mask-5" fill="white">
                        <use xlink:href="#path-4"></use>
                    </mask>
                    <use id="蒙版" fill="#EDF3FC" xlink:href="#path-4"></use>
                    <rect fill="#FFFFFF" mask="url(#mask-5)" x="-1" y="1" width="36" height="4" rx="2"></rect>
                </g>
                <g id="矩形备份-16" transform="translate(22.000000, 23.000000)">
                    <mask id="mask-7" fill="white">
                        <use xlink:href="#path-6"></use>
                    </mask>
                    <use id="蒙版" fill="#EDF3FC" xlink:href="#path-6"></use>
                    <rect fill="#FFFFFF" mask="url(#mask-7)" x="-1" y="1" width="27" height="4" rx="2"></rect>
                </g>
                <g id="矩形备份-17" transform="translate(22.000000, 34.000000)">
                    <mask id="mask-9" fill="white">
                        <use xlink:href="#path-8"></use>
                    </mask>
                    <use id="蒙版" fill="#EDF3FC" xlink:href="#path-8"></use>
                    <rect fill="#FFFFFF" mask="url(#mask-9)" x="-1" y="1" width="17" height="4" rx="2"></rect>
                </g>
                <g id="编组备份" transform="translate(0.000000, 51.000000)" fill-rule="nonzero">
                    <path d="M32.436071,9.60893379 L49.0476512,7.60489153 C50.4816882,7.43049844 51.8233344,8.26516833 52.1137005,9.51235344 C52.3618298,10.5802835 51.7655411,11.6619827 50.6584712,12.1522164 L37.2868355,18.0919079 C35.2341917,19.0017781 32.9917274,19.5331126 30.6971177,19.6532902 L0.915254237,21.2307692 L0.915254237,5.23867297 L10.754069,1.68934511 C13.4503602,0.720202326 16.4501018,0.623241957 19.2200259,1.41570077 L28.7293547,4.12799894 C30.5243326,4.62037575 31.571732,6.2578068 31.1181274,7.86243912 L30.7154225,9.27895095 L32.436071,9.60893379 Z" id="路径" fill="#FFDBCD"></path>
                    <path d="M51.8065551,9.16059059 C51.5435508,9.58318 51.1524162,9.92686318 50.6805199,10.1500182 L37.2962634,16.3297485 C35.2430875,17.2776734 33.0000419,17.8312306 30.7048375,17.9564345 L0.915254237,19.5998906 L0.915254237,22.1153846 L30.7048375,20.4383886 C33.0000419,20.3131846 35.2430875,19.7596275 37.2962634,18.8117025 L50.6713651,12.6235873 C51.3434205,12.3094896 51.8447977,11.7559928 52.0577843,11.0930461 C52.2707709,10.4300993 52.1766071,9.71609493 51.7974004,9.11866566 L51.8065551,9.16059059 Z M31.1259563,7.23204517 C31.1098484,6.87580491 31.0294184,6.52446955 30.8879326,6.19230769 L30.6315994,7.13142541 L31.1259563,7.23204517 Z" id="形状" fill="#FFCDB9"></path>
                    <path d="M20.0918589,4.27509824 L29.5786304,7.09126962 C30.5306544,7.37223229 31.3073867,8.01020352 31.7152083,8.84615385 L31.9252139,8.10241716 C32.3819995,6.44839607 31.3601714,4.75334756 29.587761,4.22495869 L20.0918589,1.43385709 C17.3246442,0.611659128 14.3283593,0.715308456 11.6368536,1.72633783 L1.83050847,5.4032381 L1.83050847,8.24447925 L11.6459843,4.55922237 C14.3358796,3.55296421 17.3285054,3.45229054 20.0918589,4.27509824 Z" id="路径" fill="#FFCDB9"></path>
                    <path d="M53.9196234,9.7431322 C53.4784899,7.85642319 51.4853237,6.5899725 49.3431849,6.83527946 L32.7947837,8.85280649 L32.8680067,8.60776273 C33.496729,6.36541438 32.0790086,4.07025481 29.6187354,3.34748987 L20.1180493,0.594831539 C17.032051,-0.296095678 13.6922544,-0.183242916 10.6905862,0.913388436 L1.98620033,4.10712555 C0.788297704,4.54389422 0.00401186674,5.58246306 1.29541771e-05,6.73726195 L1.29541771e-05,20.1329879 C-0.00240598792,20.8926147 0.334031427,21.6218731 0.935069317,22.1597782 C1.53610721,22.6976834 2.35232189,23 3.20353297,23 L3.39574338,23 L31.2387946,21.505233 C33.722306,21.3766975 36.1494864,20.7927838 38.3688857,19.7899266 L51.71378,13.76185 C53.3809935,13.0199594 54.2847578,11.3734355 53.9196234,9.7431322 Z M50.5056003,11.5727923 L37.1240944,17.600869 C35.2410982,18.4533198 33.1817391,18.9509978 31.0740429,19.0629634 L3.20353297,20.5495623 C3.08885271,20.548782 2.97834449,20.5110748 2.89233514,20.4433767 C2.80048369,20.3673747 2.74755429,20.2610965 2.74588909,20.1493242 L2.74588909,6.73726195 C2.74681132,6.57238757 2.85873241,6.42417919 3.02962829,6.36152821 L11.7340141,3.17595924 C14.1328581,2.30117182 16.8010433,2.21148211 19.2668318,2.92274733 L28.7675179,5.68357375 C29.8558462,6.00385654 30.4827337,7.01940256 30.2045195,8.01148954 L29.9573918,8.88547901 C29.5538269,8.84075125 29.1534871,8.77529765 28.758365,8.689444 L21.7564142,7.17834073 C21.2789931,7.07474498 20.775907,7.20631661 20.4366636,7.52349386 C20.0974202,7.84067111 19.9735587,8.29526721 20.1117365,8.7160402 C20.2499143,9.1368132 20.6291388,9.45983767 21.10656,9.56343342 L28.1085108,11.0745366 C29.7343926,11.428594 31.4204566,11.5061824 33.0785229,11.3032442 L49.7001471,9.26121276 C50.3689874,9.17364115 51.0097306,9.53358783 51.2104553,10.10965 C51.41118,10.6857122 51.1121476,11.3064454 50.5056003,11.5727923 L50.5056003,11.5727923 Z" id="形状" fill="#FFECE5"></path>
                </g>
            </g>
        </g>
    </g>
</svg>