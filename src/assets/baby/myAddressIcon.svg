<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="120.292" height="115.696" viewBox="0 0 120.292 115.696">
  <defs>
    <linearGradient id="linear-gradient" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0.15" stop-color="#b6cadc"/>
      <stop offset="1" stop-color="#dfe8f9"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="1" y1="0.5" x2="0" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0.116" stop-color="#dee4ff"/>
      <stop offset="0.847" stop-color="#bacbee"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="1" y1="0.5" x2="0" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0.116" stop-color="#d5ddfb"/>
      <stop offset="0.847" stop-color="#bacbee"/>
    </linearGradient>
    <linearGradient id="linear-gradient-8" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#d5defb"/>
      <stop offset="0.646" stop-color="#fff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-9" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#c3d2f2"/>
      <stop offset="0.608" stop-color="#fff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-10" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-9"/>
    <linearGradient id="linear-gradient-11" x1="0.098" y1="0.144" x2="0.899" y2="0.733" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#c8cbf2"/>
      <stop offset="1" stop-color="#9d9ddc"/>
    </linearGradient>
    <linearGradient id="linear-gradient-12" x1="0.854" y1="0.856" x2="0.228" y2="0.059" xlink:href="#linear-gradient-4"/>
    <linearGradient id="linear-gradient-13" x1="0.5" y1="1" x2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#4f5c7c"/>
      <stop offset="1" stop-color="#274168"/>
    </linearGradient>
    <linearGradient id="linear-gradient-14" x1="0.5" y1="1" x2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#c3d5fd"/>
      <stop offset="1" stop-color="#1a90fc"/>
    </linearGradient>
    <linearGradient id="linear-gradient-15" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#f4ae98"/>
      <stop offset="1" stop-color="#fad1bb"/>
    </linearGradient>
    <linearGradient id="linear-gradient-16" x1="0.001" y1="5.859" x2="0.287" y2="4.869" xlink:href="#linear-gradient-14"/>
    <linearGradient id="linear-gradient-17" x1="-135.576" y1="-3.546" x2="-135.291" y2="-4.536" xlink:href="#linear-gradient-14"/>
    <linearGradient id="linear-gradient-18" x1="0.5" y1="1" x2="0.5" y2="0" xlink:href="#linear-gradient-13"/>
    <linearGradient id="linear-gradient-19" x1="0.034" y1="-3.789" x2="0.034" y2="-2.786" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#f4b9a4"/>
      <stop offset="0.652" stop-color="#fad1bb"/>
    </linearGradient>
    <linearGradient id="linear-gradient-20" x1="0.5" y1="1" x2="0.5" y2="0" xlink:href="#linear-gradient-13"/>
    <linearGradient id="linear-gradient-21" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#275c89"/>
      <stop offset="1" stop-color="#013f7c"/>
    </linearGradient>
    <linearGradient id="linear-gradient-22" x1="-332.934" y1="0.5" x2="-331.934" y2="0.5" xlink:href="#linear-gradient-19"/>
    <linearGradient id="linear-gradient-23" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#18264b"/>
      <stop offset="0.652" stop-color="#2d3c65"/>
    </linearGradient>
    <linearGradient id="linear-gradient-24" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffdb80"/>
      <stop offset="1" stop-color="#ffbb24"/>
    </linearGradient>
    <linearGradient id="linear-gradient-25" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-19"/>
    <linearGradient id="linear-gradient-26" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#445677"/>
      <stop offset="1" stop-color="#293861"/>
    </linearGradient>
    <linearGradient id="linear-gradient-27" x1="0.5" y1="0" x2="0.5" y2="1" xlink:href="#linear-gradient-24"/>
  </defs>
  <g id="图层_4" transform="translate(-90.399 -140.602)">
    <g id="组_4971" data-name="组 4971" transform="translate(90.399 140.602)">
      <path id="路径_8965" data-name="路径 8965" d="M195.13,413.229l16.661-68.4,28.983-7.991,42.257,15.712,26.411-6.906-14.356,72.19-23.7,6.095-44.695-18.284Z" transform="translate(-189.15 -309.362)" fill="url(#linear-gradient)"/>
      <path id="路径_8966" data-name="路径 8966" d="M195.13,413.229l16.661-68.4,28.983-7.991,42.257,15.712,26.411-6.906-14.356,72.19-23.7,6.095-44.695-18.284Z" transform="translate(-189.15 -309.362)" fill="url(#linear-gradient)"/>
      <path id="路径_8967" data-name="路径 8967" d="M211.771,344.9,195.11,413.3l31.559-7.585,14.086-68.8Z" transform="translate(-189.136 -309.412)" fill="url(#linear-gradient)"/>
      <path id="路径_8968" data-name="路径 8968" d="M211.771,344.9,195.11,413.3l31.559-7.585,14.086-68.8Z" transform="translate(-189.136 -309.412)" fill="url(#linear-gradient-4)"/>
      <path id="路径_8969" data-name="路径 8969" d="M316.556,336.84,302.47,405.646l44.695,18.284,11.649-71.379Z" transform="translate(-264.932 -309.362)" fill="url(#linear-gradient-5)"/>
      <path id="路径_8970" data-name="路径 8970" d="M466.138,373.7,454.49,445.075l23.7-6.095,14.356-72.19Z" transform="translate(-372.257 -330.507)" fill="url(#linear-gradient)"/>
      <path id="路径_8971" data-name="路径 8971" d="M466.138,373.7,454.49,445.075l23.7-6.095,14.356-72.19Z" transform="translate(-372.257 -330.507)" fill="url(#linear-gradient-4)"/>
      <path id="路径_8972" data-name="路径 8972" d="M325.968,411.554c-5.613,3.822-6.592,11.369-7.118,15.421-.347,2.684-.791,2.867-5.93,2.431-1.546-.129-3.3-.279-5.283-.279-4.807-.073-10.249-.159-11.3-1.52-.465-.606-.4-2.055.188-4.31.847-3.246.773-5.436-.244-7.1-1.394-2.276-4.116-2.9-7.562-3.687-.976-.224-1.988-.456-3.093-.747a6.039,6.039,0,0,0-4.883.679c-3.957,2.508-5.18,9.438-6.365,16.138-.4,2.252-.773,4.381-1.214,5.971-.241.873-.538,1.25-.717,1.311-1.194.394-4.836-2.4-6.791-3.9a41.644,41.644,0,0,0-4.134-2.928l-.5-.288-.576.018a12.274,12.274,0,0,0-1.92.268,6.2,6.2,0,0,1-1.82.2c-.009-.009-.794-.879.121-5.9,1.053-5.786.673-9.264-1.226-11.275-1.964-2.079-4.98-1.893-7.159-1.661.068-1.517,1.291-4.989,1.99-6.968a32.757,32.757,0,0,0,1.958-6.927,3.244,3.244,0,0,0-1.341-2.99c-1.126-.8-3.237-1.452-9.626.556-.541.17-.121.079-.629.256l-1.138,4.631c4.078-1.6,7.127-2.39,8.682-2.273a42,42,0,0,1-1.679,5.442c-1.949,5.53-3.061,9.026-1.391,11.1a3.375,3.375,0,0,0,3.166,1.161c1.779-.2,3.572-.335,4.248.379.515.544,1.226,2.287.212,7.865-.914,5.027-.612,7.847,1.011,9.429,1.641,1.6,3.9,1.156,5.4.864.259-.05.509-.1.738-.135.947.609,2.029,1.441,3.166,2.311,3.055,2.343,6.2,4.754,9.052,4.754a4.616,4.616,0,0,0,1.452-.229c1.6-.529,2.714-1.882,3.308-4.019.491-1.773.885-3.99,1.3-6.339.888-5.027,2.105-11.916,4.581-13.486a1.968,1.968,0,0,1,1.7-.188c1.167.306,2.258.556,3.219.776,2.528.579,4.525,1.035,5.042,1.882.27.444.456,1.508-.209,4.051-.679,2.6-1.153,5.551.5,7.7,2.123,2.761,6.636,2.964,14.433,3.081h.029c1.823,0,3.49.141,4.96.265,4.607.388,9.37.788,10.234-5.877.476-3.681,1.276-9.843,5.416-12.663a11.04,11.04,0,0,1,7.044-1.576l.785-3.916A15.3,15.3,0,0,0,325.968,411.554Z" transform="translate(-220.588 -348.825)" fill="#fff"/>
      <path id="路径_8973" data-name="路径 8973" d="M325.968,411.554c-5.613,3.822-6.592,11.369-7.118,15.421-.347,2.684-.791,2.867-5.93,2.431-1.546-.129-3.3-.279-5.283-.279-4.807-.073-10.249-.159-11.3-1.52-.465-.606-.4-2.055.188-4.31.847-3.246.773-5.436-.244-7.1-1.394-2.276-4.116-2.9-7.562-3.687-.976-.224-1.988-.456-3.093-.747a6.039,6.039,0,0,0-4.883.679c-3.957,2.508-5.18,9.438-6.365,16.138-.4,2.252-.773,4.381-1.214,5.971-.241.873-.538,1.25-.717,1.311-1.194.394-4.836-2.4-6.791-3.9a41.644,41.644,0,0,0-4.134-2.928l-.5-.288-.576.018a12.274,12.274,0,0,0-1.92.268,6.2,6.2,0,0,1-1.82.2c-.009-.009-.794-.879.121-5.9,1.053-5.786.673-9.264-1.226-11.275-1.964-2.079-4.98-1.893-7.159-1.661.068-1.517,1.291-4.989,1.99-6.968a32.757,32.757,0,0,0,1.958-6.927,3.244,3.244,0,0,0-1.341-2.99c-1.126-.8-3.237-1.452-9.626.556-.541.17-.121.079-.629.256l-1.138,4.631c4.078-1.6,7.127-2.39,8.682-2.273a42,42,0,0,1-1.679,5.442c-1.949,5.53-3.061,9.026-1.391,11.1a3.375,3.375,0,0,0,3.166,1.161c1.779-.2,3.572-.335,4.248.379.515.544,1.226,2.287.212,7.865-.914,5.027-.612,7.847,1.011,9.429,1.641,1.6,3.9,1.156,5.4.864.259-.05.509-.1.738-.135.947.609,2.029,1.441,3.166,2.311,3.055,2.343,6.2,4.754,9.052,4.754a4.616,4.616,0,0,0,1.452-.229c1.6-.529,2.714-1.882,3.308-4.019.491-1.773.885-3.99,1.3-6.339.888-5.027,2.105-11.916,4.581-13.486a1.968,1.968,0,0,1,1.7-.188c1.167.306,2.258.556,3.219.776,2.528.579,4.525,1.035,5.042,1.882.27.444.456,1.508-.209,4.051-.679,2.6-1.153,5.551.5,7.7,2.123,2.761,6.636,2.964,14.433,3.081h.029c1.823,0,3.49.141,4.96.265,4.607.388,9.37.788,10.234-5.877.476-3.681,1.276-9.843,5.416-12.663a11.04,11.04,0,0,1,7.044-1.576l.785-3.916A15.3,15.3,0,0,0,325.968,411.554Z" transform="translate(-220.588 -348.825)" fill="#fff"/>
      <path id="路径_8974" data-name="路径 8974" d="M489.512,451.194c-5.613,3.822-6.592,11.369-7.118,15.421-.347,2.684-.791,2.867-5.93,2.431-1.341-.112-2.837-.238-4.507-.27l-.647,3.957c1.764.006,3.381.141,4.813.262,4.607.388,9.37.788,10.234-5.877.476-3.681,1.276-9.843,5.416-12.663a11.039,11.039,0,0,1,7.044-1.576l.785-3.913v0A15.238,15.238,0,0,0,489.512,451.194Z" transform="translate(-384.132 -388.465)" fill="url(#linear-gradient-8)"/>
      <path id="路径_8975" data-name="路径 8975" d="M357.859,474.277c-4.807-.074-10.249-.159-11.3-1.52-.464-.606-.4-2.055.188-4.31.847-3.246.773-5.436-.244-7.1-1.394-2.276-4.116-2.9-7.562-3.687-.976-.224-1.987-.456-3.093-.747a6.039,6.039,0,0,0-4.883.679c-3.957,2.508-5.18,9.438-6.365,16.138-.4,2.252-.773,4.381-1.214,5.971-.241.873-.538,1.25-.717,1.311-1.194.394-4.836-2.4-6.792-3.9a41.628,41.628,0,0,0-4.134-2.928l-.091-.053L310.8,478.3c.811.559,1.7,1.241,2.628,1.952,3.055,2.343,6.2,4.754,9.052,4.754a4.616,4.616,0,0,0,1.452-.229c1.6-.529,2.714-1.882,3.308-4.019.491-1.773.885-3.99,1.3-6.339.888-5.028,2.105-11.916,4.581-13.486a1.968,1.968,0,0,1,1.7-.188c1.167.306,2.258.556,3.219.776,2.528.579,4.525,1.035,5.042,1.882.27.444.456,1.508-.209,4.051-.679,2.6-1.152,5.551.5,7.7,2.123,2.761,6.636,2.964,14.433,3.081h.176l.647-3.957C358.382,474.282,358.123,474.277,357.859,474.277Z" transform="translate(-270.813 -393.972)" fill="#fff"/>
      <path id="路径_8976" data-name="路径 8976" d="M357.859,474.277c-4.807-.074-10.249-.159-11.3-1.52-.464-.606-.4-2.055.188-4.31.847-3.246.773-5.436-.244-7.1-1.394-2.276-4.116-2.9-7.562-3.687-.976-.224-1.987-.456-3.093-.747a6.039,6.039,0,0,0-4.883.679c-3.957,2.508-5.18,9.438-6.365,16.138-.4,2.252-.773,4.381-1.214,5.971-.241.873-.538,1.25-.717,1.311-1.194.394-4.836-2.4-6.792-3.9a41.628,41.628,0,0,0-4.134-2.928l-.091-.053-.306,1.5L310.8,478.3c.811.559,1.7,1.241,2.628,1.952,3.055,2.343,6.2,4.754,9.052,4.754a4.614,4.614,0,0,0,1.452-.229c1.6-.529,2.714-1.882,3.308-4.019.491-1.773.885-3.99,1.3-6.339.888-5.028,2.105-11.916,4.581-13.486a1.968,1.968,0,0,1,1.7-.188c1.167.306,2.258.556,3.219.776,2.528.579,4.525,1.035,5.042,1.882.27.444.456,1.508-.209,4.051-.679,2.6-1.152,5.551.5,7.7,2.123,2.761,6.636,2.964,14.433,3.081h.176l.647-3.957C358.382,474.282,358.123,474.277,357.859,474.277Z" transform="translate(-270.813 -393.972)" fill="url(#linear-gradient-9)"/>
      <path id="路径_8977" data-name="路径 8977" d="M261.02,428.767l-.576.018a12.274,12.274,0,0,0-1.92.268,6.212,6.212,0,0,1-1.82.2c-.009-.009-.794-.879.12-5.9,1.053-5.786.673-9.264-1.226-11.275-1.964-2.079-4.98-1.893-7.159-1.661.068-1.517,1.291-4.989,1.99-6.968a32.76,32.76,0,0,0,1.958-6.927,3.244,3.244,0,0,0-1.341-2.99c-1.126-.8-3.237-1.452-9.626.556-.541.171-.121.079-.629.256l-.67,2.728-.462,1.9c4.075-1.6,7.121-2.387,8.679-2.27a42,42,0,0,1-1.679,5.442c-1.949,5.53-3.061,9.026-1.391,11.1a3.375,3.375,0,0,0,3.166,1.161c1.779-.2,3.572-.335,4.248.379.514.544,1.226,2.287.212,7.865-.914,5.027-.612,7.847,1.011,9.429,1.641,1.6,3.9,1.155,5.4.864.259-.05.509-.1.738-.135.173.112.356.235.538.359l.853-4.163Z" transform="translate(-220.588 -348.839)" fill="url(#linear-gradient-10)"/>
      <path id="路径_8978" data-name="路径 8978" d="M384.054,248.94a20.6,20.6,0,0,0-20.6,20.6c0,19.281,18.058,36.154,21.089,35.375,2.1-.312,20.116-16.794,20.116-35.375A20.6,20.6,0,0,0,384.054,248.94Zm0,28.61a8.294,8.294,0,1,1,8.294-8.294A8.294,8.294,0,0,1,384.054,277.55Z" transform="translate(-307.983 -247.305)" fill="url(#linear-gradient-11)"/>
      <path id="路径_8979" data-name="路径 8979" d="M376.654,243.38a20.6,20.6,0,0,0-20.6,20.6c0,19.281,18.058,36.154,21.089,35.375,2.1-.312,20.116-16.794,20.116-35.375A20.6,20.6,0,0,0,376.654,243.38Zm0,28.613a8.294,8.294,0,1,1,8.294-8.294A8.294,8.294,0,0,1,376.654,271.993Z" transform="translate(-302.759 -243.38)" fill="url(#linear-gradient-12)"/>
      <g id="组_4970" data-name="组 4970" transform="translate(0 63.428)">
        <path id="路径_8980" data-name="路径 8980" d="M201.136,477.221s-3.4.82-4.057,3.763c-1.373.335-5.995,2.822-2.5,8.317,1.761,2.155,2.029,2.52,2.029,2.52l10.081-2.975V476.13Z" transform="translate(-187.856 -471.128)" fill="url(#linear-gradient-13)"/>
        <path id="路径_8981" data-name="路径 8981" d="M207.427,503.135s-5.648-.782-7.482,1.814-2.5,9.238-2.5,9.238l4.251-.223-.359,7.738,7.844,4.6,11.172-6.009-1.7-7.012,3.569-.932s-1.064-10.176-6.03-8.673a25.129,25.129,0,0,1-3.355.226,2.888,2.888,0,0,1-2.975,1.958C207.554,505.675,207.427,503.135,207.427,503.135Z" transform="translate(-190.781 -490.115)" fill="url(#linear-gradient-14)"/>
        <path id="路径_8982" data-name="路径 8982" d="M230.482,490.4l-1.173,5.233a.514.514,0,0,0,.306.591,8.607,8.607,0,0,0,3.069.667,5.923,5.923,0,0,0,3.078-1.088.516.516,0,0,0,.2-.594l-1.793-5.339a.518.518,0,0,0-.582-.344l-2.69.476A.511.511,0,0,0,230.482,490.4Z" transform="translate(-213.271 -480.583)" fill="url(#linear-gradient-15)"/>
        <path id="路径_8983" data-name="路径 8983" d="M197.484,505.993c1.643-.547,3.872-.179,3.963,3.508s-3.522,9.564-2.229,11.607a43.183,43.183,0,0,0,5.9,6.689,11.576,11.576,0,0,1-1.535,3.625s-6.574-3.034-9.438-6.071-1.417-10-.091-14.962C195.053,506.651,197.484,505.993,197.484,505.993Z" transform="translate(-187.285 -492.067)" fill="url(#linear-gradient-16)"/>
        <path id="路径_8984" data-name="路径 8984" d="M249.752,505.993c-1.643-.547-3.872-.179-3.963,3.508s3.522,9.564,2.229,11.607a43.183,43.183,0,0,1-5.9,6.689,11.576,11.576,0,0,0,1.535,3.625s6.574-3.034,9.438-6.071,1.417-10,.091-14.962C252.181,506.651,249.752,505.993,249.752,505.993Z" transform="translate(-222.325 -492.067)" fill="url(#linear-gradient-17)"/>
        <path id="路径_8985" data-name="路径 8985" d="M243.588,482.049s1.194,3.61.894,5.615.121,4.886,2.655,5.765a4.548,4.548,0,0,0,5.815-3.125c.864-2.52.029-4.339-2.087-5.98.359-1.546-1.385-4.357-4.487-5.36C243.341,478.541,243.588,482.049,243.588,482.049Z" transform="translate(-223.358 -473.104)" fill="url(#linear-gradient-18)"/>
        <path id="路径_8986" data-name="路径 8986" d="M223.425,469.595a4.553,4.553,0,0,0-1.9,3.3c.041,1.937,1.6,1.576,1.6,1.576a3.641,3.641,0,0,0,3.528,2.97,3.422,3.422,0,0,0,3.49-2.961,1.408,1.408,0,0,0,1.57-1.57,6.124,6.124,0,0,0-2.637-4.8C227.744,467.437,224.466,468.248,223.425,469.595Z" transform="translate(-207.787 -465.295)" fill="url(#linear-gradient-19)"/>
        <path id="路径_8987" data-name="路径 8987" d="M222.72,462.81a3.307,3.307,0,0,1-2.149,2.064c-1.538.3-1.673,1.088-1.673,1.823a3.052,3.052,0,0,0,.312,1.3c-.829-.456-1.961-1.544-1.241-4.542.7-2.9,2.84-4.3,5.727-4.26,2.417-.638,5.995,2.6,5.683,6.809a2.488,2.488,0,0,0-1.349-.964,2.357,2.357,0,0,1-1.194-1.608C226.21,463.569,224.017,463.871,222.72,462.81Z" transform="translate(-205.112 -459.116)" fill="url(#linear-gradient-20)"/>
        <path id="路径_8988" data-name="路径 8988" d="M200.661,568.391h15.962a1.776,1.776,0,0,0,1.62-1.373l3.216-10.584c.417-1.373-.418-2.834-1.62-2.834h-22.25c-1.191,0-2.026,1.435-1.629,2.8l3.072,10.584A1.786,1.786,0,0,0,200.661,568.391Z" transform="translate(-189.666 -525.821)" fill="url(#linear-gradient-21)"/>
        <g id="组_4968" data-name="组 4968" transform="translate(0 35.408)">
          <path id="路径_8989" data-name="路径 8989" d="M185.152,617.313s-.247-.544-.435-.6-2.919-.167-3.916-.426-3.866-.667-5.1.5a3.3,3.3,0,0,0,.206,4.748c.612.532,1.214.688,3.481.582s5.554-.082,6.274-1.132A18.548,18.548,0,0,0,185.152,617.313Z" transform="translate(-174.791 -605.282)" fill="#fff"/>
          <path id="路径_8990" data-name="路径 8990" d="M208.939,618.42a19.413,19.413,0,0,1-3.578.506c-.941-.088-.153,3.71-.153,3.71a15.77,15.77,0,0,0,4.742-.47C210.7,619.255,208.939,618.42,208.939,618.42Z" transform="translate(-196.047 -606.992)" fill="url(#linear-gradient-22)"/>
          <path id="路径_8991" data-name="路径 8991" d="M215.43,590.839l1.37,4.26s29.439-3.878,25.232-13.43C238.515,573.681,215.43,590.839,215.43,590.839Z" transform="translate(-203.482 -579.549)" fill="url(#linear-gradient-23)"/>
          <path id="路径_8992" data-name="路径 8992" d="M185.908,617.582a2.21,2.21,0,0,1,1.214,1.926c.138,1.505-.309,2.514-2.6,2.793a30.049,30.049,0,0,1-4.313.209c-1.438.035-3.525.173-3.834-2.561s2.487-3.431,5.386-2.734C182.674,617.391,185.908,617.582,185.908,617.582Z" transform="translate(-175.892 -605.963)" fill="url(#linear-gradient-24)"/>
        </g>
        <g id="组_4969" data-name="组 4969" transform="translate(0.126 35.408)">
          <path id="路径_8993" data-name="路径 8993" d="M271.282,617.286s.247-.544.435-.6,2.919-.167,3.916-.426,3.672-.688,5.025.465a3.3,3.3,0,0,1-.091,4.742c-.612.532-1.255.726-3.522.62s-5.554-.082-6.274-1.132A18.544,18.544,0,0,1,271.282,617.286Z" transform="translate(-242.678 -605.255)" fill="#fff"/>
          <path id="路径_8994" data-name="路径 8994" d="M261.015,618.42a19.413,19.413,0,0,0,3.578.506c.941-.088.153,3.71.153,3.71a15.77,15.77,0,0,1-4.742-.47C259.254,619.255,261.015,618.42,261.015,618.42Z" transform="translate(-234.946 -606.992)" fill="url(#linear-gradient-25)"/>
          <path id="路径_8995" data-name="路径 8995" d="M202.233,590.839l-1.37,4.26s-29.439-3.878-25.232-13.43C179.151,573.681,202.233,590.839,202.233,590.839Z" transform="translate(-175.22 -579.549)" fill="url(#linear-gradient-26)"/>
          <path id="路径_8996" data-name="路径 8996" d="M270.7,617.582a2.21,2.21,0,0,0-1.214,1.926c-.138,1.505.309,2.514,2.6,2.793a30.049,30.049,0,0,0,4.313.209c1.438.035,3.525.173,3.834-2.561s-2.487-3.431-5.386-2.734C273.937,617.391,270.7,617.582,270.7,617.582Z" transform="translate(-241.755 -605.963)" fill="url(#linear-gradient-27)"/>
        </g>
      </g>
    </g>
  </g>
</svg>
