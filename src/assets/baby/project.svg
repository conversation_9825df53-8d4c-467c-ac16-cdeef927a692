<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="750" height="366" viewBox="0 0 750 366">
  <defs>
    <linearGradient id="linear-gradient" x1="0.5" y1="-1.018" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#3fa1fc"/>
      <stop offset="1" stop-color="#5860da"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="0.5" y1="-0.658" x2="0.5" y2="1.418" xlink:href="#linear-gradient"/>
  </defs>
  <g id="组_5323" data-name="组 5323" transform="translate(0 -118)">
    <g id="_2" data-name="2" transform="translate(20.17 102)">
      <path id="Rectangle_10" data-name="Rectangle 10" d="M0,0H730V348.636H0Z" transform="translate(-0.17 32.495)" fill="url(#linear-gradient)"/>
      <rect id="Rectangle" width="750" height="366" transform="translate(-20.17 16)" fill="url(#linear-gradient-2)"/>
      <g id="Group_2" data-name="Group 2" transform="translate(327.831 348)">
        <circle id="Oval" cx="3" cy="3" r="3" transform="translate(0)" opacity="0.3" fill="url(#linear-gradient)"/>
        <circle id="Oval_Copy" data-name="Oval Copy" cx="3" cy="3" r="3" transform="translate(12)" opacity="0.3" fill="url(#linear-gradient)"/>
        <circle id="Oval_Copy_2" data-name="Oval Copy 2" cx="3" cy="3" r="3" transform="translate(24)" fill="url(#linear-gradient)"/>
        <circle id="Oval_Copy_3" data-name="Oval Copy 3" cx="3" cy="3" r="3" transform="translate(36)" opacity="0.3" fill="url(#linear-gradient)"/>
        <circle id="Oval_Copy_4" data-name="Oval Copy 4" cx="3" cy="3" r="3" transform="translate(48)" opacity="0.3" fill="url(#linear-gradient)"/>
      </g>
    </g>
    <g id="组_4716" data-name="组 4716" transform="translate(18334.684 -12088.654)">
      <g id="Floor" transform="translate(-18007.07 12305.236)">
        <path id="路径_17" data-name="路径 17" d="M438.4,383.582l115.164,70.01a30.921,30.921,0,0,1-.664,53.237L394.12,598.007a89.094,89.094,0,0,1-88.907-.1l-119.59-69.032a23.669,23.669,0,0,1,0-41L357.682,388.6l9.405-5.44a70.5,70.5,0,0,1,71.314.422Z" transform="translate(-173.787 -373.687)" fill="#fff"/>
        <g id="组_1" data-name="组 1" transform="translate(0.06 107.084)">
          <path id="路径_18" data-name="路径 18" d="M553.328,1272.5l-158.781,91.178a89.094,89.094,0,0,1-88.907-.1l-119.59-69.032a23.4,23.4,0,0,1-11.776-18.8v2.471a27,27,0,0,0,13.5,23.4L300,1366.4a99.888,99.888,0,0,0,99.678.112l155.45-89.266a27.274,27.274,0,0,0,13.692-23.582l.018-7.221A30.632,30.632,0,0,1,553.328,1272.5Z" transform="translate(-174.273 -1246.442)" fill="#9ba2bd"/>
          <path id="路径_19" data-name="路径 19" d="M174.309,1451.834q.027-.374.066-.746A5.452,5.452,0,0,0,174.309,1451.834Z" transform="translate(-174.305 -1425.979)" fill="#9ba2bd"/>
        </g>
      </g>
      <g id="Laptop" transform="translate(-17863.371 12264.654)">
        <path id="路径_20" data-name="路径 20" d="M1511.584,1064.489l-61.619,35.627a10.633,10.633,0,0,1-10.639,0l-92.062-53.162a4.227,4.227,0,0,1-2.112-3.23v3.523a4.081,4.081,0,0,0,2.042,3.536l92.132,53.13a10.633,10.633,0,0,0,10.639,0l61.71-35.615a2.512,2.512,0,0,0,1.257-2.176v-3.657A2.71,2.71,0,0,1,1511.584,1064.489Z" transform="translate(-1345.128 -920.934)" fill="#585971"/>
        <path id="路径_21" data-name="路径 21" d="M1511.584,1094.265l-61.619,35.628a10.633,10.633,0,0,1-10.639,0l-92.062-53.162a4.227,4.227,0,0,1-2.112-3.23v3.523a4.081,4.081,0,0,0,2.042,3.535l92.132,53.13a10.631,10.631,0,0,0,10.639,0l61.71-35.615a2.513,2.513,0,0,0,1.257-2.176v-3.657A2.71,2.71,0,0,1,1511.584,1094.265Z" transform="translate(-1345.128 -947.056)" fill="#dddfe6"/>
        <g id="组_3" data-name="组 3" transform="translate(0 0)">
          <path id="路径_22" data-name="路径 22" d="M1901.708,119.476l-.5-75.2,1.554-.911a3.083,3.083,0,0,1,3.1-.011l96.488,55.676a6.427,6.427,0,0,1,3.226,5.535V177a3.315,3.315,0,0,1-1.652,2.868l-2.037,1.18-96.835-55.788A6.668,6.668,0,0,1,1901.708,119.476Z" transform="translate(-1832.956 -42.941)" fill="#585971"/>
          <path id="路径_23" data-name="路径 23" d="M1449.793,811.866l61.619-35.628a2.743,2.743,0,0,0,0-4.75l-95.756-55.3a7.848,7.848,0,0,0-7.853,0l-60.712,35.1a4.279,4.279,0,0,0,0,7.409l92.062,53.162A10.632,10.632,0,0,0,1449.793,811.866Z" transform="translate(-1344.953 -632.664)" fill="#ebedf6"/>
          <path id="路径_24" data-name="路径 24" d="M1889.176,127.776v-73.8a2.724,2.724,0,0,1,4.086-2.359l96.554,55.717a6.452,6.452,0,0,1,3.228,5.589v72.416a3.521,3.521,0,0,1-5.277,3.051l-95.249-54.83A6.668,6.668,0,0,1,1889.176,127.776Z" transform="translate(-1822.401 -50.225)" fill="#333"/>
          <path id="路径_25" data-name="路径 25" d="M1906.172,151.044V81.673a2.578,2.578,0,0,1,3.867-2.233l93.149,54.017a6.107,6.107,0,0,1,3.055,5.289V206.8a3.332,3.332,0,0,1-4.995,2.888l-91.913-53.177A6.312,6.312,0,0,1,1906.172,151.044Z" transform="translate(-1837.312 -74.655)" fill="#fda601"/>
          <path id="路径_26" data-name="路径 26" d="M1609.141,1057.99c4.344-2.529,16.965-9.8,21.964-12.685a4.135,4.135,0,0,1,4.142.007l38.436,22.323a1.013,1.013,0,0,1,0,1.751l-22.237,12.944a3.678,3.678,0,0,1-3.708,0l-38.6-22.586A1.013,1.013,0,0,1,1609.141,1057.99Z" transform="translate(-1576.284 -921.832)" fill="#9ba2bd"/>
          <path id="路径_27" data-name="路径 27" d="M2316.548,1182.458c.4-.28,29.2-17,29.484-17s11.428,6.488,11.428,6.488l-29.555,17.146Z" transform="translate(-2197.336 -1027.726)" fill="#dddfe6"/>
          <path id="路径_28" data-name="路径 28" d="M1607.121,771.1c.4-.28,29.2-17,29.484-17s11.428,6.487,11.428,6.487l-29.555,17.146Z" transform="translate(-1574.954 -666.836)" fill="#dddfe6"/>
          <g id="组_2" data-name="组 2" transform="translate(45.153 94.724)">
            <path id="路径_29" data-name="路径 29" d="M1717.311,932.4l-4.215,2.444a.282.282,0,0,0,0,.488l3.966,2.292a.282.282,0,0,0,.282,0l4.216-2.444a.282.282,0,0,0,0-.488l-3.966-2.292A.282.282,0,0,0,1717.311,932.4Z" transform="translate(-1712.955 -917.957)" fill="#444"/>
            <path id="路径_30" data-name="路径 30" d="M1758.634,956.284l-4.215,2.444a.282.282,0,0,0,0,.488l3.966,2.292a.282.282,0,0,0,.282,0l4.215-2.444a.282.282,0,0,0,0-.488l-3.966-2.292A.282.282,0,0,0,1758.634,956.284Z" transform="translate(-1749.208 -938.91)" fill="#444"/>
            <path id="路径_31" data-name="路径 31" d="M1799.555,979.936l-4.215,2.444a.282.282,0,0,0,0,.488l3.966,2.292a.282.282,0,0,0,.282,0l4.216-2.444a.282.282,0,0,0,0-.488l-3.966-2.292A.282.282,0,0,0,1799.555,979.936Z" transform="translate(-1785.108 -959.661)" fill="#444"/>
            <path id="路径_32" data-name="路径 32" d="M1840.31,1003.554l-4.266,2.473a.257.257,0,0,0,0,.444l5.394,3.118a.257.257,0,0,0,.257,0l4.266-2.473a.257.257,0,0,0,0-.444l-5.394-3.118A.257.257,0,0,0,1840.31,1003.554Z" transform="translate(-1820.829 -980.383)" fill="#444"/>
            <path id="路径_33" data-name="路径 33" d="M1891.857,1033.277l-4.207,2.439a.286.286,0,0,0,0,.5l24.189,13.981a.287.287,0,0,0,.287,0l4.207-2.439a.286.286,0,0,0,0-.5l-24.189-13.981A.286.286,0,0,0,1891.857,1033.277Z" transform="translate(-1866.091 -1006.456)" fill="#444"/>
            <path id="路径_34" data-name="路径 34" d="M1766.184,910.632l-5.246-3.032a.283.283,0,0,0-.283,0l-4.215,2.444a.282.282,0,0,0,0,.488l5.245,3.032a.282.282,0,0,0,.283,0l4.215-2.444A.282.282,0,0,0,1766.184,910.632Z" transform="translate(-1750.982 -896.2)" fill="#444"/>
            <path id="路径_35" data-name="路径 35" d="M1853.276,861.05l-6.8-3.915a.282.282,0,0,0-.282,0l-4.215,2.444a.282.282,0,0,0,0,.488l6.8,3.914a.282.282,0,0,0,.282,0l4.215-2.444A.282.282,0,0,0,1853.276,861.05Z" transform="translate(-1826.024 -851.928)" fill="#444"/>
            <path id="路径_36" data-name="路径 36" d="M1811.686,886.859l-7.708-4.456a.281.281,0,0,0-.284,0l-4.162,2.442a.282.282,0,0,0,0,.487l7.708,4.456a.282.282,0,0,0,.284,0l4.162-2.442A.282.282,0,0,0,1811.686,886.859Z" transform="translate(-1788.787 -874.095)" fill="#444"/>
            <path id="路径_37" data-name="路径 37" d="M1927.1,859.03l3.966,2.292a.281.281,0,0,0,.282,0l4.215-2.444a.282.282,0,0,0,0-.488L1931.6,856.1a.282.282,0,0,0-.282,0l-4.215,2.444A.282.282,0,0,0,1927.1,859.03Z" transform="translate(-1900.703 -851.017)" fill="#444"/>
            <path id="路径_38" data-name="路径 38" d="M1894.242,834.506l-3.966-2.292a.281.281,0,0,0-.282,0l-4.216,2.444a.282.282,0,0,0,0,.488l3.966,2.292a.282.282,0,0,0,.282,0l4.215-2.444A.282.282,0,0,0,1894.242,834.506Z" transform="translate(-1864.45 -830.063)" fill="#444"/>
            <path id="路径_39" data-name="路径 39" d="M1968.424,882.915l3.966,2.292a.282.282,0,0,0,.282,0l4.216-2.444a.282.282,0,0,0,0-.488l-3.966-2.292a.282.282,0,0,0-.282,0l-4.215,2.444A.282.282,0,0,0,1968.424,882.915Z" transform="translate(-1936.955 -871.971)" fill="#444"/>
            <path id="路径_40" data-name="路径 40" d="M2009.128,906.517l4.016,2.321a.257.257,0,0,0,.257,0l4.266-2.473a.257.257,0,0,0,0-.444l-4.016-2.321a.257.257,0,0,0-.257,0l-4.266,2.473A.257.257,0,0,0,2009.128,906.517Z" transform="translate(-1972.676 -892.693)" fill="#444"/>
            <path id="路径_41" data-name="路径 41" d="M2050.451,930.4l4.016,2.321a.257.257,0,0,0,.257,0l4.266-2.473a.257.257,0,0,0,0-.444l-4.016-2.321a.258.258,0,0,0-.258,0l-4.266,2.473A.257.257,0,0,0,2050.451,930.4Z" transform="translate(-2008.929 -913.647)" fill="#444"/>
            <path id="路径_42" data-name="路径 42" d="M2091.773,954.286l4.016,2.321a.256.256,0,0,0,.257,0l4.266-2.473a.257.257,0,0,0,0-.444l-4.016-2.321a.257.257,0,0,0-.258,0l-4.265,2.473A.257.257,0,0,0,2091.773,954.286Z" transform="translate(-2045.181 -934.601)" fill="#444"/>
            <path id="路径_43" data-name="路径 43" d="M2132.293,977.706l4.016,2.321a.258.258,0,0,0,.258,0l4.265-2.473a.257.257,0,0,0,0-.444l-4.016-2.321a.255.255,0,0,0-.257,0l-4.266,2.473A.257.257,0,0,0,2132.293,977.706Z" transform="translate(-2080.729 -955.148)" fill="#444"/>
            <path id="路径_44" data-name="路径 44" d="M2174.271,1001.881l3.957,2.287a.286.286,0,0,0,.286,0l4.207-2.439a.286.286,0,0,0,0-.5l-3.957-2.287a.286.286,0,0,0-.287,0l-4.207,2.439A.286.286,0,0,0,2174.271,1001.881Z" transform="translate(-2117.544 -976.338)" fill="#444"/>
            <path id="路径_45" data-name="路径 45" d="M2215.594,1025.765l3.957,2.287a.286.286,0,0,0,.287,0l4.207-2.439a.286.286,0,0,0,0-.5l-3.958-2.287a.285.285,0,0,0-.287,0l-4.207,2.439A.286.286,0,0,0,2215.594,1025.765Z" transform="translate(-2153.796 -997.292)" fill="#444"/>
            <path id="路径_46" data-name="路径 46" d="M2256.917,1049.65l3.957,2.287a.285.285,0,0,0,.287,0l4.207-2.439a.286.286,0,0,0,0-.5l-3.958-2.287a.286.286,0,0,0-.286,0l-4.207,2.439A.286.286,0,0,0,2256.917,1049.65Z" transform="translate(-2190.049 -1018.245)" fill="#444"/>
            <path id="路径_47" data-name="路径 47" d="M2298.239,1073.534l3.957,2.287a.286.286,0,0,0,.287,0l4.207-2.439a.286.286,0,0,0,0-.5l-3.957-2.287a.287.287,0,0,0-.287,0l-4.207,2.439A.286.286,0,0,0,2298.239,1073.534Z" transform="translate(-2226.301 -1039.199)" fill="#444"/>
            <path id="路径_48" data-name="路径 48" d="M2343.67,1094.463l-4.236,2.456a.271.271,0,0,0,0,.47l3.987,2.3a.271.271,0,0,0,.272,0l4.236-2.456a.271.271,0,0,0,0-.47l-3.986-2.3A.272.272,0,0,0,2343.67,1094.463Z" transform="translate(-2262.448 -1060.136)" fill="#444"/>
            <path id="路径_49" data-name="路径 49" d="M2384.591,1118.115l-4.236,2.457a.271.271,0,0,0,0,.47l3.987,2.3a.272.272,0,0,0,.272,0l4.237-2.456a.271.271,0,0,0,0-.47l-3.987-2.3A.272.272,0,0,0,2384.591,1118.115Z" transform="translate(-2298.348 -1080.887)" fill="#444"/>
            <path id="路径_50" data-name="路径 50" d="M1909.856,899l4.215-2.444a.282.282,0,0,0,0-.488l-3.966-2.292a.281.281,0,0,0-.282,0l-4.216,2.444a.282.282,0,0,0,0,.488l3.966,2.292A.281.281,0,0,0,1909.856,899Z" transform="translate(-1881.847 -884.071)" fill="#444"/>
            <path id="路径_51" data-name="路径 51" d="M1945.772,919.433l3.966,2.292a.281.281,0,0,0,.282,0l4.215-2.444a.282.282,0,0,0,0-.488l-3.966-2.292a.282.282,0,0,0-.282,0l-4.215,2.444A.282.282,0,0,0,1945.772,919.433Z" transform="translate(-1917.083 -904.008)" fill="#444"/>
            <path id="路径_52" data-name="路径 52" d="M1986.477,943.035l4.016,2.321a.257.257,0,0,0,.257,0l4.266-2.473a.257.257,0,0,0,0-.444L1991,940.117a.256.256,0,0,0-.257,0l-4.266,2.473A.257.257,0,0,0,1986.477,943.035Z" transform="translate(-1952.804 -924.731)" fill="#444"/>
            <path id="路径_53" data-name="路径 53" d="M2027.8,966.92l4.016,2.321a.257.257,0,0,0,.257,0l4.266-2.473a.257.257,0,0,0,0-.444L2032.322,964a.257.257,0,0,0-.257,0l-4.266,2.473A.257.257,0,0,0,2027.8,966.92Z" transform="translate(-1989.056 -945.685)" fill="#444"/>
            <path id="路径_54" data-name="路径 54" d="M2069.122,990.8l4.016,2.321a.256.256,0,0,0,.257,0l4.266-2.473a.257.257,0,0,0,0-.444l-4.017-2.321a.256.256,0,0,0-.257,0l-4.265,2.473A.257.257,0,0,0,2069.122,990.8Z" transform="translate(-2025.309 -966.638)" fill="#444"/>
            <path id="路径_55" data-name="路径 55" d="M2109.642,1014.225l4.016,2.321a.256.256,0,0,0,.257,0l4.266-2.473a.257.257,0,0,0,0-.444l-4.016-2.321a.256.256,0,0,0-.257,0l-4.266,2.473A.257.257,0,0,0,2109.642,1014.225Z" transform="translate(-2060.857 -987.185)" fill="#444"/>
            <path id="路径_56" data-name="路径 56" d="M2151.62,1038.4l3.957,2.287a.286.286,0,0,0,.287,0l4.207-2.439a.286.286,0,0,0,0-.5l-3.957-2.287a.286.286,0,0,0-.287,0l-4.207,2.439A.286.286,0,0,0,2151.62,1038.4Z" transform="translate(-2097.672 -1008.375)" fill="#444"/>
            <path id="路径_57" data-name="路径 57" d="M2192.942,1062.283l3.957,2.287a.287.287,0,0,0,.287,0l4.207-2.439a.286.286,0,0,0,0-.5l-3.957-2.287a.287.287,0,0,0-.287,0l-4.207,2.439A.286.286,0,0,0,2192.942,1062.283Z" transform="translate(-2133.925 -1029.329)" fill="#444"/>
            <path id="路径_58" data-name="路径 58" d="M2234.265,1086.168l3.957,2.287a.286.286,0,0,0,.287,0l4.207-2.439a.286.286,0,0,0,0-.5l-3.958-2.287a.286.286,0,0,0-.287,0l-4.207,2.439A.286.286,0,0,0,2234.265,1086.168Z" transform="translate(-2170.176 -1050.283)" fill="#444"/>
            <path id="路径_59" data-name="路径 59" d="M2283.955,1109.4l-3.987-2.3a.27.27,0,0,0-.271,0l-4.237,2.456a.272.272,0,0,0,0,.47l3.987,2.3a.27.27,0,0,0,.271,0l4.236-2.456A.271.271,0,0,0,2283.955,1109.4Z" transform="translate(-2206.323 -1071.22)" fill="#444"/>
            <path id="路径_60" data-name="路径 60" d="M2325.277,1133.286l-3.987-2.3a.272.272,0,0,0-.272,0l-4.237,2.456a.272.272,0,0,0,0,.47l3.987,2.3a.27.27,0,0,0,.272,0l4.236-2.456A.271.271,0,0,0,2325.277,1133.286Z" transform="translate(-2242.576 -1092.174)" fill="#444"/>
            <path id="路径_61" data-name="路径 61" d="M2361.94,1155.1l-4.236,2.456a.272.272,0,0,0,0,.47l3.988,2.3a.271.271,0,0,0,.272,0l4.236-2.456a.272.272,0,0,0,0-.47l-3.987-2.3A.272.272,0,0,0,2361.94,1155.1Z" transform="translate(-2278.477 -1113.331)" fill="#444"/>
            <path id="路径_62" data-name="路径 62" d="M2379.206,1181.153l-4.062-2.357a.271.271,0,0,0-.274,0l-4.25,2.507a.271.271,0,0,0,0,.468l.407.238a.271.271,0,0,1,0,.467l-4.281,2.566a.271.271,0,0,0,0,.468l2.714,1.569a.272.272,0,0,0,.271,0l9.467-5.457A.271.271,0,0,0,2379.206,1181.153Z" transform="translate(-2286.416 -1134.122)" fill="#444"/>
            <path id="路径_63" data-name="路径 63" d="M1876.462,928.564l4.162-2.442a.282.282,0,0,0,0-.487l-3.968-2.293a.282.282,0,0,0-.284,0l-4.162,2.442a.282.282,0,0,0,0,.487l3.968,2.293A.281.281,0,0,0,1876.462,928.564Z" transform="translate(-1852.547 -910.01)" fill="#444"/>
            <path id="路径_64" data-name="路径 64" d="M1917.785,952.448l4.162-2.442a.282.282,0,0,0,0-.487l-3.967-2.293a.282.282,0,0,0-.284,0l-4.162,2.442a.282.282,0,0,0,0,.487l3.967,2.293A.282.282,0,0,0,1917.785,952.448Z" transform="translate(-1888.8 -930.964)" fill="#444"/>
            <path id="路径_65" data-name="路径 65" d="M1962.726,973.164l-4.017-2.322a.257.257,0,0,0-.259,0l-4.212,2.472a.257.257,0,0,0,0,.444l4.017,2.322a.257.257,0,0,0,.259,0l4.212-2.472A.257.257,0,0,0,1962.726,973.164Z" transform="translate(-1924.522 -951.685)" fill="#444"/>
            <path id="路径_66" data-name="路径 66" d="M2004.048,997.049l-4.018-2.322a.257.257,0,0,0-.258,0l-4.212,2.472a.257.257,0,0,0,0,.444l4.018,2.322a.256.256,0,0,0,.258,0l4.212-2.472A.257.257,0,0,0,2004.048,997.049Z" transform="translate(-1960.774 -972.64)" fill="#444"/>
            <path id="路径_67" data-name="路径 67" d="M2045.371,1020.933l-4.018-2.322a.257.257,0,0,0-.258,0l-4.212,2.472a.257.257,0,0,0,0,.444l4.018,2.322a.257.257,0,0,0,.258,0l4.212-2.472A.257.257,0,0,0,2045.371,1020.933Z" transform="translate(-1997.027 -993.593)" fill="#444"/>
            <path id="路径_68" data-name="路径 68" d="M2085.892,1044.354l-4.018-2.322a.257.257,0,0,0-.259,0l-4.212,2.472a.257.257,0,0,0,0,.444l4.018,2.322a.257.257,0,0,0,.259,0l4.212-2.472A.257.257,0,0,0,2085.892,1044.354Z" transform="translate(-2032.576 -1014.14)" fill="#444"/>
            <path id="路径_69" data-name="路径 69" d="M2127.779,1068.478l-3.958-2.288a.287.287,0,0,0-.288,0l-4.153,2.437a.286.286,0,0,0,0,.495l3.959,2.288a.286.286,0,0,0,.288,0l4.153-2.437A.286.286,0,0,0,2127.779,1068.478Z" transform="translate(-2069.388 -1035.33)" fill="#444"/>
            <path id="路径_70" data-name="路径 70" d="M2169.1,1092.362l-3.959-2.288a.286.286,0,0,0-.288,0l-4.154,2.437a.286.286,0,0,0,0,.495l3.958,2.288a.286.286,0,0,0,.288,0l4.154-2.437A.286.286,0,0,0,2169.1,1092.362Z" transform="translate(-2105.64 -1056.284)" fill="#444"/>
            <path id="路径_71" data-name="路径 71" d="M2210.424,1116.246l-3.959-2.288a.287.287,0,0,0-.288,0l-4.153,2.437a.286.286,0,0,0,0,.495l3.959,2.288a.285.285,0,0,0,.288,0l4.154-2.437A.286.286,0,0,0,2210.424,1116.246Z" transform="translate(-2141.893 -1077.238)" fill="#444"/>
            <path id="路径_72" data-name="路径 72" d="M2251.665,1140.127l-3.989-2.305a.272.272,0,0,0-.273,0l-4.183,2.454a.272.272,0,0,0,0,.469l3.988,2.305a.272.272,0,0,0,.273,0l4.183-2.455A.271.271,0,0,0,2251.665,1140.127Z" transform="translate(-2178.041 -1098.175)" fill="#444"/>
            <path id="路径_73" data-name="路径 73" d="M2292.987,1164.474l-3.988-2.305a.272.272,0,0,0-.273,0l-4.183,2.454a.272.272,0,0,0,0,.469l3.989,2.305a.271.271,0,0,0,.273,0l4.183-2.454A.272.272,0,0,0,2292.987,1164.474Z" transform="translate(-2214.293 -1119.536)" fill="#444"/>
            <path id="路径_74" data-name="路径 74" d="M2333.909,1188.127l-3.989-2.305a.271.271,0,0,0-.273,0l-4.183,2.454a.271.271,0,0,0,0,.469l3.988,2.305a.271.271,0,0,0,.273,0l4.182-2.454A.271.271,0,0,0,2333.909,1188.127Z" transform="translate(-2250.194 -1140.286)" fill="#444"/>
            <path id="路径_75" data-name="路径 75" d="M1807.391,939.982l3.966,2.292a.282.282,0,0,0,.282,0l4.215-2.444a.282.282,0,0,0,0-.488l-3.966-2.292a.282.282,0,0,0-.282,0l-4.216,2.444A.282.282,0,0,0,1807.391,939.982Z" transform="translate(-1795.681 -922.036)" fill="#444"/>
            <path id="路径_76" data-name="路径 76" d="M1857.178,963.227l-3.966-2.292a.282.282,0,0,0-.282,0l-4.215,2.444a.282.282,0,0,0,0,.488l3.966,2.292a.282.282,0,0,0,.282,0l4.215-2.444A.282.282,0,0,0,1857.178,963.227Z" transform="translate(-1831.934 -942.99)" fill="#444"/>
            <path id="路径_77" data-name="路径 77" d="M1897.958,986.873l-4.016-2.321a.256.256,0,0,0-.257,0l-4.266,2.473a.257.257,0,0,0,0,.444l4.016,2.321a.256.256,0,0,0,.257,0l4.266-2.473A.257.257,0,0,0,1897.958,986.873Z" transform="translate(-1867.655 -963.713)" fill="#444"/>
            <path id="路径_78" data-name="路径 78" d="M1939.28,1010.757l-4.016-2.321a.257.257,0,0,0-.257,0l-4.266,2.473a.257.257,0,0,0,0,.444l4.016,2.321a.256.256,0,0,0,.257,0l4.266-2.473A.257.257,0,0,0,1939.28,1010.757Z" transform="translate(-1903.907 -984.667)" fill="#444"/>
            <path id="路径_79" data-name="路径 79" d="M1980.6,1034.641l-4.016-2.321a.256.256,0,0,0-.257,0l-4.266,2.473a.257.257,0,0,0,0,.445l4.016,2.321a.257.257,0,0,0,.257,0l4.266-2.473A.257.257,0,0,0,1980.6,1034.641Z" transform="translate(-1940.16 -1005.62)" fill="#444"/>
            <path id="路径_80" data-name="路径 80" d="M2021.122,1058.062l-4.016-2.321a.257.257,0,0,0-.257,0l-4.266,2.473a.257.257,0,0,0,0,.445l4.016,2.321a.256.256,0,0,0,.257,0l4.266-2.474A.257.257,0,0,0,2021.122,1058.062Z" transform="translate(-1975.708 -1026.167)" fill="#444"/>
            <path id="路径_81" data-name="路径 81" d="M2063.012,1082.186l-3.957-2.287a.286.286,0,0,0-.286,0l-4.207,2.439a.286.286,0,0,0,0,.5l3.957,2.287a.286.286,0,0,0,.287,0l4.207-2.439A.286.286,0,0,0,2063.012,1082.186Z" transform="translate(-2012.522 -1047.358)" fill="#444"/>
            <path id="路径_82" data-name="路径 82" d="M2104.335,1105.143l-3.957-2.287a.288.288,0,0,0-.288,0l-4.207,2.439a.286.286,0,0,0,0,.5l3.957,2.287a.287.287,0,0,0,.287,0l4.207-2.439A.286.286,0,0,0,2104.335,1105.143Z" transform="translate(-2048.775 -1067.498)" fill="#444"/>
            <path id="路径_83" data-name="路径 83" d="M2145.657,1129.027l-3.958-2.287a.285.285,0,0,0-.286,0l-4.207,2.439a.286.286,0,0,0,0,.5l3.957,2.287a.287.287,0,0,0,.287,0l4.207-2.439A.286.286,0,0,0,2145.657,1129.027Z" transform="translate(-2085.027 -1088.452)" fill="#444"/>
            <path id="路径_84" data-name="路径 84" d="M2186.9,1153.371l-3.987-2.3a.271.271,0,0,0-.272,0l-4.236,2.456a.272.272,0,0,0,0,.47l3.987,2.3a.271.271,0,0,0,.272,0l4.236-2.456A.271.271,0,0,0,2186.9,1153.371Z" transform="translate(-2121.174 -1109.796)" fill="#444"/>
            <path id="路径_85" data-name="路径 85" d="M2228.219,1177.255l-3.987-2.3a.271.271,0,0,0-.271,0l-4.237,2.456a.272.272,0,0,0,0,.47l3.987,2.3a.271.271,0,0,0,.272,0l4.237-2.456A.272.272,0,0,0,2228.219,1177.255Z" transform="translate(-2157.427 -1130.749)" fill="#444"/>
            <path id="路径_86" data-name="路径 86" d="M2189.291,1217.827l-1.848,1.071a.272.272,0,0,0,0,.47l3.987,2.3a.271.271,0,0,0,.272,0l1.847-1.071a.271.271,0,0,0,0-.47l-3.987-2.3A.271.271,0,0,0,2189.291,1217.827Z" transform="translate(-2129.107 -1168.364)" fill="#444"/>
            <path id="路径_87" data-name="路径 87" d="M2269.688,1266.9l3.987,2.3a.271.271,0,0,0,.272,0l1.847-1.071a.271.271,0,0,0,0-.47l-3.987-2.3a.271.271,0,0,0-.272,0l-1.847,1.071A.272.272,0,0,0,2269.688,1266.9Z" transform="translate(-2201.26 -1210.068)" fill="#444"/>
            <path id="路径_88" data-name="路径 88" d="M2236.851,1232.941l-3.981-2.3a.271.271,0,0,0-.268,0l-2.255,1.254-1.983,1.15a.271.271,0,0,0,0,.47l3.987,2.3a.271.271,0,0,0,.272,0l2.253-1.306,1.97-1.1A.271.271,0,0,0,2236.851,1232.941Z" transform="translate(-2165.007 -1179.605)" fill="#444"/>
            <path id="路径_89" data-name="路径 89" d="M2150.357,1182.653l-4.237,2.456a.272.272,0,0,0,0,.47l3.987,2.3a.272.272,0,0,0,.272,0l4.236-2.456a.271.271,0,0,0,0-.47l-3.987-2.3A.27.27,0,0,0,2150.357,1182.653Z" transform="translate(-2092.854 -1137.506)" fill="#444"/>
            <path id="路径_90" data-name="路径 90" d="M2098.3,1152.065l-4.207,2.439a.286.286,0,0,0,0,.5l5.286,3.056a.287.287,0,0,0,.287,0l4.207-2.439a.286.286,0,0,0,0-.5l-5.286-3.055A.285.285,0,0,0,2098.3,1152.065Z" transform="translate(-2047.204 -1110.669)" fill="#444"/>
            <path id="路径_91" data-name="路径 91" d="M2260.646,1201.53l10.386,6a.271.271,0,0,0,.272,0l4.236-2.456a.271.271,0,0,0,0-.47l-10.386-6a.271.271,0,0,0-.271,0l-4.236,2.456A.272.272,0,0,0,2260.646,1201.53Z" transform="translate(-2193.327 -1151.5)" fill="#444"/>
            <path id="路径_92" data-name="路径 92" d="M2421.678,1144.926l6.5,3.755a.272.272,0,0,0,.272,0l4.236-2.456a.271.271,0,0,0,0-.47l-6.5-3.755a.271.271,0,0,0-.272,0l-4.237,2.456A.271.271,0,0,0,2421.678,1144.926Z" transform="translate(-2334.601 -1101.841)" fill="#444"/>
            <path id="路径_93" data-name="路径 93" d="M1936.453,817.287,1932.489,815a.282.282,0,0,0-.281,0l-2.589,1.474a.282.282,0,0,0,0,.489l3.963,2.291a.282.282,0,0,0,.28,0l2.589-1.474A.282.282,0,0,0,1936.453,817.287Z" transform="translate(-1902.911 -814.959)" fill="#444"/>
            <path id="路径_94" data-name="路径 94" d="M2501.427,1144.029l-2.61,1.486a.271.271,0,0,0,0,.471l2.409,1.392a.271.271,0,0,0,.27,0l2.611-1.487a.271.271,0,0,0,0-.471l-2.409-1.392A.271.271,0,0,0,2501.427,1144.029Z" transform="translate(-2402.273 -1103.622)" fill="#444"/>
            <path id="路径_95" data-name="路径 95" d="M2034.927,877.651l2.581-1.47a.286.286,0,0,0,0-.5l-63.664-36.8a.286.286,0,0,0-.285,0l-2.581,1.469a.286.286,0,0,0,0,.5l63.664,36.8A.286.286,0,0,0,2034.927,877.651Z" transform="translate(-1939.195 -835.917)" fill="#444"/>
          </g>
          <path id="路径_96" data-name="路径 96" d="M1669.027,1240.872l1.24-.66a2.189,2.189,0,0,1,2.123.036l14.2,8.2a.732.732,0,0,1-.022,1.28l-1.147.611Z" transform="translate(-1629.264 -1093.085)" fill="#9ba2bd"/>
          <path id="路径_97" data-name="路径 97" d="M2633.133,1248.066v0a.6.6,0,0,0,.906.522l3.181-1.842a.716.716,0,0,0,.357-.62h0a.6.6,0,0,0-.9-.525l-3.154,1.793A.768.768,0,0,0,2633.133,1248.066Z" transform="translate(-2475.078 -1097.972)" fill="#dddfe6"/>
          <path id="路径_98" data-name="路径 98" d="M2574.029,1282.384v0a.6.6,0,0,0,.906.522l3.181-1.842a.716.716,0,0,0,.357-.62h0a.6.6,0,0,0-.9-.524l-3.155,1.793A.769.769,0,0,0,2574.029,1282.384Z" transform="translate(-2423.225 -1128.08)" fill="#dddfe6"/>
        </g>
        <g id="组_4" data-name="组 4" transform="translate(70.827 42.948)">
          <path id="路径_99" data-name="路径 99" d="M2102.692,459.04l-2.262-1.312-67.871,38.108,2.269,1.313Z" transform="translate(-2019.019 -449.784)" fill="#fff" opacity="0.6"/>
          <path id="路径_100" data-name="路径 100" d="M1923.4,431.888l7.846,4.54,67.871-38.114-9.2-5.336L1922.2,431A6.312,6.312,0,0,0,1923.4,431.888Z" transform="translate(-1922.203 -392.977)" fill="#fff" opacity="0.6"/>
          <path id="路径_101" data-name="路径 101" d="M2155.578,489.676l-67.863,38.1,2.569,1.486,67.855-38.1Z" transform="translate(-2067.407 -477.811)" fill="#fff" opacity="0.6"/>
        </g>
      </g>
      <g id="Identity_Card" data-name="Identity Card" transform="translate(-17683.279 12359.988)">
        <path id="路径_102" data-name="路径 102" d="M2819.241,825.6l39.582,22.864a5.861,5.861,0,0,1,2.929,5.075V880.4a4.337,4.337,0,0,1-6.506,3.756l-39.589-22.86a5.861,5.861,0,0,1-2.93-5.077l.008-26.857A4.337,4.337,0,0,1,2819.241,825.6Z" transform="translate(-2812.727 -824.392)" fill="#585971"/>
        <path id="路径_103" data-name="路径 103" d="M2878.311,843.352l-39.582-22.864c-1.663-.961-4.187-.6-6.031.51a4.288,4.288,0,0,1,4.064.146l39.582,22.864a5.86,5.86,0,0,1,2.929,5.075v26.854a4.337,4.337,0,0,1-6.27,3.883l0,0c2.891,1.67,8.234-1.2,8.234-4.54V848.427A5.861,5.861,0,0,0,2878.311,843.352Z" transform="translate(-2830.248 -819.932)" fill="#9ba2bd"/>
        <g id="组_5" data-name="组 5" transform="translate(21.828 27.433)">
          <path id="路径_104" data-name="路径 104" d="M2991.1,1043.821v.076a.306.306,0,0,0,.152.264l13.572,7.864a.3.3,0,0,0,.458-.264v-.076a.3.3,0,0,0-.152-.263l-13.572-7.864A.3.3,0,0,0,2991.1,1043.821Z" transform="translate(-2991.041 -1043.516)" fill="#fff"/>
          <path id="路径_105" data-name="路径 105" d="M3042.99,1118.12v.076a.305.305,0,0,0,.152.264l13.571,7.864a.3.3,0,0,0,.457-.264v-.076a.305.305,0,0,0-.152-.264l-13.571-7.864A.3.3,0,0,0,3042.99,1118.12Z" transform="translate(-3036.565 -1108.699)" fill="#fff"/>
          <path id="路径_106" data-name="路径 106" d="M2990.627,1073.541v.076a.305.305,0,0,0,.152.264l13.571,7.864a.3.3,0,0,0,.458-.264v-.076a.3.3,0,0,0-.152-.264l-13.571-7.864A.3.3,0,0,0,2990.627,1073.541Z" transform="translate(-2990.627 -1069.589)" fill="#fff"/>
          <path id="路径_107" data-name="路径 107" d="M3110.685,1112.931v.076a.3.3,0,0,0,.152.264l5.266,3.031a.3.3,0,0,0,.457-.263v-.076a.305.305,0,0,0-.152-.264l-5.266-3.031A.3.3,0,0,0,3110.685,1112.931Z" transform="translate(-3095.955 -1104.146)" fill="#fff"/>
          <path id="路径_108" data-name="路径 108" d="M3045.585,1090.523v.076a.306.306,0,0,0,.152.264l5.265,3.031a.3.3,0,0,0,.458-.264v-.076a.3.3,0,0,0-.152-.264l-5.265-3.031A.3.3,0,0,0,3045.585,1090.523Z" transform="translate(-3038.842 -1084.488)" fill="#fff"/>
          <path id="路径_109" data-name="路径 109" d="M3110.685,1141.707v.076a.305.305,0,0,0,.152.264l5.266,3.031a.3.3,0,0,0,.457-.263v-.076a.306.306,0,0,0-.152-.264l-5.266-3.031A.3.3,0,0,0,3110.685,1141.707Z" transform="translate(-3095.955 -1129.392)" fill="#fff"/>
          <path id="路径_110" data-name="路径 110" d="M2990.627,1087.929V1088a.305.305,0,0,0,.152.264l5.265,3.031a.3.3,0,0,0,.458-.264v-.076a.3.3,0,0,0-.152-.264l-5.265-3.031A.3.3,0,0,0,2990.627,1087.929Z" transform="translate(-2990.627 -1082.212)" fill="#fff"/>
          <path id="路径_111" data-name="路径 111" d="M3098.184,1120.715v.076a.3.3,0,0,0,.152.264l6.8,3.928a.3.3,0,0,0,.458-.264v-.076a.3.3,0,0,0-.152-.264l-6.8-3.928A.3.3,0,0,0,3098.184,1120.715Z" transform="translate(-3084.987 -1110.975)" fill="#fff"/>
          <path id="路径_112" data-name="路径 112" d="M2990.627,1058.445v.076a.407.407,0,0,0,.209.322l5.265,3.031a.332.332,0,0,0,.4-.321v-.076a.305.305,0,0,0-.152-.264l-5.265-3.031A.3.3,0,0,0,2990.627,1058.445Z" transform="translate(-2990.627 -1056.346)" fill="#fff"/>
          <path id="路径_113" data-name="路径 113" d="M2991.1,1102.553v.076a.306.306,0,0,0,.152.263l13.572,7.864a.3.3,0,0,0,.458-.264v-.076a.3.3,0,0,0-.152-.264l-13.572-7.864A.3.3,0,0,0,2991.1,1102.553Z" transform="translate(-2991.041 -1095.042)" fill="#fff"/>
          <path id="路径_114" data-name="路径 114" d="M3042.99,1176.851v.076a.305.305,0,0,0,.152.264l13.571,7.864a.3.3,0,0,0,.457-.264v-.076a.305.305,0,0,0-.152-.264l-13.571-7.864A.3.3,0,0,0,3042.99,1176.851Z" transform="translate(-3036.565 -1160.224)" fill="#fff"/>
          <path id="路径_115" data-name="路径 115" d="M2990.627,1132.272v.076a.305.305,0,0,0,.152.264l13.571,7.864a.3.3,0,0,0,.458-.264v-.076a.3.3,0,0,0-.152-.264l-13.571-7.864A.3.3,0,0,0,2990.627,1132.272Z" transform="translate(-2990.627 -1121.115)" fill="#fff"/>
          <path id="路径_116" data-name="路径 116" d="M3110.685,1171.662v.076a.3.3,0,0,0,.152.264l5.266,3.031a.3.3,0,0,0,.457-.264v-.076a.305.305,0,0,0-.152-.264l-5.266-3.031A.3.3,0,0,0,3110.685,1171.662Z" transform="translate(-3095.955 -1155.671)" fill="#fff"/>
          <path id="路径_117" data-name="路径 117" d="M3045.585,1149.255v.076a.305.305,0,0,0,.152.264l5.265,3.031a.3.3,0,0,0,.458-.264v-.076a.3.3,0,0,0-.152-.263l-5.265-3.031A.3.3,0,0,0,3045.585,1149.255Z" transform="translate(-3038.842 -1136.013)" fill="#fff"/>
          <path id="路径_118" data-name="路径 118" d="M3110.685,1200.438v.076a.3.3,0,0,0,.152.264l5.266,3.031a.3.3,0,0,0,.457-.264v-.076a.305.305,0,0,0-.152-.264l-5.266-3.031A.3.3,0,0,0,3110.685,1200.438Z" transform="translate(-3095.955 -1180.917)" fill="#fff"/>
          <path id="路径_119" data-name="路径 119" d="M2990.627,1146.66v.076a.306.306,0,0,0,.152.264l5.265,3.031a.3.3,0,0,0,.458-.264v-.076a.305.305,0,0,0-.152-.264l-5.265-3.031A.3.3,0,0,0,2990.627,1146.66Z" transform="translate(-2990.627 -1133.737)" fill="#fff"/>
          <path id="路径_120" data-name="路径 120" d="M3098.184,1179.446v.076a.3.3,0,0,0,.152.264l6.8,3.928a.3.3,0,0,0,.458-.264v-.076a.3.3,0,0,0-.152-.264l-6.8-3.928A.3.3,0,0,0,3098.184,1179.446Z" transform="translate(-3084.987 -1162.5)" fill="#fff"/>
          <path id="路径_121" data-name="路径 121" d="M2990.627,1117.177v.076a.407.407,0,0,0,.209.322l5.265,3.031a.332.332,0,0,0,.4-.322v-.076a.3.3,0,0,0-.152-.263l-5.265-3.031A.3.3,0,0,0,2990.627,1117.177Z" transform="translate(-2990.627 -1107.871)" fill="#fff"/>
        </g>
        <g id="组_6" data-name="组 6" transform="translate(5.543 9.648)">
          <path id="路径_122" data-name="路径 122" d="M2857.9,898.909V899a.347.347,0,0,0,.173.3l36.457,20.845a.347.347,0,0,0,.521-.3v-.086a.348.348,0,0,0-.173-.3l-36.457-20.845A.347.347,0,0,0,2857.9,898.909Z" transform="translate(-2857.903 -898.561)" fill="#fff"/>
        </g>
        <g id="组_7" data-name="组 7" transform="translate(5.543 12.307)">
          <path id="路径_123" data-name="路径 123" d="M2857.9,920.586v.086a.347.347,0,0,0,.173.3l36.457,20.845a.347.347,0,0,0,.521-.3v-.086a.348.348,0,0,0-.173-.3l-36.457-20.845A.347.347,0,0,0,2857.9,920.586Z" transform="translate(-2857.903 -920.238)" fill="#fff"/>
        </g>
        <g id="组_9" data-name="组 9" transform="translate(5.543 17.392)">
          <path id="路径_124" data-name="路径 124" d="M2857.9,961.679v13.683l15.4,8.891-.141-13.631Z" transform="translate(-2857.903 -961.679)" fill="#ebedf6"/>
          <path id="路径_125" data-name="路径 125" d="M2886.271,1068.625c.021-3.346-.751-7.341-4.374-8.162,0,0-1.634,1.357-2.215-.353-.02-.059-2.009-.193-3.123,2.909Z" transform="translate(-2874.271 -1048.014)" fill="#d35050"/>
          <g id="组_8" data-name="组 8" transform="translate(4.204 7.515)">
            <path id="路径_126" data-name="路径 126" d="M2919.636,1052.276c-.014-.167.22,0,.4-.243a.883.883,0,0,0-.161.494c.005.134-.09.167-.183.168C2919.666,1052.548,2919.645,1052.386,2919.636,1052.276Z" transform="translate(-2916.264 -1048.462)" fill="#120621"/>
            <path id="路径_127" data-name="路径 127" d="M2894.768,1033.8s-.195,3.362.849,3.076c0,0,.2.123.1.471s1.657.432,2.086-.551c0,0,.021-1,0-1.25s.491.225.573-.921C2898.38,1034.624,2897.444,1031.516,2894.768,1033.8Z" transform="translate(-2894.435 -1031.731)" fill="#fbaa78"/>
            <path id="路径_128" data-name="路径 128" d="M2895.663,1023.79a.929.929,0,0,1,.673.589.771.771,0,0,1-.066.667c-.287.5-.164.778-.164.778s-.235-.334-.462-.063a.447.447,0,0,0-.083.151c-.076.242-.348.514-.54.322-.058-.058.245-1.248-.266-1.269s-2.435.8-2.579-.593.758-1.762.921-1.167Z" transform="translate(-2892.162 -1022.93)" fill="#120621"/>
            <path id="路径_129" data-name="路径 129" d="M2900.273,1063.07c0,.085,0,.14,0,.14-.429.982-2.189.9-2.087.55s-.1-.471-.1-.471a.386.386,0,0,1-.35-.06c-.26.461-.372,1.091.818,1.236,2,.21,2.117-1.3,2.117-1.3l-.344-.09Z" transform="translate(-2896.9 -1058.145)" fill="#fff"/>
          </g>
        </g>
      </g>
      <g id="Table" transform="translate(-17948.748 12377.994)">
        <path id="路径_130" data-name="路径 130" d="M777.167,1013.086,697.956,967.34a4.921,4.921,0,0,0-4.924,0l-43.92,25.383.008,0v56.625l3.268,1.891,46.383-26.8V1021.4l32.014,18.486a10.918,10.918,0,0,1,5.458,9.455v50.326l46.383-26.8v-50.328A10.918,10.918,0,0,0,777.167,1013.086Zm-42.435,25.556L655.372,992.9l37.987-21.954a4.921,4.921,0,0,1,4.92,0l77.465,44.656a7.533,7.533,0,0,1,2.114,1.81l-40.72,23.517A7.819,7.819,0,0,0,734.732,1038.641Zm45.158,32.546-40.447,23.369V1048.13a13,13,0,0,0-.961-4.713l40.744-23.541a12.016,12.016,0,0,1,.664,4.067Z" transform="translate(-649.112 -966.681)" fill="#dddfe6"/>
        <g id="组_10" data-name="组 10" transform="translate(6.309 3.615)">
          <path id="路径_131" data-name="路径 131" d="M823.016,1043.265a7.532,7.532,0,0,0-2.114-1.81L743.437,996.8a4.921,4.921,0,0,0-4.92,0l-37.987,21.954,79.36,45.744a7.816,7.816,0,0,1,2.407,2.282Z" transform="translate(-700.53 -996.141)" fill="#9ba2bd"/>
          <path id="路径_132" data-name="路径 132" d="M1377.491,1423.776a13,13,0,0,1,.961,4.713V1478l40.447-23.2V1404.3a12.014,12.014,0,0,0-.664-4.066Z" transform="translate(-1294.43 -1350.654)" fill="#9ba2bd"/>
        </g>
        <path id="路径_133" data-name="路径 133" d="M675.812,1194.359v56.628l43.114-24.91v-6.829Z" transform="translate(-672.536 -1166.424)" fill="#9ba2bd"/>
      </g>
      <g id="Paper" transform="translate(-17911.436 12410.858)">
        <path id="路径_134" data-name="路径 134" d="M953.215,1243.632l15.751-9.1,9.867,5.689-15.768,9.1Z" transform="translate(-953.215 -1234.527)" fill="#fff"/>
        <path id="路径_135" data-name="路径 135" d="M1072.036,1253.6s1.415-.234,1.445.472a.532.532,0,0,1-.186.421c-.31.27-1.207.684-3.7.318,0,0-.018-1.67,1.134-2.054S1072.331,1253.2,1072.036,1253.6Z" transform="translate(-1055.312 -1250.434)" fill="#d35050"/>
        <path id="路径_136" data-name="路径 136" d="M1075.555,1258.615l-.915-.538.276-.364-1.277.441.792-.924-.673.339-.791-.458.024-.035.024-.033.745.431,1-.494-.843.985,1.24-.429-.392.517.9.531-.051.015Z" transform="translate(-1058.275 -1254.225)" fill="#fff"/>
        <path id="路径_137" data-name="路径 137" d="M1102.941,1271.859l2.145,1.224a.116.116,0,0,0,.117-.2l-2.145-1.224a.116.116,0,0,0-.117.2Z" transform="translate(-1084.521 -1267.089)" fill="#ffba6e"/>
        <path id="路径_138" data-name="路径 138" d="M1048.679,1269.189c.715.408,4.864,2.785,5.58,3.193a.116.116,0,0,0,.116-.2c-.715-.408-4.864-2.785-5.579-3.193a.116.116,0,0,0-.117.2Z" transform="translate(-1036.917 -1264.747)" fill="#9ba2bd"/>
        <path id="路径_139" data-name="路径 139" d="M1039.42,1274.487c.715.408,4.865,2.785,5.58,3.193a.116.116,0,0,0,.117-.2c-.715-.408-4.865-2.785-5.579-3.193a.116.116,0,0,0-.117.2Z" transform="translate(-1028.794 -1269.395)" fill="#9ba2bd"/>
        <path id="路径_140" data-name="路径 140" d="M1030.348,1279.751c.715.408,4.864,2.785,5.579,3.193a.116.116,0,0,0,.117-.2c-.715-.408-4.864-2.785-5.579-3.193a.116.116,0,0,0-.117.2Z" transform="translate(-1020.835 -1274.014)" fill="#9ba2bd"/>
        <path id="路径_141" data-name="路径 141" d="M1020.873,1285.17c.715.408,4.864,2.785,5.579,3.193a.116.116,0,0,0,.117-.2c-.715-.408-4.864-2.785-5.579-3.193a.116.116,0,0,0-.117.2Z" transform="translate(-1012.522 -1278.767)" fill="#9ba2bd"/>
        <path id="路径_142" data-name="路径 142" d="M1011.181,1290.817c.715.408,4.864,2.785,5.579,3.193a.116.116,0,0,0,.117-.2c-.715-.408-4.864-2.785-5.579-3.193a.116.116,0,0,0-.117.2Z" transform="translate(-1004.02 -1283.721)" fill="#9ba2bd"/>
        <path id="路径_143" data-name="路径 143" d="M1096.935,1275.34l2.145,1.224a.116.116,0,0,0,.116-.2l-2.145-1.224a.116.116,0,0,0-.117.2Z" transform="translate(-1079.252 -1270.143)" fill="#ffba6e"/>
        <path id="路径_144" data-name="路径 144" d="M1007.266,1322.883l2.145,1.224a.116.116,0,0,0,.117-.2l-2.145-1.223a.116.116,0,0,0-.117.2Z" transform="translate(-1000.585 -1311.853)" fill="#9ba2bd"/>
      </g>
      <g id="Man" transform="translate(-17946.074 12391.684)">
        <ellipse id="椭圆_1" data-name="椭圆 1" cx="36.573" cy="18.87" rx="36.573" ry="18.87" transform="translate(0 67.206)" fill="#dddfe6"/>
        <path id="路径_145" data-name="路径 145" d="M951.77,1462.963v26.4s-6.074,2.823-10.2.468c-.147-.883-.926-26.565-.926-26.565l-.355,20.577s-5.7,3.938-9.715-.221c-.087-.435.2-10.533.447-18.717.013-.417-10.54-4.219-10.528-4.625.156-5.1,10.854-5.78,10.854-5.78s1.959-.15,9.251-1.2A9.77,9.77,0,0,1,951.77,1462.963Z" transform="translate(-889.873 -1407.187)" fill="#333"/>
        <path id="路径_146" data-name="路径 146" d="M964.8,1284.062c-12.117-14.389-5.952-16.64-5.952-16.64,5.183-4.934,12.027,9.284,12.027,9.284l3.116,5.252,9.72-3.315,2.643,2.446s-11.228,10.058-15.022,7.913A39.3,39.3,0,0,1,964.8,1284.062Z" transform="translate(-922.139 -1243.299)" fill="#d35050"/>
        <path id="路径_147" data-name="路径 147" d="M865.64,1260.754a24.831,24.831,0,0,1-6.441,1.17,25.382,25.382,0,0,1-3.246-.049c-7.621-.731-9.868-2.654-9.521-7.439.366-5.048-.731-16.315-.731-16.315-2.857-20.115,8.095-15.275,8.095-15.275a4.521,4.521,0,0,1,1.349.723,10.177,10.177,0,0,0,4.176,1.492,11.494,11.494,0,0,1,7.133,3.9c10.368,11.768.8,14.475.888,16.17.088,1.729-.045,2.722-.091,4.938a27.929,27.929,0,0,0,.2,4.432,18.51,18.51,0,0,1,.007,4.64A2.791,2.791,0,0,1,865.64,1260.754Z" transform="translate(-823.837 -1204.656)" fill="#d35050"/>
        <path id="路径_148" data-name="路径 148" d="M923.513,1134.156c.227.084,1.58,12.012-4.019,10.672a6.009,6.009,0,0,0,.488,2.3s-3.973,1.428-6.368-.543a1.322,1.322,0,0,1-.41-1.442c.168-.5.413-1.217.711-2.05C914.7,1140.884,912.751,1130.182,923.513,1134.156Z" transform="translate(-883.414 -1126.528)" fill="#fbaa78"/>
        <path id="路径_149" data-name="路径 149" d="M920.1,1228.971l-.548-.824s-4.531,1.238-6.3-1.649c0,0-1.425,1.958,2.137,2.762C918.83,1229.958,920.1,1228.971,920.1,1228.971Z" transform="translate(-883.265 -1208.308)" fill="#fff"/>
        <path id="路径_150" data-name="路径 150" d="M971.249,1340.51c-.032-.029-.018-.012.05.06l-.05-.06c.075.067.4.382.921.843-.031,1.9-.147,4.239-.127,5.191a17.8,17.8,0,0,1-7.3-15.751A41.54,41.54,0,0,0,971.249,1340.51Z" transform="translate(-928.642 -1299.807)" fill="#a82d2d"/>
        <g id="组_11" data-name="组 11" transform="translate(18.829 18.293)">
          <path id="路径_151" data-name="路径 151" d="M873.2,1227.343h0Z" transform="translate(-867.209 -1227.343)" fill="#333"/>
          <path id="路径_152" data-name="路径 152" d="M824.365,1250.612l.216-4.957c.107-8.485,3.026-12.255,4.981-13.808-1.995,1.713-3.43,5.728-2.167,14.619a92.634,92.634,0,0,1,.937,12.05l-3.168-4.94A5.08,5.08,0,0,1,824.365,1250.612Z" transform="translate(-824.361 -1231.294)" fill="#d35050"/>
        </g>
        <path id="路径_153" data-name="路径 153" d="M844.84,1321.169h0c.075,1.118.2,2.334.391,3.659,0,0,.688,4.717.888,9.21h0C843.968,1330,844.84,1321.169,844.84,1321.169Z" transform="translate(-823.345 -1291.364)" fill="#a82d2d"/>
        <path id="路径_154" data-name="路径 154" d="M857.12,1508.61c.669,2.138,2.572,3.353,6.353,4.029a30.186,30.186,0,0,0,.787-3.05,15.5,15.5,0,0,1,.237,3.214q.885.126,1.891.222a25.448,25.448,0,0,0,3.246.049,24.839,24.839,0,0,0,6.441-1.17,3.415,3.415,0,0,0,1.518-1.02v3.182s-18.057,3.745-20.473-5.456Z" transform="translate(-834.271 -1455.806)" fill="#191919"/>
        <g id="组_12" data-name="组 12" transform="translate(40.689 71.193)">
          <path id="路径_155" data-name="路径 155" d="M1012.257,1663.945l.083-5.261.637-.17s.124,0,.342-.028c.039,2.783.077,5.26.111,7.193-.73.269-1.52.555-2.365.871v.829s-5.84,3.354-8.54-.737v-3.966c0,.629.005.995.016,1.049C1006.553,1667.882,1012.257,1663.945,1012.257,1663.945Z" transform="translate(-1002.526 -1658.486)" fill="#191919"/>
          <path id="路径_156" data-name="路径 156" d="M1002.531,1681.436c0,.543,0,1-.005,1.373v-1.372Z" transform="translate(-1002.526 -1678.62)" fill="#191919"/>
        </g>
        <path id="路径_157" data-name="路径 157" d="M1091.544,1703.768c4.122,2.355,10.2-.468,10.2-.468v-4.917l.152-.041a9.716,9.716,0,0,0,2.317-.444,2.833,2.833,0,0,1,3.6,2c.009.035.075,1.064.083,1.1.531,2.581-2.641,3.415-7.915,5.385v.829s-5.84,3.354-8.539-.737v-5.337l.024-.007C1091.5,1702.689,1091.524,1703.647,1091.544,1703.768Z" transform="translate(-1039.843 -1621.755)" fill="#191919"/>
        <g id="组_13" data-name="组 13" transform="translate(51.599 80.08)">
          <path id="路径_158" data-name="路径 158" d="M1161.04,1735.454c4.654-1.739,7.671-2.593,7.95-4.543.314,2.414-2.826,3.256-7.95,5.171Z" transform="translate(-1152.501 -1730.911)" fill="#606060"/>
          <path id="路径_159" data-name="路径 159" d="M1099.981,1769.422v.627s-5.84,3.354-8.539-.738v-.627C1094.141,1772.775,1099.981,1769.422,1099.981,1769.422Z" transform="translate(-1091.442 -1764.049)" fill="#606060"/>
        </g>
        <path id="路径_160" data-name="路径 160" d="M1011.107,1725.543v.627s-5.84,3.354-8.54-.737v-.627C1005.267,1728.9,1011.107,1725.543,1011.107,1725.543Z" transform="translate(-961.874 -1645.475)" fill="#606060"/>
        <path id="路径_161" data-name="路径 161" d="M1074.536,1717.6c-.732.27-1.524.557-2.37.873v-.627c.842-.315,1.631-.6,2.359-.869Q1074.531,1717.3,1074.536,1717.6Z" transform="translate(-1022.932 -1638.609)" fill="#606060"/>
        <rect id="矩形_1" data-name="矩形 1" width="0.162" height="23.772" transform="translate(57.197 57.599)" fill="#fff"/>
        <path id="路径_162" data-name="路径 162" d="M1044.934,1581.717l.854-17.621-.161,0-.854,17.621Z" transform="translate(-998.9 -1504.481)" fill="#fff"/>
        <path id="路径_163" data-name="路径 163" d="M897.736,1091.938c-.4.07-.358-4.6-2.43,1.185s-5.262,2.366-4.933,1.644-1.581-3.154-1.578-3.438-4.3-4.106.06-9.462c1.9-2.336,9.6-2.1,10.74-3.454s5.709,6.1-.925,8.617C898.67,1087.03,899.027,1091.709,897.736,1091.938Z" transform="translate(-860.401 -1078.249)" fill="#120621"/>
        <path id="路径_164" data-name="路径 164" d="M1174.414,1351.344l1.558-.744.757-1.372a.385.385,0,0,1,.672-.007c.149.269-.008.6-.069.925a6.284,6.284,0,0,0,2.26-.784.664.664,0,0,1,.8.153,1.4,1.4,0,0,1-.45,2.421,7.73,7.73,0,0,1-2.392.886l-1.063.441Z" transform="translate(-1112.635 -1315.804)" fill="#fbaa78"/>
      </g>
      <g id="Chair" transform="translate(-17944.127 12428.516)">
        <path id="路径_165" data-name="路径 165" d="M717.7,1397.8c.079-.341,3.885-13.745,3.885-13.745s3.4-8.014,12.773-4.906a18.163,18.163,0,0,1,11.608,11.705s5.271,13.356,13.927,7.917,6.957,6.7,6.957,6.7l-13.988,8.16s-8.159,3.983-16.513.583a98.149,98.149,0,0,1-16.076-8.694A7.916,7.916,0,0,1,717.7,1397.8Z" transform="translate(-713.722 -1378.437)" fill="#d2deef"/>
        <g id="组_14" data-name="组 14" transform="translate(44.298 32.86)">
          <path id="路径_166" data-name="路径 166" d="M1050.331,1646.248l-2.519,1.47,4.911,15.889a1.421,1.421,0,0,0,1.777.938h0a1.421,1.421,0,0,0,.938-1.777Z" transform="translate(-1047.812 -1646.248)" fill="#585971"/>
        </g>
        <path id="路径_167" data-name="路径 167" d="M859.931,1692.885l-3.767,25.926a1.42,1.42,0,0,0,1.2,1.61h0a1.421,1.421,0,0,0,1.61-1.2l3.745-25.776A18.874,18.874,0,0,1,859.931,1692.885Z" transform="translate(-835.368 -1654.303)" fill="#585971"/>
        <path id="路径_168" data-name="路径 168" d="M694.368,1622.231l-7.51,21.322a1.421,1.421,0,0,0,.868,1.812h0a1.421,1.421,0,0,0,1.812-.868l7.3-20.718C695.809,1623.155,694.969,1622.62,694.368,1622.231Z" transform="translate(-686.777 -1592.318)" fill="#585971"/>
        <path id="路径_169" data-name="路径 169" d="M736.356,1573.124a98.154,98.154,0,0,1-16.076-8.694,8.183,8.183,0,0,1-2.708-4.865,7.964,7.964,0,0,0,2.708,7,98.17,98.17,0,0,0,16.076,8.694c8.354,3.4,16.513-.583,16.513-.583l13.746-8.018s.051-.354.241-2.278l-13.988,8.16S744.71,1576.523,736.356,1573.124Z" transform="translate(-713.727 -1537.341)" fill="#585971"/>
      </g>
      <g id="Woman" transform="translate(-17818.49 12268.233)">
        <path id="路径_170" data-name="路径 170" d="M1710.86,79.8l-.005,62.776a3.638,3.638,0,0,0,1.819,3.151l73.175,42.241A1.1,1.1,0,0,0,1787.5,187l-.66-64.066a3.272,3.272,0,0,0-1.618-2.789L1713.644,78.2A1.849,1.849,0,0,0,1710.86,79.8Z" transform="translate(-1710.84 -77.231)" fill="#9ba2bd"/>
        <path id="路径_171" data-name="路径 171" d="M2022.232,640.564c.287-1.8.552-3.408.707-4.682.917-7.509,10.526-21.39,10.526-21.39s-7.547,2.575-10.618-2.563c-9.679,11.026-10.633,17.9-11.2,20.395a12.043,12.043,0,0,0-.111,2.064Z" transform="translate(-1974.625 -545.694)" fill="#585971"/>
        <path id="路径_172" data-name="路径 172" d="M2100.847,662.789c-.044-2.843.122-5.154.983-6.074l.326,6.83,9.084,5.244-.434-22.9s-.238-11.85-11.676-16.382c0,0-9.246,14.466-10.774,21.874-.243,1.174-.461,2.459-.651,3.823Z" transform="translate(-2041.453 -561.115)" fill="#fff"/>
        <path id="路径_173" data-name="路径 173" d="M1981.969,629.476a27.112,27.112,0,0,1,.211-3.074c.571-2.492.4-8.943,10.078-19.969-11.728-4.5-14.8,11.039-15.633,19.959Z" transform="translate(-1944.001 -540.17)" fill="#fff"/>
        <path id="路径_174" data-name="路径 174" d="M2031.235,604.563a26,26,0,0,0-2.514,4.186,12.72,12.72,0,0,0-1.522,4.76c-.019.446.674.445.693,0a7.957,7.957,0,0,1,.588-2.44q.41-1.092.922-2.141a26,26,0,0,1,2.432-4.015c.265-.36-.336-.705-.6-.35Z" transform="translate(-1988.369 -539.101)" fill="#444"/>
        <path id="路径_175" data-name="路径 175" d="M2025.768,612.154l-6.227.936v3.063l-3.852,2.662a42.721,42.721,0,0,0-.2,16.209S2014.27,625.14,2025.768,612.154Z" transform="translate(-1977.511 -545.892)" fill="#dddfe6"/>
        <path id="路径_176" data-name="路径 176" d="M2052.4,454.382c-.162-.235-.115-7.918-.115-7.918l-1.737-1.359c-.01-.061-.044-.1-.1-.082a3.467,3.467,0,0,0-1.875,1.21,6.041,6.041,0,0,0-.738.849,8.857,8.857,0,0,0-1.456,3.05,13.577,13.577,0,0,0-.374,3.612,20.861,20.861,0,0,0,.493,3.864c.281,1.313.649,2.6,1.026,3.892l.028.095c-.009.264-.026.527-.055.787-.195,1.776-.96,3.292,1.843,3.944a7.863,7.863,0,0,0,2.376.079c.741-.114,1.393-.913,1.742-1.188C2054.925,464.059,2052.565,454.617,2052.4,454.382Zm-5.549,3.739a22.56,22.56,0,0,1-.591-3.82,14.051,14.051,0,0,1,.222-3.566,7.986,7.986,0,0,1,.869-2.277,15.306,15.306,0,0,0-.546,5.819c.136,2.1.672,4.32.741,6.465C2047.3,459.872,2047.059,459,2046.854,458.12Z" transform="translate(-2004.862 -399.262)" fill="#120621"/>
        <g id="组_15" data-name="组 15" transform="translate(44.207 44.723)">
          <path id="路径_177" data-name="路径 177" d="M2085.021,460.867c.083-.063.128-.1.128-.1-1.693-2.034-1.012-9.648-1.012-9.648,5.111-2.53.306-6.182.306-6.182-4.476-13.636-11.822-6.435-11.822-6.435-4.831,17.5,2.851,17.105,2.851,17.105a2.278,2.278,0,0,1-.4,2.534S2077.187,462.8,2085.021,460.867Z" transform="translate(-2071.025 -436.602)" fill="#fcb697"/>
        </g>
        <path id="路径_178" data-name="路径 178" d="M2100.725,629.506l1.992,4.424s-4.151,2.805-4.151,3.129-.54,5.4-.54,5.4l-8.5,11.263S2089.523,647.148,2100.725,629.506Z" transform="translate(-2043.047 -561.115)" fill="#dddfe6"/>
        <path id="路径_179" data-name="路径 179" d="M2103.632,429.3a12.479,12.479,0,0,1-1.906-3.7,40.363,40.363,0,0,1-1-4.37q-.095-.473-.189-.945a13.487,13.487,0,0,0-.315-1.572l-.005-.027a.142.142,0,0,0-.01-.03c-2.856-10.553-17.5-9.523-18.293-3.775a6.013,6.013,0,0,0,.089,2.043,9.965,9.965,0,0,0,1.8,4.179,11.6,11.6,0,0,0,3.973,3.429,6.536,6.536,0,0,0,3.253.842c.152-.006.153-.244,0-.238a6.065,6.065,0,0,1-2.849-.667,11.291,11.291,0,0,1-5.036-4.885c1.635,2.431,5.12,5.223,9.619,4.52,0,0,2.811-.024,1.188,2.693-.156.161-.791,1.1-1.5,1.03a7.4,7.4,0,0,1-.74,6.633c-1.495,2.426,1.238,5.783,5.214,5.308a6.879,6.879,0,0,0,5.907-8.042c-.231-1.081-.829-2.4-1.2-4.244a36.686,36.686,0,0,1-.933-5.2q.1.5.2.99a17.241,17.241,0,0,0,1.306,4.155,12.519,12.519,0,0,0,1.223,1.992C2103.518,429.539,2103.724,429.421,2103.632,429.3Z" transform="translate(-2036.33 -369.081)" fill="#120621"/>
        <path id="路径_180" data-name="路径 180" d="M1710.755,79.8l-.02,4.394,76.053,43.9-.054-5.159a3.272,3.272,0,0,0-1.618-2.789L1713.539,78.2A1.849,1.849,0,0,0,1710.755,79.8Z" transform="translate(-1710.735 -77.231)" fill="#ebedf6"/>
        <path id="路径_181" data-name="路径 181" d="M2143.439,712.825c-2.4.238-3.951,4.438-3.46,9.379.026.259.058.515.094.768a.528.528,0,0,0,.028.135,2.312,2.312,0,0,0,1.214,1.235,3.523,3.523,0,0,0,1.86.42c.374-.023.306-.6-.067-.578a2.92,2.92,0,0,1-1.491-.34,1.769,1.769,0,0,1-.988-.939c0-.009-.01-.043-.01-.043s-.041-.438-.068-.715c-.447-4.505.882-8.325,2.968-8.532s4.141,3.277,4.588,7.782a13.641,13.641,0,0,1-.687,6.282h0c-.044-.022-.1-.032-.145-.052a1.4,1.4,0,0,1-.306-.195,4.568,4.568,0,0,1-.54-.517,6.533,6.533,0,0,1-.9-1.26c-.206-.381-.842-.131-.633.256a7.315,7.315,0,0,0,1.362,1.8c.342.329,1.227,1.015,1.623.452h0l0-.008a.746.746,0,0,0,.039-.064.324.324,0,0,0,.041-.132,14.725,14.725,0,0,0,.711-6.622C2148.185,716.4,2145.841,712.587,2143.439,712.825Z" transform="translate(-2087.232 -634.202)" fill="#d3d5dd"/>
        <path id="路径_182" data-name="路径 182" d="M2150.1,661.294c-.382-3.851-1.89-7.015-3.674-8.112a.378.378,0,0,0,.02-.09,4.967,4.967,0,0,1,.869-1.974,12.18,12.18,0,0,0,1.046-2.215,4.894,4.894,0,0,0-.4-4.381c-.256-.361-.857-.015-.6.35a4.422,4.422,0,0,1,.2,4.209,13.841,13.841,0,0,1-1.071,2.067,4.822,4.822,0,0,0-.709,1.733,2.26,2.26,0,0,0-.918-.1c-1.643.163-2.888,2.181-3.352,5.022l.541.312c.394-2.577,1.465-4.4,2.89-4.544,2.086-.207,4.141,3.277,4.588,7.782q.057.569.075,1.122l.583.336C2150.178,662.315,2150.149,661.809,2150.1,661.294Z" transform="translate(-2088.655 -574.155)" fill="#444"/>
        <g id="组_16" data-name="组 16" transform="translate(0.749 0)">
          <path id="路径_183" data-name="路径 183" d="M2322.878,975.039h0l.021.009Z" transform="translate(-2248.519 -864.252)" fill="#585971"/>
          <path id="路径_184" data-name="路径 184" d="M1793.5,117.036a3.273,3.273,0,0,0-1.618-2.789L1720.3,72.3c-.868-.509-2.546.035-3.463.9a1.837,1.837,0,0,1,2.054-.124l71.578,41.944a3.272,3.272,0,0,1,1.618,2.789l.66,64.066a1.1,1.1,0,0,1-1.505,1.035,2.137,2.137,0,0,0,2.914-1.812Z" transform="translate(-1716.839 -72.106)" fill="#585971"/>
        </g>
        <ellipse id="椭圆_2" data-name="椭圆 2" cx="1.458" cy="2.166" rx="1.458" ry="2.166" transform="translate(2.604 3.593) rotate(-5.785)" fill="#d35050"/>
        <ellipse id="椭圆_3" data-name="椭圆 3" cx="1.458" cy="2.166" rx="1.458" ry="2.166" transform="translate(6.894 6.21) rotate(-5.785)" fill="#ff9e26"/>
        <path id="路径_185" data-name="路径 185" d="M1806.795,143.769c.12,1.19-.431,2.221-1.233,2.3s-1.549-.818-1.669-2.008.431-2.221,1.233-2.3S1806.674,142.578,1806.795,143.769Z" transform="translate(-1792.448 -133.21)" fill="#6a8da5"/>
        <g id="组_18" data-name="组 18" transform="translate(5.784 46.212)">
          <path id="路径_186" data-name="路径 186" d="M1757.879,448.744v20.363l22.922,13.232-.209-20.286Z" transform="translate(-1757.877 -448.744)" fill="#ebedf6"/>
          <path id="路径_187" data-name="路径 187" d="M1800.1,607.9c.031-4.979-1.118-10.924-6.51-12.147,0,0-2.432,2.019-3.3-.526-.03-.087-2.99-.286-4.647,4.33Z" transform="translate(-1782.237 -577.227)" fill="#d35050"/>
          <g id="组_17" data-name="组 17" transform="translate(6.256 11.184)">
            <path id="路径_188" data-name="路径 188" d="M1849.749,583.57c-.021-.249.327,0,.589-.362a1.313,1.313,0,0,0-.24.735c.008.2-.135.249-.272.25C1849.794,583.975,1849.762,583.733,1849.749,583.57Z" transform="translate(-1844.731 -577.894)" fill="#120621"/>
            <path id="路径_189" data-name="路径 189" d="M1812.741,556.073s-.289,5,1.264,4.577c0,0,.3.183.152.7s2.466.642,3.106-.819c0,0,.031-1.5,0-1.861s.731.335.853-1.37C1818.117,557.3,1816.723,552.675,1812.741,556.073Z" transform="translate(-1812.246 -552.994)" fill="#fbaa78"/>
            <path id="路径_190" data-name="路径 190" d="M1814.072,541.176a1.382,1.382,0,0,1,1,.877,1.15,1.15,0,0,1-.1.992c-.427.745-.244,1.157-.244,1.157s-.351-.5-.688-.095a.663.663,0,0,0-.122.225c-.114.36-.518.765-.8.479-.086-.086.365-1.858-.4-1.888s-3.624,1.188-3.837-.883,1.128-2.622,1.371-1.736Z" transform="translate(-1808.862 -539.897)" fill="#120621"/>
            <path id="路径_191" data-name="路径 191" d="M1820.934,599.633c0,.127,0,.207,0,.207-.639,1.462-3.258,1.337-3.105.819s-.152-.7-.152-.7a.576.576,0,0,1-.521-.09c-.386.685-.553,1.624,1.217,1.84,2.974.312,3.151-1.931,3.151-1.931l-.511-.134Z" transform="translate(-1815.913 -592.303)" fill="#fff"/>
          </g>
        </g>
        <path id="路径_192" data-name="路径 192" d="M1750.992,240.873v14.38l32.754,19.211V259.753Z" transform="translate(-1746.052 -220.166)" fill="#d35050"/>
        <path id="路径_193" data-name="路径 193" d="M1779.707,300.715l-1.422-9.832-1.425,3.494-3.724-2.14-.475-1.511-1.158,5.119-1.625-9.571-1.041,3.471-3.336-1.928-.472-1.705-1.259,5.216-1.561-9.482-1.055,3.458-3.291-1.885-.506-1.752-1.194,4.419-1.58-8.661-1.326,3.266-2.266-1.242v-.608l2,1.1,1.791-4.408,1.485,8.142,1.076-3.982.975,3.376,2.524,1.445,1.526-5,1.5,9.137,1.125-4.661.962,3.479,2.564,1.481,1.5-5.008,1.555,9.161,1-4.417,1.006,3.2,3.015,1.733,1.935-4.745,1.3,9.014,1.129-4.732.817,3.462,1.964,1.125v.614l-2.429-1.392-.354-1.5Z" transform="translate(-1746.052 -250.57)" fill="#fff"/>
      </g>
      <g id="Smartphone" transform="translate(-17796.838 12423.806)">
        <g id="组_19" data-name="组 19" transform="translate(0 0)">
          <path id="路径_194" data-name="路径 194" d="M1922.26,1413.727l-33.544-19.371a2.987,2.987,0,0,1,.014-5.181l84.2-48.029a8.311,8.311,0,0,1,8.28.025l32.986,19.083a2.987,2.987,0,0,1,0,5.172l-83.625,48.3A8.311,8.311,0,0,1,1922.26,1413.727Z" transform="translate(-1887.224 -1340.054)" fill="#333"/>
          <path id="路径_195" data-name="路径 195" d="M2015.691,1526.092a2.957,2.957,0,0,1-1.492,2.546l-83.625,48.3a8.311,8.311,0,0,1-8.313,0l-33.544-19.371a2.958,2.958,0,0,1-1.492-2.627h0v4.461a3.425,3.425,0,0,0,1.709,2.964l33.328,19.3a8.312,8.312,0,0,0,8.313,0l82.892-47.868a4.453,4.453,0,0,0,2.226-3.857v-3.846Z" transform="translate(-1887.224 -1503.266)" fill="#585971"/>
          <path id="路径_196" data-name="路径 196" d="M2046.264,1386.025l-5.751-3.334-2.6,1.5a3.489,3.489,0,0,1-3.49,0l-14.711-8.451c-.838-.484-.7-1.1.141-1.578l3-1.706-5.1-2.956a6.979,6.979,0,0,0-6.953-.021l-80.212,45.621c-1.682.959-1.958,2.188-.281,3.157l29.422,16.9a6.979,6.979,0,0,0,6.981,0l79.786-45.9C2048.167,1388.292,2047.934,1386.991,2046.264,1386.025Z" transform="translate(-1924.031 -1365.06)" fill="#fff"/>
        </g>
        <path id="路径_197" data-name="路径 197" d="M2500.088,1439.96s7.341-1.216,7.494,2.45a2.762,2.762,0,0,1-.964,2.185c-1.608,1.4-6.261,3.548-19.211,1.648,0,0-.092-8.662,5.882-10.654S2501.618,1437.9,2500.088,1439.96Z" transform="translate(-2413.767 -1423.417)" fill="#d35050"/>
        <path id="路径_198" data-name="路径 198" d="M2518.345,1465.969l-4.746-2.789,1.433-1.89-6.623,2.286,4.112-4.792-3.492,1.757-4.1-2.375s.082-.123.125-.18.123-.169.123-.169l3.863,2.235,5.169-2.563-4.373,5.11,6.431-2.225-2.035,2.682,4.687,2.754-.266.078C2518.554,1465.916,2518.345,1465.969,2518.345,1465.969Z" transform="translate(-2429.135 -1443.08)" fill="#fff"/>
        <path id="路径_199" data-name="路径 199" d="M2660.4,1534.668l11.127,6.347a.6.6,0,0,0,.605-1.036L2661,1533.632a.6.6,0,0,0-.605,1.035Z" transform="translate(-2565.276 -1509.808)" fill="#ffba6e"/>
        <path id="路径_200" data-name="路径 200" d="M2378.942,1520.82c3.709,2.116,25.231,14.446,28.94,16.561a.6.6,0,0,0,.605-1.035c-3.709-2.116-25.232-14.446-28.941-16.561a.6.6,0,0,0-.605,1.035Z" transform="translate(-2318.353 -1497.659)" fill="#9ba2bd"/>
        <path id="路径_201" data-name="路径 201" d="M2330.914,1548.3c3.709,2.115,25.232,14.446,28.941,16.561a.6.6,0,0,0,.6-1.035c-3.709-2.116-25.231-14.446-28.94-16.561a.6.6,0,0,0-.605,1.035Z" transform="translate(-2276.218 -1521.765)" fill="#9ba2bd"/>
        <path id="路径_202" data-name="路径 202" d="M2283.861,1575.606c3.709,2.116,25.231,14.446,28.94,16.561a.6.6,0,0,0,.605-1.035c-3.709-2.116-25.232-14.446-28.94-16.561a.6.6,0,0,0-.605,1.035Z" transform="translate(-2234.939 -1545.723)" fill="#9ba2bd"/>
        <path id="路径_203" data-name="路径 203" d="M2234.71,1603.711c3.709,2.116,25.232,14.446,28.941,16.561a.6.6,0,0,0,.605-1.035c-3.709-2.116-25.231-14.446-28.94-16.561a.6.6,0,0,0-.605,1.035Z" transform="translate(-2191.819 -1570.379)" fill="#9ba2bd"/>
        <path id="路径_204" data-name="路径 204" d="M2184.44,1633c3.709,2.115,25.231,14.445,28.94,16.561a.6.6,0,0,0,.6-1.035c-3.709-2.115-25.231-14.446-28.94-16.561a.6.6,0,0,0-.605,1.035Z" transform="translate(-2147.716 -1596.077)" fill="#9ba2bd"/>
        <path id="路径_205" data-name="路径 205" d="M2629.243,1552.722l11.127,6.347a.6.6,0,0,0,.605-1.035l-11.127-6.347a.6.6,0,0,0-.605,1.035Z" transform="translate(-2537.943 -1525.646)" fill="#ffba6e"/>
        <path id="路径_206" data-name="路径 206" d="M2164.134,1799.329l11.127,6.347a.6.6,0,0,0,.6-1.035l-11.127-6.347a.6.6,0,0,0-.605,1.035Z" transform="translate(-2129.901 -1741.996)" fill="#9ba2bd"/>
      </g>
      <g id="Smartphone-2" data-name="Smartphone" transform="translate(-17885.307 12427.214)">
        <g id="组_20" data-name="组 20" transform="translate(0 0)">
          <path id="路径_207" data-name="路径 207" d="M1170.2,1376.284l-3.851-2.224a.343.343,0,0,1,0-.595l9.667-5.514a.955.955,0,0,1,.951,0l3.787,2.191a.343.343,0,0,1,0,.594l-9.6,5.545A.954.954,0,0,1,1170.2,1376.284Z" transform="translate(-1166.179 -1367.826)" fill="#333"/>
          <path id="路径_208" data-name="路径 208" d="M1180.929,1389.184a.339.339,0,0,1-.171.292l-9.6,5.545a.954.954,0,0,1-.955,0l-3.851-2.224a.34.34,0,0,1-.171-.3h0v.512a.393.393,0,0,0,.2.34l3.826,2.216a.955.955,0,0,0,.955,0l9.517-5.5a.512.512,0,0,0,.256-.443v-.441Z" transform="translate(-1166.18 -1386.563)" fill="#585971"/>
          <path id="路径_209" data-name="路径 209" d="M1184.439,1373.1l-.66-.382-.3.171a.4.4,0,0,1-.4,0l-1.689-.97c-.1-.056-.08-.126.016-.181l.344-.2-.585-.34a.8.8,0,0,0-.8,0l-9.209,5.238c-.193.11-.225.251-.032.362l3.378,1.941a.8.8,0,0,0,.8,0l9.16-5.269C1184.657,1373.364,1184.63,1373.214,1184.439,1373.1Z" transform="translate(-1170.405 -1370.696)" fill="#fff"/>
        </g>
        <path id="路径_210" data-name="路径 210" d="M1236.542,1379.3s.843-.14.86.281a.317.317,0,0,1-.111.251c-.185.161-.719.407-2.206.189,0,0-.01-.994.675-1.223S1236.717,1379.059,1236.542,1379.3Z" transform="translate(-1226.631 -1377.397)" fill="#d35050"/>
        <path id="路径_211" data-name="路径 211" d="M1238.638,1382.282l-.545-.32.165-.217-.76.262.472-.55-.4.2-.471-.273.014-.021.014-.019.444.256.593-.294-.5.587.738-.256-.234.308.538.316-.031.009Z" transform="translate(-1228.396 -1379.653)" fill="#fff"/>
        <path id="路径_212" data-name="路径 212" d="M1254.947,1390.169l1.277.729a.069.069,0,0,0,.07-.119l-1.278-.729a.069.069,0,0,0-.069.119Z" transform="translate(-1244.026 -1387.315)" fill="#ffba6e"/>
        <path id="路径_213" data-name="路径 213" d="M1222.633,1388.58c.426.243,2.9,1.658,3.323,1.9a.069.069,0,0,0,.069-.119c-.426-.243-2.9-1.658-3.323-1.9a.069.069,0,0,0-.069.119Z" transform="translate(-1215.677 -1385.92)" fill="#9ba2bd"/>
        <path id="路径_214" data-name="路径 214" d="M1217.119,1391.734l3.323,1.9a.069.069,0,0,0,.069-.119l-3.323-1.9a.069.069,0,0,0-.07.119Z" transform="translate(-1210.839 -1388.688)" fill="#9ba2bd"/>
        <path id="路径_215" data-name="路径 215" d="M1211.717,1394.869l3.323,1.9a.069.069,0,0,0,.069-.119l-3.323-1.9a.069.069,0,0,0-.069.119Z" transform="translate(-1206.1 -1391.438)" fill="#9ba2bd"/>
        <path id="路径_216" data-name="路径 216" d="M1206.074,1398.1l3.323,1.9a.069.069,0,0,0,.069-.119l-3.323-1.9a.069.069,0,0,0-.069.119Z" transform="translate(-1201.15 -1394.269)" fill="#9ba2bd"/>
        <path id="路径_217" data-name="路径 217" d="M1200.3,1401.458l3.323,1.9a.069.069,0,0,0,.07-.119l-3.323-1.9a.069.069,0,0,0-.069.119Z" transform="translate(-1196.087 -1397.219)" fill="#9ba2bd"/>
        <path id="路径_218" data-name="路径 218" d="M1251.37,1392.241l1.277.729a.069.069,0,0,0,.07-.119l-1.278-.729a.069.069,0,0,0-.069.119Z" transform="translate(-1240.888 -1389.133)" fill="#ffba6e"/>
        <path id="路径_219" data-name="路径 219" d="M1197.971,1420.554l1.278.729a.069.069,0,0,0,.069-.119l-1.277-.729a.069.069,0,0,0-.07.119Z" transform="translate(-1194.041 -1413.972)" fill="#9ba2bd"/>
      </g>
      <g id="Medicine_Storage_Box" data-name="Medicine Storage Box" transform="translate(-17923.301 12311.479)">
        <g id="组_22" data-name="组 22" transform="translate(38.84 30.486)">
          <path id="路径_220" data-name="路径 220" d="M1251.166,700.887l-23.976,13.848a4.954,4.954,0,0,0-2.477,4.29v20.769a.944.944,0,0,0,1.417.817l26.848-15.523a1.811,1.811,0,0,0,.9-1.567V702.455A1.811,1.811,0,0,0,1251.166,700.887Z" transform="translate(-1218.378 -697.255)" fill="#d35050"/>
          <path id="路径_221" data-name="路径 221" d="M1179.409,715.577V694.809a4.955,4.955,0,0,1,2.476-4.29l23.976-13.848a1.786,1.786,0,0,1,1.558-.121v0l-5.492-3.08a3.359,3.359,0,0,0-3.324.021l-23.056,13.325a4.955,4.955,0,0,0-2.477,4.291l.026,20.278a1.853,1.853,0,0,0,.923,1.6l5.718,3.319,0,0A.931.931,0,0,1,1179.409,715.577Z" transform="translate(-1173.072 -673.037)" fill="#a82d2d"/>
          <path id="路径_222" data-name="路径 222" d="M1310.009,806.069l-3.638,2.1v-4.221l-3.637,2.12v4.2l-3.642,2.1V816.6l3.642-2.108V818.7l3.637-2.108v-4.2l3.638-2.107Z" transform="translate(-1283.63 -787.886)" fill="#fff"/>
          <path id="路径_223" data-name="路径 223" d="M1287.881,773.186c-3.36-1.94-8.837,1.255-12.233,7.138s-3.425,12.223-.065,14.163,8.837-1.256,12.233-7.138S1291.241,775.125,1287.881,773.186Zm-.511,13.838c-3.212,5.563-8.327,8.623-11.425,6.834s-3.005-7.748.207-13.312,8.327-8.623,11.425-6.835S1290.583,781.461,1287.371,787.024Z" transform="translate(-1260.811 -760.4)" fill="#fff"/>
          <path id="路径_224" data-name="路径 224" d="M1223.048,771.291l1.651-.957,3.3,1.914-1.652.956Z" transform="translate(-1216.916 -758.396)" fill="#fff"/>
          <path id="路径_225" data-name="路径 225" d="M1226.348,780.049l-3.3-1.912v.516l3.3,1.912,1.652-.956v-.516Z" transform="translate(-1216.916 -765.242)" fill="#d8d8d8"/>
          <path id="路径_226" data-name="路径 226" d="M1369.308,689.079l1.651-.957,3.3,1.914-1.652.956Z" transform="translate(-1345.23 -686.271)" fill="#fff"/>
          <path id="路径_227" data-name="路径 227" d="M1372.608,697.837l-3.3-1.912v.516l3.3,1.912,1.652-.956v-.516Z" transform="translate(-1345.23 -693.117)" fill="#d8d8d8"/>
          <path id="路径_228" data-name="路径 228" d="M1283.937,701.783v3.608l2.139-1.245,0-1.938a.919.919,0,0,1,.458-.8l3.354-1.941a.325.325,0,0,1,.488.281v1.911l2.139-1.234v-2.447a1.444,1.444,0,0,0-2.166-1.25l-5.243,3.03A2.334,2.334,0,0,0,1283.937,701.783Z" transform="translate(-1270.334 -693.654)" fill="#fff"/>
          <g id="组_21" data-name="组 21" transform="translate(12.603 2.558)">
            <path id="路径_229" data-name="路径 229" d="M1347.525,697.906h0Z" transform="translate(-1338.72 -697.411)" fill="#d8d8d8"/>
            <path id="路径_230" data-name="路径 230" d="M1328.754,720.139l-.512.3v1.314l1,.582v-1.911A.325.325,0,0,0,1328.754,720.139Z" transform="translate(-1321.806 -716.879)" fill="#d8d8d8"/>
            <path id="路径_231" data-name="路径 231" d="M1277.953,697.437l5.243-3.03a1.424,1.424,0,0,1,.912-.182L1283.4,694a2.3,2.3,0,0,0-1.853.2l-4.591,2.659a2.334,2.334,0,0,0-1.166,2.021v3.608l1,.582v-3.608A2.334,2.334,0,0,1,1277.953,697.437Z" transform="translate(-1275.787 -693.887)" fill="#d8d8d8"/>
          </g>
        </g>
        <g id="组_41" data-name="组 41" transform="translate(77.764 27.395)">
          <path id="路径_232" data-name="路径 232" d="M1490.307,736.667l10.441,6.018L1518.52,732.3l-10.421-6.073Z" transform="translate(-1490.307 -716.613)" fill="#bababa"/>
          <path id="路径_233" data-name="路径 233" d="M1570.1,732.886v3.877l3.3-1.938Z" transform="translate(-1560.309 -722.452)" fill="#6072ad"/>
          <g id="组_25" data-name="组 25" transform="translate(8.66 20.828)">
            <g id="组_24" data-name="组 24">
              <ellipse id="椭圆_4" data-name="椭圆 4" cx="1.872" cy="1.078" rx="1.872" ry="1.078" fill="#fff"/>
              <g id="组_23" data-name="组 23" transform="translate(0 1.063)">
                <path id="路径_234" data-name="路径 234" d="M1560.888,826.319h0v0Z" transform="translate(-1560.888 -826.312)" fill="#dddfe6"/>
                <path id="路径_235" data-name="路径 235" d="M1591.4,826.268v-.007h0Z" transform="translate(-1587.657 -826.261)" fill="#dddfe6"/>
                <path id="路径_236" data-name="路径 236" d="M1564.632,826.408c-.007.592-.843,1.07-1.872,1.07s-1.87-.481-1.872-1.076v.586c0,.6.838,1.078,1.872,1.078s1.872-.483,1.872-1.078C1564.632,826.8,1564.632,826.484,1564.632,826.408Z" transform="translate(-1560.888 -826.385)" fill="#d8d8d8"/>
              </g>
            </g>
            <path id="路径_237" data-name="路径 237" d="M1564.734,819.509a1.809,1.809,0,0,0-.268.15l2.649,1.532.265-.155Z" transform="translate(-1564.027 -819.275)" fill="#d8d8d8"/>
          </g>
          <g id="组_28" data-name="组 28" transform="translate(12.404 18.612)">
            <g id="组_27" data-name="组 27">
              <ellipse id="椭圆_5" data-name="椭圆 5" cx="1.872" cy="1.078" rx="1.872" ry="1.078" fill="#fff"/>
              <g id="组_26" data-name="组 26" transform="translate(0 1.063)">
                <path id="路径_238" data-name="路径 238" d="M1591.4,808.262h0v0Z" transform="translate(-1591.404 -808.255)" fill="#dddfe6"/>
                <path id="路径_239" data-name="路径 239" d="M1621.917,808.211V808.2h0Z" transform="translate(-1618.173 -808.204)" fill="#dddfe6"/>
                <path id="路径_240" data-name="路径 240" d="M1595.148,808.352c-.007.592-.842,1.07-1.872,1.07s-1.87-.481-1.872-1.075v.586c0,.6.838,1.078,1.872,1.078s1.872-.483,1.872-1.078C1595.148,808.745,1595.148,808.427,1595.148,808.352Z" transform="translate(-1591.404 -808.329)" fill="#d8d8d8"/>
              </g>
            </g>
            <path id="路径_241" data-name="路径 241" d="M1595.25,801.452a1.8,1.8,0,0,0-.268.15l2.649,1.532.265-.155Z" transform="translate(-1594.543 -801.218)" fill="#d8d8d8"/>
          </g>
          <g id="组_31" data-name="组 31" transform="translate(16.148 16.487)">
            <g id="组_30" data-name="组 30">
              <ellipse id="椭圆_6" data-name="椭圆 6" cx="1.872" cy="1.078" rx="1.872" ry="1.078" transform="translate(0)" fill="#fff"/>
              <g id="组_29" data-name="组 29" transform="translate(0 1.063)">
                <path id="路径_242" data-name="路径 242" d="M1621.92,790.941h0v0Z" transform="translate(-1621.92 -790.934)" fill="#dddfe6"/>
                <path id="路径_243" data-name="路径 243" d="M1652.432,790.889v-.007h0Z" transform="translate(-1648.688 -790.882)" fill="#dddfe6"/>
                <path id="路径_244" data-name="路径 244" d="M1625.663,791.03c-.007.592-.843,1.07-1.872,1.07s-1.87-.481-1.872-1.075c0,.056,0,.407,0,.586,0,.6.838,1.078,1.872,1.078s1.872-.483,1.872-1.078C1625.663,791.424,1625.663,791.106,1625.663,791.03Z" transform="translate(-1621.919 -791.007)" fill="#d8d8d8"/>
              </g>
            </g>
            <path id="路径_245" data-name="路径 245" d="M1625.766,784.131a1.818,1.818,0,0,0-.268.15l2.649,1.533.265-.155Z" transform="translate(-1625.059 -783.897)" fill="#d8d8d8"/>
          </g>
          <g id="组_34" data-name="组 34" transform="translate(4.255 18.348)">
            <g id="组_33" data-name="组 33">
              <ellipse id="椭圆_7" data-name="椭圆 7" cx="1.872" cy="1.078" rx="1.872" ry="1.078" transform="translate(0)" fill="#fff"/>
              <g id="组_32" data-name="组 32" transform="translate(0 1.063)">
                <path id="路径_246" data-name="路径 246" d="M1524.987,806.11h0v0Z" transform="translate(-1524.987 -806.103)" fill="#dddfe6"/>
                <path id="路径_247" data-name="路径 247" d="M1555.5,806.058v-.007h0Z" transform="translate(-1551.756 -806.051)" fill="#dddfe6"/>
                <path id="路径_248" data-name="路径 248" d="M1528.73,806.2c-.007.592-.843,1.07-1.872,1.07s-1.87-.481-1.872-1.075c0,.056,0,.407,0,.586,0,.6.838,1.078,1.872,1.078s1.872-.483,1.872-1.078C1528.73,806.593,1528.73,806.274,1528.73,806.2Z" transform="translate(-1524.986 -806.176)" fill="#d8d8d8"/>
              </g>
            </g>
            <path id="路径_249" data-name="路径 249" d="M1528.833,799.3a1.8,1.8,0,0,0-.268.15l2.649,1.532.265-.155Z" transform="translate(-1528.126 -799.066)" fill="#d8d8d8"/>
          </g>
          <g id="组_37" data-name="组 37" transform="translate(7.999 16.133)">
            <g id="组_36" data-name="组 36">
              <ellipse id="椭圆_8" data-name="椭圆 8" cx="1.872" cy="1.078" rx="1.872" ry="1.078" transform="translate(0)" fill="#fff"/>
              <g id="组_35" data-name="组 35" transform="translate(0 1.063)">
                <path id="路径_250" data-name="路径 250" d="M1555.5,788.052h0v0Z" transform="translate(-1555.503 -788.045)" fill="#dddfe6"/>
                <path id="路径_251" data-name="路径 251" d="M1586.015,788v-.007h0Z" transform="translate(-1582.271 -787.994)" fill="#dddfe6"/>
                <path id="路径_252" data-name="路径 252" d="M1559.246,788.142c-.007.592-.843,1.07-1.872,1.07s-1.87-.481-1.872-1.076c0,.056,0,.407,0,.586,0,.6.838,1.078,1.872,1.078s1.872-.483,1.872-1.078C1559.246,788.535,1559.246,788.217,1559.246,788.142Z" transform="translate(-1555.502 -788.119)" fill="#d8d8d8"/>
              </g>
            </g>
            <path id="路径_253" data-name="路径 253" d="M1559.349,781.242a1.811,1.811,0,0,0-.268.15l2.649,1.532.265-.155Z" transform="translate(-1558.642 -781.008)" fill="#d8d8d8"/>
          </g>
          <g id="组_40" data-name="组 40" transform="translate(11.743 14.007)">
            <g id="组_39" data-name="组 39">
              <ellipse id="椭圆_9" data-name="椭圆 9" cx="1.872" cy="1.078" rx="1.872" ry="1.078" fill="#fff"/>
              <g id="组_38" data-name="组 38" transform="translate(0 1.063)">
                <path id="路径_254" data-name="路径 254" d="M1586.018,770.731h0v0Z" transform="translate(-1586.018 -770.724)" fill="#dddfe6"/>
                <path id="路径_255" data-name="路径 255" d="M1616.531,770.679v-.007h0Z" transform="translate(-1612.787 -770.672)" fill="#dddfe6"/>
                <path id="路径_256" data-name="路径 256" d="M1589.762,770.82c-.007.592-.843,1.07-1.872,1.07s-1.87-.481-1.872-1.075v.586c0,.6.838,1.078,1.872,1.078s1.872-.483,1.872-1.078C1589.762,771.214,1589.762,770.9,1589.762,770.82Z" transform="translate(-1586.018 -770.797)" fill="#d8d8d8"/>
              </g>
            </g>
            <path id="路径_257" data-name="路径 257" d="M1589.864,763.921a1.822,1.822,0,0,0-.268.15l2.649,1.532.265-.155Z" transform="translate(-1589.157 -763.687)" fill="#d8d8d8"/>
          </g>
          <path id="路径_258" data-name="路径 258" d="M1570.1,658.285l10.509,6.013,17.862-10.329-10.578-6.12Z" transform="translate(-1560.309 -647.849)" fill="#7a88d2"/>
          <path id="路径_259" data-name="路径 259" d="M1620.328,687.671l17.881-10.346,2.836,1.641-17.806,10.371Z" transform="translate(-1604.375 -673.708)" fill="#fff"/>
          <path id="路径_260" data-name="路径 260" d="M1655.751,708.076v3.875l17.862-10.463v-3.741Z" transform="translate(-1635.452 -691.625)" fill="#6072ad"/>
        </g>
        <g id="组_42" data-name="组 42" transform="translate(35.913 0.176)">
          <path id="路径_261" data-name="路径 261" d="M1199.386,438.134c1.439,1.532,8.359,8.422,17.54,9.585a.375.375,0,0,1,.329.372v10.827a.314.314,0,0,1-.38.307c-2.069-.444-12.073-2.922-17.547-10.346a1.12,1.12,0,0,1-.217-.665v-9.971A.16.16,0,0,1,1199.386,438.134Z" transform="translate(-1192.988 -436.601)" fill="#7cd1a4"/>
          <path id="路径_262" data-name="路径 262" d="M1217.255,464.628v-2.852a.756.756,0,0,1-.189-.017c-2.949-.636-12.334-3.141-17.956-10.172v2.694C1201.3,456.628,1208.236,463.32,1217.255,464.628Z" transform="translate(-1192.988 -448.449)" fill="#60a873"/>
          <path id="路径_263" data-name="路径 263" d="M1217.255,504.615v-2.987a.756.756,0,0,1-.189-.017c-2.949-.636-12.334-3.141-17.956-10.172v2.829C1201.3,496.614,1208.236,503.306,1217.255,504.615Z" transform="translate(-1192.988 -483.411)" fill="#60a873"/>
          <path id="路径_264" data-name="路径 264" d="M1178.237,442.251l-4.029-2.33v-2.751a.64.64,0,0,0-.554-.634c-9.14-1.252-16.172-8.065-18.345-10.4a.4.4,0,0,0-.7.275v2.2l-2.053-1.191a2.225,2.225,0,0,0-3.342,1.925,4.516,4.516,0,0,0,2.266,3.915l3.13,1.8v2.657a1.779,1.779,0,0,0,.359,1.071c5.571,7.394,15.31,10,18.324,10.646a.756.756,0,0,0,.916-.74v-2.3l1.171.679a2.818,2.818,0,0,0,4.231-2.438A2.751,2.751,0,0,0,1178.237,442.251Zm-26.716-9.243a4.177,4.177,0,0,1-2.082-3.627,2,2,0,0,1,3.077-1.74l2.094,1.215v5.927Zm21.961,15.315a.315.315,0,0,1-.38.307c-2.069-.444-12.073-2.922-17.547-10.346a1.12,1.12,0,0,1-.217-.665v-9.971a.16.16,0,0,1,.276-.11c1.438,1.532,8.359,8.422,17.54,9.585a.375.375,0,0,1,.329.372Zm1.994-1.449-1.267-.734v-5.981l3.933,2.275a2.566,2.566,0,0,1,1.282,2.221A2.606,2.606,0,0,1,1175.476,446.875Z" transform="translate(-1149.214 -426.007)" fill="#c7cbd8"/>
        </g>
        <g id="组_43" data-name="组 43" transform="translate(27.656 19.863)">
          <path id="路径_265" data-name="路径 265" d="M1081.925,594.467a.651.651,0,0,0,.326.564l14.755,8.514a1.486,1.486,0,0,0,1.486,0l3.914-2.246a.678.678,0,0,0,.341-.588v-.55l-15.122-8.351a1.486,1.486,0,0,0-1.487,0l-4.213,2.111Z" transform="translate(-1081.925 -590.977)" fill="#c17249"/>
          <path id="路径_266" data-name="路径 266" d="M1082.193,589.86l14.814,8.539a1.486,1.486,0,0,0,1.486,0l4.031-2.332a.448.448,0,0,0,0-.775l-14.9-8.628a1.486,1.486,0,0,0-1.487,0l-3.946,2.274A.534.534,0,0,0,1082.193,589.86Z" transform="translate(-1081.925 -586.463)" fill="#dd8c5f"/>
          <path id="路径_267" data-name="路径 267" d="M1125.7,613.551l7.193,4.147a.721.721,0,0,0,.721,0l1.958-1.132a.218.218,0,0,0,0-.377l-7.234-4.19a.722.722,0,0,0-.722,0l-1.916,1.1A.259.259,0,0,0,1125.7,613.551Z" transform="translate(-1120.218 -608.78)" fill="#fff"/>
        </g>
        <g id="组_47" data-name="组 47" transform="translate(0 39.77)">
          <path id="路径_268" data-name="路径 268" d="M877.077,792.816l-14.814,8.54a1.486,1.486,0,0,1-1.486,0l-4.031-2.332a.448.448,0,0,1,0-.775l14.9-8.628a1.486,1.486,0,0,1,1.487,0l3.946,2.274A.534.534,0,0,1,877.077,792.816Z" transform="translate(-856.522 -784.424)" fill="#ebedf6"/>
          <path id="路径_269" data-name="路径 269" d="M1004.461,748.776h0a.534.534,0,0,0-.48-.022c-.487.218-1.532.685-1.588.7-.073.024-10.454,4.4-10.511,4.443l3.946,2.274,8.69-6.507A.534.534,0,0,0,1004.461,748.776Z" transform="translate(-975.273 -748.707)" fill="#ebedf6"/>
          <path id="路径_270" data-name="路径 270" d="M903.278,811.584l-8.777,5.06a.88.88,0,0,1-.88,0l-2.388-1.381a.265.265,0,0,1,0-.459l8.827-5.112a.88.88,0,0,1,.881,0l2.338,1.347A.316.316,0,0,1,903.278,811.584Z" transform="translate(-886.857 -802.103)" fill="#ea604a"/>
          <path id="路径_271" data-name="路径 271" d="M932.374,819.947l-4.262,2.457a.428.428,0,0,1-.428,0l-1.16-.671a.129.129,0,0,1,0-.223l4.286-2.482a.428.428,0,0,1,.428,0l1.135.654A.153.153,0,0,1,932.374,819.947Z" transform="translate(-917.879 -810.349)" fill="#5daece"/>
          <path id="路径_272" data-name="路径 272" d="M908.243,848.4l-.808.45a.427.427,0,0,1-.428,0l-1.16-.671a.129.129,0,0,1,0-.223l.833-.475a.428.428,0,0,1,.428,0l1.135.654A.153.153,0,0,1,908.243,848.4Z" transform="translate(-899.739 -835.312)" fill="#fff"/>
          <path id="路径_273" data-name="路径 273" d="M877.342,813.389h0a.528.528,0,0,1-.267.456l-14.814,8.54a1.486,1.486,0,0,1-1.486,0l-4.031-2.332a.444.444,0,0,1-.222-.423h0v.55a.678.678,0,0,0,.341.588l3.914,2.247a1.486,1.486,0,0,0,1.486,0l14.756-8.514a.65.65,0,0,0,.325-.564Z" transform="translate(-856.52 -805.453)" fill="#aaacb2"/>
          <g id="组_44" data-name="组 44" transform="translate(20.555 0.433)">
            <path id="路径_274" data-name="路径 274" d="M1096.535,752.234h0Z" transform="translate(-1087.64 -752.234)" fill="#aaacb2"/>
            <path id="路径_275" data-name="路径 275" d="M1032.948,753.323h0a.528.528,0,0,1-.213.395l-8.69,6.507a.528.528,0,0,1,.266.449l8.08-6.133A1.414,1.414,0,0,0,1032.948,753.323Z" transform="translate(-1024.045 -753.189)" fill="#aaacb2"/>
          </g>
          <g id="组_45" data-name="组 45" transform="translate(27.024)">
            <path id="路径_276" data-name="路径 276" d="M1077.93,750.463l1.063-.8a.534.534,0,0,0-.538-.915c-.487.218-1.532.685-1.588.7l-.095.039S1077.9,749.579,1077.93,750.463Z" transform="translate(-1076.772 -748.707)" fill="#94969b"/>
          </g>
          <g id="组_46" data-name="组 46" transform="translate(28.02 0.566)">
            <path id="路径_277" data-name="路径 277" d="M1084.89,755.21l.881-.669a1.414,1.414,0,0,0,.557-1.218.529.529,0,0,1-.213.395l-1.063.8A1.62,1.62,0,0,1,1084.89,755.21Z" transform="translate(-1084.89 -753.323)" fill="#848589"/>
          </g>
        </g>
        <g id="组_49" data-name="组 49" transform="translate(74.398 0)">
          <ellipse id="椭圆_10" data-name="椭圆 10" cx="7.433" cy="4.279" rx="7.433" ry="4.279" fill="#ebedf6"/>
          <path id="路径_278" data-name="路径 278" d="M1477.745,459.09c-.029,2.349-3.345,4.248-7.432,4.248s-7.424-1.911-7.433-4.27c0,.223,0,2.974,0,3.683,0,2.363,3.328,4.279,7.433,4.279s7.433-1.916,7.433-4.279C1477.745,462.009,1477.745,459.388,1477.745,459.09Z" transform="translate(-1462.879 -454.836)" fill="#aaacb2"/>
          <path id="路径_279" data-name="路径 279" d="M1470.312,493.388c-4.1,0-7.429-1.914-7.433-4.275v20.942c0,2.363,3.328,4.279,7.433,4.279s7.433-1.916,7.433-4.279c0-.364,0-11.969,0-20.934C1477.734,491.479,1474.41,493.388,1470.312,493.388Z" transform="translate(-1462.879 -481.194)" fill="#ff7e27"/>
          <path id="路径_280" data-name="路径 280" d="M1470.312,544.2c-4.1,0-7.429-1.914-7.433-4.275v9.432c0,2.363,3.328,4.279,7.433,4.279s7.433-1.916,7.433-4.279c0-.364,0-.458,0-9.424C1477.734,542.294,1474.41,544.2,1470.312,544.2Z" transform="translate(-1462.879 -525.775)" fill="#fff"/>
          <g id="组_48" data-name="组 48" transform="translate(4.936 19.953)">
            <path id="路径_281" data-name="路径 281" d="M1506.281,589.947l1.224.37a2.252,2.252,0,0,1-.389.86,1.686,1.686,0,0,1-.658.521,2.462,2.462,0,0,1-1,.176,2.905,2.905,0,0,1-1.2-.214,1.91,1.91,0,0,1-.807-.754,2.554,2.554,0,0,1-.339-1.381,2.347,2.347,0,0,1,.6-1.724,2.716,2.716,0,0,1,3.032-.257,2.03,2.03,0,0,1,.726,1.061l-1.234.275a1.055,1.055,0,0,0-.136-.3.808.808,0,0,0-.286-.247.883.883,0,0,0-1.107.3,1.621,1.621,0,0,0-.191.89,1.69,1.69,0,0,0,.229,1.031.9.9,0,0,0,1.247.054A1.393,1.393,0,0,0,1506.281,589.947Z" transform="translate(-1503.107 -587.199)" fill="#333"/>
          </g>
        </g>
        <path id="路径_282" data-name="路径 282" d="M1111.529,734.375l-7.851-4.511-3.273,1.9-.225-.388,3.5-2.025,8.075,4.64Z" transform="translate(-1070.283 -691.952)" fill="#444"/>
        <path id="路径_283" data-name="路径 283" d="M1480.639,699.988l-7.851-4.511-3.273,1.9-.225-.388,3.5-2.025,8.075,4.64Z" transform="translate(-1394.105 -661.783)" fill="#444"/>
        <rect id="矩形_2" data-name="矩形 2" width="0.448" height="7.442" transform="translate(46.266 31.718)" fill="#444"/>
        <rect id="矩形_3" data-name="矩形 3" width="0.448" height="7.442" transform="translate(59.54 24.143)" fill="#444"/>
        <path id="路径_284" data-name="路径 284" d="M1405.979,558.227h-.449V545.951l5.343-3.084.224.388-5.119,2.954Z" transform="translate(-1338.168 -528.353)" fill="#444"/>
      </g>
      <g id="Paper-2" data-name="Paper" transform="translate(-17737.818 12323.008)">
        <path id="路径_285" data-name="路径 285" d="M2569.39,881.265" transform="translate(-2544.707 -836.759)" fill="#d5d8eb"/>
        <path id="路径_286" data-name="路径 286" d="M2438.737,574.243V540.71a5.686,5.686,0,0,1,5.687-5.686h0a5.66,5.66,0,0,1,2.543.6l-28.3-16.316a5.677,5.677,0,0,0-2.525-.757c-2.814-.153-8.442.6-8.442,9.051v36.665s-.014.052-.041.143a5.547,5.547,0,0,1-2.436,4.066l18.5,10.852a7.523,7.523,0,0,0,6.629,7.468l1.15.048a7.532,7.532,0,0,0,7.278-7.515V575A5.713,5.713,0,0,1,2438.737,574.243Z" transform="translate(-2400.686 -518.532)" fill="#fff"/>
        <g id="组_50" data-name="组 50" transform="translate(40.742 16.491)">
          <path id="路径_287" data-name="路径 287" d="M2708.961,658.827v-.206a5.687,5.687,0,0,0-5.687-5.686h0a5.659,5.659,0,0,0-3,.852C2701.972,654.767,2705.572,656.858,2708.961,658.827Z" transform="translate(-2700.279 -652.934)" fill="#d0d2d6"/>
        </g>
        <g id="组_51" data-name="组 51" transform="translate(0 47.16)">
          <path id="路径_288" data-name="路径 288" d="M2391.269,916.529l-18.505-10.852-4.54-2.782.089,1.174a5.65,5.65,0,0,0,2.709,4.412L2394.35,922.6A7.51,7.51,0,0,1,2391.269,916.529Z" transform="translate(-2368.224 -902.895)" fill="#d0d2d6"/>
        </g>
        <g id="组_52" data-name="组 52" transform="translate(9.99 10.551)">
          <path id="路径_289" data-name="路径 289" d="M2449.646,604.741v.054a.216.216,0,0,0,.108.187l22.715,12.988a.216.216,0,0,0,.325-.187v-.054a.216.216,0,0,0-.108-.187l-22.715-12.988A.216.216,0,0,0,2449.646,604.741Z" transform="translate(-2449.646 -604.524)" fill="#444"/>
        </g>
        <g id="组_53" data-name="组 53" transform="translate(9.99 14.509)">
          <path id="路径_290" data-name="路径 290" d="M2449.646,637v.054a.217.217,0,0,0,.108.187l22.715,12.988a.216.216,0,0,0,.325-.187v-.054a.215.215,0,0,0-.108-.187l-22.715-12.988A.216.216,0,0,0,2449.646,637Z" transform="translate(-2449.646 -636.783)" fill="#444"/>
        </g>
        <g id="组_54" data-name="组 54" transform="translate(9.99 18.333)">
          <path id="路径_291" data-name="路径 291" d="M2449.646,668.166v.054a.217.217,0,0,0,.108.187l22.715,12.988a.216.216,0,0,0,.325-.187v-.054a.215.215,0,0,0-.108-.187l-22.715-12.988A.216.216,0,0,0,2449.646,668.166Z" transform="translate(-2449.646 -667.95)" fill="#444"/>
        </g>
        <g id="组_55" data-name="组 55" transform="translate(9.99 22.108)">
          <path id="路径_292" data-name="路径 292" d="M2449.646,698.936v.054a.216.216,0,0,0,.108.187l22.715,12.988a.216.216,0,0,0,.325-.187v-.054a.215.215,0,0,0-.108-.187l-22.715-12.988A.216.216,0,0,0,2449.646,698.936Z" transform="translate(-2449.646 -698.72)" fill="#444"/>
        </g>
        <g id="组_56" data-name="组 56" transform="translate(9.99 26.017)">
          <path id="路径_293" data-name="路径 293" d="M2449.646,730.8v.054a.217.217,0,0,0,.108.187l22.715,12.988a.216.216,0,0,0,.325-.187v-.054a.215.215,0,0,0-.108-.187l-22.715-12.988A.216.216,0,0,0,2449.646,730.8Z" transform="translate(-2449.646 -730.579)" fill="#444"/>
        </g>
        <g id="组_57" data-name="组 57" transform="translate(9.99 29.943)">
          <path id="路径_294" data-name="路径 294" d="M2449.646,762.793v.054a.217.217,0,0,0,.108.187l22.715,12.988a.216.216,0,0,0,.325-.187v-.054a.216.216,0,0,0-.108-.187l-22.715-12.988A.216.216,0,0,0,2449.646,762.793Z" transform="translate(-2449.646 -762.577)" fill="#444"/>
        </g>
        <g id="组_58" data-name="组 58" transform="translate(9.99 33.959)">
          <path id="路径_295" data-name="路径 295" d="M2449.646,795.523v.054a.217.217,0,0,0,.108.187l22.715,12.988a.216.216,0,0,0,.325-.187v-.054a.216.216,0,0,0-.108-.187l-22.715-12.988A.216.216,0,0,0,2449.646,795.523Z" transform="translate(-2449.646 -795.307)" fill="#444"/>
        </g>
        <g id="组_59" data-name="组 59" transform="translate(9.99 37.749)">
          <path id="路径_296" data-name="路径 296" d="M2449.646,826.413v.054a.217.217,0,0,0,.108.187l22.715,12.988a.216.216,0,0,0,.325-.187V839.4a.216.216,0,0,0-.108-.187l-22.715-12.988A.216.216,0,0,0,2449.646,826.413Z" transform="translate(-2449.646 -826.197)" fill="#444"/>
        </g>
        <path id="路径_297" data-name="路径 297" d="M2537.428,933.163v.066a.266.266,0,0,0,.133.23l11.846,6.864a.266.266,0,0,0,.4-.23v-.066a.266.266,0,0,0-.133-.23l-11.845-6.864A.266.266,0,0,0,2537.428,933.163Z" transform="translate(-2516.667 -882.056)" fill="#444"/>
      </g>
      <g id="plant" transform="translate(-17854.523 12462.586)">
        <ellipse id="椭圆_11" data-name="椭圆 11" cx="8.004" cy="4.13" rx="8.004" ry="4.13" transform="translate(18.47 55.532)" fill="#dddfe6"/>
        <g id="plant-2" data-name="plant" transform="translate(0 0)">
          <path id="路径_298" data-name="路径 298" d="M1574.16,1947.819c-4.11,0-7.5-1.434-7.979-3.285a.831.831,0,0,1-.058-.08l3.976,25.44a6.412,6.412,0,0,0,4.037,1.214,6.2,6.2,0,0,0,3.974-1.214l4.021-25.328C1581.62,1946.4,1578.247,1947.819,1574.16,1947.819Z" transform="translate(-1547.833 -1909.076)" fill="#585971"/>
          <g id="组_60" data-name="组 60" transform="translate(19.988 46.031)">
            <path id="路径_299" data-name="路径 299" d="M1586.313,2035.171a13.578,13.578,0,0,0,5.894-1.2l.42-2.646a12.98,12.98,0,0,1-6.289,1.41,12.84,12.84,0,0,1-6.373-1.459l.419,2.678A13.529,13.529,0,0,0,1586.313,2035.171Z" transform="translate(-1579.966 -2031.279)" fill="#ebedf6"/>
            <path id="路径_300" data-name="路径 300" d="M1591.464,2069.909a13.933,13.933,0,0,1-5.627-1.069l.2,1.288a15.529,15.529,0,0,0,10.805.041l.208-1.311A13.987,13.987,0,0,1,1591.464,2069.909Z" transform="translate(-1585.117 -2064.231)" fill="#ebedf6"/>
          </g>
          <ellipse id="椭圆_12" data-name="椭圆 12" cx="8.038" cy="3.736" rx="8.038" ry="3.736" transform="translate(18.29 31.458)" fill="#727b8e"/>
          <ellipse id="椭圆_13" data-name="椭圆 13" cx="6.69" cy="2.937" rx="6.69" ry="2.937" transform="translate(19.752 32.128)" fill="#ebedf6"/>
          <path id="路径_301" data-name="路径 301" d="M1679.345,1899.466" transform="translate(-1647.163 -1869.608)" fill="none" stroke="#727b8e" stroke-miterlimit="10" stroke-width="1"/>
          <path id="路径_302" data-name="路径 302" d="M1678.514,1846.06s4.12,3.186,11.239-1.285c6.476-4.067,11.756,3.224,11.756,3.224s-7.6-13.692-18.777-8.042C1682.731,1839.957,1678.7,1841.916,1678.514,1846.06Z" transform="translate(-1646.434 -1816.201)" fill="#7bc980"/>
          <path id="路径_303" data-name="路径 303" d="M1440.041,1860.094s-4.611,2.422-10.845-3.217c-5.671-5.129-12.137,1.135-12.137,1.135s9.858-12.166,19.888-4.66C1436.947,1853.352,1440.577,1855.98,1440.041,1860.094Z" transform="translate(-1417.059 -1827.006)" fill="#7bc980"/>
          <path id="路径_304" data-name="路径 304" d="M1641.936,1778s4.731-.133,8.188-7.8c2.024-4.486,9.542-5.744,9.542-5.744s-10.336-5.566-16.6,4.9C1643.068,1769.357,1640.352,1774.4,1641.936,1778Z" transform="translate(-1613.909 -1749.866)" fill="#7bc980"/>
          <path id="路径_305" data-name="路径 305" d="M1472.35,1774.96s-4.717.389-9-6.846c-2.506-4.236-10.117-4.658-10.117-4.658s9.66-6.671,17.038,3.043C1470.274,1766.5,1473.529,1771.207,1472.35,1774.96Z" transform="translate(-1448.797 -1748.325)" fill="#7bc980"/>
          <path id="路径_306" data-name="路径 306" d="M1598.36,1681.162s5.207-4.719,1.843-12.423c-3.291-7.536-2.076-12.624-2.076-12.624s-2.439.854-3.944,11.267a18.525,18.525,0,0,0,.677,9.354A11.372,11.372,0,0,0,1598.36,1681.162Z" transform="translate(-1572.198 -1656.115)" fill="#7bc980"/>
          <path id="路径_307" data-name="路径 307" d="M1644.226,1883.968a14.15,14.15,0,0,1,6.589-9.638,13.044,13.044,0,0,1,3.071-1.369.18.18,0,0,0-.1-.346,14.5,14.5,0,0,0-8.916,7.949,13.376,13.376,0,0,0-.994,3.308c-.036.226.309.323.346.1Z" transform="translate(-1616.047 -1846.043)" fill="#4da34f"/>
          <path id="路径_308" data-name="路径 308" d="M1552.876,1890.424a13.416,13.416,0,0,0-6.28-9.175,12.677,12.677,0,0,0-2.944-1.3.18.18,0,0,0-.1.346,13.075,13.075,0,0,1,8.09,7.227,12.323,12.323,0,0,1,.884,3c.036.228.383.131.346-.1Z" transform="translate(-1527.926 -1852.476)" fill="#4da34f"/>
          <path id="路径_309" data-name="路径 309" d="M1638.673,1805.891a31.448,31.448,0,0,1,.653-10.051,24.35,24.35,0,0,1,3.645-8.291,20.127,20.127,0,0,1,3.354-3.72c.175-.151-.079-.4-.254-.254a21.476,21.476,0,0,0-5.34,7.135,27.464,27.464,0,0,0-2.445,9.346,34.065,34.065,0,0,0,.028,5.836c.02.228.379.23.359,0Z" transform="translate(-1611.044 -1767.896)" fill="#4da34f"/>
          <path id="路径_310" data-name="路径 310" d="M1565.644,1820.586a21.755,21.755,0,0,0,.005-4.369,23.4,23.4,0,0,0-.843-3.861,35.152,35.152,0,0,0-1.519-4.368,22.906,22.906,0,0,0-2.2-3.911,20.493,20.493,0,0,0-3.386-3.759c-.174-.15-.429.1-.254.254a21.231,21.231,0,0,1,5.284,7.062,30.8,30.8,0,0,1,1.568,4.281,26.938,26.938,0,0,1,.937,3.893,20.856,20.856,0,0,1,.047,4.777c-.02.23.339.229.359,0Z" transform="translate(-1540.172 -1782.585)" fill="#4da34f"/>
          <path id="路径_311" data-name="路径 311" d="M1623.041,1778.3a110.713,110.713,0,0,1,.761,17.82q-.1,2.52-.308,5.034c-.019.23.34.229.359,0a110.686,110.686,0,0,0,.054-17.836q-.2-2.515-.507-5.018c-.028-.226-.387-.229-.359,0Z" transform="translate(-1597.766 -1763.159)" fill="#4da34f"/>
        </g>
      </g>
      <g id="Laptop-2" data-name="Laptop" transform="translate(-17887.254 12393.909)">
        <path id="路径_312" data-name="路径 312" d="M1176.158,1254.97l-9.565,5.53a1.65,1.65,0,0,1-1.652,0l-14.291-8.252a.657.657,0,0,1-.328-.5v.547a.634.634,0,0,0,.317.549l14.3,8.247a1.651,1.651,0,0,0,1.652,0l9.579-5.529a.39.39,0,0,0,.2-.338v-.568A.42.42,0,0,1,1176.158,1254.97Z" transform="translate(-1150.319 -1232.686)" fill="#585971"/>
        <path id="路径_313" data-name="路径 313" d="M1176.158,1259.592l-9.565,5.531a1.65,1.65,0,0,1-1.652,0l-14.291-8.252a.656.656,0,0,1-.328-.5v.547a.633.633,0,0,0,.317.549l14.3,8.248a1.651,1.651,0,0,0,1.652,0l9.579-5.529a.39.39,0,0,0,.2-.338v-.568A.42.42,0,0,1,1176.158,1259.592Z" transform="translate(-1150.319 -1236.741)" fill="#dddfe6"/>
        <g id="组_62" data-name="组 62" transform="translate(0 0)">
          <path id="路径_314" data-name="路径 314" d="M1236.717,1108.275l-.078-11.673.241-.141a.479.479,0,0,1,.481,0l14.978,8.643a1,1,0,0,1,.5.859V1117.2a.515.515,0,0,1-.257.445l-.316.183-15.032-8.66A1.035,1.035,0,0,1,1236.717,1108.275Z" transform="translate(-1226.045 -1096.395)" fill="#585971"/>
          <path id="路径_315" data-name="路径 315" d="M1166.566,1215.755l9.565-5.53a.426.426,0,0,0,0-.737l-14.864-8.584a1.218,1.218,0,0,0-1.219,0l-9.424,5.449a.664.664,0,0,0,0,1.15l14.291,8.252A1.65,1.65,0,0,0,1166.566,1215.755Z" transform="translate(-1150.292 -1187.938)" fill="#ebedf6"/>
          <path id="路径_316" data-name="路径 316" d="M1234.772,1109.564v-11.457a.423.423,0,0,1,.634-.366l14.988,8.649a1,1,0,0,1,.5.867V1118.5a.546.546,0,0,1-.819.474l-14.786-8.511A1.036,1.036,0,0,1,1234.772,1109.564Z" transform="translate(-1224.406 -1097.526)" fill="#333"/>
          <path id="路径_317" data-name="路径 317" d="M1237.41,1113.176v-10.769a.4.4,0,0,1,.6-.347l14.459,8.385a.948.948,0,0,1,.474.821v10.565a.517.517,0,0,1-.776.448l-14.267-8.255A.98.98,0,0,1,1237.41,1113.176Z" transform="translate(-1226.721 -1101.318)" fill="#ebedf6"/>
          <path id="路径_318" data-name="路径 318" d="M1191.3,1253.961c.674-.392,2.634-1.522,3.409-1.969a.642.642,0,0,1,.643,0l5.966,3.465a.157.157,0,0,1,0,.272l-3.452,2.009a.571.571,0,0,1-.575,0l-5.992-3.506A.157.157,0,0,1,1191.3,1253.961Z" transform="translate(-1186.202 -1232.825)" fill="#9ba2bd"/>
          <path id="路径_319" data-name="路径 319" d="M1301.113,1273.281c.061-.043,4.533-2.639,4.577-2.639s1.774,1.007,1.774,1.007l-4.588,2.662Z" transform="translate(-1282.608 -1249.262)" fill="#dddfe6"/>
          <path id="路径_320" data-name="路径 320" d="M1190.988,1209.426c.062-.043,4.534-2.639,4.577-2.64s1.774,1.007,1.774,1.007l-4.588,2.662Z" transform="translate(-1185.995 -1193.242)" fill="#dddfe6"/>
          <g id="组_61" data-name="组 61" transform="translate(7.009 14.704)">
            <path id="路径_321" data-name="路径 321" d="M1208.093,1234.466l-.654.38a.044.044,0,0,0,0,.076l.616.356a.044.044,0,0,0,.044,0l.654-.379a.044.044,0,0,0,0-.076l-.616-.356A.044.044,0,0,0,1208.093,1234.466Z" transform="translate(-1207.417 -1232.224)" fill="#444"/>
            <path id="路径_322" data-name="路径 322" d="M1214.507,1238.173l-.654.379a.044.044,0,0,0,0,.076l.616.356a.043.043,0,0,0,.044,0l.654-.379a.044.044,0,0,0,0-.076l-.616-.356A.044.044,0,0,0,1214.507,1238.173Z" transform="translate(-1213.044 -1235.476)" fill="#444"/>
            <path id="路径_323" data-name="路径 323" d="M1220.86,1241.845l-.655.38a.044.044,0,0,0,0,.076l.615.356a.044.044,0,0,0,.044,0l.654-.379a.044.044,0,0,0,0-.076l-.615-.356A.045.045,0,0,0,1220.86,1241.845Z" transform="translate(-1218.617 -1238.698)" fill="#444"/>
            <path id="路径_324" data-name="路径 324" d="M1227.186,1245.511l-.662.384a.04.04,0,0,0,0,.069l.837.484a.042.042,0,0,0,.04,0l.662-.384a.04.04,0,0,0,0-.069l-.837-.484A.04.04,0,0,0,1227.186,1245.511Z" transform="translate(-1224.162 -1241.914)" fill="#444"/>
            <path id="路径_325" data-name="路径 325" d="M1235.188,1250.124l-.653.379a.044.044,0,0,0,0,.077l3.755,2.17a.044.044,0,0,0,.045,0l.653-.379a.044.044,0,0,0,0-.077l-3.755-2.17A.045.045,0,0,0,1235.188,1250.124Z" transform="translate(-1231.188 -1245.961)" fill="#444"/>
            <path id="路径_326" data-name="路径 326" d="M1215.68,1231.086l-.814-.471a.045.045,0,0,0-.044,0l-.654.38a.044.044,0,0,0,0,.076l.814.471a.044.044,0,0,0,.044,0l.654-.379A.044.044,0,0,0,1215.68,1231.086Z" transform="translate(-1213.32 -1228.846)" fill="#444"/>
            <path id="路径_327" data-name="路径 327" d="M1229.2,1223.39l-1.056-.608a.045.045,0,0,0-.044,0l-.654.38a.044.044,0,0,0,0,.076l1.056.607a.043.043,0,0,0,.044,0l.654-.379A.044.044,0,0,0,1229.2,1223.39Z" transform="translate(-1224.969 -1221.974)" fill="#444"/>
            <path id="路径_328" data-name="路径 328" d="M1222.743,1227.4l-1.2-.692a.044.044,0,0,0-.044,0l-.646.379a.044.044,0,0,0,0,.075l1.2.692a.043.043,0,0,0,.044,0l.646-.379A.044.044,0,0,0,1222.743,1227.4Z" transform="translate(-1219.188 -1225.415)" fill="#444"/>
            <path id="路径_329" data-name="路径 329" d="M1240.659,1223.076l.616.356a.044.044,0,0,0,.044,0l.654-.38a.044.044,0,0,0,0-.075l-.616-.356a.043.043,0,0,0-.044,0l-.654.379A.044.044,0,0,0,1240.659,1223.076Z" transform="translate(-1236.561 -1221.832)" fill="#444"/>
            <path id="路径_330" data-name="路径 330" d="M1235.558,1219.27l-.616-.356a.044.044,0,0,0-.044,0l-.654.379a.044.044,0,0,0,0,.076l.615.356a.045.045,0,0,0,.044,0l.655-.38A.044.044,0,0,0,1235.558,1219.27Z" transform="translate(-1230.933 -1218.58)" fill="#444"/>
            <path id="路径_331" data-name="路径 331" d="M1247.073,1226.784l.616.356a.045.045,0,0,0,.044,0l.654-.379a.044.044,0,0,0,0-.076l-.615-.356a.045.045,0,0,0-.044,0l-.655.379A.044.044,0,0,0,1247.073,1226.784Z" transform="translate(-1242.188 -1225.085)" fill="#444"/>
            <path id="路径_332" data-name="路径 332" d="M1253.392,1230.448l.624.36a.04.04,0,0,0,.04,0l.662-.384a.04.04,0,0,0,0-.069l-.623-.36a.039.039,0,0,0-.04,0l-.662.384A.04.04,0,0,0,1253.392,1230.448Z" transform="translate(-1247.733 -1228.302)" fill="#444"/>
            <path id="路径_333" data-name="路径 333" d="M1259.806,1234.155l.623.36a.04.04,0,0,0,.04,0l.662-.384a.04.04,0,0,0,0-.069l-.623-.36a.04.04,0,0,0-.04,0l-.662.384A.04.04,0,0,0,1259.806,1234.155Z" transform="translate(-1253.361 -1231.554)" fill="#444"/>
            <path id="路径_334" data-name="路径 334" d="M1266.221,1237.863l.623.36a.04.04,0,0,0,.04,0l.662-.384a.04.04,0,0,0,0-.069l-.624-.36a.04.04,0,0,0-.04,0l-.662.384A.04.04,0,0,0,1266.221,1237.863Z" transform="translate(-1258.988 -1234.807)" fill="#444"/>
            <path id="路径_335" data-name="路径 335" d="M1272.511,1241.5l.623.36a.04.04,0,0,0,.04,0l.662-.384a.04.04,0,0,0,0-.069l-.623-.36a.04.04,0,0,0-.04,0l-.662.384A.04.04,0,0,0,1272.511,1241.5Z" transform="translate(-1264.506 -1237.997)" fill="#444"/>
            <path id="路径_336" data-name="路径 336" d="M1279.027,1245.251l.614.355a.044.044,0,0,0,.045,0l.653-.378a.045.045,0,0,0,0-.077l-.614-.355a.044.044,0,0,0-.044,0l-.653.379A.044.044,0,0,0,1279.027,1245.251Z" transform="translate(-1270.221 -1241.286)" fill="#444"/>
            <path id="路径_337" data-name="路径 337" d="M1285.442,1248.959l.614.355a.045.045,0,0,0,.045,0l.653-.379a.045.045,0,0,0,0-.077l-.614-.355a.045.045,0,0,0-.045,0l-.653.379A.044.044,0,0,0,1285.442,1248.959Z" transform="translate(-1275.848 -1244.539)" fill="#444"/>
            <path id="路径_338" data-name="路径 338" d="M1291.856,1252.666l.614.355a.044.044,0,0,0,.045,0l.653-.379a.044.044,0,0,0,0-.077l-.614-.355a.044.044,0,0,0-.044,0l-.653.378A.045.045,0,0,0,1291.856,1252.666Z" transform="translate(-1281.476 -1247.791)" fill="#444"/>
            <path id="路径_339" data-name="路径 339" d="M1298.27,1256.374l.614.355a.044.044,0,0,0,.044,0l.653-.379a.044.044,0,0,0,0-.077l-.614-.355a.043.043,0,0,0-.044,0l-.653.378A.044.044,0,0,0,1298.27,1256.374Z" transform="translate(-1287.103 -1251.044)" fill="#444"/>
            <path id="路径_340" data-name="路径 340" d="M1305.323,1259.622l-.658.381a.042.042,0,0,0,0,.073l.619.358a.042.042,0,0,0,.042,0l.658-.381a.042.042,0,0,0,0-.073l-.619-.358A.042.042,0,0,0,1305.323,1259.622Z" transform="translate(-1292.715 -1254.294)" fill="#444"/>
            <path id="路径_341" data-name="路径 341" d="M1311.675,1263.294l-.658.382a.042.042,0,0,0,0,.073l.619.358a.043.043,0,0,0,.042,0l.657-.381a.042.042,0,0,0,0-.073l-.619-.358A.043.043,0,0,0,1311.675,1263.294Z" transform="translate(-1298.287 -1257.515)" fill="#444"/>
            <path id="路径_342" data-name="路径 342" d="M1237.982,1229.281l.654-.379a.044.044,0,0,0,0-.076l-.616-.356a.044.044,0,0,0-.044,0l-.655.38a.044.044,0,0,0,0,.076l.615.356A.044.044,0,0,0,1237.982,1229.281Z" transform="translate(-1233.634 -1226.964)" fill="#444"/>
            <path id="路径_343" data-name="路径 343" d="M1243.557,1232.452l.616.356a.044.044,0,0,0,.044,0l.654-.38a.044.044,0,0,0,0-.076l-.615-.356a.043.043,0,0,0-.044,0l-.654.379A.044.044,0,0,0,1243.557,1232.452Z" transform="translate(-1239.104 -1230.058)" fill="#444"/>
            <path id="路径_344" data-name="路径 344" d="M1249.876,1236.117l.624.36a.039.039,0,0,0,.04,0l.662-.384a.04.04,0,0,0,0-.069l-.624-.36a.04.04,0,0,0-.04,0l-.662.384A.04.04,0,0,0,1249.876,1236.117Z" transform="translate(-1244.648 -1233.275)" fill="#444"/>
            <path id="路径_345" data-name="路径 345" d="M1256.29,1239.824l.623.361a.041.041,0,0,0,.04,0l.662-.384a.04.04,0,0,0,0-.069l-.623-.36a.04.04,0,0,0-.04,0l-.662.384A.04.04,0,0,0,1256.29,1239.824Z" transform="translate(-1250.276 -1236.527)" fill="#444"/>
            <path id="路径_346" data-name="路径 346" d="M1262.7,1243.532l.624.36a.04.04,0,0,0,.04,0l.662-.384a.04.04,0,0,0,0-.069l-.624-.36a.04.04,0,0,0-.04,0l-.662.384A.04.04,0,0,0,1262.7,1243.532Z" transform="translate(-1255.904 -1239.781)" fill="#444"/>
            <path id="路径_347" data-name="路径 347" d="M1268.995,1247.167l.623.36a.039.039,0,0,0,.04,0l.662-.384a.04.04,0,0,0,0-.069l-.623-.361a.041.041,0,0,0-.04,0l-.662.384A.04.04,0,0,0,1268.995,1247.167Z" transform="translate(-1261.422 -1242.97)" fill="#444"/>
            <path id="路径_348" data-name="路径 348" d="M1275.511,1250.92l.614.355a.045.045,0,0,0,.044,0l.653-.379a.044.044,0,0,0,0-.077l-.614-.355a.044.044,0,0,0-.045,0l-.653.379A.044.044,0,0,0,1275.511,1250.92Z" transform="translate(-1267.137 -1246.259)" fill="#444"/>
            <path id="路径_349" data-name="路径 349" d="M1281.925,1254.627l.614.355a.044.044,0,0,0,.045,0l.653-.379a.044.044,0,0,0,0-.077l-.614-.355a.045.045,0,0,0-.045,0l-.653.379A.044.044,0,0,0,1281.925,1254.627Z" transform="translate(-1272.764 -1249.512)" fill="#444"/>
            <path id="路径_350" data-name="路径 350" d="M1288.34,1258.335l.614.355a.046.046,0,0,0,.045,0l.653-.379a.044.044,0,0,0,0-.077l-.614-.355a.045.045,0,0,0-.045,0l-.653.379A.044.044,0,0,0,1288.34,1258.335Z" transform="translate(-1278.391 -1252.764)" fill="#444"/>
            <path id="路径_351" data-name="路径 351" d="M1296.053,1261.942l-.619-.358a.042.042,0,0,0-.042,0l-.657.381a.042.042,0,0,0,0,.073l.619.358a.043.043,0,0,0,.042,0l.657-.381A.042.042,0,0,0,1296.053,1261.942Z" transform="translate(-1284.002 -1256.015)" fill="#444"/>
            <path id="路径_352" data-name="路径 352" d="M1302.468,1265.649l-.619-.358a.042.042,0,0,0-.042,0l-.658.381a.042.042,0,0,0,0,.073l.619.358a.041.041,0,0,0,.042,0l.658-.381A.042.042,0,0,0,1302.468,1265.649Z" transform="translate(-1289.63 -1259.267)" fill="#444"/>
            <path id="路径_353" data-name="路径 353" d="M1308.159,1269.035l-.658.381a.042.042,0,0,0,0,.073l.619.358a.042.042,0,0,0,.042,0l.658-.381a.042.042,0,0,0,0-.073l-.619-.358A.043.043,0,0,0,1308.159,1269.035Z" transform="translate(-1295.203 -1262.551)" fill="#444"/>
            <path id="路径_354" data-name="路径 354" d="M1310.839,1273.079l-.63-.366a.042.042,0,0,0-.043,0l-.66.389a.042.042,0,0,0,0,.073l.063.037a.042.042,0,0,1,0,.072l-.665.4a.042.042,0,0,0,0,.073l.421.244a.042.042,0,0,0,.042,0l1.47-.847A.042.042,0,0,0,1310.839,1273.079Z" transform="translate(-1296.435 -1265.779)" fill="#444"/>
            <path id="路径_355" data-name="路径 355" d="M1232.8,1233.87l.646-.379a.044.044,0,0,0,0-.075l-.616-.356a.043.043,0,0,0-.044,0l-.646.379a.044.044,0,0,0,0,.076l.616.356A.044.044,0,0,0,1232.8,1233.87Z" transform="translate(-1229.086 -1230.99)" fill="#444"/>
            <path id="路径_356" data-name="路径 356" d="M1239.213,1237.578l.646-.379a.044.044,0,0,0,0-.076l-.616-.356a.044.044,0,0,0-.044,0l-.646.379a.044.044,0,0,0,0,.075l.616.356A.043.043,0,0,0,1239.213,1237.578Z" transform="translate(-1234.713 -1234.243)" fill="#444"/>
            <path id="路径_357" data-name="路径 357" d="M1246.188,1240.793l-.624-.361a.04.04,0,0,0-.04,0l-.654.384a.04.04,0,0,0,0,.069l.624.361a.041.041,0,0,0,.04,0l.654-.384A.04.04,0,0,0,1246.188,1240.793Z" transform="translate(-1240.258 -1237.459)" fill="#444"/>
            <path id="路径_358" data-name="路径 358" d="M1252.6,1244.5l-.624-.36a.04.04,0,0,0-.04,0l-.654.384a.04.04,0,0,0,0,.069l.623.36a.04.04,0,0,0,.04,0l.654-.384A.04.04,0,0,0,1252.6,1244.5Z" transform="translate(-1245.886 -1240.712)" fill="#444"/>
            <path id="路径_359" data-name="路径 359" d="M1259.018,1248.208l-.624-.36a.04.04,0,0,0-.04,0l-.654.384a.04.04,0,0,0,0,.069l.624.36a.039.039,0,0,0,.04,0l.654-.384A.04.04,0,0,0,1259.018,1248.208Z" transform="translate(-1251.513 -1243.965)" fill="#444"/>
            <path id="路径_360" data-name="路径 360" d="M1265.308,1251.844l-.624-.361a.04.04,0,0,0-.04,0l-.654.384a.04.04,0,0,0,0,.069l.624.361a.039.039,0,0,0,.04,0l.654-.384A.04.04,0,0,0,1265.308,1251.844Z" transform="translate(-1257.031 -1247.154)" fill="#444"/>
            <path id="路径_361" data-name="路径 361" d="M1271.811,1255.589l-.615-.355a.046.046,0,0,0-.045,0l-.645.378a.045.045,0,0,0,0,.077l.615.355a.044.044,0,0,0,.045,0l.645-.378A.044.044,0,0,0,1271.811,1255.589Z" transform="translate(-1262.746 -1250.443)" fill="#444"/>
            <path id="路径_362" data-name="路径 362" d="M1278.224,1259.3l-.615-.355a.045.045,0,0,0-.045,0l-.645.378a.044.044,0,0,0,0,.077l.615.355a.045.045,0,0,0,.045,0l.645-.378A.044.044,0,0,0,1278.224,1259.3Z" transform="translate(-1268.373 -1253.696)" fill="#444"/>
            <path id="路径_363" data-name="路径 363" d="M1284.639,1263l-.615-.355a.044.044,0,0,0-.045,0l-.645.378a.045.045,0,0,0,0,.077l.615.355a.044.044,0,0,0,.045,0l.645-.378A.044.044,0,0,0,1284.639,1263Z" transform="translate(-1274.001 -1256.949)" fill="#444"/>
            <path id="路径_364" data-name="路径 364" d="M1291.041,1266.711l-.619-.358a.042.042,0,0,0-.042,0l-.649.381a.042.042,0,0,0,0,.073l.619.358a.042.042,0,0,0,.042,0l.65-.381A.042.042,0,0,0,1291.041,1266.711Z" transform="translate(-1279.612 -1260.199)" fill="#444"/>
            <path id="路径_365" data-name="路径 365" d="M1297.455,1270.49l-.619-.358a.042.042,0,0,0-.043,0l-.649.381a.042.042,0,0,0,0,.073l.619.358a.042.042,0,0,0,.042,0l.649-.381A.042.042,0,0,0,1297.455,1270.49Z" transform="translate(-1285.24 -1263.515)" fill="#444"/>
            <path id="路径_366" data-name="路径 366" d="M1303.807,1274.162l-.619-.358a.041.041,0,0,0-.042,0l-.649.381a.042.042,0,0,0,0,.073l.619.358a.043.043,0,0,0,.043,0l.649-.381A.042.042,0,0,0,1303.807,1274.162Z" transform="translate(-1290.812 -1266.735)" fill="#444"/>
            <path id="路径_367" data-name="路径 367" d="M1222.076,1235.642l.616.356a.043.043,0,0,0,.044,0l.654-.379a.044.044,0,0,0,0-.076l-.615-.356a.044.044,0,0,0-.044,0l-.654.379A.044.044,0,0,0,1222.076,1235.642Z" transform="translate(-1220.259 -1232.856)" fill="#444"/>
            <path id="路径_368" data-name="路径 368" d="M1229.8,1239.251l-.616-.356a.044.044,0,0,0-.044,0l-.655.38a.044.044,0,0,0,0,.076l.615.356a.043.043,0,0,0,.044,0l.654-.379A.044.044,0,0,0,1229.8,1239.251Z" transform="translate(-1225.886 -1236.109)" fill="#444"/>
            <path id="路径_369" data-name="路径 369" d="M1236.135,1242.921l-.623-.36a.04.04,0,0,0-.04,0l-.662.384a.04.04,0,0,0,0,.069l.623.36a.04.04,0,0,0,.04,0l.662-.384A.04.04,0,0,0,1236.135,1242.921Z" transform="translate(-1231.431 -1239.326)" fill="#444"/>
            <path id="路径_370" data-name="路径 370" d="M1242.549,1246.629l-.623-.36a.04.04,0,0,0-.04,0l-.662.384a.04.04,0,0,0,0,.069l.623.36a.041.041,0,0,0,.04,0l.662-.384A.04.04,0,0,0,1242.549,1246.629Z" transform="translate(-1237.058 -1242.579)" fill="#444"/>
            <path id="路径_371" data-name="路径 371" d="M1248.964,1250.336l-.624-.36a.039.039,0,0,0-.04,0l-.662.384a.04.04,0,0,0,0,.069l.624.36a.039.039,0,0,0,.04,0l.662-.384A.04.04,0,0,0,1248.964,1250.336Z" transform="translate(-1242.686 -1245.831)" fill="#444"/>
            <path id="路径_372" data-name="路径 372" d="M1255.254,1253.972l-.623-.36a.041.041,0,0,0-.04,0l-.662.384a.04.04,0,0,0,0,.069l.623.36a.04.04,0,0,0,.04,0l.662-.384A.04.04,0,0,0,1255.254,1253.972Z" transform="translate(-1248.204 -1249.021)" fill="#444"/>
            <path id="路径_373" data-name="路径 373" d="M1261.756,1257.716l-.614-.355a.045.045,0,0,0-.045,0l-.653.378a.044.044,0,0,0,0,.077l.614.355a.044.044,0,0,0,.044,0l.653-.378A.045.045,0,0,0,1261.756,1257.716Z" transform="translate(-1253.919 -1252.31)" fill="#444"/>
            <path id="路径_374" data-name="路径 374" d="M1268.171,1261.28l-.614-.355a.044.044,0,0,0-.044,0l-.653.379a.044.044,0,0,0,0,.077l.614.355a.044.044,0,0,0,.044,0l.653-.379A.044.044,0,0,0,1268.171,1261.28Z" transform="translate(-1259.547 -1255.437)" fill="#444"/>
            <path id="路径_375" data-name="路径 375" d="M1274.585,1264.988l-.614-.355a.045.045,0,0,0-.045,0l-.653.379a.044.044,0,0,0,0,.077l.614.355a.044.044,0,0,0,.044,0l.653-.379A.044.044,0,0,0,1274.585,1264.988Z" transform="translate(-1265.173 -1258.69)" fill="#444"/>
            <path id="路径_376" data-name="路径 376" d="M1280.987,1268.767l-.619-.358a.043.043,0,0,0-.042,0l-.658.381a.042.042,0,0,0,0,.073l.619.358a.042.042,0,0,0,.042,0l.657-.381A.042.042,0,0,0,1280.987,1268.767Z" transform="translate(-1270.785 -1262.003)" fill="#444"/>
            <path id="路径_377" data-name="路径 377" d="M1287.4,1272.474l-.619-.358a.043.043,0,0,0-.042,0l-.657.381a.042.042,0,0,0,0,.073l.619.358a.042.042,0,0,0,.042,0l.658-.381A.042.042,0,0,0,1287.4,1272.474Z" transform="translate(-1276.412 -1265.255)" fill="#444"/>
            <path id="路径_378" data-name="路径 378" d="M1281.358,1278.772l-.287.166a.042.042,0,0,0,0,.073l.619.358a.042.042,0,0,0,.042,0l.287-.166a.042.042,0,0,0,0-.073l-.619-.357A.041.041,0,0,0,1281.358,1278.772Z" transform="translate(-1272.016 -1271.094)" fill="#444"/>
            <path id="路径_379" data-name="路径 379" d="M1293.838,1286.39l.619.358a.043.043,0,0,0,.042,0l.287-.166a.042.042,0,0,0,0-.073l-.619-.358a.041.041,0,0,0-.042,0l-.287.166A.042.042,0,0,0,1293.838,1286.39Z" transform="translate(-1283.216 -1277.568)" fill="#444"/>
            <path id="路径_380" data-name="路径 380" d="M1288.741,1281.118l-.618-.357a.042.042,0,0,0-.042,0l-.35.195-.308.178a.042.042,0,0,0,0,.073l.619.358a.042.042,0,0,0,.042,0l.35-.2.306-.17A.042.042,0,0,0,1288.741,1281.118Z" transform="translate(-1277.589 -1272.839)" fill="#444"/>
            <path id="路径_381" data-name="路径 381" d="M1275.315,1273.312l-.658.381a.042.042,0,0,0,0,.073l.619.358a.042.042,0,0,0,.042,0l.657-.381a.042.042,0,0,0,0-.073l-.619-.358A.042.042,0,0,0,1275.315,1273.312Z" transform="translate(-1266.389 -1266.304)" fill="#444"/>
            <path id="路径_382" data-name="路径 382" d="M1267.234,1268.564l-.653.379a.045.045,0,0,0,0,.077l.82.474a.045.045,0,0,0,.045,0l.653-.379a.045.045,0,0,0,0-.077l-.821-.474A.044.044,0,0,0,1267.234,1268.564Z" transform="translate(-1259.303 -1262.138)" fill="#444"/>
            <path id="路径_383" data-name="路径 383" d="M1292.435,1276.242l1.612.932a.042.042,0,0,0,.042,0l.658-.381a.042.042,0,0,0,0-.073l-1.612-.932a.042.042,0,0,0-.042,0l-.658.381A.042.042,0,0,0,1292.435,1276.242Z" transform="translate(-1281.985 -1268.476)" fill="#444"/>
            <path id="路径_384" data-name="路径 384" d="M1317.432,1267.456l1.009.583a.042.042,0,0,0,.042,0l.658-.381a.042.042,0,0,0,0-.073l-1.009-.583a.041.041,0,0,0-.042,0l-.658.381A.042.042,0,0,0,1317.432,1267.456Z" transform="translate(-1303.915 -1260.767)" fill="#444"/>
            <path id="路径_385" data-name="路径 385" d="M1242.11,1216.6l-.615-.356a.044.044,0,0,0-.044,0l-.4.229a.044.044,0,0,0,0,.076l.615.355a.043.043,0,0,0,.043,0l.4-.229A.044.044,0,0,0,1242.11,1216.6Z" transform="translate(-1236.904 -1216.235)" fill="#444"/>
            <path id="路径_386" data-name="路径 386" d="M1329.811,1267.317l-.405.231a.042.042,0,0,0,0,.073l.374.216a.042.042,0,0,0,.042,0l.406-.231a.042.042,0,0,0,0-.073l-.374-.216A.042.042,0,0,0,1329.811,1267.317Z" transform="translate(-1314.42 -1261.044)" fill="#444"/>
            <path id="路径_387" data-name="路径 387" d="M1257.4,1225.967l.4-.228a.044.044,0,0,0,0-.077l-9.883-5.712a.044.044,0,0,0-.044,0l-.4.228a.044.044,0,0,0,0,.077l9.882,5.712A.044.044,0,0,0,1257.4,1225.967Z" transform="translate(-1242.536 -1219.488)" fill="#444"/>
          </g>
          <path id="路径_388" data-name="路径 388" d="M1200.6,1282.35l.193-.1a.34.34,0,0,1,.33.006l2.2,1.273a.114.114,0,0,1,0,.2l-.178.095Z" transform="translate(-1194.426 -1259.409)" fill="#9ba2bd"/>
          <path id="路径_389" data-name="路径 389" d="M1350.256,1283.466h0a.094.094,0,0,0,.141.081l.494-.286a.111.111,0,0,0,.056-.1h0a.094.094,0,0,0-.14-.081l-.49.278A.119.119,0,0,0,1350.256,1283.466Z" transform="translate(-1325.721 -1260.167)" fill="#dddfe6"/>
          <path id="路径_390" data-name="路径 390" d="M1341.081,1288.794h0a.094.094,0,0,0,.141.081l.494-.286a.111.111,0,0,0,.056-.1h0a.094.094,0,0,0-.14-.082l-.49.278A.12.12,0,0,0,1341.081,1288.794Z" transform="translate(-1317.672 -1264.841)" fill="#dddfe6"/>
        </g>
        <g id="组_63" data-name="组 63" transform="translate(10.994 6.667)">
          <path id="路径_391" data-name="路径 391" d="M1267.916,1160.986l-.351-.2-10.535,5.916.352.2Z" transform="translate(-1254.927 -1159.549)" fill="#fff" opacity="0.6"/>
          <path id="路径_392" data-name="路径 392" d="M1240.084,1156.771l1.218.7,10.536-5.916-1.428-.828-10.511,5.9A.987.987,0,0,0,1240.084,1156.771Z" transform="translate(-1239.898 -1150.731)" fill="#fff" opacity="0.6"/>
          <path id="路径_393" data-name="路径 393" d="M1276.125,1165.741l-10.534,5.915.4.231,10.533-5.915Z" transform="translate(-1262.438 -1163.9)" fill="#fff" opacity="0.6"/>
        </g>
      </g>
    </g>
    <text id="远程智能临床研究平台_Decentralized_Clinical_Trials" data-name="远程智能临床研究平台
Decentralized Clinical Trials" transform="translate(27.75 211)" fill="#fff" font-size="33" font-family="PingFangSC-Medium, PingFang SC" font-weight="500"><tspan x="0" y="0">远程智能临床研究平台</tspan><tspan font-size="20"><tspan x="0" y="32">Decentralized Clinical Trials</tspan></tspan></text>
    <text id="_·_专注_·_专业_·_精准_·_高效_" data-name="· 专注         · 专业
· 精准         · 高效
" transform="translate(27.75 321)" fill="#fff" font-size="24" font-family="PingFangSC-Regular, PingFang SC"><tspan x="0" y="0" xml:space="preserve">· 专注         · 专业</tspan><tspan x="0" y="40" xml:space="preserve">· 精准         · 高效</tspan><tspan x="0" y="80"></tspan></text>
  </g>
</svg>
