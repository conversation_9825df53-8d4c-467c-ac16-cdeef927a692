<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="713" height="190" viewBox="0 0 713 190">
  <defs>
    <linearGradient id="linear-gradient" x1="0.5" y1="-0.201" x2="0.5" y2="1.157" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#3fa1fc"/>
      <stop offset="1" stop-color="#5860da"/>
    </linearGradient>
    <filter id="矩形_2908" x="0" y="0" width="713" height="190" filterUnits="userSpaceOnUse">
      <feOffset dy="8" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="10" result="blur"/>
      <feFlood flood-color="#5860da" flood-opacity="0.102"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="组_5014" data-name="组 5014" transform="translate(-40 -487.5)">
    <g transform="matrix(1, 0, 0, 1, 40, 487.5)" filter="url(#矩形_2908)">
      <path id="矩形_2908-2" data-name="矩形 2908" d="M10,0H643a10,10,0,0,1,10,10V130a0,0,0,0,1,0,0H0a0,0,0,0,1,0,0V10A10,10,0,0,1,10,0Z" transform="translate(30 22)" fill="url(#linear-gradient)"/>
    </g>
  </g>
</svg>
