<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="83.32" height="91.499" viewBox="0 0 83.32 91.499">
  <defs>
    <linearGradient id="linear-gradient" x1="0.34" y1="-0.116" x2="1.044" y2="0.926" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#c8cbf2"/>
      <stop offset="1" stop-color="#afb0e7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="1.176" y1="0.778" x2="0.172" y2="-0.074" gradientUnits="objectBoundingBox">
      <stop offset="0.15" stop-color="#acc5ea"/>
      <stop offset="1" stop-color="#d9e1fd"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="0.153" y1="-0.013" x2="0.847" y2="1.013" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-4" x1="0.147" y1="-0.02" x2="0.853" y2="1.019" gradientUnits="objectBoundingBox">
      <stop offset="0.116" stop-color="#dee4ff"/>
      <stop offset="0.847" stop-color="#bacbee"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="0.346" y1="-0.699" x2="0.654" y2="1.699" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-6" x1="0.36" y1="-1.408" x2="0.64" y2="2.408" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-7" x1="0.35" y1="-0.871" x2="0.65" y2="1.871" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-8" x1="0.35" y1="-0.871" x2="0.65" y2="1.871" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-9" x1="0.25" y1="0.067" x2="0.75" y2="0.933" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-10" x1="0.25" y1="0.067" x2="0.75" y2="0.933" xlink:href="#linear-gradient-4"/>
    <linearGradient id="linear-gradient-11" x1="0.5" y1="1" x2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#c3d5fd"/>
      <stop offset="1" stop-color="#1a90fc"/>
    </linearGradient>
    <linearGradient id="linear-gradient-12" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#f4ae98"/>
      <stop offset="1" stop-color="#fad1bb"/>
    </linearGradient>
    <linearGradient id="linear-gradient-13" x1="1" y1="0.5" x2="0" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#f4b9a4"/>
      <stop offset="0.652" stop-color="#fad1bb"/>
    </linearGradient>
    <linearGradient id="linear-gradient-14" y1="0.5" x2="0.999" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#4f5c7c"/>
      <stop offset="1" stop-color="#274168"/>
    </linearGradient>
    <linearGradient id="linear-gradient-15" x1="-36.551" y1="5.011" x2="-36.265" y2="4.021" xlink:href="#linear-gradient-11"/>
    <linearGradient id="linear-gradient-16" x1="-184.193" y1="-5.239" x2="-183.908" y2="-6.229" xlink:href="#linear-gradient-11"/>
    <linearGradient id="linear-gradient-17" x1="75.067" y1="9.125" x2="75.067" y2="8.08" xlink:href="#linear-gradient-12"/>
    <linearGradient id="linear-gradient-18" x1="0.5" y1="1" x2="0.5" y2="0" xlink:href="#linear-gradient-11"/>
    <linearGradient id="linear-gradient-19" x1="426.566" y1="-11.759" x2="426.566" y2="-12.804" xlink:href="#linear-gradient-12"/>
    <linearGradient id="linear-gradient-20" x1="158.876" y1="1" x2="158.876" y2="0" xlink:href="#linear-gradient-11"/>
    <linearGradient id="linear-gradient-21" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#275c89"/>
      <stop offset="1" stop-color="#013f7c"/>
    </linearGradient>
    <linearGradient id="linear-gradient-22" x1="-362.294" y1="0.5" x2="-361.295" y2="0.5" xlink:href="#linear-gradient-13"/>
    <linearGradient id="linear-gradient-23" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#18264b"/>
      <stop offset="0.652" stop-color="#2d3c65"/>
    </linearGradient>
    <linearGradient id="linear-gradient-24" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffdb80"/>
      <stop offset="1" stop-color="#ffbb24"/>
    </linearGradient>
    <linearGradient id="linear-gradient-25" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-13"/>
    <linearGradient id="linear-gradient-26" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#445677"/>
      <stop offset="1" stop-color="#293861"/>
    </linearGradient>
    <linearGradient id="linear-gradient-27" x1="0.5" y1="0" x2="0.5" y2="1" xlink:href="#linear-gradient-24"/>
  </defs>
  <g id="组_5315" data-name="组 5315" transform="translate(-375 -1229.5)">
    <ellipse id="椭圆_6486" data-name="椭圆 6486" cx="41.66" cy="3.745" rx="41.66" ry="3.745" transform="translate(375 1313.51)" fill="#f5f5f5"/>
    <g id="组_4947" data-name="组 4947" transform="translate(-0.018 183)">
      <g id="图层_15" transform="translate(383.017 1046.5)">
        <g id="组_4946" data-name="组 4946" transform="translate(0 0)">
          <g id="组_4942" data-name="组 4942" transform="translate(0 0)">
            <path id="路径_6137" data-name="路径 6137" d="M323.587,212.29H285.9a7.954,7.954,0,0,0-7.842,8.063v.666h35.084a7.954,7.954,0,0,1,7.842,8.063v47.457h4.124a7.954,7.954,0,0,0,7.842-8.063V221.2C332.95,216.746,328.828,212.29,323.587,212.29Z" transform="translate(-266.518 -212.29)" fill="url(#linear-gradient)"/>
            <path id="路径_6138" data-name="路径 6138" d="M323.444,222.83H285.623a7.7,7.7,0,0,0-7.563,7.822v.646H311.9a7.7,7.7,0,0,1,7.563,7.822v46.048h3.977a7.7,7.7,0,0,0,7.563-7.822V230.652A7.7,7.7,0,0,0,323.444,222.83Z" transform="translate(-266.518 -220.924)" fill="url(#linear-gradient-2)"/>
            <path id="路径_6139" data-name="路径 6139" d="M259.681,322.069H223.817a9.567,9.567,0,0,1-9.567-9.567V267.107a9.567,9.567,0,0,1,9.567-9.567h35.866a9.567,9.567,0,0,1,9.567,9.567V312.5A9.57,9.57,0,0,1,259.681,322.069Z" transform="translate(-214.248 -249.356)" fill="url(#linear-gradient-3)"/>
            <path id="路径_6140" data-name="路径 6140" d="M259.44,331.989H221.99a7.75,7.75,0,0,1-7.75-7.75V277.4a7.75,7.75,0,0,1,7.75-7.75h37.45a7.75,7.75,0,0,1,7.75,7.75v46.84A7.75,7.75,0,0,1,259.44,331.989Z" transform="translate(-214.24 -259.276)" fill="url(#linear-gradient-4)"/>
            <path id="路径_6141" data-name="路径 6141" d="M277.774,316.844H260.721a2.391,2.391,0,0,1,0-4.764h17.053a2.391,2.391,0,0,1,0,4.764Z" transform="translate(-250.528 -294.032)" fill="url(#linear-gradient-5)"/>
            <path id="路径_6142" data-name="路径 6142" d="M293.379,391.844h-32.3a2.386,2.386,0,1,1,0-4.764h32.3a2.386,2.386,0,1,1,0,4.764Z" transform="translate(-250.536 -355.468)" fill="url(#linear-gradient-6)"/>
            <path id="路径_6143" data-name="路径 6143" d="M281.566,466.274H260.731a2.391,2.391,0,0,1,0-4.764h20.835a2.391,2.391,0,0,1,0,4.764Z" transform="translate(-250.536 -416.437)" fill="url(#linear-gradient-7)"/>
            <path id="路径_6144" data-name="路径 6144" d="M277.2,319.268H260.664a2.114,2.114,0,0,1,0-4.228H277.2a2.114,2.114,0,0,1,0,4.228Z" transform="translate(-250.536 -296.457)" fill="#fff"/>
            <path id="路径_6145" data-name="路径 6145" d="M281.058,468.658H260.664a2.114,2.114,0,0,1,0-4.228h20.393a2.114,2.114,0,0,1,0,4.228Z" transform="translate(-250.536 -418.829)" fill="#fff"/>
            <path id="路径_6146" data-name="路径 6146" d="M281.566,538.524H260.731a2.391,2.391,0,0,1,0-4.764h20.835a2.391,2.391,0,0,1,0,4.764Z" transform="translate(-250.536 -475.62)" fill="url(#linear-gradient-8)"/>
            <path id="路径_6147" data-name="路径 6147" d="M281.058,540.908H260.664a2.114,2.114,0,1,1,0-4.228h20.393a2.114,2.114,0,1,1,0,4.228Z" transform="translate(-250.536 -478.012)" fill="#fff"/>
            <path id="路径_6148" data-name="路径 6148" d="M293.083,394.278H260.664a2.114,2.114,0,0,1,0-4.228h32.419a2.114,2.114,0,0,1,0,4.228Z" transform="translate(-250.536 -357.901)" fill="#fff"/>
            <circle id="椭圆_6461" data-name="椭圆 6461" cx="13.779" cy="13.779" r="13.779" transform="translate(39.097 45.185)" fill="url(#linear-gradient-9)"/>
            <circle id="椭圆_6462" data-name="椭圆 6462" cx="12.841" cy="12.841" r="12.841" transform="translate(39.099 47.062)" fill="url(#linear-gradient-10)"/>
          </g>
          <g id="组_4945" data-name="组 4945" transform="translate(40.195 17.309)">
            <path id="路径_6150" data-name="路径 6150" d="M465.294,351.072s-3.474-.481-4.6,1.116-1.541,5.682-1.541,5.682l2.615-.137-.221,4.76,4.825,2.829,6.873-3.7-1.045-4.313,2.2-.573s-.655-6.259-3.709-5.335a15.453,15.453,0,0,1-2.064.139,1.776,1.776,0,0,1-1.83,1.2C465.371,352.635,465.294,351.072,465.294,351.072Z" transform="translate(-455.052 -343.226)" fill="url(#linear-gradient-11)"/>
            <path id="路径_6151" data-name="路径 6151" d="M491.736,338.035l-.722,3.219a.316.316,0,0,0,.188.363,5.293,5.293,0,0,0,1.888.411,3.645,3.645,0,0,0,1.894-.669.318.318,0,0,0,.123-.365L494,337.71a.319.319,0,0,0-.358-.212l-1.655.293A.319.319,0,0,0,491.736,338.035Z" transform="translate(-481.147 -332.159)" fill="url(#linear-gradient-12)"/>
            <path id="路径_6152" data-name="路径 6152" d="M491.825,319.6a.824.824,0,0,0,.089-.66.3.3,0,0,0-.423.058,14.436,14.436,0,0,0-.08-1.915c-.058-.575-.644-1.628-2.555-1.628a2.164,2.164,0,0,0-2.282,1.38,15.189,15.189,0,0,0-.087,2.161.294.294,0,0,0-.42-.052.808.808,0,0,0,.089.66,1.936,1.936,0,0,1,.2.718c.018.192-.119.7.383.637a3.019,3.019,0,0,0,2.27,2.2,2.909,2.909,0,0,0,2.235-2.2c.514.071.374-.443.392-.635A1.9,1.9,0,0,1,491.825,319.6Z" transform="translate(-477.067 -314.102)" fill="url(#linear-gradient-13)"/>
            <path id="路径_6153" data-name="路径 6153" d="M486.538,308.935a1.47,1.47,0,0,0-1.8-.893c-1.431.28-1.526.957-2.089.984-.959.045-1.724,1.239-.5,2.5.548.562.085.8.345,1.3a2.335,2.335,0,0,1,.259.984,4.853,4.853,0,0,0,.5-1.608c.013-.345.38-.3,1.239-.226s1.984-.121,2.091-.705c.253,1.2.546.657.868.837s.342,1.628.342,1.628a3.455,3.455,0,0,1,.282-1.3c.185-.392.548-1.935.137-2.31S488.144,308.556,486.538,308.935Z" transform="translate(-473.372 -307.998)" fill="url(#linear-gradient-14)"/>
            <path id="路径_6154" data-name="路径 6154" d="M457.262,353.895c1.011-.336,2.382-.11,2.438,2.158s-2.167,5.883-1.371,7.14a26.568,26.568,0,0,0,3.628,4.115,7.12,7.12,0,0,1-.944,2.23s-4.044-1.866-5.806-3.735-.872-6.154-.056-9.2C455.768,354.3,457.262,353.895,457.262,353.895Z" transform="translate(-450.988 -345.492)" fill="url(#linear-gradient-15)"/>
            <path id="路径_6155" data-name="路径 6155" d="M508.515,353.895c-1.011-.336-2.382-.11-2.438,2.158s2.167,5.883,1.371,7.14a26.568,26.568,0,0,1-3.628,4.115,7.123,7.123,0,0,0,.944,2.23s4.044-1.866,5.805-3.735.872-6.154.056-9.2C510.011,354.3,508.515,353.895,508.515,353.895Z" transform="translate(-491.643 -345.492)" fill="url(#linear-gradient-16)"/>
            <path id="路径_6156" data-name="路径 6156" d="M480.437,329.078c-.016-.058.275-2.2.3-2.633s.421-.816.657-1.181-.034-.526.658-.816,1.163-.532,1.259-.38.007.226.007.226.416.089.051.427a3.361,3.361,0,0,0-.778,1.121c.338-.3.506-.485.5-.092a1.325,1.325,0,0,1-.354,1.015c-.335.416-.268.89-.385,1.275a3.374,3.374,0,0,1-1,1.4c-.532.459-.881.711-.881.711Z" transform="translate(-472.489 -321.123)" fill="url(#linear-gradient-17)"/>
            <path id="路径_6157" data-name="路径 6157" d="M458.585,346.47l2.273.8s-.629,14.606-5.679,12.9c-1.874-1.053-1.154-4.487-.326-6.288C455.483,352.52,458.585,346.47,458.585,346.47Z" transform="translate(-450.832 -339.512)" fill="url(#linear-gradient-18)"/>
            <path id="路径_6158" data-name="路径 6158" d="M507.575,329.078c.016-.058-.275-2.2-.3-2.633s-.421-.816-.657-1.181.034-.526-.658-.816-1.163-.532-1.259-.38-.007.226-.007.226-.416.089-.051.427a3.362,3.362,0,0,1,.778,1.121c-.338-.3-.506-.485-.5-.092a1.325,1.325,0,0,0,.354,1.015c.335.416.268.89.385,1.275a3.374,3.374,0,0,0,1,1.4c.532.459.881.711.881.711Z" transform="translate(-492.192 -321.123)" fill="url(#linear-gradient-19)"/>
            <path id="路径_6159" data-name="路径 6159" d="M512.343,346.47l-2.273.8s.629,14.606,5.679,12.9c1.874-1.053,1.154-4.487.326-6.288C515.445,352.52,512.343,346.47,512.343,346.47Z" transform="translate(-496.763 -339.512)" fill="url(#linear-gradient-20)"/>
            <path id="路径_6160" data-name="路径 6160" d="M460.523,410.679h9.819a1.093,1.093,0,0,0,1-.845l1.979-6.511a1.282,1.282,0,0,0-1-1.743H458.634a1.279,1.279,0,0,0-1,1.725l1.89,6.511A1.1,1.1,0,0,0,460.523,410.679Z" transform="translate(-453.758 -384.655)" fill="url(#linear-gradient-21)"/>
            <g id="组_4943" data-name="组 4943" transform="translate(0 21.618)">
              <path id="路径_6161" data-name="路径 6161" d="M442.864,464.777s-.152-.335-.268-.371-1.8-.1-2.409-.262-2.378-.411-3.136.306a2.029,2.029,0,0,0,.127,2.921c.376.327.747.423,2.141.358s3.416-.051,3.859-.7A11.442,11.442,0,0,0,442.864,464.777Z" transform="translate(-436.491 -457.378)" fill="#fff"/>
              <path id="路径_6162" data-name="路径 6162" d="M469.091,466.4a11.94,11.94,0,0,1-2.2.311c-.581-.052-.094,2.282-.094,2.282a9.7,9.7,0,0,0,2.917-.289C470.174,466.914,469.091,466.4,469.091,466.4Z" transform="translate(-461.159 -459.37)" fill="url(#linear-gradient-22)"/>
              <path id="路径_6163" data-name="路径 6163" d="M477.14,434.474l.843,2.621s18.109-2.385,15.521-8.262C491.339,423.919,477.14,434.474,477.14,434.474Z" transform="translate(-469.788 -427.529)" fill="url(#linear-gradient-23)"/>
              <path id="路径_6164" data-name="路径 6164" d="M443.94,465.324a1.359,1.359,0,0,1,.747,1.185c.085.926-.19,1.546-1.6,1.718a18.484,18.484,0,0,1-2.653.128c-.884.022-2.168.107-2.358-1.575s1.53-2.111,3.313-1.682C441.949,465.206,443.94,465.324,443.94,465.324Z" transform="translate(-437.777 -458.176)" fill="url(#linear-gradient-24)"/>
            </g>
            <g id="组_4944" data-name="组 4944" transform="translate(0.079 21.618)">
              <path id="路径_6165" data-name="路径 6165" d="M532.785,464.745s.152-.335.268-.371,1.8-.1,2.409-.262,2.259-.423,3.091.286a2.032,2.032,0,0,1-.056,2.917c-.376.327-.772.447-2.167.382s-3.416-.051-3.86-.7A11.44,11.44,0,0,1,532.785,464.745Z" transform="translate(-515.191 -457.346)" fill="#fff"/>
              <path id="路径_6166" data-name="路径 6166" d="M522.261,466.4a11.94,11.94,0,0,0,2.2.311c.579-.054.094,2.282.094,2.282a9.7,9.7,0,0,1-2.917-.289C521.176,466.914,522.261,466.4,522.261,466.4Z" transform="translate(-506.225 -459.37)" fill="url(#linear-gradient-25)"/>
              <path id="路径_6167" data-name="路径 6167" d="M453.547,434.474l-.843,2.621s-18.109-2.385-15.521-8.262C439.347,423.919,453.547,434.474,453.547,434.474Z" transform="translate(-436.93 -427.529)" fill="url(#linear-gradient-26)"/>
              <path id="路径_6168" data-name="路径 6168" d="M531.934,465.324a1.359,1.359,0,0,0-.747,1.185c-.085.926.19,1.546,1.6,1.718a18.482,18.482,0,0,0,2.653.128c.884.022,2.169.107,2.358-1.575s-1.53-2.111-3.313-1.682C533.923,465.206,531.934,465.324,531.934,465.324Z" transform="translate(-514.128 -458.176)" fill="url(#linear-gradient-27)"/>
            </g>
          </g>
        </g>
      </g>
      <ellipse id="椭圆_6449" data-name="椭圆 6449" cx="13" cy="13.5" rx="13" ry="13.5" transform="translate(422 1093)" fill="#d33838"/>
      <text id="AE" transform="translate(427.5 1110.5)" fill="#fff" font-size="10" font-family="Arial-Black, Arial Black" font-weight="800"><tspan x="0" y="0">AE</tspan></text>
    </g>
  </g>
</svg>
