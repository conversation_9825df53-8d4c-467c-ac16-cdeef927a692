<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="254.013" height="212.697" viewBox="0 0 254.013 212.697"><defs><style>.a{fill:url(#a);}.b{fill:url(#b);}.c{fill:url(#c);}.d{fill:#fff;}.e{fill:url(#d);}.f{fill:url(#e);}.g{fill:url(#f);}.h{fill:url(#g);}.i{fill:url(#h);}.j{fill:url(#i);}.k{fill:url(#j);}.l{fill:url(#k);}.m{fill:url(#l);}.n{fill:url(#m);}.o{fill:url(#n);}.p{fill:url(#o);}.q{fill:url(#p);}.r{fill:url(#q);}.s{fill:url(#r);}.t{fill:url(#t);}.u{fill:url(#u);}.v{fill:url(#v);}.w{fill:url(#w);}.x{fill:url(#x);}.y{fill:#d4e4fe;}.z{fill:url(#y);}.aa{fill:url(#z);}.ab{fill:url(#aa);}.ac{fill:url(#ab);}.ad{fill:url(#ac);}.ae{fill:url(#ad);}.af{fill:url(#ae);}.ag{fill:url(#af);}.ah{fill:url(#ag);}.ai{fill:url(#ah);}</style><linearGradient id="a" x1="0.151" y1="-0.04" x2="0.844" y2="1.047" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#c8cbf2"/><stop offset="1" stop-color="#afb0e7"/></linearGradient><linearGradient id="b" x1="0.151" y1="-0.037" x2="0.85" y2="1.044" gradientUnits="objectBoundingBox"><stop offset="0.116" stop-color="#dee4ff"/><stop offset="0.847" stop-color="#bacbee"/></linearGradient><linearGradient id="c" x1="0.129" y1="0.163" x2="0.724" y2="1.121" xlink:href="#a"/><linearGradient id="d" x1="0.333" y1="-0.435" x2="0.666" y2="1.448" xlink:href="#a"/><linearGradient id="e" x1="0.333" y1="-0.435" x2="0.666" y2="1.448" xlink:href="#a"/><linearGradient id="f" x1="0.333" y1="-0.435" x2="0.666" y2="1.448" xlink:href="#a"/><linearGradient id="g" x1="0.336" y1="-0.47" x2="0.663" y2="1.462" gradientUnits="objectBoundingBox"><stop offset="0.116" stop-color="#dee4ff"/><stop offset="0.847" stop-color="#c6d5f4"/></linearGradient><linearGradient id="h" x1="0.335" y1="-0.459" x2="0.664" y2="1.471" xlink:href="#g"/><linearGradient id="i" x1="0.336" y1="-0.488" x2="0.663" y2="1.496" xlink:href="#g"/><linearGradient id="j" x1="0.325" y1="-0.554" x2="0.675" y2="1.554" xlink:href="#a"/><linearGradient id="k" x1="0.327" y1="-0.629" x2="0.673" y2="1.629" xlink:href="#b"/><linearGradient id="l" x1="0.129" y1="0.163" x2="0.724" y2="1.121" xlink:href="#a"/><linearGradient id="m" x1="-0.03" y1="0.115" x2="0.895" y2="1.035" xlink:href="#a"/><linearGradient id="n" x1="0.09" y1="0.094" x2="0.878" y2="0.924" xlink:href="#a"/><linearGradient id="o" x1="0.089" y1="0.094" x2="0.878" y2="0.924" xlink:href="#a"/><linearGradient id="p" x1="0.093" y1="-0.033" x2="0.897" y2="1.046" xlink:href="#a"/><linearGradient id="q" x1="0.093" y1="-0.033" x2="0.897" y2="1.046" xlink:href="#a"/><linearGradient id="r" x1="0.093" y1="-0.033" x2="0.897" y2="1.046" xlink:href="#a"/><linearGradient id="t" x1="0.09" y1="0.094" x2="0.879" y2="0.924" xlink:href="#a"/><linearGradient id="u" x1="0.09" y1="0.094" x2="0.879" y2="0.924" xlink:href="#a"/><linearGradient id="v" x1="0.09" y1="0.094" x2="0.879" y2="0.924" xlink:href="#a"/><linearGradient id="w" x1="0.87" y1="0.14" x2="-0.238" y2="0.781" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#18264b"/><stop offset="0.652" stop-color="#2d3c65"/></linearGradient><linearGradient id="x" x1="0.499" x2="0.499" y2="1" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#ffdb80"/><stop offset="1" stop-color="#ffbb24"/></linearGradient><linearGradient id="y" x1="0.5" y1="1" x2="0.5" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#445677"/><stop offset="1" stop-color="#293861"/></linearGradient><linearGradient id="z" x1="0.5" y1="1" x2="0.5" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#f4b9a4"/><stop offset="0.652" stop-color="#fad1bb"/></linearGradient><linearGradient id="aa" x1="50.344" y1="-42.433" x2="49.405" y2="-42.433" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#4f5c7c"/><stop offset="1" stop-color="#274168"/></linearGradient><linearGradient id="ab" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#aa"/><linearGradient id="ac" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#z"/><linearGradient id="ad" x1="1" y1="0.5" x2="0" y2="0.5" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#c3d5fd"/><stop offset="1" stop-color="#1a90fc"/></linearGradient><linearGradient id="ae" x1="0.5" y1="1" x2="0.5" y2="0" xlink:href="#ad"/><linearGradient id="af" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#z"/><linearGradient id="ag" x1="0.5" y1="1" x2="0.5" y2="0" xlink:href="#ad"/><linearGradient id="ah" x1="0.5" y1="0" x2="0.5" y2="1.001" xlink:href="#x"/></defs><g transform="translate(0 0)"><path class="a" d="M419.017,273.772q.009-1.108-.035-2.182a20.457,20.457,0,0,0-20.547-19.479H253.511A18.7,18.7,0,0,0,234.875,268.6a39.757,39.757,0,0,0-.133,6.674c.485,8.441.1,16.933.1,26.223,2.217,1.617,7.852,5.45,10,6.807,1.461,0-.121-6.83,1.322-6.79,7.6.214,12.049,4,12.147,10.207.092,5.721-5,9.936-12.309,10.1-3.533.075-7.067.012-10.877.012v23.936c2.061,1.1,7.54,3.724,9.982,4.855,1.391,0-.26-4.873,1.126-4.838,6.922.167,11.581,4.353,11.57,10.282-.012,5.612-4.6,9.763-11.3,10.028-3.695.144-7.4.029-11.374.029v24.231c2.627.872,7.967,2.182,9.982,2.887,2.765-.075.947-3.112,3.655-2.794,8.325.976,11.951,10.08,6.414,16.3-1.692,1.894-4.832,3.048-7.477,3.424-3.972.566-8.083.15-12.268.15,0,11.166-.739,21.812.237,32.3.768,8.244,7.4,13.036,15.727,13.054q75.5.113,151,0c8.752-.012,15.086-5.883,16.189-14.526a52.4,52.4,0,0,0,.283-6.6C418.89,380.96,418.74,327.366,419.017,273.772Z" transform="translate(-207.365 -252.11)"/><path class="b" d="M409.273,287.891a31.709,31.709,0,0,0-1.189-9.543,16.188,16.188,0,0,0-15.53-11.218H253.716a18.734,18.734,0,0,0-18.74,17.736,41.359,41.359,0,0,0,.029,4.469c.462,8.089.092,16.229.092,25.131,3.758,0,7.234-.081,10.7.017,7.188.208,11.391,3.828,11.489,9.78.092,5.485-4.728,9.526-11.639,9.676-3.343.075-6.686.012-10.282.012v22.943c3.556,0,7.032-.069,10.508.017,6.547.162,10.952,4.168,10.941,9.855-.012,5.375-4.353,9.359-10.687,9.613-3.493.139-7,.023-10.756.023v23.221c4.336,0,8.666-.416,12.9.087a9.294,9.294,0,0,1,6.062,15.617c-1.6,1.819-4.573,2.921-7.067,3.285-3.753.543-7.644.139-11.6.139,0,10.7-.7,20.905.225,30.957.722,7.9,7,12.494,14.872,12.505q71.4.1,142.8,0c8.279-.012,14.266-5.635,15.311-13.92a50.158,50.158,0,0,0,.266-6.322C409.152,390.611,409.008,339.251,409.273,287.891Z" transform="translate(-207.477 -258.458)"/><path class="c" d="M376.936,382.565c17.026,6.507,29.386,17.574,36.066,34.121,2.506,6.206,3.649,13.059,4.676,19.728.831,5.381-2.344,8.677-7.9,8.695q-44.27.156-88.535.012c-6.12-.017-8.972-3.782-8.181-10.063,3.066-24.352,15.542-41.7,38.289-51.389a20.033,20.033,0,0,0,1.859-.941c.092-.052.081-.277.121-.421-16.575-15.605-18.694-30.045-6.212-42.377,10.542-10.415,27.62-9.809,37.862,1.339C396.069,353.328,393.252,368.662,376.936,382.565Z" transform="translate(-240.448 -286.086)"/><path class="d" d="M372.5,392.631c15.842,6.051,27.348,16.356,33.566,31.754,2.332,5.773,3.395,12.153,4.353,18.359.774,5.006-2.182,8.071-7.35,8.089q-41.2.147-82.392.006c-5.693-.017-8.348-3.516-7.615-9.364,2.852-22.66,14.462-38.809,35.633-47.821a18.046,18.046,0,0,0,1.732-.878c.087-.046.075-.26.11-.393-15.426-14.52-17.4-27.96-5.779-39.438a24.406,24.406,0,0,1,35.235,1.247C390.3,365.421,387.683,379.692,372.5,392.631Z" transform="translate(-240.451 -291.804)"/><path class="e" d="M211.663,510.261c-4.463,0-8.926-.087-13.388.023-3.828.092-6.218-1.4-6.4-5.617-.2-4.434,2.125-6.524,6.172-6.559,9.284-.075,18.567-.092,27.845.046,3.758.058,6.05,2.159,5.952,6.281s-2.367,5.895-6.247,5.837C220.946,510.209,216.3,510.261,211.663,510.261Z" transform="translate(-189.274 -356.06)"/><path class="f" d="M211.663,435.671c-4.463,0-8.926-.087-13.388.023-3.828.092-6.218-1.4-6.4-5.617-.2-4.434,2.125-6.524,6.172-6.559,9.284-.075,18.567-.092,27.845.046,3.758.058,6.05,2.159,5.952,6.281s-2.367,5.895-6.247,5.837C220.946,435.619,216.3,435.671,211.663,435.671Z" transform="translate(-189.274 -324.533)"/><path class="g" d="M211.663,363.141c-4.463,0-8.926-.087-13.388.023-3.828.092-6.218-1.4-6.4-5.618-.2-4.434,2.125-6.524,6.172-6.559,9.284-.075,18.567-.092,27.845.046,3.758.058,6.05,2.159,5.952,6.281s-2.367,5.895-6.247,5.837C220.946,363.089,216.3,363.141,211.663,363.141Z" transform="translate(-189.274 -293.878)"/><path class="h" d="M210.56,353.624c4.215.006,8.429-.035,12.644.023,3.227.046,5.236,1.617,5.392,4.965.173,3.6-1.842,5.542-5.271,5.6-8.591.139-17.182.116-25.772.029-3.476-.035-5.415-1.928-5.335-5.514.081-3.666,2.229-5.127,5.693-5.109C202.131,353.635,206.346,353.618,210.56,353.624Z" transform="translate(-189.424 -295.013)"/><path class="i" d="M210.271,511.372c-4.064,0-8.129-.075-12.188.023-3.481.081-5.658-1.23-5.831-4.93-.179-3.886,1.934-5.721,5.617-5.75,8.446-.064,16.9-.081,25.345.04,3.424.052,5.508,1.894,5.415,5.508s-2.153,5.173-5.687,5.121C218.717,511.325,214.5,511.372,210.271,511.372Z" transform="translate(-189.435 -357.165)"/><path class="j" d="M210.556,437.528c-4.37,0-8.741-.046-13.106.012-3.424.046-5.109-1.68-5.213-4.977-.1-3.314,1.6-5.283,4.913-5.323q13.1-.164,26.205-.017c3.5.035,5.421,1.974,5.254,5.548-.162,3.366-2.113,4.832-5.433,4.775C218.967,437.476,214.759,437.534,210.556,437.528Z" transform="translate(-189.431 -326.096)"/><path class="k" d="M317.373,591.208H196.923a9.543,9.543,0,0,1-9.543-9.543V560.713a9.543,9.543,0,0,1,9.543-9.543H317.379a9.543,9.543,0,0,1,9.543,9.543v20.952A9.551,9.551,0,0,1,317.373,591.208Z" transform="translate(-187.38 -378.512)"/><path class="l" d="M314.152,594.569H194.395a7.015,7.015,0,0,1-7.015-7.015V566.135a7.015,7.015,0,0,1,7.015-7.015H314.152a7.015,7.015,0,0,1,7.015,7.015v21.419A7.015,7.015,0,0,1,314.152,594.569Z" transform="translate(-187.38 -381.872)"/><path class="m" d="M215.4,578.333a12.653,12.653,0,0,1,7.638,7.228,18.933,18.933,0,0,1,.993,4.18,1.5,1.5,0,0,1-1.674,1.842q-9.379.035-18.752,0c-1.293-.006-1.9-.8-1.732-2.13a12.779,12.779,0,0,1,8.112-10.883,3.6,3.6,0,0,0,.393-.2c.017-.012.017-.058.023-.092-3.51-3.3-3.961-6.362-1.316-8.978a5.556,5.556,0,0,1,8.019.283C219.45,572.138,218.85,575.388,215.4,578.333Z" transform="translate(-193.493 -385.508)"/><path class="d" d="M214.457,580.471a11.773,11.773,0,0,1,7.107,6.726,17.381,17.381,0,0,1,.924,3.891,1.4,1.4,0,0,1-1.559,1.715q-8.721.035-17.447,0c-1.207-.006-1.767-.745-1.611-1.98a11.9,11.9,0,0,1,7.546-10.127c.127-.052.248-.121.369-.185.017-.012.017-.052.023-.081-3.268-3.077-3.683-5.924-1.224-8.354a5.162,5.162,0,0,1,7.459.266C218.227,574.709,217.673,577.735,214.457,580.471Z" transform="translate(-193.494 -386.728)"/><g transform="translate(39.848 189.107)"><path class="n" d="M256.4,589.125h2.182V582.7h-1.894v-1.668a6.523,6.523,0,0,0,2.54-1.016h1.807v9.1h1.853v2.182H256.4Z" transform="translate(-256.4 -579.812)"/><path class="o" d="M270.25,588.349a3.15,3.15,0,0,1,1.686-2.852v-.075a3.357,3.357,0,0,1-1.339-2.714,2.981,2.981,0,0,1,3.262-3.048,2.869,2.869,0,0,1,3.083,3.019,3.184,3.184,0,0,1-1.3,2.506v.075a3.286,3.286,0,0,1,1.726,3.048c0,1.744-1.438,3.066-3.591,3.066C271.757,591.368,270.25,590.139,270.25,588.349Zm4.832-.15c0-.987-.785-1.409-2.044-2a2.724,2.724,0,0,0-.693,1.836,1.4,1.4,0,0,0,1.478,1.455A1.166,1.166,0,0,0,275.082,588.2Zm-.139-5.34c0-.82-.427-1.334-1.16-1.334a1.04,1.04,0,0,0-1.033,1.183c0,.9.635,1.38,1.645,1.819A2.763,2.763,0,0,0,274.944,582.859Z" transform="translate(-262.254 -579.66)"/><path class="p" d="M284.82,588.349a3.15,3.15,0,0,1,1.686-2.852v-.075a3.357,3.357,0,0,1-1.339-2.714,2.981,2.981,0,0,1,3.262-3.048,2.869,2.869,0,0,1,3.083,3.019,3.184,3.184,0,0,1-1.3,2.506v.075a3.286,3.286,0,0,1,1.726,3.048c0,1.744-1.438,3.066-3.591,3.066C286.321,591.368,284.82,590.139,284.82,588.349Zm4.832-.15c0-.987-.785-1.409-2.044-2a2.724,2.724,0,0,0-.693,1.836,1.4,1.4,0,0,0,1.478,1.455A1.166,1.166,0,0,0,289.652,588.2Zm-.139-5.34c0-.82-.427-1.334-1.16-1.334a1.04,1.04,0,0,0-1.033,1.183c0,.9.635,1.38,1.645,1.819A2.763,2.763,0,0,0,289.514,582.859Z" transform="translate(-268.412 -579.66)"/><path class="q" d="M301.355,585.5l-2.667-5.479h2.748l.831,2.021c.219.531.456,1.12.745,1.853h.058c.237-.728.456-1.322.664-1.853l.745-2.021h2.61l-2.65,5.6,2.829,5.687h-2.748l-.953-2.2c-.248-.589-.485-1.183-.762-1.882h-.058c-.248.7-.468,1.287-.7,1.882l-.883,2.2H298.52Z" transform="translate(-274.203 -579.812)"/><path class="r" d="M317.065,585.5,314.4,580.02h2.748l.831,2.021c.219.531.456,1.12.745,1.853h.058c.237-.728.456-1.322.664-1.853l.745-2.021h2.61l-2.65,5.6,2.829,5.687h-2.748l-.953-2.2c-.248-.589-.485-1.183-.762-1.882h-.058c-.248.7-.468,1.287-.7,1.882l-.883,2.2H314.23Z" transform="translate(-280.843 -579.812)"/><path class="s" d="M332.785,585.5l-2.667-5.479h2.748l.831,2.021c.219.531.456,1.12.745,1.853h.058c.237-.728.456-1.322.664-1.853l.745-2.021h2.61l-2.65,5.6,2.829,5.687h-2.748l-.953-2.2c-.248-.589-.485-1.183-.762-1.882h-.058c-.248.7-.468,1.287-.7,1.882l-.883,2.2H329.95Z" transform="translate(-287.487 -579.812)"/><path class="q" d="M348.5,585.5l-2.667-5.479h2.748l.831,2.021c.219.531.456,1.12.745,1.853h.058c.237-.728.456-1.322.664-1.853l.745-2.021h2.61l-2.65,5.6,2.829,5.687h-2.748l-.953-2.2c-.248-.589-.485-1.183-.762-1.882H349.9c-.248.7-.468,1.287-.7,1.882l-.883,2.2H345.67Z" transform="translate(-294.131 -579.812)"/><path class="t" d="M362.25,588.349a3.15,3.15,0,0,1,1.686-2.852v-.075a3.357,3.357,0,0,1-1.339-2.714,2.981,2.981,0,0,1,3.262-3.048,2.869,2.869,0,0,1,3.083,3.019,3.184,3.184,0,0,1-1.3,2.506v.075a3.286,3.286,0,0,1,1.726,3.048c0,1.744-1.438,3.066-3.591,3.066C363.757,591.368,362.25,590.139,362.25,588.349Zm4.838-.15c0-.987-.785-1.409-2.044-2a2.724,2.724,0,0,0-.693,1.836,1.4,1.4,0,0,0,1.478,1.455A1.166,1.166,0,0,0,367.088,588.2Zm-.139-5.34c0-.82-.427-1.334-1.16-1.334a1.04,1.04,0,0,0-1.033,1.183c0,.9.635,1.38,1.645,1.819A2.763,2.763,0,0,0,366.95,582.859Z" transform="translate(-301.139 -579.66)"/><path class="u" d="M376.82,588.349a3.15,3.15,0,0,1,1.686-2.852v-.075a3.357,3.357,0,0,1-1.339-2.714,2.981,2.981,0,0,1,3.262-3.048,2.869,2.869,0,0,1,3.083,3.019,3.184,3.184,0,0,1-1.3,2.506v.075a3.286,3.286,0,0,1,1.726,3.048c0,1.744-1.438,3.066-3.591,3.066C378.327,591.368,376.82,590.139,376.82,588.349Zm4.832-.15c0-.987-.785-1.409-2.044-2a2.724,2.724,0,0,0-.693,1.836,1.4,1.4,0,0,0,1.478,1.455A1.164,1.164,0,0,0,381.652,588.2Zm-.139-5.34c0-.82-.427-1.334-1.16-1.334a1.04,1.04,0,0,0-1.033,1.183c0,.9.635,1.38,1.645,1.819A2.726,2.726,0,0,0,381.514,582.859Z" transform="translate(-307.297 -579.66)"/><path class="v" d="M391.39,588.349a3.15,3.15,0,0,1,1.686-2.852v-.075a3.357,3.357,0,0,1-1.339-2.714A2.981,2.981,0,0,1,395,579.66a2.869,2.869,0,0,1,3.083,3.019,3.184,3.184,0,0,1-1.3,2.506v.075a3.286,3.286,0,0,1,1.726,3.048c0,1.744-1.438,3.066-3.591,3.066C392.9,591.368,391.39,590.139,391.39,588.349Zm4.832-.15c0-.987-.785-1.409-2.044-2a2.724,2.724,0,0,0-.693,1.836,1.4,1.4,0,0,0,1.478,1.455A1.164,1.164,0,0,0,396.222,588.2Zm-.139-5.34c0-.82-.427-1.334-1.16-1.334a1.04,1.04,0,0,0-1.033,1.183c0,.9.635,1.38,1.645,1.819A2.726,2.726,0,0,0,396.084,582.859Z" transform="translate(-313.455 -579.66)"/></g><g transform="translate(38.659 190.123)"><path class="d" d="M254.34,590.064h2.188V584.2h-1.9v-1.524a6.954,6.954,0,0,0,2.552-.929h1.813v8.314h1.859v2H254.34v-2Z" transform="translate(-254.34 -581.559)"/><path class="d" d="M268.24,589.358a2.851,2.851,0,0,1,1.692-2.6v-.069a2.989,2.989,0,0,1-1.345-2.482c0-1.721,1.357-2.783,3.268-2.783,1.859,0,3.089,1.08,3.089,2.76a2.838,2.838,0,0,1-1.3,2.286v.069a2.98,2.98,0,0,1,1.732,2.783c0,1.593-1.443,2.8-3.6,2.8C269.747,592.112,268.24,590.992,268.24,589.358Zm4.85-.139c0-.9-.791-1.287-2.05-1.83a2.383,2.383,0,0,0-.693,1.674,1.354,1.354,0,0,0,1.484,1.328A1.115,1.115,0,0,0,273.09,589.22Zm-.139-4.878a1.1,1.1,0,0,0-1.166-1.218.992.992,0,0,0-1.039,1.08c0,.82.635,1.259,1.651,1.663A2.368,2.368,0,0,0,272.951,584.341Z" transform="translate(-260.215 -581.42)"/><path class="d" d="M282.85,589.358a2.851,2.851,0,0,1,1.692-2.6v-.069A2.989,2.989,0,0,1,283.2,584.2c0-1.721,1.357-2.783,3.268-2.783,1.859,0,3.089,1.08,3.089,2.76a2.838,2.838,0,0,1-1.3,2.286v.069a2.98,2.98,0,0,1,1.732,2.783c0,1.593-1.443,2.8-3.6,2.8C284.363,592.112,282.85,590.992,282.85,589.358Zm4.85-.139c0-.9-.791-1.287-2.05-1.83a2.383,2.383,0,0,0-.693,1.674,1.354,1.354,0,0,0,1.484,1.328A1.115,1.115,0,0,0,287.7,589.22Zm-.139-4.878a1.1,1.1,0,0,0-1.166-1.218.992.992,0,0,0-1.039,1.08c0,.82.635,1.259,1.651,1.663A2.336,2.336,0,0,0,287.561,584.341Z" transform="translate(-266.39 -581.42)"/><path class="d" d="M299.455,586.755l-2.673-5.005h2.76l.831,1.842c.219.485.456,1.028.751,1.692h.058c.237-.664.456-1.207.664-1.692l.75-1.842h2.621l-2.662,5.115,2.841,5.2h-2.76l-.958-2.009c-.248-.543-.485-1.08-.762-1.721h-.058c-.248.635-.473,1.178-.7,1.721l-.889,2.009H296.62Z" transform="translate(-272.21 -581.559)"/><path class="d" d="M315.215,586.755l-2.673-5.005h2.76l.831,1.842c.219.485.456,1.028.751,1.692h.058c.237-.664.456-1.207.664-1.692l.75-1.842h2.621l-2.662,5.115,2.841,5.2H318.4l-.958-2.009c-.248-.543-.485-1.08-.762-1.721h-.058c-.248.635-.473,1.178-.7,1.721l-.889,2.009H312.38Z" transform="translate(-278.871 -581.559)"/><path class="d" d="M330.985,586.755l-2.673-5.005h2.76l.831,1.842c.219.485.456,1.028.751,1.692h.058c.237-.664.456-1.207.664-1.692l.751-1.842h2.621l-2.662,5.115,2.84,5.2h-2.76l-.958-2.009c-.248-.543-.485-1.08-.762-1.721h-.058c-.248.635-.473,1.178-.7,1.721l-.889,2.009H328.15Z" transform="translate(-285.537 -581.559)"/><path class="d" d="M346.755,586.755l-2.673-5.005h2.76l.831,1.842c.219.485.456,1.028.751,1.692h.058c.237-.664.456-1.207.664-1.692l.751-1.842h2.621l-2.662,5.115,2.841,5.2h-2.76l-.958-2.009c-.248-.543-.485-1.08-.762-1.721h-.058c-.248.635-.473,1.178-.7,1.721l-.889,2.009H343.92Z" transform="translate(-292.202 -581.559)"/><path class="d" d="M360.54,589.358a2.851,2.851,0,0,1,1.692-2.6v-.069a2.989,2.989,0,0,1-1.345-2.482c0-1.721,1.357-2.783,3.268-2.783,1.859,0,3.089,1.08,3.089,2.76a2.838,2.838,0,0,1-1.3,2.286v.069a2.98,2.98,0,0,1,1.732,2.783c0,1.593-1.443,2.8-3.6,2.8C362.053,592.112,360.54,590.992,360.54,589.358Zm4.85-.139c0-.9-.791-1.287-2.05-1.83a2.383,2.383,0,0,0-.693,1.674,1.354,1.354,0,0,0,1.484,1.328A1.115,1.115,0,0,0,365.39,589.22Zm-.139-4.878a1.1,1.1,0,0,0-1.166-1.218.992.992,0,0,0-1.039,1.08c0,.82.635,1.259,1.651,1.663A2.368,2.368,0,0,0,365.251,584.341Z" transform="translate(-299.227 -581.42)"/><path class="d" d="M375.16,589.358a2.851,2.851,0,0,1,1.692-2.6v-.069a2.989,2.989,0,0,1-1.345-2.482c0-1.721,1.357-2.783,3.268-2.783,1.859,0,3.089,1.08,3.089,2.76a2.838,2.838,0,0,1-1.3,2.286v.069a2.98,2.98,0,0,1,1.732,2.783c0,1.593-1.443,2.8-3.6,2.8C376.667,592.112,375.16,590.992,375.16,589.358Zm4.85-.139c0-.9-.791-1.287-2.05-1.83a2.383,2.383,0,0,0-.693,1.674,1.354,1.354,0,0,0,1.484,1.328A1.115,1.115,0,0,0,380.01,589.22Zm-.139-4.878a1.1,1.1,0,0,0-1.166-1.218.992.992,0,0,0-1.039,1.08c0,.82.635,1.259,1.651,1.663A2.368,2.368,0,0,0,379.871,584.341Z" transform="translate(-305.406 -581.42)"/><path class="d" d="M389.77,589.358a2.851,2.851,0,0,1,1.692-2.6v-.069a2.989,2.989,0,0,1-1.345-2.482c0-1.721,1.357-2.783,3.268-2.783,1.859,0,3.089,1.08,3.089,2.76a2.838,2.838,0,0,1-1.3,2.286v.069a2.98,2.98,0,0,1,1.732,2.783c0,1.593-1.443,2.8-3.6,2.8C391.283,592.112,389.77,590.992,389.77,589.358Zm4.85-.139c0-.9-.791-1.287-2.05-1.83a2.383,2.383,0,0,0-.693,1.674,1.354,1.354,0,0,0,1.484,1.328A1.115,1.115,0,0,0,394.62,589.22Zm-.139-4.878a1.1,1.1,0,0,0-1.166-1.218.992.992,0,0,0-1.039,1.08c0,.82.635,1.259,1.651,1.663A2.337,2.337,0,0,0,394.481,584.341Z" transform="translate(-311.581 -581.42)"/></g></g><g transform="translate(204.955 55.823)"><path class="w" d="M569.634,475.54s17.158,23.215,8.926,31.956-22.932,19.9-22.932,19.9l-4.838-4.243s15.963-19.964,15.271-23.486-10.536-21.968-10.536-21.968Z" transform="translate(-545.935 -402.368)"/><path class="x" d="M549.3,554.98s-.144,3.533,5.092,5.219a32.8,32.8,0,0,0,4.238,8.089,2.847,2.847,0,0,1-.854,4.2c-2.252-2.309-9.676-7.933-13.1-13.591C547.873,556.048,549.3,554.98,549.3,554.98Z" transform="translate(-543.352 -435.944)"/><path class="y" d="M542.38,562.342l1.432-1.172s4.5,5.883,14.376,13.273a11.431,11.431,0,0,0-.751,1.207A93.244,93.244,0,0,1,542.38,562.342Z" transform="translate(-542.38 -438.561)"/><path class="z" d="M572.43,472.889s-.462,17.245-2.46,33.491-2.685,36.026-2.685,36.026h-6.449s-5.219-26.119-6.218-36.869-5.3-23.249-3.071-29.542S572.43,472.889,572.43,472.889Z" transform="translate(-545.987 -400.907)"/><path class="aa" d="M579.693,363.838s.369,8.181-4.584,11.783c-.537.421-1.189.583-2.829-.139s-1.657,2.627-1.992,4.757-8.908-2.205-8.868-2.315,3.816-9.572,4.8-12.99S579.693,363.838,579.693,363.838Z" transform="translate(-550.427 -354.832)"/><path class="ab" d="M577.1,350.492s-3.851-3.672-6.5-.2c-5.433-1.524-7.754,2.656-6.576,6.443a4.431,4.431,0,0,1-.75,2.789c-.5.993-2.471,5.808,4.3,8.533,2.194.808,3.816-1.986,3.239-3.914s.664-4.134,1.842-2.246.5,2.1.837,2.177,1.1-1.212.866-2.887.2-2.546,1.986-.779,10.277,4.428,12.771-2.529S584.66,347.091,577.1,350.492Z" transform="translate(-550.938 -348.8)"/><path class="ac" d="M606.31,400.333s9.561-5.144,10.248-5.173,1.749,1.12,1.83,1.316.37.4-1.189,1.23-8.937,5.381-8.937,5.381Z" transform="translate(-569.401 -368.394)"/><path class="ad" d="M593.7,407.155c.156-.139,7.886-2.494,7.529-3.262s-1-3.025-.306-3.048,1.345,1.287,1.744,1.917,1.38,1.928.924.37-.468-2.344-.127-2.523.687.075,1.189,1.137.779,1.651.485.676-.525-2.281-.087-2.454c.6-.242,1.259,1.137,1.79,2.321.439-.312.364-.075.762-1.068s.774-1.923,1.189-1.98.543,2.159.364,2.835-1.622,2.1-3.181,3.4-2.8,1.149-4.515,2.327-6.212,4.832-6.212,4.832Z" transform="translate(-564.071 -370.118)"/><path class="ae" d="M590.935,408.81l2.217,4s-6.662,7.24-14.208,11.553c-3.395-5.45-3.683-7.777-3.683-7.777S584.14,411.847,590.935,408.81Z" transform="translate(-556.277 -374.164)"/><path class="af" d="M555.282,381.51s7.488,4.728,9.289,5.109a28.8,28.8,0,0,1-.537,3.522,36.432,36.432,0,0,1,4.913,18.919c-.462,10.369,3.227,26.91,3.227,26.91s-13.51,4.278-25.8,2.841c-.612-15.669-4.1-44.686,7.217-52.076A13.348,13.348,0,0,1,555.282,381.51Z" transform="translate(-543.654 -362.625)"/><path class="ag" d="M563.551,478.1s.006.035.023.1a15.45,15.45,0,0,0,1.091,3.389,10.091,10.091,0,0,1,2.384,2.777c-.953,2.13-3.626,5-7.28,3.412-.346-3-.393-3.949-.225-4.948.225-.953-1.114-4.018-1.114-4.018Z" transform="translate(-549.164 -403.45)"/><path class="ah" d="M554.526,448.139l5.144-1.362s4.22-50.488-6.755-48.594C546.351,399.544,544.07,418.868,554.526,448.139Z" transform="translate(-544.475 -369.65)"/><path class="ai" d="M566.9,591.34s2.367,2.656,7.338.312a34.132,34.132,0,0,0,8.729,2.973,2.868,2.868,0,0,1,2.321,3.626c-3.245-.139-12.557.837-18.994-.924C566.613,593.084,566.9,591.34,566.9,591.34Z" transform="translate(-552.486 -451.313)"/><path class="y" d="M565.63,602.838l.219-1.824s7.373,1.2,19.7-.173a11.364,11.364,0,0,0,.3,1.38A94.444,94.444,0,0,1,565.63,602.838Z" transform="translate(-552.207 -455.328)"/></g></svg>