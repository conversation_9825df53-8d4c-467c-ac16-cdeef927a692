import axios from 'axios'
// import { router } from '@/route'
import { Toast } from 'vant'
import store from '@/store';

const baseUrl = window.TrialData_UI_Config.VUE_APP_API_BASE_URL // process.env.VUE_APP_API_BASE_URL
const timeoutMS = 300000;
const lowNetSpeedInfo = '网络信号较弱，请等待或稍后再试';
const noNetInfo = '无法连接到网络，请检查网络设置';

function request(options) {
  return new Promise((resolve, reject) => {
    const token = sessionStorage.getItem('patientToken')
    let curUrl = window.location.href.split('?')[1];
    if (window.location.href.split('?')[1]?.indexOf('#/') > -1) {
      curUrl = window.location.href.split('?')[1]?.substring(0, window.location.href.split('?')[1]?.indexOf('#/'));
    }
    const urlParams = new URLSearchParams(curUrl);
    let appKey = sessionStorage.getItem('appKey');
    if (urlParams != undefined) {
      const queryAppKey = urlParams.get('appkey') || urlParams.get('AppKey');
      const queryAppToken = urlParams.get('code');
      if (queryAppKey !== null && queryAppKey && queryAppKey?.toUpperCase() === 'APP') {
        appKey = queryAppKey?.toUpperCase();
        store.dispatch('setAppKey', appKey);
        store.dispatch('setAppToken', queryAppToken);
      }
    }
    if (store.state.appKey) {
      appKey = store.state.appKey;
    }
    const headers = {}
    let shoudShow = true;
    if (!options.hasOwnProperty('showLayout') || options?.showLayout) {
      setTimeout(() => {
        if (shoudShow) { // 应该显示弹窗, 调用弹窗显示方法
          Toast.loading({
            duration: timeoutMS, // 注意这里和超时时间一样
            message: '加载中...',
            forbidClick: true,
          });
        }
      }, 300); // 等待时间
    }

    // 10秒后提示网络信号较弱
    const timeoutTimer = setTimeout(() => {
      Toast.clear();
      Toast({
        message: lowNetSpeedInfo,
        duration: 0,
        forbidClick: false,
      });
    }, 10000);

    if (token || store?.state?.patientToken) {
      // sessionStorage.getItem 可能会慢些取到null，目前vuex的store更快
      headers['TrialAuth'] = store?.state?.patientToken || token
      headers['apiVer'] = 2
      // 测试token
      // headers['TrialAuth'] = 'b7e2128a-42e5-4690-8c18-4b4809569cf5'
    }
    if (appKey) {
      headers['AppKey'] = appKey; // 如果存在AppKey则在每次调用都是用
    }
    // 用于加密
    if (store.state?.userInformation?.dctStudyId) {
      headers['DCTStudyId'] = store.state?.userInformation.dctStudyId
    }
    const instance = axios.create({
      baseURL: baseUrl,
      timeout: timeoutMS,
      headers
    })

    return instance(options)
      .then(response => response.data)
      .then((result) => {
        clearTimeout(timeoutTimer);
        shoudShow = false;
        Toast.clear();
        return resolve(result);
      })
      .catch((result) => {
        clearTimeout(timeoutTimer);
        if (result.response) {
          Toast.clear();
          if (result.response.status == 401 || result.response.status == 403) {
            if (appKey?.toLowerCase() === 'app') {
              const timeoutMsg = {
                data: {
                  action: 'reLogin',
                  payload: ''
                }
              };
              store.dispatch('setToken', '');
              // 如果是iOS
              if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.jumpToNative) {
                window.webkit.messageHandlers.jumpToNative.postMessage(timeoutMsg);
              }
              // 如果是非iOS环境(即uniapp、安卓)
              uni.postMessage(timeoutMsg);
            }
          }
          else if (result.response.status == 500) {
            // console.log(result.response.status)
          } else {
            if (result.response.status === 503) {
              Toast('系统开小差了，请稍后再试~');
            } else if (result?.response?.data?.detail)
              Toast(result.response.data.detail === 'Network Error' || result.response.data.detail.indexOf('timeout') > -1 ? noNetInfo : result.response.data.detail);
          }
        }
        else if (result?.message && result?.response?.status !== 500) {
          Toast(result.message === 'Network Error' || result.message.indexOf('timeout') > -1 ? noNetInfo : result.message);
        }
        reject({ type: 'error', msg: noNetInfo })
      }).then(() => {
        clearTimeout(timeoutTimer);
        shoudShow = false;
        // Toast.clear();
      })
  })
}

// function requestFileCS(url, file) {
//   return new Promise((resolve, reject) => {
//     const token = store.state.patientToken
//     const params = new FormData()
//     params.append('fileinputs', file)
//     axios.post( baseUrl + url, params, {
//       headers: {
//         'Content-Type': 'multipart/form-data',
//         'TrialAuth': token
//       }
//     }).then(res => {
//       resolve(res.data);
//     }, err => {
//       reject(err)
//     })
//   })
// }

/**
 * 上传文件的统一接口
 * @param url 上传文件的Url
 * @param data 文件数据
 * @param showLayout 是否显示遮照, 默认显示, 主要是多次上传文件时, 需要自定义的遮照, 不用这个, 需要关闭掉
 * @returns 
 */
function requestFile(url, data, showLayout = true) {
  let shoudShow = true;
  if (showLayout) {
    setTimeout(() => {
      if (shoudShow) { // 应该显示弹窗, 调用弹窗显示方法
        Toast.loading({
          duration: timeoutMS, // 注意这里和超时时间一样
          message: '加载中...',
          forbidClick: true,
        });
      }
    }, 300); // 等待时间
  }

  // 10秒后提示网络信号较弱
  const timeoutTimer = setTimeout(() => {
    Toast.clear();
    Toast({
      message: lowNetSpeedInfo,
      duration: 0,
      forbidClick: false,
    });
  }, 10000);

  return new Promise((resolve, reject) => {
    const token = sessionStorage.getItem('patientToken')
    const params = new FormData()
    if (data?.hasOwnProperty('PhoneModels')) {
      params.append('PhoneModels', data.PhoneModels)
    }
    if (data?.hasOwnProperty('SystemVersion')) {
      params.append('SystemVersion', data.SystemVersion)
    }
    if (data?.hasOwnProperty('ProblemDescription')) {
      params.append('ProblemDescription', data.ProblemDescription)
    }
    if (data?.hasOwnProperty('CheckImageFiles')) {
      for (let i = 0; i < data.CheckImageFiles.length; i++) {
        params.append('checkImageFiles', data.CheckImageFiles[i].file)
      }
    } else {
      params.append('checkImageFiles', data)
    }
    if (data?.hasOwnProperty('PortType')) {
      params.append('PortType', data.PortType)
    }
    axios.post(baseUrl + url, params, {
      headers: {
        'Content-Type': 'multipart/form-data',
        'TrialAuth': store?.state?.patientToken || token,
        'DCTStudyId': store.state?.userInformation?.dctStudyId || ''
      }
    }).then(res => {
      clearTimeout(timeoutTimer);
      shoudShow = false;
      if (showLayout) {
        Toast.clear();
      }
      resolve(res.data);
    }, err => {
      clearTimeout(timeoutTimer);
      shoudShow = false;
      if (err.response) {
        if (err.response.status == 401 || err.response.status == 403) {
          let appKey = sessionStorage.getItem('appKey');
          if (store.state.appKey) {
            appKey = store.state.appKey;
          }
          if (appKey?.toLowerCase() === 'app') {
            const timeoutMsg = {
              data: {
                action: 'reLogin',
                payload: ''
              }
            };
            store.dispatch('setToken', '');
            // 如果是iOS
            if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.jumpToNative) {
              window.webkit.messageHandlers.jumpToNative.postMessage(timeoutMsg);
            }
            // 如果是非iOS环境(即uniapp、安卓)
            uni.postMessage(timeoutMsg);
          }
        }
        else if (err.response.status == 500) {
          //
        } else {
          if (err.response.status === 503) {
            Toast('系统开小差了，请稍后再试~');
          } else if (err?.response?.data?.detail)
            Toast(err.response.data.detail === 'Network Error' || err.response.data.detail.indexOf('timeout') > -1 ? noNetInfo : err.response.data.detail);
        }
      }
      else if (err?.message && err?.response?.status !== 500) {
        Toast(err.message === 'Network Error' || err.message.indexOf('timeout') > -1 ? noNetInfo : err.message);
      }
      if (showLayout) {
        Toast.clear();
      }
      reject(err)
    })
  })
}

function requestFileA(url, data, showLayout = true) {
  let shoudShow = true;
  if (showLayout) {
    setTimeout(() => {
      if (shoudShow) { // 应该显示弹窗, 调用弹窗显示方法
        Toast.loading({
          duration: timeoutMS, // 注意这里和超时时间一样
          message: '加载中...',
          forbidClick: true,
        });
      }
    }, 300); // 等待时间
  }

  // 10秒后提示网络信号较弱
  const timeoutTimer = setTimeout(() => {
    Toast.clear();
    Toast({
      message: lowNetSpeedInfo,
      duration: 0,
      forbidClick: false,
    });
  }, 10000);

  return new Promise((resolve, reject) => {
    const token = sessionStorage.getItem('patientToken')
    const params = new FormData()
    const Version = new Date().getTime();
    params.append("checkImageFiles", data, `recorder${Version}${url.indexOf('api/Patient/Statement/AudioFile') !== -1 || url.indexOf('api/patient/StatementFile/AudioFile') !== -1 ? '.mp3' : '.wav'}`);
    // 和普通form表单并无二致，后端接收到upfile参数的文件，文件名为recorder.mp3
    axios.post(baseUrl + url, params, {
      headers: {
        'Content-Type': 'multipart/form-data',
        'TrialAuth': store?.state?.patientToken || token,
        'DCTStudyId': store.state?.userInformation?.dctStudyId || ''
      }
    }).then(res => {
      clearTimeout(timeoutTimer);
      shoudShow = false;
      if (showLayout) {
        Toast.clear();
      }
      resolve(res.data);
    }, err => {
      clearTimeout(timeoutTimer);
      shoudShow = false;
      if (err.response) {
        if (err.response.status == 401 || err.response.status == 403) {
          let appKey = sessionStorage.getItem('appKey');
          if (store.state.appKey) {
            appKey = store.state.appKey;
          }
          if (appKey?.toLowerCase() === 'app') {
            const timeoutMsg = {
              data: {
                action: 'reLogin',
                payload: ''
              }
            };
            store.dispatch('setToken', '');
            // 如果是iOS
            if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.jumpToNative) {
              window.webkit.messageHandlers.jumpToNative.postMessage(timeoutMsg);
            }
            // 如果是非iOS环境(即uniapp、安卓)
            uni.postMessage(timeoutMsg);
          }
        }
        else if (err.response.status == 500) {
          //
        } else {
          if (err.response.status === 503) {
            Toast('系统开小差了，请稍后再试~');
          } else if (err?.response?.data?.detail)
            Toast(err.response.data.detail === 'Network Error' || err.response.data.detail.indexOf('timeout') > -1 ? noNetInfo : err.response.data.detail);
        }
      }
      else if (err?.message && err?.response?.status !== 500) {
        Toast(err.message === 'Network Error' || err.message.indexOf('timeout') > -1 ? noNetInfo : err.message);
      }
      if (showLayout) {
        Toast.clear();
      }
      reject(err)
    })
  })
}
/*wx获取接口*/
function requestWX(options) {
  return new Promise((resolve, reject) => {
    const headers = {}
    let shoudShow = true;
    if (!options.hasOwnProperty('showLayout')) {
      setTimeout(() => {
        if (shoudShow) { // 应该显示弹窗, 调用弹窗显示方法
          Toast.loading({
            duration: timeoutMS, // 注意这里和超时时间一样
            message: '加载中...',
            forbidClick: true,
          });
        }
      }, 300); // 等待时间
    }

    // 10秒后提示网络信号较弱
    const timeoutTimer = setTimeout(() => {
      Toast.clear();
      Toast({
        message: lowNetSpeedInfo,
        duration: 0,
        forbidClick: false,
      });
    }, 10000);

    const instance = axios.create({
      baseURL: '',// 'https://api.weixin.qq.com/',
      timeout: timeoutMS,
      headers
    })

    return instance(options)
      .then(response => response.data)
      .then((result) => {
        clearTimeout(timeoutTimer);
        shoudShow = false;
        Toast.clear();
        return resolve(result);
      })
      .catch((result) => {
        clearTimeout(timeoutTimer);
        if (result.response) {
          Toast.clear();
          if (result.response.status == 401 || result.response.status == 403) {
            let appKey = sessionStorage.getItem('appKey');
            if (store.state.appKey) {
              appKey = store.state.appKey;
            }
            if (appKey?.toLowerCase() === 'app') {
              const timeoutMsg = {
                data: {
                  action: 'reLogin',
                  payload: ''
                }
              };
              store.dispatch('setToken', '');
              // 如果是iOS
              if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.jumpToNative) {
                window.webkit.messageHandlers.jumpToNative.postMessage(timeoutMsg);
              }
              // 如果是非iOS环境(即uniapp、安卓)
              uni.postMessage(timeoutMsg);
            }
          }
          else if (result.response.status == 500) {
            // console.log(result.response.status)
          } else {
            if (result.response.status === 503) {
              Toast('系统开小差了，请稍后再试~');
            } else if (result?.response?.data?.detail)
              Toast(result.response.data.detail === 'Network Error' || result.response.data.detail.indexOf('timeout') > -1 ? noNetInfo : result.response.data.detail);
          }
        }
        else if (result?.message && result?.response?.status !== 500) {
          Toast(result.message === 'Network Error' || result.message.indexOf('timeout') > -1 ? noNetInfo : result.message);
        }
        reject({ type: 'error', msg: noNetInfo })
      }).then(() => {
        clearTimeout(timeoutTimer);
        shoudShow = false;
        // Toast.clear();
      })
  })
}

export { request, requestFile, requestFileA, baseUrl, requestWX }


// /**
//  * axios二次封装
//  1. 配置统一的请求基础路径: 开发环境与生产环境不同
//  2. 配置请求超时时间
//  3. 请求时, 通过请求头携带登陆用户的token
//  4. 请求成功得到的不是response, 而是请求体response.data
//  5. 对请求出错进行统一的提示处理, 具体请求可以选择处理或不处理
//  6. 对请求操作失败进行统一提示处理, 具体请求可以选择处理或不处理
//  */
// import axios from 'axios'
// import { Notify } from 'vant';
// import store from '@/store'
// //路由跳转用
// import { router } from '@/route'

// // 创建一个新的axios
// const service = axios.create({
//   baseURL: process.env.VUE_APP_API_BASE_URL,
//   timeout: 20000 //请求超时时间
// })

// // 请求拦截器y
// service.interceptors.request.use(
//   config => {
//     // 如果有token, 通过token请求头携带token
//     if (store.state.token) {
//       config.headers['TrialAuth'] = store.state.token
//     }
//     return config
//   }
// )

// // 响应拦截器
// service.interceptors.response.use(
//   response => {
//     const result = response.data
//     const code = response.status
//     /* code为非20000或200是抛错 可结合自己业务进行修改*/
//     if (code !== 20000 && code !== 200 && code !== 204) {
//       // 返回了一个失败的promise
//       return Promise.reject(new Error(result.data || result.message || '未知错误'))
//     } else {
//       return result
//     }
//   },

//   error => {
//     // console.log(error.request) //"StatusCode":500,"ErrCode":500,
//      if (error.request.status === 401){
//       store.commit("SET_TOKEN", null);
//       router.push({
//         path: '/login'
//       })
//       return
//     }else if (error.request.status === 404) {
//       router.push({
//         path: '/404'
//       })
//       return
//     }
//     const { Message, Details, ErrCode , details , message } = JSON.parse(error.request.responseText)

//     if (ErrCode === 404) {
//       router.push({
//         path: '/404'
//       })
//       return
//     }
//     // 统一显示错误提示
//     Notify({ type: 'danger', message: Message || Details || details || message || error.message || '请求出错了' });

//     // 向下传递错误, 具体的请求右以选择处理或不处理
//     return Promise.reject(error)
//   }
// )

// export default service