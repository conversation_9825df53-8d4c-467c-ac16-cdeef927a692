import { getMaintained } from '@/api/home';
import { getPatientStudyId } from '@/api/user';
import { router } from '@/route';
import store from '@/store';
// declare const window: any;
const windowCloseFun = () => {
  // setTimeout(() => {
  //     console.log('2',window.isCloseHint,window.onbeforeunload)
  window.addEventListener("beforeunload", function (e) {
    if (window?.isCloseHint) {
      const confirmationMessage = `要记得保存！你确定要离开我吗？`;
      (e || window.event).returnValue = confirmationMessage // 兼容 Gecko + IE
      return confirmationMessage // 兼容 Gecko + Webkit, Safari, Chrome
    }
  })
  // },500)
  // window.onbeforeunload = function(e){
  //     return false
  // }
}

export function downloadFile(url, fileName) {
  const x = new XMLHttpRequest()
  x.open('GET', url, true)
  x.responseType = 'blob'
  x.onload = function () {
    const url = window.URL.createObjectURL(x.response)
    const elink = document.createElement('a')
    elink.href = url
    elink.target = '_self'
    // elink.setAttribute('download', row.fileTypeStr)
    elink.download = `${fileName || '附件'}`
    elink.style.display = 'none'
    document.body.appendChild(elink)
    setTimeout(() => {
      elink.click()
      document.body.removeChild(elink)
    }, 6)
  }
  x.send()
}

// const disableGo = (disableMark) => {
//     if (disableMark) {
//         console.log(document.URL, 11)
//         history.pushState(null, '', document.URL)
//         window.addEventListener('popstate', function() {
//             console.log(document.URL, 'document.URL')
//             history.pushState(null, '', document.URL)
//         })
//     }
// }

const windowEndCloseFun = () => {
  window.isCloseHint = false
}
const windowOpenCloseFun = () => {
  window.isCloseHint = true
}
interface MaintainedInter {
  doctorApplicationName: string;
  outOfServiceList: any[];
  patientApplicationName: string;
}

/**判断是否项目维护中 */
const maintainedFun = async (path) => {
  if (sessionStorage.getItem('patientToken') || store?.state?.patientToken) {
    try {
      const rest = await getMaintained()

      const res = rest as MaintainedInter
      const data = {
        maintainedFlag: false,
        maintainedHtml: ''
      }
      if (Array.isArray(res?.outOfServiceList) && res?.outOfServiceList?.length) {
        res.outOfServiceList.forEach((ite, idx) => {
          const newTime = new Date()
          if (ite?.startTime && ite?.endTime &&
            newTime.getTime() >= new Date(ite.startTime).getTime() &&
            newTime.getTime() <= new Date(ite.endTime).getTime() &&
            ite.sites?.length
          ) {
            data.maintainedFlag = true
            if ((idx !== 0 && new Date(ite.endTime).getTime() > new Date(res[idx - 1].endTime).getTime()) || idx === 0) {
              data.maintainedHtml = ite?.content || ''
            }
          }
        })
      }
      const resStudyId = await getPatientStudyId() as string
      if (resStudyId) {
        // const userInformation = store.state?.userInformation
        // userInformation.dctStudyId = resStudyId
        if (!resStudyId?.token) {
          const token = sessionStorage.getItem('patientToken')
          resStudyId.token = token || store?.state?.patientToken
        }
        if (store.state?.userInformation?.icfStatementID) {
          store.dispatch('setUserInformation', { ...resStudyId, icfStatementID: store.state?.userInformation?.icfStatementID });
        } else {
          store.dispatch('setUserInformation', resStudyId);
        }
      }
      // 可以传path判断当前是否在维护页面
      if (path !== '/maintained' && data.maintainedFlag) {
        router.replace('/maintained')
      }
      if (document.title !== res?.patientApplicationName) {
        document.title = res?.patientApplicationName || 'EDCT'
      }
      return data
    } catch {
      return null
    }
  }
}
// 判断是不是瑞年 true 是 false 不是
const isLeapYear = (year) => {
  if (year % 4 === 0 && year % 100 !== 0) {
    return true;
  } else if (year % 400 === 0) {
    return true;
  } else {
    return false;
  }
}
const hourArr = [
  "00",
  "01",
  "02",
  "03",
  "04",
  "05",
  "06",
  "07",
  "08",
  "09",
  "10",
  "11",
  "12",
  "13",
  "14",
  "15",
  "16",
  "17",
  "18",
  "19",
  "20",
  "21",
  "22",
  "23",
];
const minuteArr = [
  "00",
  "01",
  "02",
  "03",
  "04",
  "05",
  "06",
  "07",
  "08",
  "09",
  "10",
  "11",
  "12",
  "13",
  "14",
  "15",
  "16",
  "17",
  "18",
  "19",
  "20",
  "21",
  "22",
  "23",
  "24",
  "25",
  "26",
  "27",
  "28",
  "29",
  "30",
  "31",
  "32",
  "33",
  "34",
  "35",
  "36",
  "37",
  "38",
  "39",
  "40",
  "41",
  "42",
  "43",
  "44",
  "45",
  "46",
  "47",
  "48",
  "49",
  "50",
  "51",
  "52",
  "53",
  "54",
  "55",
  "56",
  "57",
  "58",
  "59",
];
const monthArr = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12']
const dayArr = [
  "01",
  "02",
  "03",
  "04",
  "05",
  "06",
  "07",
  "08",
  "09",
  "10",
  "11",
  "12",
  "13",
  "14",
  "15",
  "16",
  "17",
  "18",
  "19",
  "20",
  "21",
  "22",
  "23",
  "24",
  "25",
  "26",
  "27",
  "28",
  "29",
  "30",
  "31",
];
// 1 3 5 7 8 10 12 是31天
// 2是28 29
// 4 6 9 11月 30天
const ThirtyDaysArr = ["04", "06", "09", "11"];
const UKArr = ['UK']

// 完整状态获取
function getPatientAllStatus(res) {
  let url = ''
  switch (res.patientStatus) {
    case 0:
    case 1:
    case 2:
      url = '/sweepCodePrompt'; // 扫码
      break;
    case 3:
      url = '/welcomeToJoinUs'; // 欢迎加入
      if (res?.isFamilyMember) {
        url = '/registerInfo' // 家属登记信息页面
      } else if (res?.withoutICF && res?.patientICFStatus_Other) {
        url = returnPatientStatusUrl(res)
      }
      break;
    case 4:
      url = returnPatientStatusUrl(res)
      break;
    case 5:
      url = '/questionnaire'; // 入排问卷
      break;
    case 6:
    case 7:
    case 8:
    case 9:
      url = '/audit'; // 审核成功
      break;
    case 10:
      url = '/'; // 入组成功
      break;
    case 11:
      url = '/audit'; // 中途脱落
      break;
    case 12:
      url = '/welcomeToJoinUs'; // 重新入组
      break;
    case 13:
      url = '/projectEnd'; // 完成试验随访中
      break;
    case 14:
      url = '/projectEnd'; //结局发生
      break;
    case 15:
      url = '/projectEnd' // 结束研究
      break;
    case 16:
      url = '/audit' // 筛选失败
      break;
    case 17:
      url = '/' // 待随机audit
      break;
    case 18:
      url = '/informed' // 已完成知情同意
      break;
    case 19:
      url = '/recordPersonalStatement' // 待录制个人声明
      break;
    case 20:
      url = '/signInformed' // 待患者签字
      break;
    case 21:
    case 22:
    case 23:
    case 24:
    case 25:
      url = '/audit' // 24 = 待医生短信签字
      break;
    case 26:
      url = '/waitInformed' // 26 = 待解答知情同意疑问
      break;
    case 27:
    case 28:
      url = '/informedVideo' // 等等观看视频
      break;
    case 29:
      url = '/waitExplainInformed' // 待讲解知情同意
    case 31:
      url = '/informationRegister' // 绑定受试者
      break;
    case 32:
      url = '/' // 已绑定微信信息
      break;
    case 33:
      url = '/' // 筛选期
      break;
    case 34:
      url = '/' // 待入组
      // case 35:
      // url = '/readyForSignature' // 待发起签署
      break;
    case 37:
      url = '/audit' // 筛选失败
      break;
  }
  return url
}
/*根据患者的状态返回要去到的url的函数*/
function returnPatientStatusUrl(res) {
  let url = ''
  const signInformedArr = [903, 4001, 4101, 4103, 4007, 4010, 4011, 4020, 4021, 4012]
  if (res?.patientICFStatus === 1 || res?.patientICFStatus_Other === 1) {
    url = '/bindingMobile' // 绑定身份用得
  } else if (res?.patientICFStatus === 101 || res?.patientICFStatus_Other === 101) {
    url = '/informedVideo' // 等待观看视频
  } else if (res?.patientICFStatus === 201 || res?.patientICFStatus_Other === 201) {
    url = '/informed' // 知情同意书
  } else if (res?.patientICFStatus === 401 || res?.patientICFStatus_Other === 401) {
    url = '/waitExplainInformed' // 待讲解知情
  }
  else if (res?.patientICFStatus === 801 || res?.patientICFStatus_Other === 801) {
    url = '/informedQuestionnaire'
  } else if (res?.patientICFStatus === 2001 || res?.patientICFStatus_Other === 2001) {
    url = '/recordPersonalStatement' // 待录制声明视频
  }
  /*
  待填写知情内容 903
  待受试者签名 = 4001
  待监护人签名 = 4101
  待公正见证人签名 = 4103
  待研究者签名 = 4007
  受试者拒签 = 4010
  研究者拒签 = 4011
  监护人拒签 = 4020
  公正见证人拒签 = 4021
  重新发起 = 4012
  */
  else if (res?.patientICFStatus === 4001 || res?.patientICFStatus_Other === 4001) {
    url = '/signInformed' // 待受试者签字
  } else if (res?.patientICFStatus === 901 || res?.patientICFStatus_Other === 901) {
    url = '/readyForSignature'
  }
  else if (signInformedArr.includes(res?.patientICFStatus) || signInformedArr.includes(res?.patientICFStatus_Other)) {
    url = '/signInformedFaDaDa'
  } else if (res?.patientICFStatus === 4004
    || res?.patientICFStatus === 4003
    || res?.patientICFStatus === 4007
    || res?.patientICFStatus === 4009
    || res?.patientICFStatus_Other === 4004
    || res?.patientICFStatus_Other === 4003
    || res?.patientICFStatus_Other === 4007
    || res?.patientICFStatus_Other === 4009) {
    url = '/audit' // 待审核个人声明
  } else if (res?.patientICFStatus === 3001
    || res?.patientICFStatus === 3003
    || res?.patientICFStatus === 61
    || res?.patientICFStatus === 62
    || res?.patientICFStatus === 63
    || res?.patientICFStatus_Other === 3001
    || res?.patientICFStatus_Other === 3003
    || res?.patientICFStatus_Other === 61
    || res?.patientICFStatus_Other === 62
    || res?.patientICFStatus_Other === 63) {
    url = '/facialRecognition' // 人脸识别
  } else if (res?.patientStatus && res?.patientStatus === 5) {
    url = '/questionnaire'
  } else if (res?.patientStatus === 10 ||
    (res?.patientStatus > 31 && res?.patientStatus < 35)) {
    url = '/' // 入组
  }
  if (res?.patientICFStatus === 1003 || res?.patientICFStatus === 1002 || res?.patientICFStatus === 1001) {
    url = '/audit' // 待审核知情同意书
  }
  return url
}
/*类似day.js库的日期格式化*/
function formatDate(inputDate, format) {
  const date = new Date(inputDate);
  // 处理月、日、小时、分钟、秒为单个数字的情况，前面补零
  const pad = (value) => (value < 10 ? '0' + value : value);
  const formats = {
    YYYY: date.getFullYear(),
    MM: pad(date.getMonth() + 1),
    DD: pad(date.getDate()),
    HH: pad(date.getHours()),
    mm: pad(date.getMinutes()),
    ss: pad(date.getSeconds()),
  };
  // 将传入的格式中的对应部分替换为实际的日期值
  const formattedDate = format.replace(/(YYYY|MM|DD|HH|mm|ss)/g, (match) => formats[match]);
  return formattedDate;
}
// 自写一个函数将js对象转为ts接口 （参数1 js对象，参数2 ts接口名）
function generateInterface(
  obj: any,
  interfaceName: string = 'myGeneratedInterface'
): string {
  const getType = (value: any): string => {
    if (value === null) {
      return 'any'
    } else if (Array.isArray(value)) {
      const arrayItemType = value.length > 0 ? getType(value[0]) : 'any'
      return `${arrayItemType}[]`
    } else if (typeof value === 'object') {
      // interfaceName + 'Item'
      return generateInterface(value, '')
    } else {
      if (typeof value === 'function') {
        return 'Function'
      }
      return typeof value
    }
  }

  const properties: string[] = []

  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key]
      const type = getType(value)
      properties.push(`${key}: ${type};`)
    }
  }
  // 最外层的添加 interface ${interfaceName}
  let interfaceCode
  if (interfaceName) {
    interfaceCode = `interface ${interfaceName} {
${properties.join('\n    ')}
}`
  } else {
    interfaceCode = `{
${properties.join('\n    ')}
}`
  }
  return interfaceCode
}
function parseTimeNYDHMS(time, cFormat) {
  if (arguments.length === 0 || !time) {
    return null;
  }
  const format = cFormat || "{y}-{m}-{d} {h}:{i}:{s}";
  let date;
  if (typeof time === "object") {
    date = time;
  } else {
    if (typeof time === "string") {
      if (/^[0-9]+$/.test(time)) {
        // support "1548221490638"
        time = parseInt(time);
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        time = time.replace(new RegExp(/-/gm), "/");
      }
    }

    if (typeof time === "number" && time.toString().length === 10) {
      time = time * 1000;
    }
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  };
  const timeStr = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key];
    // Note: getDay() returns 0 on Sunday
    if (key === "a") {
      return ["日", "一", "二", "三", "四", "五", "六"][value];
    }
    return value.toString().padStart(2, "0");
  });
  return timeStr;
}

const nowDayArr = () => {
  return dayArr.filter((item: any) => item / 1 <= new Date().getDate() / 1);
};
const nowMonthArr = () => {
  return monthArr.filter(
    (item: any) => item / 1 <= (new Date().getMonth() + 1) / 1
  );
};

function toWildfire(
  token, patientId,
  icfStatementID, makeReservation = false,
  patientName, patientNo, waitExplainInformedId = '',) {
  // makeReservation 能否预定会议
  // backUrl 当前页面的路径
  const backUrl = location.href.split('#')[1]
  // console.log(location,backUrl);
  // return
  const info = {
    token,
    patientId,
    icfStatementID,
    makeReservation,
    host: location.host,
    pathname: location.pathname,
    code: sessionStorage.getItem("code"),
    documentTitle: document.title,
    patientName,
    patientNo,
    backUrl: backUrl.includes('waitExplainInformed') ? 'waitExplainInformed' : '',
    waitExplainInformedId,
    studyId: store.state?.userInformation?.dctStudyId || ''
  }
  const userInfo = JSON.stringify(info)
  let path = `https://${location.host}/patientui/conference/unpackage/dist/build/h5/index.html#/?userInfo=${userInfo}`

  if (location.href.includes('trialdata')) {
    // path = `https://imh5.trialdata.cn/?userInfo=${userInfo}`
  } else {
    // path = `http://${location.host}/doctorui/conference/unpackage/dist/build/h5/index.html#/?userInfo=${userInfo}`
    path = `http://localhost:5173/?userInfo=${userInfo}`
  }
  return path // location.replace(path)
}

/*判断 是否是IOS设备*/
function isIOS() {
  const userAgent = navigator.userAgent.toLowerCase();
  return /iphone|ipad|ipod/.test(userAgent);
}

// 示例使用
// const originalDate = "12-25-1995";
// const formattedDate = formatDate(originalDate, "YYYY-MM-DD");
// const originalDate2 = "2012-05-19";
// const formattedDate2 = formatDate(originalDate2, "YYYY-MM-DD");
// console.log(formattedDate,formattedDate2);

export {
  isIOS,
  windowCloseFun,
  windowEndCloseFun,
  windowOpenCloseFun,
  maintainedFun,
  isLeapYear,
  monthArr,
  hourArr,
  minuteArr,
  dayArr,
  ThirtyDaysArr,
  UKArr,
  getPatientAllStatus,
  returnPatientStatusUrl,
  formatDate,
  generateInterface,
  parseTimeNYDHMS,
  nowDayArr,
  nowMonthArr,
  toWildfire
};

