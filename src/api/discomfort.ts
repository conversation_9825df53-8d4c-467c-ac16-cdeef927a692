import { request } from "@/utils/axios";
/*不适记录相关API*/

/*获取不良事件清单*/
export const getAdverseEvents = (data) => {
    return request({
        url: `api/Patient/AdverseEvent`,
        method: "get",
        params: { ...data } //get传参不可带请求体参数-后续看改成post还是JSON.stringfly(data)拼接url后
    });
};

/*创建不良事件*/
export const postAdverseEvent = () => {
    return request({
        url: `api/Patient/AdverseEvent`,
        method: "post",
    });
};

/*获取不良事件数据*/
export const getViewData = (aeId,data) => {
    return request({
        url: `api/Patient/Quest/AECM/ViewData/${aeId}`,
        method: "get",
        params: {...data}
    });
};

/*获取某个不良事件*/
export const getAdverseEvent = () => {
    return request({
        url: `api/Patient/AdverseEvent/View`,
        method: "get",
    });
};

/*删除不良事件*/
export const deleteAdverseEvent = (aeId) => {
    return request({
        url: `api/Patient/AdverseEvent/${aeId}`,
        method: "delete",
    });
};

/*获取某个不良事件已填写数据*/
export const getAdverseEventData = (aeId) => {
    return request({
        url: `api/Patient/AdverseEvent/${aeId}/Data`,
        method: "get",
    });
};

/*更新某个不良事件已填写数据*/
export const putAdverseEventData = (aeId,data) => {
    return request({
        url: `api/Patient/AdverseEvent/${aeId}/Data`,
        method: "put",
        data
    });
};