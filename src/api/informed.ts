import { request, requestFileA } from "@/utils/axios";

export const postAudioFile = (statementFileId,statementVideoId,totalSeconds, data) => {
    return requestFileA(
        `api/Patient/Statement/AudioFile/${statementFileId}/${statementVideoId}/${totalSeconds}`,
        data
    );
};

/*获取知情问卷信息 
IncludeData: 0 1 ,是否获取答案
*/
export const getQuestICFGeneralInfo = (params) => {
    return request({
        url: `api/Patient/Quest/ICFGeneralInfo`,
        method: "get",
        params: {
            IncludeData: 0,
            ...params
        }
    });
};
/*记录:
观看视频时间
结束视频时间
视频完成
*/
export const postStudyIntroduceVideoStartTime = (data) => {
    return request({
        url: "api/Patient/StudyIntroduceVideo/StartTime",
        method: "post",
        data
    });
};
export const getStudyIntroduceVideoNewStartTime = (params) => {
    return request({
        url: "api/Patient/StudyIntroduceVideoNew",
        method: "get",
        params: params
    });
};
/*记录观看视频结束时间*/
export const postStudyIntroduceVideoEndTime = (data) => {
    return request({
        url: "api/Patient/StudyIntroduceVideo/EndTime",
        method: "post",
        data
    });
};
/*标记观看视频完成*/
export const postStudyIntroduceVideoFinish = (data) => {
    return request({
        url: "api/Patient/StudyIntroduceVideo/Finish",
        method: "post",
        data
    });
};
/*阅读知情书：
开始时间
结束时间
完成
*/
export const postStudyIcfStartTime = (data) => {
    return request({
        url: "api/Patient/StudyICF/StartTime",
        method: "post",
        data
    });
};
/*记录阅读知情结束时间*/
export const postStudyIcfEndTime = (data) => {
    return request({
        url: "api/Patient/StudyICF/EndTime",
        method: "post",
        data
    });
};
/*标记阅读知情完成*/
export const postStudyIcfFinish = (data) => {
    return request({
        url: "api/Patient/StudyICF/Finish",
        method: "post",
        data
    });
};
/*记录填写知情问卷的时间*/
export const postICFGeneralInfoTime = (data) => {
    return request({
        url: "api/Patient/ICFGeneralInfo/Time",
        method: "post",
        data
    });
};
/*知情问卷审核成功后： 点下一部按钮*/
export const postICFGeneralInfoComfirm = (data) => {
    return request({
        url: "api/Patient/ICFGeneralInfo/Comfirm",
        method: "post",
        data
    });
};
/*受试者身份证审核成功后： 点下一部按钮*/
export const postSignComfirm = () => {
    return request({
        url: "api/Patient/Statement/Sign/Comfirm",
        method: "post",
        // data
    });
};

/*确认提交知情问卷
*/
export const putICFGeneralInfo = (id,data) => {
    return request({
        url: `api/Patient/Quest/${id}/ICFData`,
        method: "put",
        data
    });
};
/*确认声明需完善*/
export const postSupplementComfirm = (data) => {
    return request({
        url: `api/Patient/Statement/Supplement/Comfirm`,
        method: "post",
        data
    });
};
/**签名 上传base64文件
 * @data { base64File: string} 
 * @returns {
    "id": "string",
    "bindTableName": "string",
    "bindTableId": "string",
    "fileType": 0,
    "fileName": "string",
    "groupNumber": 0,
    "relativePath": "string",
    "fileUrl": "string",
    "thumbnailUrl": "string",
    "thirdCode": "string",
    "seqence": 0
    }
 */
export const postSignatureFile = (data) => {
    return request({
        url: `api/Home/SignatureFile`,
        method: "post",
        data
    });
};
/**手写签名验证签署
 * @data {
  "signaturePath_Patient": "string",
  "signatureUrl_Patient": "string"
}
 * @returns {
  "patientStatus": 0,
  "auditComments": "string",
  "patientICFStatus": 0,
  "icfAuditComments": "string",
  "message": "string",
  "signatureType": 0,
  "bizId": "string",
  "certUrl": "string",
  "certName": "string"
}
*/
export const postStatementHandwrittenSignature = (data) => {
    return request({
        url: `api/patient/Statement/HandwrittenSignature`,
        method: "post",
        data
    });
};

/**知情版本信息接口
* @data {
  "xxxid": "string",
}
 * @returns*/
export const getStatementInfo = (params) => {
    return request({
        url: `api/Patient/Statement/Info`,
        method: "get",
        params: params
    });
};

// 是否绑定过身份
export const getIdentityFlow = (params) => {
    return request({
        url: `api/Patient/Identity/Flow`,
        method: "get",
        params: params
    });
};

// 初始列表知情之前代码
export const getUpdateICFStatus = () => {
    return request({
      url: `api/patient/Identity/UpdateICFStatus`,
      method: "get"
    });
  };

// 获取签名CA
export const getGetSignTask = (icfStatementID) => {
    return request({
      url: `api/Signature/${icfStatementID}/GetSignTask`,
      method: "get"
    });
};

// 获取是否为CA认证
export const getGetCASignatureStrategy = (icfStatementID) => {
    return request({
      url: `api/Signature/${icfStatementID}/GetCASignatureStrategy`,
      method: "get",
    });
};

// 获取签名任务参与者的三方签署Url成功
export const getThirdActorSignTaskUrl = (icfStatementID, params) => {
    return request({
      url: `api/Signature/${icfStatementID}/GetThirdActorSignTaskUrl`,
      method: "get",
      params: params
    });
};

// 获取签名任务详情
export const getThirdSignTaskDetailUrl = (icfStatementID, params) => {
    return request({
      url: `api/Signature/${icfStatementID}/GetThirdSignTaskDetailUrl`,
      method: "get",
      params: params
    });
};
/*知情同意书=>语音*/
export const postStatementFile = (statementFileId,statementVideoId,totalSeconds, data) => {
    return requestFileA(
        `api/patient/StatementFile/AudioFile/${statementFileId}/${statementVideoId}/${totalSeconds}`,
        data
    );
};