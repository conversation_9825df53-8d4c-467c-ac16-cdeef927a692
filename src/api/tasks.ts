//任务：- 问卷-上传-其它
import { request, requestFile } from "@/utils/axios";

/*获取访视中的全部问卷列表
*/
export const getVisitQuOr = (visitId,questContentType) => {
    return request({
        url: `api/Patient/Visit/${visitId}`,
        method: "get",
        params: {questContentType}
    });
};

// 拍照上传任务 列表PictureTask
export const getPictureTask = (visitId) => {
    return request({
        url: `api/Patient/Visit/${visitId}/PictureTask`,
        method: "get",
    });
};
/** 获取访视中的全部问卷列表
 *访视的类型, 目前只有两个可选, 1 问卷任务, 2 图片类型, 3 其它记录,
 *如果传其它0则返回所有的问卷, 默认为0
 * @param questContentType 
 * @param pageIndex
 * @param pageSize
 * @returns 
 */
export const getVisits = (params) => {
    return request({
        url: `api/Patient/Visit/Visits`,
        method: "get",
        params: params
    });
};
/*访视下的图片列表
*/
export const getVisitFile = (visitId) => {
    return request({
        url: `api/Patient/Visit/${visitId}/File`,
        method: "get",
    });
};

/*上传图片到访视
*/
export const postVisitFile = (visitId,data) => {
    return requestFile(
        `api/Patient/Visit/${visitId}/File`,
        data,
        false
      );
};

//删除访视中的某个图片
export const deleteVisitFile = (visitId,fieldDataId) => {
    return request({
        url: `api/Patient/Visit/${visitId}/File/${fieldDataId}`,
        method: "delete",
    });
};

/**
 * otherTask其他任务
 * @param pageIndex 
 * @param pageSize
 * @returns {
  "items": [
    {
      "id": "string",
      "taskName": "string",
      "taskDescription": "string",
      "patientId": "string",
      "finishedCount": 0,
      "totalCount": 0,
      "status": 0,
      "isExpired": true,
      "statusText": "string"
    }
  ],
  "totalItemCount": 0
}
 */
export const getTasks = (params) => {
    return request({
        url: `api/Patient/Tasks`,
        method: "get",
        params: params
    });
}
/**
 * otherTask其他任务详情
 * @param taskId 
 * @returns {
  "id": "string",
  "taskName": "string",
  "taskDescription": "string",
  "patientId": "string",
  "finishedCount": 0,
  "totalCount": 0,
  "status": 0,
  "isExpired": true,
  "statusText": "string"
}
 */
export const getTaskDetails = (taskId) => {
    return request({
        url: `api/Patient/Task/${taskId}`,
        method: "get",
    });
}
/**
 * otherTask其他子任务详情
 * @param taskId 
 * @returns [
  {
    "id": "string",
    "dctPatientCustomTaskId": "string",
    "subtaskType": 0,
    "specificItems": "string",
    "specificItemList": [
      "string"
    ],
    "isFinished": true,
    "finishedTime": "2023-02-23T02:33:35.003Z",
    "associatedNumber": "string"
  }
]
 */
export const getTaskSubTasksDetails = (taskId) => {
    return request({
        url: `api/Patient/Task/${taskId}/SubTasks`,
        method: "get",
    });
}