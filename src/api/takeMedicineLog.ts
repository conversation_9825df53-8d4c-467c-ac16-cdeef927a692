import { request } from "@/utils/axios";
/*服药日志相关API*/

/*获取当前受试者服药日志相关清单*/
export const getMedications = (data) => {
    return request({
        url: `api/Patient/MedicationLog`,
        method: "get",
        params: { ...data }
    });
};

/*创建服药日志
*/
export const postMedication = (data) => {
    return request({
        url: `api/Patient/MedicationLog`,
        method: "post",
        data
    });
};

/*获取药物列表*/
export const getDrugs = () => {
    return request({
        url: `/api/Patient/Drugs`,
        method: "get",
    });
};

/*删除服药记录*/
export const deleteMedication = (medicationLogId) => {
    return request({
        url: `api/Patient/${medicationLogId}`,
        method: "delete",
    });
};

/*获取某个服药日志已填写数据
*/
// export const getMedicationData = (medicationId) => {
//     return request({
//         url: `api/Patient/MedicationLog/${medicationId}/Data`,
//         method: "get",
//     });
// };

/*更新某个服药日志已填写数据
*/
export const putMedicationData = (data) => {
    return request({
        url: `api/Patient/MedicationLog`,
        method: "put",
        data
    });
};

/**新版服药列表 */
export const getDurgRecord = (params) => {
    return request({
        url: `/api/Patient/DurgRecord`,
        method: "get",
        params: params
    });
};
/*根据药物获取问卷列表
return [
  {
    "questTemplateId": "string",
    "questTemplateName": "string"
  }
]
*/
export const getDurgQuest = (drugId) => {
    return request({
        url: `/api/Patient/Drug/${drugId}/Quest`,
        method: "get",
        // params: params
    });
};
/*获取服药问卷的模板 数据 
questTemplateId
includeData 可以不传
*/
export const getDurgView = (questId,params) => {
    return request({
        url: `/api/Patient/Quest/${questId}/DurgView`,
        method: "get",
        params: params
    });
};
/*获取服药问卷的 数据 
questTemplateId
*/
export const getDurgData = (questId,params) => {
    return request({
        url: `/api/Patient/Quest/${questId}/DurgData`,
        method: "get",
        params: params
    });
};
export const putDurgData = (questId,data) => {
    return request({
        url: `/api/Patient/Quest/${questId}/DurgData`,
        method: "put",
        data
    });
};

/*获取服药问卷预生成数据*/
export const getDurgDataBeforehand = (questId,params) => {
    return request({
        url: `/api/Patient/Quest/${questId}/DurgData/Beforehand`,
        method: "get",
        params: params
    });
};

/*新增服药问卷*/
export const getCustomDrugQuest = (questTemplateId,patientId) => {
    return request({
        url: `/api/Patient/Quest/${questTemplateId}/${patientId}/CustomDrugQuest`,
        method: "get"
    });
};