import { request, requestFile } from "@/utils/axios";

// 获取入排问卷内容
/*
{
  "id": 0,
  "questCrfType": 0,
  "studyId": 0,
  "visitId": 0,
  "crfName": "string",
  "crfGuideline": "string",
  "finishStatus": 0,
  "questUrl": "string",
  "questCrfItemes": [
    {
      "id": 0,
      "dctCode": "string",
      "fieldCode": "string",
      "crfFieldType": 0,
      "crfFieldControl": 0,
      "refFieldCode": "string",
      "refItemValue": "string",
      "refType": 0,
      "fieldItems": [
        {
          "itemValue": "string",
          "itemName": "string",
          "itemContent": "string"
        }
      ],
      "fieldLabel": "string",
      "fieldDescription": "string",
      "children": [
        "string"
      ]
    }
  ]
}
*/
export const getIEGeneralInfo = (params) => {
  return request({
    url: "api/Patient/Quest/IEGeneralInfo",
    method: "get",
    params: params,
  });
};

/* 获取问卷是否只读，是否提交*/
export const getQuestInfo = (questId, params = {}) => {
  return request({
    url: `api/Patient/Quest/${questId}/QuestInfo`,
    method: "get",
    params: params
  });
};

/*获取问卷的模板
*/
export const getQuestView = (id) => {
  return request({
    url: `api/Patient/Quest/${id}/View`,
    method: "get",
  });
};

/*上传检查单等图片文件
 */
export const postQuestFile = (questId, dctCode, data) => {
  return requestFile(
    `api/Patient/Quest/File/${questId}/${dctCode}`,
    data,
    false
  );
};

/*删除单个图片文件
 */
export const deleteQuestFile = (fieldDataId) => {
  return request({
    url: `api/Patient/Quest/File/${fieldDataId}`,
    method: "delete",
  });
};

/*获取问卷的数据
*/
export const getQuestData = (id) => {
  return request({
    url: `api/Patient/Quest/${id}/Data`,
    method: "get",
  });
};

/*更新入排问卷信息
*/
export const putQuestData = (id, data) => {
  return request({
    url: `api/Patient/Quest/${id}/Data`,
    method: "put",
    data,
    showLayout: !data?.isStaging
  });
};

/*查询图片的Access URL*/
export const getAccessFile = (id) => {
  return request({
    url: `api/Patient/Quest/File/${id}`,
    method: "get",
  });
};

/** 获取展示的列表问卷
 * @param {number} id
 * @param {string} dctCode
 * @returns [
  {
    "questId": "string",
    "rowId": "string",
    "tableName": "string",
    "items": [
      {
        "dctCode": "string",
        "fieldLabel": "string",
        "fieldValue": "string",
        "fieldValueStr": "string",
        "dctQuestUnit": "string"
      }
    ]
  }
]
 */
export const getTableList = (id, dctCode) => {
  return request({
    url: `api/Patient/Quest/${id}/${dctCode}/Table/List`,
    method: "get",
  });
};

/** 获取某个列表问卷模板
 * @param {number} id
 * @param {string} dctCode
 * @returns {object}
 */
export const getTableView = (id, dctCode) => {
  return request({
    url: `api/Patient/Quest/${id}/${dctCode}/Table/View`,
    method: "get",
  });
};

/** 获取某个列表问卷答案
 * @param {number} id
 * @param {string} dctCode
 * @param {string} rowId
 * @returns {object}
 */
export const getTableData = (id, dctCode, rowId) => {
  return request({
    url: `api/Patient/Quest/${id}/${dctCode}/Table/${rowId}/Data`,
    method: "get",
  });
};

/** 修改某个列表问卷答案
 * @param {number} id
 * @param {string} dctCode
 * @param {string} rowId
 * @returns {object}
 */
export const putTableData = (id, dctCode, data) => {
  return request({
    url: `api/Patient/Quest/${id}/${dctCode}/Table/Data`,
    method: "put",
    data
  });
};

/** 删除某个列表问卷
 * @param {number} id
 * @param {string} dctCode
 * @param {string} rowId
 * @returns {Boolean}
 */
export const deleteTable = (id, dctCode, rowId) => {
  return request({
    url: `api/Patient/Quest/${id}/${dctCode}/Table/${rowId}`,
    method: "delete",
  });
};

/** 新增一个列表问卷
 * @param {number} id
 * @param {string} dctCode
 * @param {string} rowId
 * @returns {object}
 */
export const postTable = (id, dctCode) => {
  return request({
    url: `api/Patient/Quest/${id}/${dctCode}/Table`,
    method: "post",
  });
};
/** 检查筛选期问卷，是否全部完成，完成则标记为待入组
 * @param questId string
 * @returns Boolean
 */
export const posCheckVisit = (questId) => {
  return request({
    url: `api/Patient/Quest/${questId}/CheckVisit`,
    method: "post",
  });
};

// 清除暂存数据
export const postClearStagingData = (questId, isTable = false) => {
  return request({
    url: `api/Patient/Quest/${questId}/ClearStagingData?isTable=${isTable}`,
    method: "post",
    showLayout: false
  });
};

// 获取数据变动的内容
export const putPatientQuestChangeData = (questId, data) => {
  return request({
    url: `api/Patient/Quest/${questId}/PatientQuestChangeData`,
    method: "put",
    data
    // showLayout: false
  });
};
// 获取题目的澄清列表
export const getQuestClarifyListInfo = (questDataId, questId) => {
  return request({
    url: `api/Patient/Quest/${questDataId}/GetQuestClarifyListInfo/${questId}`,
    method: "get",
  });
};
// 获取题目的澄清 详情
export const getQuestClarifyInfo = (questDataId, questId) => {
  return request({
    url: `api/Patient/Quest/${questDataId}/GetQuestClarifyInfo/${questId}`,
    method: "get",
  });
};
// 新增
export const putAddClarify = (data) => {
  return request({
    url: `api/Patient/Quest/AddClarify`,
    method: "put",
    data
  });
}
// 回复澄清
export const putAddClarifyReply = (data) => {
  return request({
    url: `api/Patient/Quest/AddClarifyReply`,
    method: "put",
    data
  });
}
// 关闭澄清
export const putCloseQuestClarify = (data) => {
  return request({
    url: `api/Patient/Quest/CloseQuestClarify`,
    method: "put",
    data
  });
}

/*检测是否 有列表问卷必填 没完成*/
export const postPatientQuestListCheck = (questId, data) => {
  return request({
    url: `api/Patient/Quest/${questId}/CheckQuestDataRequired`,
    method: "post",
    data
  });
}

/*重新填写知情问卷，患者状态更改*/
export const postConsentSupplementComfirm = (data) => {
  return request({
      url: `api/Patient/ICFGeneralInfo/Supplement/Comfirm`,
      method: "post",
      data
  });
};
/**
 * 同意/拒绝打开计划外窗口
 * @param data
 * @returns
 */
export const postOprtQuestUnplannedWindow = (data) => {
  return request({
    url: '/api/Doctor/Patient/OprtQuestUnplannedWindow',
    method: 'post',
    data
  });
};

/**
 * 获取计划外窗口信息
 * @param questId
 * @returns
 */
export const getQuestUnPlannedWindow = (questId, params = {}) => {
  return request({
    url: `/api/Doctor/Patient/${questId}/QuestUnPlannedWindow`,
    method: 'get',
    params: params
  });
};