import { request } from "@/utils/axios";

/*
获取app未读消息条数
*/
export const getUnReadCount = async () => {
    let count = '';
    let resCount = await request({
        url: "api/Home/Msg/UnReadCount?product=patient",
        method: "get",
    });
    resCount = resCount as number;
    if (resCount > 99) {
        count = 99 + '+';
    } else if (resCount != 0) {
        count = resCount + '';
    }
    return count;
};