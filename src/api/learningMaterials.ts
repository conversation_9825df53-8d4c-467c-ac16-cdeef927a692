import { request } from "@/utils/axios";

/*学习资料
{
    pageIndex,
    pageSize
}
"items": [
    {
      "id": "string",
      "studyId": "string",
      "userType": 0,
      "fileType": 0,
      "trianingTitle": "string",
      "minimumTrainingTime": 0,
      "trainingFilePath": "string",
      "trainingFileName": "string",
      "trainingFileUrl": "string",
      "videoCoverFilePath": "string",
      "videoCoverFileName": "string",
      "videoCoverUrl": "string"
    }
  ],
  "totalItemCount": 0
*/
export const getTrainings = (params) => {
    return request({
        url: `api/Patient/Training`,
        method: "get",
        params: {...params}
    });
};
// 获取详情页数据
export const getTrainingsDetails = (trainingId) => {
  return request({
      url: `api/Patient/Training/${trainingId}`,
      method: "get"
  });
};

// 记录学习资料和学习开始的时间
export const getTrainingsStartTime = (trainingId, ruleId) => {
  return request({
    url: `api/Patient/Training/${trainingId}/${ruleId}/StartTime`,
    method: "post",
    showLayout: false
  })
}

// 更新学习资料的学习结束时间
export const getTrainingsEndTime = (recordId) => {
  return request({
    url: `api/Patient/Training/${recordId}/EndTime`,
    method: "post",
    showLayout: false
  })
}

// 获取全部的学习资料标签
export const getTrainingDocumentTag = (studyId) => {
  return request({
    url: `api/Operator/Study/${studyId}/TrainingDocument/Tag`,
    method: "get",
  });
};