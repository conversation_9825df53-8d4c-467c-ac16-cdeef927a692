import { request } from "@/utils/axios";

/**获取受试者余额信息
 * @param {string} patientId
 * @returns {
  "id": "string",
  "amount": 0,
  "freezeAmount": 0,
  "withdrawalAmount": 0
}
 */
export const getPatientFinance = (patientId) => {
    return request({
        url: `api/Patient/Compensation/${patientId}/PatientFinance`,
        method: "get",
        // params: params
    });
};

/**获取银行账户信息
 * @param {string} patientId
 * @returns {
  "id": "string",
  "bank": "string",银行
  "cardNumber": "string",银行卡号
  "openAccountBank": "string",开户支行
  "accountName": "string",户名
  "idCard": "***************"身份证号
}
 */
export const getBankAccount = (patientId) => {
    return request({
        url: `api/Patient/Compensation/${patientId}/BankAccount`,
        method: "get",
        // params: params
    });
};

/**保存银行账户信息
 * @param {string} patientId
 * @param {
  "id": "string",
  "bank": "string",银行
  "cardNumber": "string",银行卡号
  "openAccountBank": "string",开户支行
  "accountName": "string",户名
  "idCard": "***************"身份证号
}
  * @returns boolean
 */
export const postBankAccount = (patientId,data) => {
    return request({
        url: `api/Patient/Compensation/${patientId}/BankAccount`,
        method: "post",
        data
    });
};

/**获取受试者余额提现订单分页列表
 * @param {string} patientId
 * @param {number} pageIndex
 * @param {number} pageSize
 * @returns
 */
export const getFinanceWithdrawalOrder = (patientId,params) => {
    return request({
        url: `api/Patient/Compensation/${patientId}/PatientFinance/WithdrawalOrder`,
        method: "get",
        params: params
    });
};

/**保存或流转受试者余额提现订单
 * @param {string} patientId
 * @param {string} orderId
 * @returns
 */
export const postFinanceWithdrawalOrder = (patientId,data) => {
    return request({
        url: `api/Patient/Compensation/${patientId}/PatientFinance/WithdrawalOrder`,
        method: "post",
        data
    });
};

/**获取受试者余额提现订单详情
 * @param {string} patientId
 * @param {string} orderId
 * @returns
 */
export const getFinanceWithdrawalOrderItem = (patientId,orderId) => {
    return request({
        url: `api/Patient/Compensation/${patientId}/PatientFinance/WithdrawalOrder/${orderId}`,
        method: "get",
        // params: params
    });
};

/**获取受试者余额变动明细
 * @param {string} patientId
 * @param {number} pageIndex
 * @param {number} pageSize
 * @returns {
  "items": [
    {
      "id": "string",
      "amount": 0,
      "changeType": 0,
      "changeTypeName": "string",
      "orderNo": "string",
      "remark": "string",
      "createTime": "string"
    }
  ],
  "totalItemCount": 0
}
 */
export const getFinanceChangeLog = (patientId,params) => {
    return request({
        url: `api/Patient/Compensation/${patientId}/PatientFinance/ChangeLog`,
        method: "get",
        params: params
    });
};

/**获取银行列表
 * @param {string} key
 * @param {number} pageSize
 * @returns [
  {
    "bankCode": "string",
    "bankName": "string"
  }
]
 */
export const getBankInfoList = (params) => {
    return request({
        url: `api/Patient/Compensation/BankInfo`,
        method: "get",
        params: params
    });
};

/**根据卡号获取银行信息
 * @param {number} accountNumber
 * @returns [
  {
    "bankCode": "string",
    "bankName": "string"
  }
]
 */
export const getAccountNumberBankInfo = (accountNumber) => {
    return request({
        url: `api/Patient/Compensation/BankInfo/${accountNumber}`,
        method: "get",
        // params: params
    });
};

/*获取流程
 * @param {string} patientId
 * @param {string} bizOrderType 费用补偿1 余额提现2
 * @returns [
  {
    "detailName": "string",
    "detailCode": "string",
    "approver": "string",
    "requestDate": "string",
    "aprStatus": "string",
    "aprBizType": "string",
    "aprBizStatus": "string",
    "comment": "string",
    "remark": "string"
  }
]
*/
export const getAPRRuntimeLog = (patientId, bizOrderType) => {
    return request({
        url: `api/Patient/Compensation/${patientId}/APRRuntimeLog/${bizOrderType}`,
        method: "get",
    });
};

/**根据卡号获取银行的支行
 * @param {number} accountNumber
 * @param {number} page 
 * @param {string} keyword
 * @returns [
  {
    "bank": "string",
    "abbr": "string",
    "bankCode": "string",
    "lName": "string",
    "city": "string",
    "province": "string",
    "addr": "string",
    "bankLogo": "string",
    "tel": "string"
  }
]
 */
export const getBankInfoSubbranch = (params) => {
    return request({
        url: `api/Patient/Compensation/BankInfo/Subbranch`,
        method: "get",
        params: params
    });
};

export const getBankAccountVerify = (patientId, data) => {
  return request({
    url: `api/Patient/Compensation/${patientId}/BankAccount/Verify`,
    method: "post",
    data: { ...data },
  });
};