import { request } from "@/utils/axios";

/**
 * 我的订单
 * @param pageIndex
 * @param pageSize
 * @returns {
  "items": [
    {
      "id": "string",
      "orderNo": "string",
      "waybillNo": "string",
      "logisticsProvider": "string",
      "logisticsProviderLogo": "string",
      "state": "string",
      "createTime": "string"
    }
  ],
  "totalItemCount": 0
}
 */
export const getMyLogisticsOrder = (params) => {
    return request({
        url: `api/Patient/MyLogisticsOrder`,
        method: "get",
        params: params
    });
};
/**
 * 我的订单-最新
 * @param pageIndex
 * @param pageSize
 * @returns {
  "items": [
    {
      "id": "string",
      "orderNo": "string",
      "waybillNo": "string",
      "logisticsProvider": "string",
      "orderType":	"number",
      "orderState":	"string",
      "createTime": "string"
      "createTimeDisplay": "string",
      "materials": "Array",
    }
  ],
  "totalItemCount": 0
}
 */
export const getMyMaterialOrder = (params) => {
  return request({
    url: `api/Patient/MyMaterialOrder`,
    method: "get",
    params: params
  });
};

/**
 * 物流订单详情
 * @param orderId
 * @returns {
  "id": "string",
  "orderNo": "string",
  "waybillNo": "string",
  "creator": "string",
  "createTime": "2023-02-20T05:56:19.074Z",
  "lastUpdator": "string",
  "lastUpdateTime": "2023-02-20T05:56:19.074Z",
  "state": 0,
  "waybillRecords": [
    {
      "id": "string",
      "waybillTime": "string",
      "logisticsInfo": "string"
    }
  ],
  "patientId": "string",
  "dctStudyId": "string",
  "dctSiteId": "string",
  "dctStudyVersionNumber": "string",
  "omsProjectId": 0,
  "goodsTypeName": "string",
  "logisticsProvider": "string",
  "shipperName": "string",
  "shipperPhoneNo": "string",
  "shipperProvinceCode": "string",
  "shipperProvinceName": "string",
  "shipperCityCode": "string",
  "shipperCityName": "string",
  "shipperAreaCode": "string",
  "shipperAreaName": "string",
  "shipperAddressDetail": "string",
  "rcptName": "string",
  "rcptPhoneNo": "string",
  "rcptProvinceCode": "string",
  "rcptProvinceName": "string",
  "rcptCityCode": "string",
  "rcptCityName": "string",
  "rcptAreaCode": "string",
  "rcptAreaName": "string",
  "rcptAddressDetail": "string",
  "shippingMethod": "string",
  "expectPickupTime": "2023-02-20T05:56:19.074Z",
  "remark": "string"
}
 */
export const getLogisticsOrder = (orderId) => {
    return request({
        url: `api/Patient/LogisticsOrder/${orderId}`,
        method: "get",
        // params: params
    });
};
/**
 * 物资订单详情
 * @param orderId
 * @returns 
 */
export const getMaterialOrder = (orderId) => {
  return request({
    url: `api/Patient/MaterialOrder/${orderId}`,
    method: "get",
  });
};

/**
 * 创建订单
 * @param {
  "patientId": "string",
  "dctStudyId": "string",
  "dctSiteId": "string",
  "dctStudyVersionNumber": "string",
  "omsProjectId": 0,
  "goodsTypeName": "string",
  "logisticsProvider": "string",
  "shipperName": "string",
  "shipperPhoneNo": "string",
  "shipperProvinceCode": "string",
  "shipperProvinceName": "string",
  "shipperCityCode": "string",
  "shipperCityName": "string",
  "shipperAreaCode": "string",
  "shipperAreaName": "string",
  "shipperAddressDetail": "string",
  "rcptName": "string",
  "rcptPhoneNo": "string",
  "rcptProvinceCode": "string",
  "rcptProvinceName": "string",
  "rcptCityCode": "string",
  "rcptCityName": "string",
  "rcptAreaCode": "string",
  "rcptAreaName": "string",
  "rcptAddressDetail": "string",
  "shippingMethod": "string",
  "expectPickupTime": "2023-02-20T05:58:51.855Z",
  "remark": "string"
}
 */
export const postLogisticsOrder = (data) => {
    return request({
        url: `api/Patient/LogisticsOrder`,
        method: "post",
        data
    });
};
/**
 * 修改订单
 * @param {
  "id": "string",
  "orderNo": "string",
  "waybillNo": "string",
  "creator": "string",
  "createTime": "2023-02-20T06:00:11.792Z",
  "lastUpdator": "string",
  "lastUpdateTime": "2023-02-20T06:00:11.792Z",
  "state": 0,
  "waybillRecords": [
    {
      "id": "string",
      "waybillTime": "string",
      "logisticsInfo": "string"
    }
  ],
  "patientId": "string",
  "dctStudyId": "string",
  "dctSiteId": "string",
  "dctStudyVersionNumber": "string",
  "omsProjectId": 0,
  "goodsTypeName": "string",
  "logisticsProvider": "string",
  "shipperName": "string",
  "shipperPhoneNo": "string",
  "shipperProvinceCode": "string",
  "shipperProvinceName": "string",
  "shipperCityCode": "string",
  "shipperCityName": "string",
  "shipperAreaCode": "string",
  "shipperAreaName": "string",
  "shipperAddressDetail": "string",
  "rcptName": "string",
  "rcptPhoneNo": "string",
  "rcptProvinceCode": "string",
  "rcptProvinceName": "string",
  "rcptCityCode": "string",
  "rcptCityName": "string",
  "rcptAreaCode": "string",
  "rcptAreaName": "string",
  "rcptAddressDetail": "string",
  "shippingMethod": "string",
  "expectPickupTime": "2023-02-20T06:00:11.792Z",
  "remark": "string"
}
 */
export const putLogisticsOrder = (data) => {
    return request({
        url: `api/Patient/LogisticsOrder`,
        method: "put",
        data
    });
};

/**
 * 获取寄送物品类型
 * @param 
 * @returns 
 */
export const getGoodsType = () => {
  return request({
      url: `api/Patient/GoodsType`,
      method: "get",
      // params: params
  });
};

/** 取消物流订单
 * @param orderId
*/
export const putCancelLogisticsOrder = (orderId) => {
  return request({
      url: `api/Patient/CancelLogisticsOrder/${orderId}`,
      method: "put",
      // data
  });
};
/** 取消物资订单
 * @param orderId
*/
export const putCancelMaterialOrderOrder = (orderId) => {
  return request({
    url: `api/Patient/CancelMaterialOrderOrder/${orderId}`,
    method: "put",
  });
};
//
export const getLogisticsProviderAndArgument = (params) => {
  return request({
      url: `api/Patient/LogisticsProviderAndArgument`,
      method: "get",
      params: params
  });
};