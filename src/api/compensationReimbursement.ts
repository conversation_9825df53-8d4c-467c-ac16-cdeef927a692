import { request, requestFile } from "@/utils/axios";

/**获取补偿报销金额统计
 * -1 = 未知, 0 = 待确认, 1 = 处理中, 2 = 已处理, 3 = 已拒绝, 5 = 已退回或拒绝
 * @param {string} patientId
 * @returns {
    "applyAmount": 0,
    "dealedAmount": 0,
    "withdrawalAmount": 0
  }
 */
export const getCompensationAmountSummary = (patientId) => {
    return request({
        url: `api/Patient/Compensation/${patientId}/CompensationAmount/Summary`,
        method: "get",
    });
};
/**获取补偿报销
 * -1 = 未知, 0 = 待确认, 1 = 处理中, 2 = 已处理, 3 = 已拒绝, 5 = 已退回或拒绝
 * @param {number} Status
 * @param {number} pageIndex
 * @param {number} pageSize
 * @returns 
 */
export const getPeriodicCompensationApplyList = (params) => {
    return request({
        url: `api/Operator/Compensation/PeriodicCompensationApplyList`,
        method: "get",
        params: params // get传参不可带请求体参数-后续看改成post还是JSON.stringfly(data)拼接url后
    });
};

/**获取补偿报销申请信息
 * @param {string} ApplyId
 * @param {string} patientId
 * @returns 
 */
export const getPeriodicCompensationApplyListInfo = (params) => {
    return request({
        url: `api/Operator/Compensation/${params.patientId}/PeriodicCompensationApply`,
        method: "get",
        params: params // get传参不可带请求体参数-后续看改成post还是JSON.stringfly(data)拼接url后
    });
};

/*公用的上传文件接口
 * @param {string} businessType
 * @param {string} fileType
 * @param {string} patientId
 * @returns 
*/
export const postCommonFile = (patientId, businessType, fileType, data) => {
    return requestFile(
        `api/Home/File?patientId=${patientId}&fileType=${fileType}&businessType=${businessType}`,
        data
    )
}

/**保存补偿报销申请金额验证
 * @param {string} patientId
 * @param {string} studyId
 * @param {string} siteId
 * @param {string} id
 * @returns 
 */
export const putValidatePeriodicCompensationApply = (data) => {
    return request({
        url: `api/Operator/Compensation/ValidatePeriodicCompensationApply`,
        method: "put",
        data
    });
};

/**保存补偿报销申请
 * @param {string} patientId
 * @param {string} studyId
 * @param {string} siteId
 * @param {string} id
 * @returns 
 */
export const putPeriodicCompensationApply = (data) => {
    return request({
        url: `api/Operator/Compensation/PeriodicCompensationApply`,
        method: "put",
        data
    });
};

/**保存补偿报销申请创建 -》 访视问卷保存完调用
 * @param {string} patientId
 * @param {string} visitId
 * @param {string} questId
 * @returns 
 */
export const putAutoPeriodicCompensationApply = (patientId, questId) => {
    return request({
        url: `api/Operator/Compensation/${patientId}/AutoPeriodicCompensationApply?QuestId=${questId}`,
        method: "put",
    });
};

/**双端 是否可申请补偿报销
 * @param {string} StudyId
 * @param {string} SiteId
 * @returns {
  "doctorAuth": true,
  "patientAuth": true
}
 */
export const getPeriodicCompensationApplyAuth = (StudyId,SiteId) => {
    return request({
        url: `api/Operator/Compensation/${StudyId}/${SiteId}/PeriodicCompensationApplyAuth`,
        method: "get",
    });
};