import { request } from "@/utils/axios";

// 获取 pageIndex1 pageSize 20   filter是否筛选自己提交的问卷boolean
export const getTobeSignedQuest = (patientId, params) => {
    return request({
        url: `api/Patient/${patientId}/TobeSignedQuest`,
        method: "get",
        params: params
    });
};

// 问卷签名
// {
// "base64File": "string",
// "questId": "string",
// "purposeOfQuestMiddleTable": 1 // 0 = 未知, 1 = 问卷签名
// }
export const psotSignatureFile = (data) => {
    return request({
        url: `api/Patient/SignatureFile`,
        method: "post",
        data
    });
};
// 问卷 签署和审阅
// {
//     "base64File": "string",
//     "list": [
//       {
//         "questId": "string",
//         "questMideleTableStatus": 0,
//         "purposeOfQuestMiddleTable": 0,
//         "signtureFileUrl": "string",
//         "remark": "string",
//         "rowNumber": 0,
//         "base64File": "string"
//       }
//     ]
//   }
export const psotQuestSigntureAndReview = (data) => {
    return request({
        url: `api/Patient/QuestSigntureAndReview`,
        method: "post",
        data
    });
};
// 验证签名图片是否与系统中保存的患者姓名一致
// {
// "base64File": "string",
// }
export const psotValidSignaturePic = (data) => {
    return request({
        url: `api/Patient/ValidSignaturePic`,
        method: "post",
        data
    });
};
/*获取签名配置
PurposeOfSignature
 签名用途0 = 未知, 1 = 问卷填写签名, 2 = 问卷审阅签名
*/
export const getQuestSigntureSet = (studyId, params) => {
    return request({
        url: `api/patient/${studyId}/QuestSigntureSet`,
        method: "get",
        params: params
    });
};
