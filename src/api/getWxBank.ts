import { requestWX } from "@/utils/axios";

export const getWXToken = (params) => {
    return requestWX({
        url: `/wxapi/cgi-bin/token`,
        method: "get",
        params: params
    });
};

export const postWXBanklist = (params,data) => {
    return requestWX({
        url: `/wxapi/shop/funds/getbanklist`,
        method: "post",
        params: params,
        data
    });
};
// shop/funds/getbankbynum
export const postWXBankByNum = (params,data) => {
    return requestWX({
        url: `/wxapi/shop/funds/getbankbynum`,
        method: "post",
        params: params,
        data
    });
};