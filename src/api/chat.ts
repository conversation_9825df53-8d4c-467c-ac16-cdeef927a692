import { request, requestFile, requestFileA } from "@/utils/axios";

/*当前受试者全部的聊天记录
 */
// export const getChat = () => {
//     return request({
//         url: `api/Patient/Chat`,
//         method: "get",
//     });
// };

// 获取患者聊天记录
export const getPatientChattingRecords = (siteId, pageIndex, pageSize) => {
  return request({
    url: `api/Patient/Chat/${siteId}/PatientChattingRecords`,
    method: "get",
    params: {
      pageIndex,
      pageSize,
    },
  });
};

/*发送聊天文本记录
{content: 'xx文本'}
*/
export const postChatText = (siteId, data) => {
  return request({
    url: `api/Patient/Chat/${siteId}/PatientChattingAsync`,
    method: "post",
    data,
  });
};

/*发送聊天图片记录
 */
export const postChatImage = (siteId, isNotify, data) => {
  return requestFile(
    `api/Patient/Chat/${siteId}/PatientImgChattingAsync?isNotify=${isNotify}`,
    data
  );
};

/*发送语音文本记录
{
  "base64Text": "string"
}
*/
// export const postChatAudio = (data) => {
//     return requestFile(
//         `api/Patient/Chat/audioBase64`,
//         data
//     );
// };

export const getTest = () => {
  return request({
    url: `api/Patient/Compensation/CheckCAValidation`,
    method: "post",
  });
};

export const getCAResult = (bizId) => {
  return request({
    url: `api/Patient/Compensation/${bizId}/GetCAResult`,
    method: "post",
  });
};
/*发送语音
audioDuration: 音频时长,
audioFile (formData)
 */
export const patientAudioChattingAsync = (siteId, audioDuration, data) => {
  return requestFileA(
    `api/Patient/Chat/${siteId}/PatientAudioChattingAsync/${audioDuration}`,
    data
  );
};
/*获取语音识别结果接口（GET）*/
export const getSpeechRecognition = (chattingRecordItemId) => {
  return request({
    url: `/api/Patient/Chat/${chattingRecordItemId}/SpeechRecognition`,
    method: "get",
  });
};
/*消息撤回接口（PUT）*/
export const withdrawChatMessage = (chattingRecordItemId) => {
  return request({
    url: `/api/Patient/Chat/${chattingRecordItemId}/Withdraw`,
    method: "put",
  });
};
/*获取未读记录*/
export const getNoReadDoctorChattingRecords = (siteId, params) => {
  return request({
    url: `/api/Patient/Chat/${siteId}/NoReadPatientChattingRecords`,
    method: "get",
    params: params
  });
};