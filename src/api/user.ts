import { request, requestFile } from "@/utils/axios";

// 使用微信code登录 -->
/*
  avatarUrl: "" //头像 url
  expiredTime: "0001-01-01T00:00:00" //登陆的失效时间
  inGroupDay: "2021-01-09" //入组日期
  patientNum: "001-001" /病例编号
  patientStatus: 1 //入组受试者状态
  token: ""
*/ 
export const getOpenId = (id) => {
  return request({
    url: "api/Patient/LoginByCode?code="+id,
    method: "get",
  });
};
// 获取课题介绍信息
/*
title 课题名称
projectCode	 项目代号(全国统一代号)
projectContent	 项目介绍信息, 可能包括图片什么的, 采用富文本的信息.
*/
export const getStudyIntroduction = () => {
  return request({
    url: "api/Patient/StudyIntroduction",
    method: "get",
  });
};
// 获取隐私协议
export const getPrivacy = () => {
  return request({
    url: "api/Patient/StudyPrivacy",
    method: "get",
  });
};
// 获取服务条款
export const getServiceLevelAgreement = () => {
  return request({
    url: "api/Patient/StudyServiceLevelAgreement",
    method: "get",
  });
};
/*发送手机验证码
{
  "mobile": "string"
}
*/
export const postSentVerifyCode = (data) => {
  return request({
    url: "api/Patient/Mobile/SentVerifyCode",
    method: "post",
    data
  });
};
/*验证手机号
{
  "mobile": "string",
  "verifyCode": "string"
}
*/
export const postVerifyCode = (data) => {
  return request({
    url: "api/Patient/Mobile/VerifyCode",
    method: "post",
    data
  });
};
/*修改手机号
{
  "mobile": "string",
  "verifyCode": "string"
}
*/
export const postVerifyCodeModifyMobile = (data) => {
  return request({
    url: "api/Patient/Mobile/VerifyCode/ModifyMobile",
    method: "post",
    data
  });
};
/**
 * 获取视频
 * @returns 
 */
export const getStudyIntroduceVideo = (params) => {
  return request({
    url: "api/Patient/StudyIntroduceVideo",
    method: "get",
    params: params,
  });
};
// 获取知情同意书
export const getIcf = (params) => {
  return request({
    url: "api/Patient/StudyICF",
    method: "get",
    params: params,
  });
};
// 保存用户已读完时间知情同意书
export const postUpdatePatientStatus = (data) => {
  return request({
    url: "api/Patient/UpdatePatientStatus",
    method: "post",
    data
  });
};
// 获取登录状态
export const getPatientStatus = (params) => {
  return request({
    url: "api/Patient/PatientStatus",
    method: "get",
    params: params,
  });
};
// 获取受试者个人申明文案
export const getStatement = (params) => {
  return request({
    url: "api/Patient/Statement",
    method: "get",
    params: params,
  });
};
// 上传视频
export const postVideoFile = (data,statementFileId) => {
  return requestFile(
    `api/Patient/Statement/VideoFile/${statementFileId}`,
    data,
    false
  );
};
// 上传身份证
export const postIdentityCard = (data,statementFileId) => {
  return requestFile(
    `api/Patient/Statement/IdentityCard/${statementFileId}`,
    data,
    // false
  );
};
// 删除身份证
export const deleteIdentityCard = (statementFileId) => {
  return request({
    url: `api/Patient/Statement/IdentityCard/${statementFileId}`,
    method: "delete"
  });
};
// 保存视频
export const postVideoComfirm = (statementFileId,data) => {
  return request({
    url: `api/Patient/Statement/${statementFileId}/VideoComfirm`,
    method: "post",
    data
  });
};
// 删除视频
export const deleteVideoFile = (statementFileId) => {
  return request({
    url: `api/Patient/Statement/VideoFile/${statementFileId}`,
    method: "delete"
  });
};
// 发送签署申明的手机验证码
export const postStatementSentVerifyCode = (data) => {
  return request({
    url: "api/Patient/Statement/Mobile/SentVerifyCode",
    method: "post",
    data
  });
};
// 验证签署申明
export const postStatementVerifyCode = (data) => {
  return request({
    url: "api/Patient/Statement/Mobile/VerifyCode",
    method: "post",
    data
  });
};
/*获取受试者手机号
*/ 
export const getStatementMobile = (params) => {
  return request({
    url: "api/Patient/Statement/Mobile",
    method: "get",
    params: params,
  });
};
/*获取是绑定手机号 或者 受试者信息登记
*/ 
export const getMobileFlow = () => {
  return request({
    url: "api/Patient/Mobile/Flow",
    method: "get",
  });
};
/** 获取知情list
 ** @return {
  "dctStudyId": "string",
  "dctStudyName": "string",
  "patientICFStatements": [
    {
      "id": "string",
      "dctStudyICFInfoId": "string",
      "icfVersionNumber": "string",
      "icfVersionDate": "string",
      "effectiveDate": "string",
      "isFirstICF": true,
      "icfStatus": 0,
      "icfStatusName": "string",
      "status": "string",
      "dctPatientId": "string",
      "dctSiteId": "string",
      "patNumber": "string",
      "avatarUrl": "string",
      "patientStatus": 0,
      "inGroupDate": "string",
      "sex": "string",
      "group": "string",
      "groupStatus": 0,
      "groupStatusName": "string"
    }
  ]
}
*/ 
// export const getSummary = () => {
//   return request({
//     url: "api/Patient/Statement/Summary",
//     method: "get",
//   });
// };
export const getStatements = () => {
  return request({
    url: "api/Patient/Statements",
    method: "get",
  });
};
/*获取某个知情*/
export const getSummaryDetail = (statementTemperatureID) => {
  return request({
    url: `api/Patient/Statement/Detail/${statementTemperatureID}`,
    method: "get",
  });
};
/*绑定受试者 发验证码*/
export const postBindingInfoSentVerifyCode = (data) => {
  return request({
    url: `api/Patient/BindingInfo/SentVerifyCode`,
    method: "post",
    data
  });
};
/*验证手机号*/
// export const postBindingInfoVerifyCode = (data) => {
//   return request({
//     url: `api/Patient/BindingInfo/VerifyCode`,
//     method: "post",
//     data
//   });
// };
/*绑定用户*/
export const putBindingInfo = (data) => {
  return request({
    url: `api/Patient/BindingInfo`,
    method: "put",
    data
  });
};
// 埋点
export const patientLoginLog = () => {
  return request({
    url: `api/Patient/LoginLog`,
    method: "post",
  });
};

// 知情没绑定过走这个
export const getGetICFInfo = (params) => {
  return request({
    url: `api/Patient/Identity/GetICFInfo`,
    method: "get",
    params: params
  });
};

// 家属绑定信息
export const postPatientMemberIdentityVerifyCode= (data) => {
  return request({
    url: `api/Patient/PatientMemberIdentity/VerifyCode`,
    method: "post",
    data: data
  });
};

/*获取会议数据
  @params {ICFStatementID} 
*/ 
export const getConference = (params) => {
  return request({
    url: `api/Patient/Conference`,
    method: "get",
    params: params
  });
};

// 获取dctStudyId
export const getPatientStudyId = () => {
  return request({
    url: "api/Patient/GetPatientStudyId",
    method: "get",
  });
};

// interface IdentityVerificationParams {
//   studyId: string;  // Required parameter (course ID)
//   configType: number;  // 配置类型 1 患者端 2 研究者端
//   triggerEvent: number;  // 触发事件 1 访问患者端 2 参加会议
// }
// 是否需要核验
export const getIdentityVerificationResult = (params) => {
  return request({
    url: `/api/Doctor/Patient/${params.studyId}/IdentityVerification/Result`,
    method: "get",
    params: params
  });
};

// interface QuestResultParams {
//   studyId: string;  // 课程ID
//   bizType: number;   // 操作类型，17表示受试者签署问卷，18表示研究者审批问卷
// }
// 获取问卷签署审阅 核验方式
export const getQuestResult = (params) => {
  return request({
    url: `/api/Doctor/Patient/${params.studyId}/IdentityVerification/QuestResult`,
    method: "get",
    params: {
      bizType: params.bizType,
    }
  });
};

// 身份核验 发送手机验证码
// {
// "mobile": "17456152417"
// }
export const postIdentityVerificationSentVerifyCode = (data) => {
  return request({
    url: `/api/Doctor/Patient/IdentityVerification/SentVerifyCode`,
    method: "post",
    data
  });
};
// 身份核验 确定手机验证码
// {
//   "mobile": "14905771654",
//   "verifyCode": "string",
//   "bizType": 16
// 0 = 未知, 1 = 物资回收, 2 = 费用补偿, 
// 3 = 余额提现, 4 = 项目款, 5 = 知情同意, 6 = 知情签名, 
// 7 = 知情声明, 8 = 身份认证, 9 = 家属银行卡, 10 = 线下知情文件, 
// 11 = 研究者入会, 12 = 受试者入会, 13 = 受试家属入会, 
// 14 = 修改问卷澄清PDF, 15 = 广告位, 16 = 受试者访问患者端, 
// 17 = 受试者签署问卷, 18 = 研究者审阅问卷
// }
export const postVerifyMobile = (studyId, data) => {
  return request({
    url: `/api/Doctor/Patient/${studyId}/IdentityVerification/VerifyMobile`,
    method: "post",
    data
  });
};

// 二要素实名认证 /VerifyIdentityCard
export const postVerifyIdentityCard = (studyId, data) => {
  return request({
    url: `/api/Doctor/Patient/${studyId}/IdentityVerification/VerifyIdentityCard`,
    method: "post",
    data
  });
};
// 获取人脸验证地址 /VerifyFace
export const postVerifyFace = (studyId, data) => {
  return request({
    url: `/api/Doctor/Patient/${studyId}/IdentityVerification/VerifyFace`,
    method: "post",
    data
  });
};

// 身份认证结果查询
export const getFacialIdentityResult = (params) => {
  return request({
    url: `/api/Doctor/Patient/${params.studyId}/IdentityVerification/FacialIdentityResult`,
    method: "get",
    params: params
  });
};


export const getStudyTerm = (studyId) => {
  return request({
    url: `api/patient/${studyId}/StudyTerm`,
    method: "get"
  });
};
/* 标记已读：隐私协议/服务条款 
 TextType: StudyPrivacy = 2, StudySLA = 3,
*/ 
export const postUpdateStudyTermRecord = (TextType) => {
  return request({
    url: `/api/patient/UpdateStudyTermRecord/${TextType}`,
    method: "post",
  });
};
