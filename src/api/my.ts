import { request } from "@/utils/axios";
import { requestFile } from "@/utils/axios";

/*常见问题
planDate 计划时间:string
activeName 活动的名字:string
patientActiveStatus	:number病例的活动状态类型
0 = 未知, 1 = 未完成, 2 = 需完成, 3 = 待完成, 4 = 已完成
patientActiveStatusText	 任务的状态文本:string
actualDate	实际完成的日期 :string状态未已完成时才需要.
*/
export const getFAQ = () => {
    return request({
        url: `api/Patient/FAQ`,
        method: "get",
    });
};

// 获取所有的城市
export const getCitys = () => {
    return request({
        url: `api/Home/city`,
        method: "get",
    });
};

// 获取我的地址信息
export const getMyAddress = () => {
    return request({
        url: `api/Patient/PatientAddresses`,
        method: "get",
    });
};

//更新地址信息
export const putMyAddress = (data) => {
    return request({
        url: `api/Patient/PatientAddressInfo`,
        method: "post",
        data
    });
};

// 获取我的订单信息
export const getMyOrder = () => {
    return request({
        url: `api/Patient/Order`,
        method: "get",
    });
};

// 获取患者的礼品清单
export const getPatientGiftList = () => {
    return request({
        url: `api/Patient/PatientGiftList`,
        method: "get",
    });
};

/*保存礼品申请
[
  {
    "giftId": "string",
    "applyQty": 0
  }
]
*/
export const postPatientGiftApplyInfoAsync = (data) => {
    return request({
        url: `api/Patient/PatientGiftApplyInfoAsync`,
        method: "post",
        data
    });
};

// 获取患者的药物列表
export const drugApplication = (params) => {
    return request({
        url: `api/Patient/Drugs`,
        method: "get",
        params: params
    });
};

// 获取患者的药物列表
export const drugDrugApply = (data) => {
    return request({
        url: `api/Patient/DrugApply`,
        method: "post",
        data
    });
};

// 获取二维码
export const getPatientQrCode = (params) => {
    return request({
        url: `api/Doctor/PatientQrCode`,
        method: "get",
        params: params
    });
};

// 获取受试者家属信息
export const getPatientMembers = (patientId) => {
    return request({
        url: `api/Doctor/Patient/${patientId}/PatientMembers`,
        method: "get"
    });
};

// 移除受试者家属
export const postRemovePatientMembers = (memberId) => {
    return request({
        url: `api/Doctor/Patient/${memberId}/RemovePatientMembers`,
        method: "post"
    });
};
// 获取版本号Doctor Patient
export const getCurrentVersion = (params) => {
    return request({
        url: `api/Patient/CurrentVersion`,
        method: "get",
        params: params
    });
};
// 上传用户系统使用反馈信息 SiteQrCode
export const SystemFeedbackInfoAsync = (data) => {
    return requestFile(
        `api/Patient/SystemFeedbackInfoAsync`,
        data,
    );
};