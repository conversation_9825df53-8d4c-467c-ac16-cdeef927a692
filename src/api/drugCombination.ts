import { request } from "@/utils/axios";
/*合并用药-改名为(其它药物)相关API*/

/*获取合并用药清单*/
export const getCMs = (data) => {
    return request({
        url: `api/Patient/CM`,
        method: "get",
        params: { ...data }
    });
};

/*创建合并用药*/
export const postCM = () => {
    return request({
        url: `api/Patient/CM`,
        method: "post",
    });
};

/*获取某个合并用药*/
export const getCM = () => {
    return request({
        url: `api/Patient/CM/View`,
        method: "get",
    });
};

/*删除合并用药*/
export const deleteCM = (cmId) => {
    return request({
        url: `api/Patient/CM/${cmId}`,
        method: "delete",
    });
};

/*获取某个合并用药已填写数据*/
export const getCMData = (cmId) => {
    return request({
        url: `api/Patient/CM/${cmId}/Data`,
        method: "get",
    });
};

/*更新某个合并用药已填写数据*/
export const putCMData = (cmId,data) => {
    return request({
        url: `api/Patient/CM/${cmId}/Data`,
        method: "put",
        data
    });
};


/*获取不良事件数据*/
export const getViewData = (cmId,data) => {
    return request({
        url: `api/Patient/Quest/AECM/ViewData/${cmId}`,
        method: "get",
        params: { ...data }
    });
};