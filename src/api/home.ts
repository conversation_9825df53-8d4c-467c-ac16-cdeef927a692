import { request } from "@/utils/axios";

/*受试者功能*/
/*
{ 
  "id": "string",病例在eDCT系统中的编号
  "studyId": 0, 课题编号, 对应EDC中的课题编号
  "patientId": 0, 病例Id, 对应EDC中的病例Id
  "patNumber": "string", 病例编号, 对应EDC中的病例编号, 如 001-001
  "avatarUrl": "string", 患者的头像
  "greeting": "string",打招呼的内容, 一般为: 嗨，今天感觉怎么样？但是每个课题可能会修改
  "currentStatusText": "string", 当前受试者状态, 比如: 目前您正在进行 V1访视
  "patientTrialStatusText": "string", 受试者在当前试验的状态-感谢您自2021-01-01加入本研究,今天是您加入以来的第 12 天
  "patientStatus": 0, 受试者状态 0-13结局发生
  "visits": [ 病例的访视列表
    {
      "visitId": 0, 访视编号, 对应EDC中的访视编号如果是负数, 则是eDCT中特殊的访视, 应该没有
      "avatarUrl": "string", 访视的图标
      "visitName": "string", 访视名称
      "beginDate": "string", 访视开始日期 如: 2021-07-08
      "endDate": "string",   访视结束日期 如: 2021-07-08
      "order": 0, 访视的排序, 一般都是按照开始日期排序, 但是考虑到有一些特殊情况, 还是按照Order来排序
      "isCurrent": 0,  是否当前的访视, 1 为是当前访视, 其它值为不是当前访视. 注意, 有可能没有当前访视, 因为所有的访视不在今天
      "isCenter": 0, 当中显示的访视, 一般是当前的访视, 没有的话, 应该是最近的访视 1 为是当中显示访视, 其它值为不是当中访视, 一般会有一个, 如果全部都没有的话,就显示第一个访视
      "editable": 0, 可否编辑, 1 为可以点击进入录入, 其它值(0) 为不可编辑,目前一般未来的访视不可以录入, 但是由后台控制
      "finishStatus": 0 问卷的完成情况:
      //0 = 未知, 1 = 未完成, 2 = 已完成, 3 = 部分完成但完成关键问卷, 4 = 部分完成且未完成关键问卷, 5 = 医生要求重填
    }
  ],
  "dailyFeature": 0, 病例的功能
  "frequencyFeature": 0  病例的功能
//0 = 未知, 1 = 服药日志, 2 = 不适记录, 4 = 合并用药, 8 = 下单寄送, 
//16 = 常见问题, 17 = 修改信息, 18 = 联系客服, 20 = 申请药物, 24 = 领取礼品
}
*/
export const getPatient = () => {
    return request({
        url: `api/Patient`,
        method: "get",
    });
};

/*项目时间轴
planDate 计划时间
activeName 活动的名字
patientActiveStatus	病例的活动状态类型number:
0 = 未知, 1 = 未完成, 2 = 需完成, 3 = 待完成, 4 = 已完成
patientActiveStatusText	任务的状态文本 :string
actualDate 实际完成的日期->状态 已完成时才需要:string
*/
// export const getDateLog = () => {
//     return request({
//         url: `api/Patient/Activies`,
//         method: "get",
//     });
// };

/*访视
*/
export const getVisit = (visitId) => {
    return request({
        url: `api/Visit/${visitId}`,
        method: "get",
    });
};

/*获取某个到院随访人员的信息 */
export const getVisitPlan = (visitId) => {
  return request({
    url: `api/Patient/VisitPlan/${visitId}`,
    method: "get",
  });
};

/*接受到院日期 */
export const postVisitPlanNotice = (visitId) => {
  return request({
    url: `api/Patient/VisitPlan/${visitId}/Accept`,
    method: "post",
  });
};
/**患者学习资料未完成数量
 * 
 * @returns number
 */
export const getTrainingNotice = () => {
  return request({
    url: `api/Patient/Training/Notice`,
    method: "get",
  });
};

// 公告
export const getMaintained = () => {
  return request({
    url: `api/Operator/Study/OutOfServiceWeChatConfig`,
    method: "get",
    showLayout: false,
  });
};

// 身份认证
export const getFacialIdentity = (params) => {
  return request({
    url: `api/Patient/FacialIdentity`,
    method: "get",
    params: params
  });
};
// 无知情 身份认证
export const getWithoutICFFacialIdentity = (params) => {
  return request({
    url: `api/Patient/WithoutICFFacialIdentity`,
    method: "get",
    params: params
  });
};
// 无知情身份认证结果处理
export const getWithoutICFFacialIdentityResult = (params) => {
  return request({
    url: `api/Patient/WithoutICFFacialIdentity/Result`,
    method: "get",
    params: params
  });
};
// 身份认证结果处理
export const getFacialIdentityResult = (params) => {
  return request({
    url: `api/Patient/FacialIdentity/Result`,
    method: "get",
    params: params
  });
};

// 跳过身份认证
export const getFacialIdentitySkip = (params) => {
  return request({
    url: `api/Patient/FacialIdentity/Skip`,
    method: "get",
    params: params
  });
};

/*根据当前状态得到 下一个状态流转
{
  "icfStatementID": "string",
  "icfStatus": 0
}
*/ 
export const postUpdateICFStatus = (data) => {
  return request({
    url: `api/Patient/UpdateICFStatus`,
    method: "post",
    data
  });
};

// 获取是否有会议
export const getStudyConference = (studyId) => {
  return request({
      url: `api/doctor/${studyId}/StudyConference`,
      method: "get"
  });
};
/**
 * 获取患者端的广告位URL
 * @param {string} studyId - 课题编号
 * @returns {Promise<any>} - 返回包含广告位URL的Promise对象
 */
export const getStudyAdvertising = (studyId) => {
    return request({
        url: `api/Patient/${studyId}/GetStudyAdvertising`,
        method: "get",
    });
}