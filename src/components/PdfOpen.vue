<template>
  <div id="pdf-view" class="pdf-view-height">
    <div id="pdf" />
  </div>
</template>

<script lang="ts">
import Pdfh5 from "pdfh5";
import "pdfh5/css/pdfh5.css";
import { reactive, toRefs, onMounted, onBeforeMount } from "vue";
import { Toast } from "vant";

export default {
  name: "PdfView", // github地址https://github.com/gjTool/pdfh5
  props: {
    // 请求数据的方法
    myPdfurl: {
      type: String,
    },
  },
  setup(props) {
    const state: any = reactive({
      pdfh5: null,
      loadingFun: () => {
        Toast.loading({
          duration: 300000, // 注意这里和超时时间一样
          message: '加载中...',
          forbidClick: true,
        });
      },
    });
    onBeforeMount(() => {
      state.loadingFun()
    }),
    onMounted(() => {
      state.pdfh5 = new Pdfh5("#pdf", {
        pdfurl: props.myPdfurl,
        // renderType: 'svg'
      });
      // status, msg, time
      state.pdfh5.on("init", function () {
        state.loadingFun()
      });
      state.pdfh5.on("ready", function () {
        setTimeout(() => {
          state.loadingFun()
        }, 500);
      });
      state.pdfh5.on("complete", function () {
        setTimeout(() => {
          Toast.clear();
        }, 500);
        // console.log("状态：" +status + "，信息：" +msg + "，耗时：" +time +"毫秒，总页数：" +this.totalNum);
      });
    });
    return {
      ...toRefs(state),
    };
  },
};
</script>

<style scoped lang="less">
  /*隐藏pdfh5自带的loading加载效果*/
  :deep(.loadEffect) ,
  :deep(.loading) {
    display: none !important;
  }
</style>