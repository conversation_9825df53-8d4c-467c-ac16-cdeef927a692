<template>
  <!-- 日期筛选 -->
  <div class="filter-date">
    <div>日期筛选</div>
    <div class="start-date">
      <van-field
        v-model.trim="startCurrentDate"
        readonly
        name="datetimePicker"
        placeholder="开始日期"
        @click="pickerDate('start')"
      />
      <van-popup v-model:show="showStartDatas" position="bottom">
        <van-datetime-picker
          v-model="currentDate"
          type="date"
          title="选择日期"
          :min-date="minDate"
          :max-date="maxDate"
          :columns-order="['year', 'month', 'day']"
          @cancel="showStartDatas = false"
          @confirm="confirmValue"
        />
      </van-popup>
      <van-icon name="notes-o" style="margin: 0 0.1rem 0 0" />
    </div>
    <div>－</div>
    <div class="start-date">
      <van-field
        v-model.trim="endCurrentDate"
        readonly
        name="datetimePicker"
        placeholder="结束日期"
        @click="pickerDate('end')"
      />
      <van-popup v-model:show="showEndDatas" position="bottom">
        <van-datetime-picker
          v-model="currentDate"
          type="date"
          title="选择日期"
          :min-date="minDate"
          :max-date="maxDate"
          :columns-order="['year', 'month', 'day']"
          @cancel="showEndDatas = false"
          @confirm="confirmValue"
        />
      </van-popup>
      <van-icon name="notes-o" style="margin: 0 0.1rem 0 0" />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, inject } from "vue";
import { Toast } from "vant";
import { year } from '@trialdata/common-fun-css/index'

export default defineComponent({
  name: "FilterDate", // 日期筛选
  props: {
    request: {
      type: Function,
      default: () => {
        //
      },
    },
    minDate: {
      type: Date,
      default: new Date(year/1 - 100, 12, 31),
    },
    maxDate: {
      type: Date,
      default: new Date(year/1 + 100, 12, 31),
    },
    pageIndex: {
      type: Number,
      default: 1,
    },
    pageSize: {
      type: Number,
      default: 20
    }
  },
  setup(props) {
    const Dates: any = inject('Dates')
    const state = reactive({
      // 日期选择
      currentDate: new Date(), // new Date()
      showStartDatas: false,
      startCurrentDate: "", // 开始日期
      showEndDatas: false,
      endCurrentDate: "", // 结束日期
      // 日期2021-01-01
      confirmValue: (val) => {
        const year = val.getFullYear();
        let month = val.getMonth() + 1;
        let day = val.getDate();
        let hour = val.getHours();
        let minute = val.getMinutes();
        if (month >= 1 && month <= 9) {
          month = `0${month}`;
        }
        if (day >= 1 && day <= 9) {
          day = `0${day}`;
        }
        if (hour >= 0 && hour <= 9) {
          hour = `0${hour}`;
        }
        if (minute >= 0 && minute <= 9) {
          minute = `0${minute}`;
        }
        let currentDates: string | Date = state.currentDate;
        currentDates = `${year}-${month}-${day}`;
        // 日期控件时直接赋值
        if (
          (state.showStartDatas && state.endCurrentDate &&
            Date.parse(currentDates) > Date.parse(state.endCurrentDate)) ||
          (state.showEndDatas && state.startCurrentDate &&
            Date.parse(currentDates) < Date.parse(state.startCurrentDate))
        ) {
          Toast("开始日期不能超过结束日期");
          return;
        }
        if (state.showStartDatas) {
          state.startCurrentDate = currentDates;
          state.showStartDatas = false;
        } else if (state.showEndDatas) {
          state.endCurrentDate = currentDates;
          state.showEndDatas = false;
        }
        // 日期选择完后 开始筛选->
        if(state.endCurrentDate && state.startCurrentDate){
          const dates = {
            dtBegin: state.startCurrentDate,
            dtEnd: state.endCurrentDate,
            pageIndex: props.pageIndex,
            pageSize: props.pageSize
          }          
          Dates.dtBegin = state.startCurrentDate
          Dates.dtEnd = state.endCurrentDate
          props.request(dates)
        } 
      },
      // 点击选择日期
      pickerDate: (falg) => {
        if (falg === "start") {
          state.showStartDatas = true;
        } else if (falg === "end") {
          state.showEndDatas = true;
        }
      },
    });

    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.filter-date {
  padding: 0 0.1rem;
  margin: 0.15rem 0;
  box-sizing: border-box;
  display: flex;
  justify-content: space-around;
  align-items: center;
  .start-date {
    width: 1.3rem;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #f7f7f7;
    border-radius: 0.05rem;
    overflow: hidden;
  }
  .van-cell {
    width: 1.1rem;
    height: 0.3rem;
    background: #f7f7f7;
    border: none;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
