<template>
  <div
    v-show="dataChangeModuleFlag"
    class="w-full absolute overflow-hidden"
    :style="{
      height: outerHeight + 'px',
      left: '0',
      top: '0',
    }"
  >
    <van-nav-bar
      title="数据变动"
      left-text=""
      left-arrow
      @click-left="
        () => {
          dataChangeModuleFlag = false;
        }
      "
    />
    <!-- 内容区 -->
    <div
      :style="{
        height: outerHeight - 96 + 'px',
      }"
      class="bg-F7F7F7 overflow-auto"
    >
      <div
       v-for="(item, index) in dataChangeModuleObj.questCrfItemes.filter((e) => e?.isDisplay)"
       class="p-01rem bg-white mb-01rem"
       :key="index"
      >
        <div class="ft-015rem">
          <div v-if="item?.fieldLabel" class="flex mb-006-rem">
            <div class="font-color-9A9A9A">题目：</div>
            <div class="font-color-333 flex-1" v-html="item.fieldLabel"></div>
          </div>
          <!-- 1 = 新增, 2 = 修改, 3 = 删除, 4 = 迁移,
           5 = 新增澄清, 6 = 回复澄清, 7 = 关闭澄清 -->
          <div v-if="item?.oprtType" class="flex mb-006-rem">
            <div class="font-color-9A9A9A">操作类型：</div>
            <div class="font-color-333">{{ oprtTypeList[item.oprtType] }}</div>
          </div>
          <!-- 可能有图片 -->
          <div v-if="item?.oprtType === 1" class="flex mb-006-rem">
            <div class="font-color-9A9A9A">结果：</div>
            <div v-if="item?.crfFieldType === 1" class="flex-1 flex flex-wrap">
                <div
                  v-for="(e,i) in item?.fieldValue.split(',')"
                  :key="i"
                >
                <img
                        v-if="e"
                        class="mr-006rem mb-015rem"
                        style="width: 0.72rem; height: 0.72rem"
                        :src="e"
                        alt=""
                    />
                </div>
            </div>
            <div v-else class="font-color-333 flex-1">{{ item?.fieldValueStr }}</div>
          </div>
          <div v-if="item?.oprtType === 2 || item?.oprtType === 3" class="flex mb-006-rem">
            <div class="font-color-9A9A9A">结果-修改前：</div>
            <div v-if="item?.crfFieldType === 1 && item?.fieldOriginalValue?.length > 1" class="flex-1 flex flex-wrap">
              <div
                v-for="(e,i) in item?.fieldOriginalValue.split(',')"
                :key="i"
              >
              <img
                  v-if="e"
                  class="mr-006rem mb-015rem"
                  style="width: 0.72rem; height: 0.72rem"
                  :src="e"
                  alt=""
                />
             </div>
            </div>
            <div v-else class="font-color-333 flex-1">{{ item?.fieldOriginalValueStr }}</div>
          </div>
          <div v-if="item?.oprtType === 2 || item?.oprtType === 3" class="flex mb-006-rem">
            <div class="font-color-9A9A9A">结果-修改后：</div>
            <div v-if="item?.crfFieldType === 1" class="flex-1 flex flex-wrap">
                <div
                  v-for="(e,i) in item?.fieldValue.split(',')"
                  :key="i"
                >
                <img
                  v-if="e"
                  class="mr-006rem mb-015rem"
                  style="width: 0.72rem; height: 0.72rem"
                  :src="e"
                  alt=""
                />
                </div>
            </div>
            <div v-else class="font-color-333 flex-1">{{ item?.fieldValueStr }}</div>
          </div>
          <div v-if="item?.oldRemarksStr" class="flex">
            <div class="font-color-9A9A9A">修改原因：</div>
            <div class="font-color-333 flex-1">{{ item?.oldRemarksStr }}</div>
          </div>
          <!-- 修改原因、其它 -->
          <div v-else-if="item?.oprtType === 2 || item?.oprtType === 3" class="flex">
            <div class="font-color-9A9A9A">修改原因：</div>
            <div class="font-color-333 flex">
                <!-- reason 0 = 无, 1 = 填写错误, 2 = 数据更新, 3 = 其它 -->
                <div 
                    v-for="(reasonItem, reasonIdx) in reasonRadioList"
                    :key="reasonIdx"
                    class="centerflex mr-01rem"
                    @click="() => {
                        item.reason = reasonItem.value 
                    }"
                >
                    <div
                        v-if="reasonItem.value === item.reason"
                        style="width: 0.14rem;height: 0.14rem;border-radius: 50%;"
                        class="bg-theme centerflex"
                    >
                        <div
                            style="width: 0.04rem;height: 0.04rem;border-radius: 50%;"
                            class="bg-white"
                        />
                    </div>
                    <div
                        v-else
                        style="width: 0.14rem;height: 0.14rem;border-radius: 50%;border: 0.5px solid #707070"
                        class="bg-white box-border"
                    />
                    &nbsp;
                    {{ reasonItem.text }}
                </div>
            </div>
          </div>
          <div v-if="!item?.oldRemarksStr && (item?.oprtType === 2 || item?.oprtType === 3) && !item.reason" class="flex">
            <div style="height: 0;opacity: 0;">修改原因：</div>
            <div style="color: #E26375;font-size: 0.1rem;"
                class="re">请选择</div>
          </div>
          <div v-if="!item?.oldRemarksStr && item.reason === 3" class="centerflex-h">
            <div class="font-color-9A9A9A">其它：</div>
            <div class="flex-1">
                <van-field
                  v-model="item.remarksStr"
                  class="pl-0 w-full font-color-333"
                  style="border-bottom: 0.5px solid rgba(0, 0, 0, 0.1)"
                  placeholder="请输入"
                  maxlength="100"
                />
            </div>
          </div>
          <div v-if="!item?.oldRemarksStr && item.reason === 3 && !item?.remarksStr && submitClickFlag" class="flex">
            <div style="height: 0;opacity: 0;">其它：</div>
            <div style="color: #E26375;font-size: 0.1rem;">请输入</div>
          </div>
        </div>
      </div>
    </div>
    <van-button
        style="height: 50px;"
        class="w-full"
        type="primary"
        :loading="submitLoadingFlag"
        loading-text="确认提交"
        @click="handleSaveQuestionnaire"
    >确认提交</van-button>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, } from "vue";
// import { Toast } from "vant";

export default defineComponent({
  name: "DataChangeModule", // 数据变动
  props: {
    saveDataChangeFun: {
      type: Function,
      default: () => {},
    }
  },
  setup(props) {
    // const proxy: any = getCurrentInstance()?.proxy
    const state = reactive({
      oprtTypeList: [
      '未知', '新增', '修改', '删除', '迁移', '新增澄清', '回复澄清','关闭澄清'
      ],
      outerHeight: document.documentElement.scrollHeight,
      // 数据澄清相关
      dataChangeModuleFlag: false,
      dataChangeModuleObj: {
        questCrfItemes: []
      },
      submitLoadingFlag: false,
      // 是否点击过提交
      submitClickFlag: false,
      reasonRadioList: [
        {
            text: '填写错误',
            value: 1
        },
        {
            text: '数据更新',
            value: 2
        },
        {
            text: '其它',
            value: 3
        },
      ],
      handleSaveQuestionnaire: () => {
        state.submitClickFlag = true
        let flag = true
        state.dataChangeModuleObj.questCrfItemes.map((item) => {
          if (item?.isDisplay && !item?.oldRemarksStr && (item?.oprtType === 2 || item?.oprtType === 3) && !item.reason) {
            flag = false
          }
          if (item?.isDisplay && !item?.oldRemarksStr && item.reason === 3 && !item?.remarksStr) {
            flag = false
          }
        })
        if (flag) {
          state.submitLoadingFlag = true
          props.saveDataChangeFun(state.dataChangeModuleObj)
          .then(() => {
              // proxy.$routerBackFun()
              // state.dataChangeModuleFlag = false
              // state.submitLoadingFlag = false
          })
          .catch(() => {
              state.submitLoadingFlag = false
          })
        }
      },
    });

    setTimeout(() => {
      state.outerHeight = document.documentElement.scrollHeight;
    }, 0);

    return {
      ...toRefs(state),
    };
  },
});
</script>
