<template>
  <!-- vant上传图片 @delete="deleteImgFun"-->
  <div class="upload">
    <van-uploader
      :after-read="onRead"
      :before-read="beforeRead"
      :before-delete="beforeDelete"
      v-model="fileList"
      accept="image/*"
      :preview-options="{ closeable: true }"
      :multiple="myMultiple"
      :max-size="maxSize"
      :max-count="maxCount"
    />
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  inject,
  onMounted
} from "vue";
import { Toast, Dialog } from "vant";
// import { deepClone } from '@trialdata/common-fun-css/index';

export default defineComponent({
  name: "UploadImg",
  props: {
    // 一次可上传多少张
    maxCount: {
      type: Number,
      default: 15,
    },
    //最大多少MB-默认10MB
    maxSize: {
      type: String,
      default: "10240 * 1024",
    },
    // 超过多少MB压缩-默认2MB
    compressFileSize: {
      type: Number,
      default: 1048576 * 100, // 改为100
    },
    // 上传函数
    postImgFun: {
      type: Function,
      default: () => { }
    },
    // 删除的函数
    deleteImgFun: {
      type: Function,
      default: () => { }
    },
    // 发药对象
    dispensingObj: {
      type: Object,
    },
    // 是否多选 :默认可以多选
    myMultiple: {
      type: Boolean,
      default: true
    },
    uploadePrams: {
      type: Function,
      default: () => { }
    },
    // 是否禁用保存等操作
    // disabledSaveFalg:{
    //   type: Object,
    // },
  },
  setup(props: any) {
    // const fileList = ref([
    //   //{ url: 'https://img.yzcdn.cn/vant/leaf.jpg' },
    // ]); // 回显图片
    const fileLists: any = inject('fileList');

    const state = reactive({
      imagePath: [], // 上传图片路径
      fileList: [],
      //vant
      dataURLtoFile: (dataurl, filename) => {
        // 将base64转换为file文件
        const arr = dataurl.split(",");
        const mime = arr[0].match(/:(.*?);/)[1];
        const bstr = atob(arr[1]);
        let n = bstr.length;
        const u8arr = new Uint8Array(n);
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n);
        }
        return new File([u8arr], filename, { type: mime });
      },
      beforeRead(file) {
        // 上传之前检测图片类似返回true和false会影响到onRead函数
        const regex = /(.jpg|.jpeg|.png|.bmp|.gif|.jfif)$/;
        let beforeReadFalg = true
        if (Array.isArray(file)) {
          // if(file.length > 15 || (fileLists.length+file.length) > 15){
          //   Notify({ message: `最多支持15张图片`, type: "danger" });
          // }
          file.forEach((item) => {
            if (!regex.test(item.type)) {
              Toast("图片格式不支持上传");
              beforeReadFalg = false;
            }
          })
        } else {
          if (!regex.test(file.type)) {
            Toast("图片格式不支持上传");
            beforeReadFalg = false;
          }
        }
        return beforeReadFalg
      },
      onRead: (file) => {
        if (Array.isArray(file)) {
          file.map((item) => {
            // if (item.file.size > (props.compressFileSize)){
            //   state.myCompress(item)
            // } else {
            //小于2MB直接上传
            state.fileListsAdd(props.dispensingObj?.flag ? item : null)
            props.uploadePrams(item.file)
            // }
          })
        } // 大于2MB的图片都缩小像素上传
        // else if (file.file.size > props.compressFileSize) {
        //   state.myCompress(file)
        // }
        else {
          // 小于2MB直接上传
          state.fileListsAdd(props.dispensingObj?.flag ? file : null)
          props.uploadePrams(file.file)
        }
      },
      // 压缩
      myCompress: (file) => {
        const canvas = document.createElement("canvas"); // 创建Canvas对象(画布)
        const context: any = canvas.getContext("2d");
        const img = new Image();
        img.src = file.content; // 指定图片的DataURL(图片的base64编码数据)
        img.onload = () => {
          canvas.width = 400;
          canvas.height = 300;
          context.drawImage(img, 0, 0, 400, 300);
          file.content = canvas.toDataURL(file.file.type, 0.92); // 0.92为默认压缩质量
          const files = state.dataURLtoFile(file.content, file.file.name);
          //上传 --压缩后格式会变 gif变为png
          state.fileListsAdd(files)
        };
      },

      //beforeDelete 删除前
      beforeDelete: (e) => {
        Dialog.confirm({
          title: "操作提示",
          message: "是否删除当前照片",
        })
          .then(() => {
            //当前数组里删除
            state.fileList.forEach((item, index) => {
              if (item === e) {
                // 删除发药审核图
                props.deleteImgFun(fileLists[index]?.fileId || index)
                state.fileList.splice(index, 1);
              }
            });
            if (props.maxCount !== 6) {
              // 有值的话-添加进动态数组
              state.fileListsAdd(null)
            }
          })
          .catch(() => {
            return false;
          });
      },

      // 通用-更改动态图片数组
      fileListsAdd: (files) => {
        if (props.dispensingObj?.flag) {
          props.postImgFun(files)
        }
        else if (state.fileList) {
          fileLists.length = 0;
          state.fileList.map((item) => {
            fileLists.push(item)
          })
          if (files) { // 压缩后的文件
            fileLists.map((items) => {
              if (items?.name) {
                if (items.name?.split('.')[0] === files.name.split('.')[0]) {
                  items.file = files
                }
              }
            })
          }
        }
      }
      // //删除时
      // deleteImgFun:(deleteItem) => {
      //   console.log(deleteItem)
      //   // props.deleteImg().then((res) => {
      //   //   console.log(res)
      //   // })
      // }
    });

    onMounted(() => {
      state.fileList = fileLists
    })

    return {
      ...toRefs(state),
    };
  },
});
</script>
