<template>
  <!-- 列表类型 -->
    <div v-if="listArrs?.length">
      <div
        v-for="(listItem,listIndex) in listArrs"
        :key="listIndex"
      >
        <div class="questCrf-list" :class="{'questCrf-list-edit': editFlag === '1'}">
          <div class="list-name">
            <div class="list-name-text" v-if="listItem?.tableName" v-html="listItem.tableName" />
            <img v-if="editFlag !== '1'" src="@/assets/baby/questionList/seeIcon.svg" alt="" @click="editListFun(listItem,editFlag,viewItem)"/>
            <img v-else src="@/assets/baby/questionList/editIcon.svg" alt="" @click="editListFun(listItem,editFlag,viewItem)"/>
          </div>
          <div 
            v-for="(questItem,questIndex) in listItem.items"
            :key="questIndex"
            class="questCrf-item"
          >
            <div class="">
              <div class="questCrf-item-title" v-html="questItem.fieldLabel"/>
              <p>
                <div v-if="questItem.fieldValueStr === '未填写'" class="none-fieldValueStr">{{ questItem.fieldValueStr }}</div>
                <div v-else class="none-warp-text-auto">{{ questItem.fieldValueStr }}</div>
                <div v-if="questItem?.dctQuestUnit" class="unit">{{ questItem.dctQuestUnit }}</div>
              </p>
              <!-- 2 = 上传控件 -->
              <!-- <div v-if="questItem.crfFieldControl === 2" class="uploadsheet-body">
                <div class="uploadsheet-module">
                  <div class="uploadsheet-items">
                    <div
                      v-if="questItem?.fileListUrl?.length"
                      class="upload-imgs"
                    >
                      <img
                        v-for="(fileItem, index) in questItem.fileListUrl"
                        :key="index"
                        :src="fileItem"
                        alt=""
                        @click="tab(questItem.fileListStr, index)"
                      />
                    </div>
                    <p
                      v-else
                      style="
                        width: 100%;
                        height: 100%;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      暂未上传
                    </p>
                  </div>
                </div>
              </div> -->

              <div class="br" />
            </div>
          </div>
          <!-- 详情页中可跟进列表内数据澄清 -->
          <div
            v-if="listItem?.dataClarification"
            class="mt-01rem flex justify-end ft-13-rem"
            style="color: #F19980"
          >
            详情页中可跟进列表内数据澄清
          </div>
        </div>
        <!-- 底部+ - -->
        <div v-if="editFlag === '1'" class="add-delete-module">
          <img
          v-if="listIndex+1 === listArrs?.length"
          src="@/assets/baby/questionList/addIcon.svg"
          alt="" 
          @click.stop="addListFun(listItem,viewItem)"/>
          <img
          v-if="listArrs?.length > 1"
          src="@/assets/baby/questionList/deleteIcon.svg"
          alt=""
          @click.stop="deleteListFun(listItem,viewItem)"/>
        </div>
      </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from "vue";

export default defineComponent({
  name: "QuestionList",
  props: {
    // 澄清数量
    // clarifyCount: {
    //   type: Number,
    //   default: 0
    // },
    // 新增接口函数
    addListFun: {
      type: Function,
      default: () => {}
    },
    editListFun: {
      type: Function,
      default: () => {}
    },
    // 删除的函数
    deleteListFun: {
      type: Function,
      default: () => {}
    },
    listArrs: {
      type: Array,
      // default: []
    },
    viewItem: {
      type: Object,
    },
    editFlag: {
      type: String,
      default: ''
    },
  },
  setup() {
    const state = reactive({
      //
    });

    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang="less">
@import "@/style/questionList.less";
</style>
