<template>
  <van-tabbar
    v-model="tabbarActiveFlag"
    @change="onTabbarChange"
    active-color="#5860d9"
    inactive-color="#BEBEBE"
    style="height: 55px"
  >
    <van-tabbar-item>
      <span>首页</span>
      <template #icon="props">
        <img src="@/assets/baby/homeIconH.svg" v-show="!props.active" />
        <img src="@/assets/baby/homeIcon.svg" v-show="props.active" />
      </template>
    </van-tabbar-item>

    <van-tabbar-item>
      <span>随访</span>
      <template #icon="props">
        <img src="@/assets/baby/followIconH.svg" v-show="!props.active" />
        <img src="@/assets/baby/followIcon.svg" v-show="props.active" />
      </template>
    </van-tabbar-item>

    <van-tabbar-item v-if="hasInteractiveCommunication">
      <span>互动沟通</span>
      <template #icon="props">
        <img src="@/assets/baby/communicateIconH.svg" v-show="!props.active" />
        <img src="@/assets/baby/communicateIcon.svg" v-show="props.active" />
        <i v-show="unReadChattingRecordsNum" class="redIcon redIcon-hd" />
      </template>
    </van-tabbar-item>

    <van-tabbar-item>
      <span>我的</span>
      <template #icon="props">
        <img src="@/assets/baby/myIconH.svg" v-show="!props.active" />
        <img src="@/assets/baby/myIcon.svg" v-show="props.active" />
      </template>
    </van-tabbar-item>
  </van-tabbar>
</template>

<script>
import {
  defineComponent,
  onMounted,
  reactive,
  toRefs,
  watch,
  getCurrentInstance,
} from "vue";
import { useRoute } from "vue-router";
import { useStore } from "vuex";
import { getPatient } from "@/api/home";

export default defineComponent({
  name: "Tabbar",
  props: {
    // 高亮的
    propsActive: {
      type: Number,
      default: 0,
    },
  },
  setup(props) {
    const route = useRoute();
    const store = useStore();
    const proxy = getCurrentInstance()?.proxy;
    const state = reactive({
      // 是否开启互动沟通
      hasInteractiveCommunication: 0,
      tabbarActiveFlag: 0,
      unReadChattingRecordsNum: 0,
      onTabbarChange: (e) => {
        if (e === 2 && !state.hasInteractiveCommunication) {
          e += 1;
        }
        switch (e) {
          case 0:
            proxy.$routerGoFun("replace", "/");
            break;
          case 1:
            proxy.$routerGoFun("replace", "/datelog");
            break;
          case 2:
            proxy.$routerGoFun(
              "routerInnPush",
              "/interactivecommunication",
              "",
              route.path
            );
            break;
          case 3:
            proxy.$routerGoFun("replace", "/my");
            break;
          default:
            break;
        }
        state.tabbarActiveFlag = e;
      },
    });
    onMounted(() => {
      state.tabbarActiveFlag = props.propsActive / 1;
      // console.log(store?.state?.homeList.hasInteractiveCommunication);
      if (store?.state?.homeList.hasInteractiveCommunication === -1) {
        getPatient().then((rest) => {
          state.hasInteractiveCommunication = rest.hasInteractiveCommunication;
          let tmp = store?.state?.homeList;
          tmp.hasInteractiveCommunication = rest.hasInteractiveCommunication;
          store.dispatch("setHomeList", tmp);
        });
      } else {
        state.hasInteractiveCommunication =
          store?.state?.homeList.hasInteractiveCommunication;
      }
    });
    // 监听 输入是否有值
    watch(
      store.state,
      () => {
        if (store?.state?.homeList) {
          state.unReadChattingRecordsNum =
            store.state.homeList.unReadChattingRecordsNum;
        }
      },
      {
        immediate: true, // 是否初始化立即执行一次, 默认是false
        deep: true, // 是否是深度监视, 默认是false
      }
    );

    return {
      ...toRefs(state),
    };
  },
});
</script>

<style lang='less' scoped>
:deep(.van-tabbar) {
  height: 55px !important;
}
:deep(.van-tabbar-item) {
  padding-bottom: 6px;
}
.redIcon {
  width: 0.065rem;
  height: 0.065rem;
  position: absolute;
  right: -0.065rem;
  top: -0.03rem;
  border-radius: 50%;
  background: #d33838;
}
.redIcon-hd {
  right: -0.04rem;
}
:deep(.van-tabbar-item__icon img) {
  height: 20px;
  width: 20px;
}
</style>