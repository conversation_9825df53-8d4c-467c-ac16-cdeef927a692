<template>
    <div
        v-show="unplannedModificationModuleFlag"
        class="w-full absolute bg-white overflow-hidden"
        :style="{
            height: outerHeight + 'px',
            left: '0',
            top: '0',
        }"
    >
        <van-nav-bar
            title="计划外修改"
            left-arrow
            @click-left="$routerBackFun"
        />
        <div
            :style="{
                height: `calc(${outerHeight - 96}px - 1.35rem)`
            }"
            class="overflow-auto"
        >
            <div class="centerflex-h h-[0.7rem] p-[0.15rem] theme-bg-linear-gradient mb-[0.1rem] text-white">
                <div class="">
                    <div class="wrap1 mb-[0.05rem]">{{ unplannedModificationModuleObj.parentType }}</div>
                    <div class="wrap1">{{ unplannedModificationModuleObj.templateName }}</div>
                </div>
            </div>

            <div class="px-[0.15rem] mt-[0.2rem]">
                <div class="" style="line-height: 0.2rem;">
                    <div class="ft-15-rem">修改窗口：</div>
                    <div class="ft-12-rem mb-[0.03rem]">{{ unplannedModificationModuleObj.windowStartTimeStr }}~{{ unplannedModificationModuleObj.windowEndTimeStr }}</div>
                </div>
                <div class="mt-[0.25rem]">
                    <div class="ft-15-rem mb-[0.06rem]">原因：</div>
                    <div class="ft-14-rem whitespace-pre-wrap break-all text-[#333]">
                        {{ unplannedModificationModuleObj.openWindowReason }}
                    </div>
                </div>
                <div class="mb-[0.15rem]" style="line-height: 0.2rem;">
                    <div class="mt-[0.15rem] ft-15-rem flex justify-between items-center">
                        <div class="mb-[0.03rem]">修改人：{{ unplannedModificationModuleObj.updateUserName }}</div>
                        <div
                            v-if="!unplannedModificationModuleObj.isFeedBack && !unplannedModificationModuleObj.isUpdateUser"
                            class="centerflex ft-10-rem w-[0.46rem] h-[0.19rem] text-[#E26375] bg-[#FCEFF1]"
                            style="border: 0.5px solid #E26375;border-radius: 0.02rem;"
                        >
                            待反馈
                        </div>
                    </div>

                    <div
                        v-if="unplannedModificationModuleObj.isAgree && unplannedModificationModuleObj.isFeedBack"
                        class="ft-15-rem"
                    >
                        <div class="mb-[0.03rem]">反馈: {{ unplannedModificationModuleObj.feedBack }}</div>
                        <div>反馈时间:{{ unplannedModificationModuleObj.feedBackTimeStr }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="">
            <div v-if="unplannedModificationModuleObj.isUpdateUser && !unplannedModificationModuleObj.isFeedBack">
                <div class="h-[1.1rem] max-h-[1.1rem] px-[0.15rem] mb-[0.25rem] text-[#F19980]">
                    请根据真实情况反馈：<br>
                    若选择 "不同意修改" ，代表您不认可这一情况，不同意进行修改;<br>
                    若选择“情况属实，同意修改”，代表您确认这一情况属实，同意进行修改；
                </div>
                <van-button
                    type="danger"
                    class="w-[50%] border-none rounded-none h-[50px]"
                    style="background: linear-gradient(180deg,#FF798B,#DB4F62 100%);"
                    @click="openPopupShow(2)"
                >不同意修改</van-button>
                <van-button
                    type="primary"
                    class="w-[50%] rounded-none h-[50px]"
                    @click="openPopupShow(1)"
                >情况属实，同意修改</van-button>
            </div>
            <van-button
                v-else
                class="mt-[1.35rem] rounded-none h-[50px]"
                type="primary"
                block
                @click="() => {
                    getQuestionnaireCustomize()
                    unplannedModificationModuleFlag = false
                }"
            >下一页</van-button>
        </div>
        <!-- 两个弹窗 
         是否确认”不同意修改“？
         是否确认“情况实属，同意修改”？
        -->
        <MyPopupShow 
            :myPopupShow="popupShowFlag"
            title="提示"
            :texts="agreeOrNotFlag === 2 ?
            `<div class='mb-01rem ft-13-rem text-[#333]'>是否确认”不同意修改“？</div>
            <div class='ft-13-rem text-center text-[#333]'>该操作代表您不认可这一情况，不同意进行修改<div>`
            : `<div class='mb-01rem ft-13-rem text-[#333]'>是否确认“情况实属，同意修改”？</div>
            <div class='ft-13-rem text-center text-[#333]'>该操作代表您确认这一情况属实，同意进行修改<div>`
            "
            cancelText="取消"
            saveText="确认"
            cancelClass="text-black"
            :handleSave="routerGo"
            :handleCancel="() => { 
                if (!saveLoading)
                popupShowFlag = false
            }"
        />
    </div>
</template>

<script lang="ts">
import { 
    defineComponent,
    getCurrentInstance,
    onMounted,
    reactive,
    toRefs
} from 'vue';
import { postOprtQuestUnplannedWindow } from '@/api/questionnaire';
import MyPopupShow from "@/components/MyPopupShow.vue";

export default defineComponent({
  name: "UnplannedModification", // 计划外修改
  components: {
    MyPopupShow,
  },
  props: {
    getQuestionnaireCustomize: {
      type: Function,
      default: () => {},
    },
  },
  setup(props) {
    const proxy: any = getCurrentInstance()?.proxy;
    const state = reactive({
        popupShowFlag: false,
        unplannedModificationModuleFlag: false,
        unplannedModificationModuleObj: {
            parentType: '',
            templateName: '',
            windowStartTimeStr: '',
            windowEndTimeStr: '',
            openWindowReason: '',
            updateUserName: '',
            feedBack: '',
            feedBackTimeStr: '',
            isAgree: false,
            isFeedBack: false,
            isUpdateUser: false,
        },
        outerHeight: document.documentElement.scrollHeight,
        agreeOrNotFlag: 1, // 1: 同意，2: 不同意
        openPopupShow: (flag) => {
            state.agreeOrNotFlag = flag
            state.popupShowFlag = true
        },
        saveLoading: false,
        routerGo: () => {
            if (state.saveLoading) {
                return
            }
            state.saveLoading = true
            state.unplannedModificationModuleObj.isAgree = state.agreeOrNotFlag === 1
            postOprtQuestUnplannedWindow(state.unplannedModificationModuleObj)
            .then(() => {
                if (state.agreeOrNotFlag === 2) {
                    proxy.$routerBackFun()
                } else {
                    // 进问卷
                    props.getQuestionnaireCustomize('1')
                    state.unplannedModificationModuleFlag = false
                }
                state.popupShowFlag = false
                state.saveLoading = false
            }).catch(() => {
                state.saveLoading = false
            })
        }
    })

    onMounted(() => {
        setTimeout(() => {
            state.outerHeight = document.documentElement.scrollHeight;
        }, 0);
    });
    
    return {
      ...toRefs(state),
    };
  },
});
</script>