<template>
  <div
    class="h-100-vh overflow-hidden font-color-555 ft-12-rem"
  >
    <van-nav-bar
      title="签署电子知情同意"
      left-text=""
      left-arrow
      @click-left="$routerBackFun()"
    />
    <div v-if="outerWidth" class="relative" @touchstart="flag = false">
      <canvas ref="signatureCanvas" :width="outerWidth" height="200"></canvas>
      <span v-if="flag" class="absolute-center ft-24-rem font-color-999 z--9">请 在 此 处 签 名</span>
    </div>
    
    <van-button class="w-1/2 h-05rem" @click="clearSignature">重写</van-button>
    <van-button class="w-1/2 h-05rem font-color-fff bg-color-5490EC" @click="getSignatureData"
      >签名确认</van-button
    >
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick } from "vue";
import SignaturePad from "signature_pad";

const outerWidth = ref(window.outerWidth);
const signatureCanvas = ref(null);
const flag = ref(true);
let signaturePad = null;

// 清除签名
function clearSignature() {
  flag.value = true
  signaturePad.clear();
}
// 获取签名数据
function getSignatureData() {
  const dataURL = signaturePad.toDataURL(); // 获取签名的Base64数据
  // 获取是否为空板 true = 未填写，false 已填写。
  console.log(signaturePad.isEmpty());
  console.log("签名数据：", dataURL);
}
onMounted(() => {
  nextTick(() => {
    const canvas = signatureCanvas.value;
    if (canvas) signaturePad = new SignaturePad(canvas);
  });
});
</script>
