<template>
  <div
    v-show="myPopupShow" 
    class="van-overlay mypopup centerflex"
    :class="myPopupShowClass || ''"
  >
    <div class="mypopup-module" :class="myPopupModuleClass || ''">
      <div v-if="title" class="mypopup-title centerflex" :class="myPopupTitleClass || ''" v-html="title"/>
      <slot name="bodyslot"></slot>
      <div
        v-if="texts"
        v-html="texts"
        class="mypopup-texts"
        :class="myPopupTextsClass || ''"
      />
      <div v-if="cancelText || saveText" class="mypopup-btns">
        <div
          v-if="cancelText"
          v-html="cancelText"
          class="mypopup-btns-l centerflex"
          @click="handleCancel"
          :class="cancelClass || ''"
        />
        <div
          v-if="centerBtnText"
          v-html="centerBtnText"
          class="mypopup-btns-l centerflex"
          :class="centerBtnClass || ''"
          @click="handleCenterBtn"
        />
        <div
          v-if="saveText"
          v-html="saveText"
          class="centerflex"
          :class="saveClass || ''"
          @click="handleSave"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount } from "vue";

export default defineComponent({
  name: "MyPopupShow", // 弹框
  props: {
    // 是否显示弹窗
    myPopupShow: {
      type: Boolean,
      default: false
    },
    // 标题
    title: {
      type: String,
      default: '提示'
    },
    // 内容
    texts: {
      type: String,
      default: ''
    },
    // 左按钮文案
    cancelText: {
      type: String,
      default: '',
    },
    // 右按钮文案
    saveText: {
      type: String,
      default: '',
    },
    myPopupTextsClass: {
      type: String,
      default: ''
    },
    myPopupModuleClass: {
      type: String,
      default: ''
    },
    myPopupTitleClass: {
      type: String,
      default: ''
    },
    // 左按钮class
    cancelClass: {
      type: String,
      default: ''
    },
    // 右按钮class
    saveClass: {
      type: String,
      default: ''
    },
    // 左按钮点击事件
    handleCancel: {
      type: Function,
      default: () => {
        //
      },
    },
    // 右按钮点击事件
    handleSave: {
      type: Function,
      default: () => {
        //
      }
    },
    // 最外层容器class
    myPopupShowClass: {
      type: String,
      default: ''
    },
    // 中间按钮
    centerBtnText: {
      type: String,
      default: ''
    },
    centerBtnClass: {
      type: String,
      default: ''
    },
    handleCenterBtn: {
      type: Function,
      default: () => {}
    },
  },
  setup() {
    const state = reactive({
      //
    });

    onBeforeMount(() => {
      //
    });

    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.mypopup {
  background: rgba(0, 0, 0, 0.3);
  .mypopup-module {
    width: 3rem;
    font-size: 0.15rem;
    border-radius: 0.06rem;
    background: #fff;
    color: #7f7f7f;
    .mypopup-title {
      margin: 0.2rem 0;
      color: #000;
    }
    .mypopup-texts {
      padding: 0 0.2rem;
      box-sizing: border-box;
    }
    .mypopup-btns {
      display: flex;
      margin: 0.24rem 0 0 0;
      border-top: 0.5px solid #ebebeb;
      color: var(--theme-color);
      div {
        width: 50%;
        height: 0.4558rem;
      }
      .mypopup-btns-l {
        border-right: 0.5px solid #ebebeb;
      }
    }
  }
}
</style>
