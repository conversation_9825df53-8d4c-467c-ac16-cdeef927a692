<template>
  <div
    v-show="dataClarificationFlag"
    class="bg-F7F7F7 w-full absolute overflow-hidden"
    :style="{
      height: outerHeight+'px',
      left: '0',
      top: '0'
    }"
  >
    <van-nav-bar
      title="数据澄清"
      left-text=""
      left-arrow
      @click-left="() => {
        if (addModuleFlag) {
          addModuleFlag = false
        } else {
          dataClarificationFlag = false
          updateDataClarification()
        }
      }"
    />
    <!-- 内容区 -->
    <div
      :style="{
        height: outerHeight - 46 + 'px'
      }"
      class="overflow-hidden"
    >
      <div
        :style="{
          height: outerHeight - 46 + 'px'
        }"
        class="overflow-auto"
      >
        <div v-if="clarifyList?.length">
          <div
            v-for="(item, index) in clarifyList"
            :key="index"
            class="p-01rem mb-01rem bg-white"
          >
            <div class="centerflex-h mb-01rem">
            <div class="font-bold ft-15-rem font-color-333">
              澄清：{{ item?.id }}
            </div>
            <div v-if="item?.clarifyTypeStatus === 2"
                class="ml-01rem centerflex ft-10-rem w-045rem h-019rem"
                style="color: #E26375;
                border: 0.5px #E26375 solid;
                background-color: #FFEDF0;
                border-radius: 0.02rem;"
            >未关闭</div>
            <div v-else-if="item?.clarifyTypeStatus === 1"
                class="ml-01rem centerflex ft-10-rem w-045rem h-019rem"
                style="color: #9A9A9A;
                border: 0.5px #9A9A9A solid;
                background-color: #F8F8F8;
                border-radius: 0.02rem;"
            >已关闭</div>
            </div>
            <div class="mt-01-rem ft-15-rem">
            <div class="flex mb-006-rem">
              <div class="font-color-9A9A9A">题目：</div>
              <div
                v-if="item?.fieldLabel"
                v-html="item.fieldLabel"
                class="font-color-333 flex-1"
              />
            </div>
            <!-- 可能有图片 -->
            <div class="flex mb-006-rem">
                <div class="font-color-9A9A9A">结果：</div>
                <div v-if="item?.crfFieldType === 1" class="flex-1 flex flex-wrap">
                  <div
                    v-for="(e,i) in item?.fieldValue.split(',')"
                    :key="i"
                  >
                    <img
                      v-if="e"
                      class="mr-006rem mb-015rem"
                      style="width: 0.72rem; height: 0.72rem"
                      :src="e"
                      alt=""
                    />
                  </div>
                </div>
                <div v-else class="font-color-333 flex-1">{{ item?.fieldDisplayValue }}</div>
            </div>
            <div class="flex mb-006-rem">
                <div class="font-color-9A9A9A">填写人：</div>
                <div class="font-color-333 flex-1">{{ item?.oprtUserTypeName }}</div>
            </div>
            <div
              class="w-full mt-01-rem"
              style="height: 0.5px;background-color: rgba(0, 0, 0, 0.10);"
            />
            </div>
            <div
              v-for="(e, i) in item.clarifyReply"
              :key="i"
              class="mt-01-rem ft-15-rem"
            >
              <div class="flex mb-006-rem">
                <span v-if="e?.contentType === 1" class="font-color-9A9A9A">提问：</span>
                <span v-else-if="e?.contentType === 2" class="font-color-9A9A9A">回复：</span>
                <span v-else-if="e?.contentType === 3" class="font-color-9A9A9A">关闭澄清：</span>
                <span class="flex-1 font-color-333 break-word">{{ e?.content }}</span>
              </div>
              <div class="flex mb-006-rem">
                <div class="font-color-9A9A9A">操作人：</div>
                <div class="font-color-333 flex-1">{{ e?.oprtUserName }}</div>
              </div>
              <div class="flex mb-006-rem">
                <div class="font-color-9A9A9A">操作时间：</div>
                <div class="font-color-333 flex-1">{{ e?.oprtTime }}</div>
              </div>
              <div
                v-if="i !== item.clarifyReply.length -1 || item?.clarifyTypeStatus === 2"
                class="w-full mt-01-rem"
                style="height: 0.5px;background-color: rgba(0, 0, 0, 0.10);"
              />
            </div>
            <!-- 回复框 -->
            <div v-if="item?.clarifyTypeStatus === 2" class="mt-01-rem ft-15-rem">
              <div class="mb-006-rem">
                  <div class="font-color-9A9A9A">回复：</div>
                  <!-- clarifyReply[1]" -->
                  <van-field
                  style="background-color: #F3F3F3;
                  border-radius: 0.04rem;
                  margin-top: 0.06rem;"
                  v-model="item.content"
                  type="textarea"
                  maxlength="200"
                  :autosize="{ maxHeight: 200, minHeight: 110 }"
                  placeholder="请输入内容"
                  />
                  <div class="flex justify-end mt-02-rem">
                    <van-button
                      style="width: 0.975rem;border-radius: 0.2rem;"
                      type="primary"
                      loading-text="回复"
                      class="ml-01rem theme-bg-btn-color h-035rem"
                      @click="handleReply(item)"
                    >
                      回复
                    </van-button>
                  </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from "vue";
import { Toast, Notify } from 'vant';
import {
  getQuestClarifyListInfo,
  putAddClarifyReply,
} from "@/api/questionnaire";
import myStateShow from "@/components/MyStateShow.vue";
import MyPopupShow from "@/components/MyPopupShow.vue";
import { useRoute } from 'vue-router';

export default defineComponent({
  name: "DataClarificationModule", // 数据澄清
  components: {
    myStateShow,
    MyPopupShow
  },
  props: {
    updateDataClarification: {
      type: Function,
      default: () => {}
    }
  },
  setup() {
    const route = useRoute()
    const state: any = reactive({
      outerHeight: document.documentElement.scrollHeight,
      dataClarificationFlag: false,
      dataClarificationObj: {},
      questionObj: {},
      clarifyList: [],
      onLoad: () => {
        if (state.dataClarificationObj?.questItemDataId &&
        (state.questionObj?.questId || route.query?.questId)) {
          state.clarifyList = []
          getQuestClarifyListInfo(state.dataClarificationObj.questItemDataId,
          state.questionObj.questId || route.query?.questId)
          .then((res) => {
            if (res && Array.isArray(res)) {
              state.clarifyList = res
            }
          }).catch(() => {
            //
          })
        }
      },
      // 回复
      replyLoadingFlag: false,
      handleReply: (item) => {
        if (!item?.content) {
          Toast('请输入内容')
        } else {
          if (state.replyLoadingFlag) {
            return
          }
          Toast.loading({
            duration: 300000,
            message: '回复中...',
            forbidClick: true,
          });
          state.replyLoadingFlag = true
          // 生成回复
          putAddClarifyReply({
            // id: item.id,
            clarifyId: item.clarifyReply[0].clarifyId,
            content: item.content,
            oprtUserType: 1,
            contentType: 2
          })
          .then(() => {
            state.onLoad()
            Notify({ type: "success", message: "回复成功" });
            state.replyLoadingFlag = false
            Toast.clear()
          }).catch(() => {
            state.replyLoadingFlag = false
            Toast.clear()
          })
        }
      },
    });

    setTimeout(() => {
      state.outerHeight = document.documentElement.scrollHeight
    }, 0);

    return {
      ...toRefs(state),
    };
  },
});
</script>
