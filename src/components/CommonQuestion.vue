<template>
  <div
    class="questionnaire-container"
    :style="{
      height: outerHeight ? outerHeight - myNavBarHeight + 'px' : '90vh',
    }"
  >
    <div
      v-if="questionObj"
      class="questionnaire-form overflow-auto scrollnone"
      :style="{
        height: outerHeight
          ? outerHeight - (45 + myNavBarHeight) + 'px'
          : '80vh',
      }"
    >
      <!-- 头部标题等 -->
      <div
        v-if="
          editable === '1' ||
          myQuestType === '用药记录' ||
          otherQuestionnaires === '1'
        "
        class="questionnaire-title"
      >
        <div class="questionnaire-title-text">
          <h4 class="wrap2">{{ questionObj?.crfName || "" }}</h4>
          <p
            v-if="questionObj?.crfGuideline"
            v-html="questionObj.crfGuideline"
            class="wrap5"
          />
        </div>
        <!-- <img src="@/assets/baby/backQuestionnaire.svg" alt="" /> -->
      </div>
      <div v-else class="questionnaire-title-up">
        <div class="questionnaire-title-text-up">
          <p>{{ visitName || questionDataObj?.questTypeName || "" }}</p>
          <p v-if="visDate || questionDataObj?.endDate">
            {{
              visDate ||
              questionDataObj?.beginDate + " ~ " + questionDataObj?.endDate
            }}
          </p>
        </div>
      </div>
      <div v-if="editable !== '1'" class="uploadsheet-title">
        {{ questItem?.questName || "" }}
      </div>
      <!-- 问卷进度 -->
      <div
        v-if="questionIndex !== -1 && questionObj.questCrfType === 2"
        class="questionnaire-schedule"
      >
        <div
          class="questionnaire-schedule-gotop centerflex-h"
          @click="saveQuestionnaire('subtract')"
        >
          <van-icon name="arrow-left" />
          <p class="nowrap">上一题</p>
        </div>
        <div
          class="questionnaire-schedule-back"
          :class="{ 'questionnaire-schedule-need': questionObj.warningContent }"
        >
          <div
            :style="{
              width:
                questionObjLength > 1
                  ? ((questionIndex + 1) / questionObjLength) * 100 + '%'
                  : '100%',
            }"
            class="questionnaire-schedule-in"
          />
        </div>
        <div class="questionnaire-schedule-proportion">
          {{
            questionObjLength > 1
              ? (questionIndex + 1) / questionObjLength > 1
                ? "100"
                : Math.floor(((questionIndex + 1) / questionObjLength) * 100)
              : "100"
          }}%
        </div>
        <!-- 需完善按钮 -->
        <div
          v-if="questionObj.warningContent"
          class="needImprove centerflex"
          @click="needImprove(questionObj)"
        >
          需完善
        </div>
      </div>
      <!-- 问卷内容 -->
      <div class="questionnaire-body">
        <div
          v-show="questionIndex !== -1 || questionObj.questCrfType === 1"
          class="questionnaire-module scrollnone"
        >
          <!-- 问卷类型questCrfType:1 = 问卷完整列表, 2 = 问卷向导列表,
             3 = 问卷Url, 4 = 上传图片的问卷 -->
          <div
            v-if="questionObj.questCrfType === 1"
            class="questionnaire-items"
          >
            <div
              v-if="questionObj.warningContent"
              class="needImprove centerflex"
              @click="needImprove(questionObj)"
            >
              需完善
            </div>
            <div
              v-for="(item, index) in questionObj.questCrfItemes"
              :key="item.id"
            >
              <!-- crfFieldType: 1 = 文件集合, 2 = 文件, 3 = 列表, 4 = 普通字段, 5 = 说明字段, 6 = 子问卷 -->
              <div v-if="item.crfFieldType === 4 || item.crfFieldType === 6">
                <!-- crfFieldControl 1 = 无控件, 2 = 文件上传, 3 = 单行文本控件, 4 = 多行文本控件, 
                  5 = 数字控件, 6 = 日期控件, 7 = 时间控件,
                  8 = 日期时间控件, 9 = 单选控件, 10 = 多选控件 11 = 年月控件-->

                <!-- all控件 -->
                <div
                  v-if="
                    item.crfFieldControl >= 3 &&
                    item.refTypeShow !== 2 &&
                    item.refTypesShow !== 2
                  "
                  class="questionnaire-item-moduel"
                >
                  <div
                    class="questionnaire-items-title"
                    :class="{
                      'mb-0':
                        item.crfFieldControl >= 6 && item.crfFieldControl <= 8,
                    }"
                  >
                    <span v-if="item.isRequired === 1" class="isRequiredActive"
                      >*&nbsp;</span
                    >
                    <div v-html="item.fieldLabel" />
                  </div>
                  <div
                    v-if="item?.fieldDescription"
                    class="text-yellow-400 underline my-0.5"
                    @click="showFieldDescription(item.fieldDescription)"
                  >
                    填写指南
                  </div>
                  <!-- 3单行文本控件 -->
                  <van-field
                    v-if="item.crfFieldControl === 3"
                    v-model="item.fieldValue"
                    class="border-input"
                    :disabled="editable !== '1' || item?.isReadOnly > 0"
                    :placeholder="editable !== '1' ? '' : '请输入'"
                    maxlength="999"
                  >
                    <template #button>
                      <span
                        v-if="item?.dctQuestUnit"
                        v-html="item.dctQuestUnit"
                      />
                    </template>
                  </van-field>
                  <!-- 4多行文本控件 -->
                  <van-field
                    v-if="item.crfFieldControl === 4"
                    v-model="item.fieldValue"
                    maxlength="3999"
                    type="textarea"
                    class="border-input"
                    :disabled="editable !== '1' || item?.isReadOnly > 0"
                    autosize
                  />
                  <!-- 5数字控件 class="border-input"-->
                  <van-field
                    v-if="item.crfFieldControl === 5"
                    :placeholder="item?.isReadOnly > 0 ? '' : '请输入'"
                    v-model="item.fieldValue"
                    readonly
                    clickable
                    @focus="item.showNumberKeyboard = true"
                    maxlength="16"
                    :disabled="editable !== '1' || item?.isReadOnly > 0"
                    class="border-input"
                  >
                    <template #button>
                      <span
                        v-if="item?.dctQuestUnit"
                        v-html="item.dctQuestUnit"
                      />
                    </template>
                  </van-field>
                  <van-number-keyboard
                    v-model="item.numberFieldValue"
                    :show="item.showNumberKeyboard"
                    theme="custom"
                    extra-key="."
                    close-button-text="完成"
                    @input="
                      (e) => {
                        item.fieldValue += e;
                        keyUpNum(item, index);
                      }
                    "
                    @delete="
                      () => {
                        if (item?.fieldValue) {
                          item.fieldValue = item.fieldValue.slice(0, -1);
                        }
                      }
                    "
                    @blur="
                      item.showNumberKeyboard = false;
                      keyUpNum(item);
                    "
                  />
                  <!-- 6日期控件 -->
                  <div v-if="item.crfFieldControl === 6">
                    <!-- 值回显 -->
                    <van-field
                      v-model="item.fieldValue"
                      is-link
                      readonly
                      name="datetimePicker"
                      label="日期选择"
                      placeholder="点击选择日期"
                      @click="pickerDate(index)"
                    />
                    <van-popup
                      v-model:show="item.showDatas"
                      position="bottom"
                      style="min-height: 308px"
                    >
                      <van-picker
                        :columns="specificColumns"
                        @change="specificColumnsChange"
                        @confirm="
                          (e) => {
                            const startIndex = e.findIndex((e) => e === 'UK');
                            if (startIndex !== -1) {
                              e.fill('UK', startIndex + 1);
                            }
                            item.fieldValue = e[0] + '-' + e[1] + '-' + e[2];
                            item.showDatas = false;
                          }
                        "
                        @cancel="item.showDatas = false"
                      >
                        <template #title>
                          <div
                            class="ft-13-rem font-color-969799"
                            @click="
                              () => {
                                item.fieldValue = '';
                                item.showDatas = false;
                              }
                            "
                          >
                            清空
                          </div>
                        </template>
                      </van-picker>
                    </van-popup>
                  </div>
                  <!-- 7 = 时间控件, -->
                  <div v-if="item.crfFieldControl === 7">
                    <van-field
                      v-model="item.fieldValue"
                      is-link
                      readonly
                      name="datetimePicker"
                      label="时间选择"
                      placeholder="点击选择时间"
                      @click="pickerDay(index)"
                    />
                    <van-popup
                      v-model:show="item.showTimes"
                      position="bottom"
                      style="min-height: 308px"
                    >
                      <van-picker
                        :columns="timeColumns"
                        @change="columnsTimeChange"
                        @confirm="
                          (e) => {
                            const startIndex = e.findIndex((e) => e === 'UK');
                            if (startIndex !== -1) {
                              e.fill('UK', startIndex + 1);
                            }
                            item.fieldValue = e[0] + ':' + e[1];
                            item.showTimes = false;
                          }
                        "
                        @cancel="item.showTimes = false"
                      >
                        <template #title>
                          <div
                            class="ft-13-rem font-color-969799"
                            @click="
                              () => {
                                item.fieldValue = '';
                                item.showTimes = false;
                              }
                            "
                          >
                            清空
                          </div>
                        </template>
                      </van-picker>
                    </van-popup>
                  </div>
                  <!-- 8 = 日期时间控件 -->
                  <div v-if="item.crfFieldControl === 8">
                    <div class="date-and-time">
                      <!-- 日期 -->
                      <van-field
                        v-model="item.fieldValue[0]"
                        is-link
                        readonly
                        name="datetimePicker"
                        label="日期选择"
                        placeholder="点击选择日期"
                        @click="pickerDate(index)"
                      />
                      <van-popup
                        v-model:show="item.showDatas"
                        position="bottom"
                      >
                        <van-picker
                          :columns="specificColumns"
                          @change="specificColumnsChange"
                          @confirm="
                            (e) => {
                              const startIndex = e.findIndex((e) => e === 'UK');
                              if (startIndex !== -1) {
                                e.fill('UK', startIndex + 1);
                              }
                              item.fieldValue[0] =
                                e[0] + '-' + e[1] + '-' + e[2];
                              item.showDatas = false;
                            }
                          "
                          @cancel="item.showDatas = false"
                        >
                          <template #title>
                            <div
                              class="ft-13-rem font-color-969799"
                              @click="
                                () => {
                                  item.fieldValue[0] = '';
                                  item.showDatas = false;
                                }
                              "
                            >
                              清空
                            </div>
                          </template>
                        </van-picker>
                      </van-popup>
                      <!-- 时间 -->
                      <van-field
                        v-model="item.fieldValue[1]"
                        is-link
                        readonly
                        name="datetimePicker"
                        label="时间选择"
                        placeholder="点击选择时间"
                        @click="pickerDay(index)"
                      />
                      <van-popup
                        v-model:show="item.showTimes"
                        position="bottom"
                      >
                        <van-picker
                          :columns="timeColumns"
                          @change="columnsTimeChange"
                          @confirm="
                            (e) => {
                              const startIndex = e.findIndex((e) => e === 'UK');
                              if (startIndex !== -1) {
                                e.fill('UK', startIndex + 1);
                              }
                              item.fieldValue[1] = e[0] + ':' + e[1];
                              item.showTimes = false;
                            }
                          "
                          @cancel="item.showTimes = false"
                        >
                          <template #title>
                            <div
                              class="ft-13-rem font-color-969799"
                              @click="
                                () => {
                                  item.fieldValue[1] = '';
                                  item.showTimes = false;
                                }
                              "
                            >
                              清空
                            </div>
                          </template>
                        </van-picker>
                      </van-popup>
                    </div>
                  </div>
                  <!-- 9单选类型 direction="horizontal"水平-->
                  <div v-if="item.crfFieldControl === 9">
                    <van-radio-group
                      v-model="item.fieldValue"
                      :disabled="editable !== '1' || item?.isReadOnly > 0"
                    >
                      <van-radio
                        v-for="(radioItem, radioIndex) in item.fieldItems"
                        :key="radioIndex"
                        :name="radioItem.itemValue"
                        @click="invertSelectionFun(radioItem.itemValue, item)"
                        >{{ radioItem.itemName }}</van-radio
                      >
                      <!-- <van-radio name="2">单选框 2</van-radio> -->
                    </van-radio-group>
                  </div>
                  <!-- 10 = 多选控件 -->
                  <div v-if="item.crfFieldControl === 10">
                    <van-checkbox-group
                      v-model="item.fieldValue"
                      :disabled="editable !== '1' || item?.isReadOnly > 0"
                    >
                      <van-checkbox
                        v-for="(checkboxItem, checkboxIndex) in item.fieldItems"
                        :key="checkboxIndex"
                        :name="checkboxItem.itemValue"
                        @click="mutualExclusion(item, checkboxItem)"
                        >{{ checkboxItem.itemName }}</van-checkbox
                      >
                      <!-- <van-checkbox name="b">复选框 b</van-checkbox> -->
                    </van-checkbox-group>
                  </div>
                  <!-- 11 = 年月控件 -->
                  <div v-if="item.crfFieldControl === 11">
                    <van-field
                      v-model="item.fieldValue"
                      is-link
                      readonly
                      label="年月选择"
                      placeholder="点击选择年月"
                      @click="pickerYearMonth(index)"
                    />
                    <van-popup
                      v-model:show="item.showYearMonth"
                      position="bottom"
                      style="min-height: 308px"
                    >
                      <van-picker
                        :columns="yearMonthColumns"
                        @change="yearMonthColumnsChange"
                        @confirm="
                          (e) => {
                            const startIndex = e.findIndex((e) => e === 'UK');
                            if (startIndex !== -1) {
                              e.fill('UK', startIndex + 1);
                            }
                            item.fieldValue = e[0] + '-' + e[1];
                            item.showYearMonth = false;
                          }
                        "
                        @cancel="item.showYearMonth = false"
                      >
                        <template #title>
                          <div
                            class="ft-13-rem font-color-969799"
                            @click="
                              () => {
                                item.fieldValue = '';
                                item.showYearMonth = false;
                              }
                            "
                          >
                            清空
                          </div>
                        </template>
                      </van-picker>
                    </van-popup>
                  </div>
                  <div
                    v-if="
                      questionObj?.dataClarification &&
                      item?.clarify?.clarifyCount
                    "
                    class="underline flex justify-end ft-13-rem"
                    style="color: #f19980"
                    @click="openDataClarification(item)"
                  >
                    数据澄清
                    <span v-if="item?.clarify?.clarifyCount">
                      （关闭{{ item?.clarify?.clarifyCloseCount }}/{{
                        item?.clarify?.clarifyCount
                      }}）</span
                    >
                  </div>
                </div>
              </div>
              <!-- 文件集合 -->
              <div
                v-if="
                  item.refTypeShow !== 2 &&
                  item.refTypesShow !== 2 &&
                  item.crfFieldType === 1 &&
                  editable === '1' &&
                  item?.isReadOnly === 0
                "
                class="pd-w10"
              >
                <div class="questionnaire-items-title">
                  <span v-if="item.isRequired === 1" class="isRequiredActive"
                    >*&nbsp;</span
                  >
                  <div v-html="item.fieldLabel" />
                </div>
                <div
                  v-if="item?.fieldDescription"
                  class="text-yellow-400 my-0.5 underline"
                  @click="showFieldDescription(item.fieldDescription)"
                >
                  填写指南
                </div>
                <div
                  style="
                    border-radius: 0.1rem;
                    background: #fff;
                    overflow: hidden;
                    margin-bottom: 0.2rem;
                  "
                >
                  <div class="upload-img">
                    <UploadImg
                      :questId="visitId || questionObj?.questId"
                      :postImgs="postQuestFile"
                      :deleteImg="deleteQuestFile"
                      :dctCodeItem="item"
                      :disabledSaveFalg="disabledSaveFalg"
                    />
                  </div>
                  <div
                    v-if="
                      questionObj?.dataClarification &&
                      item?.clarify?.clarifyCount
                    "
                    class="underline mt-01-rem flex justify-end ft-13-rem"
                    style="color: #f19980"
                    @click="openDataClarification(item)"
                  >
                    数据澄清
                    <span v-if="item?.clarify?.clarifyCount">
                      （关闭{{ item?.clarify?.clarifyCloseCount }}/{{
                        item?.clarify?.clarifyCount
                      }}）
                    </span>
                  </div>
                </div>
              </div>
              <!-- 只读 -->
              <div
                v-if="
                  item.refTypeShow !== 2 &&
                  item.refTypesShow !== 2 &&
                  item.crfFieldType === 1 &&
                  (editable === '0' || item?.isReadOnly > 0)
                "
                class="pd-w10"
              >
                <div class="questionnaire-items-title">
                  <span v-if="item.isRequired === 1" class="isRequiredActive"
                    >*&nbsp;</span
                  >
                  <div v-html="item.fieldLabel" />
                </div>
                <div
                  v-if="item?.fieldDescription"
                  class="text-yellow-400 my-0.5 underline"
                  @click="showFieldDescription(item.fieldDescription)"
                >
                  填写指南
                </div>
                <div
                  style="
                    border-radius: 0.1rem;
                    background: #fff;
                    overflow: hidden;
                    margin-bottom: 0.2rem;
                  "
                >
                  <div
                    v-if="item?.fileListUrl?.length"
                    class="upload-img upload-imgs"
                  >
                    <img
                      v-for="(fileItem, index) in item.fileListUrl"
                      :key="index"
                      :src="fileItem"
                      alt=""
                      @click="tab(item.fileListStr, index)"
                    />
                  </div>
                  <div v-else class="upload-img upload-imgs">
                    <p class="w-full centerflex">暂未上传</p>
                  </div>
                  <div
                    v-if="
                      questionObj?.dataClarification &&
                      item?.clarify?.clarifyCount
                    "
                    class="mr-01rem underline flex justify-end ft-13-rem"
                    style="color: #f19980"
                    @click="openDataClarification(item)"
                  >
                    数据澄清
                    <span v-if="item?.clarify?.clarifyCount">
                      （关闭{{ item?.clarify?.clarifyCloseCount }}/{{
                        item?.clarify?.clarifyCount
                      }}）
                    </span>
                  </div>
                </div>
              </div>
              <!-- 列表类型 -->
              <QuestionList
                v-if="
                  item.crfFieldType === 3 &&
                  item.refTypeShow !== 2 &&
                  item.refTypesShow !== 2 &&
                  item?.children?.length
                "
                :listArrs="item.children"
                :viewItem="item"
                :addListFun="addListFun"
                :editListFun="editListFun"
                :deleteListFun="deleteListFun"
                :editFlag="
                  editable === '1' && item?.isReadOnly === 0 ? '1' : '0'
                "
              />
              <!-- xx类型 -->
            </div>
          </div>

          <!-- 问卷 2 = 问卷向导列表 -->
          <div
            v-if="questionObj.questCrfType === 2"
            class="questionnaire-items"
          >
            <div
              v-for="(item, index) in questionObj.questCrfItemes"
              :key="item.id"
            >
              <!-- crfFieldType: 1 = 文件集合, 2 = 文件, 3 = 列表, 4 = 普通字段, 5 = 说明字段, 6 = 子问卷 -->
              <div v-if="item.crfFieldType === 4 || item.crfFieldType === 6">
                <!-- crfFieldControl 1 = 无控件, 2 = 文件上传, 3 = 单行文本控件, 4 = 多行文本控件, 
                  5 = 数字控件, 6 = 日期控件, 7 = 时间控件,
                  8 = 日期时间控件, 9 = 单选控件, 10 = 多选控件 11 = 年月控件-->

                <!-- all控件 -->
                <div
                  v-if="
                    item.crfFieldControl >= 3 &&
                    index === questionIndex &&
                    item.refTypeShow !== 2
                  "
                  class="questionnaire-item-moduel"
                >
                  <div
                    class="questionnaire-items-title"
                    :class="{
                      'mb-0':
                        item.crfFieldControl >= 6 && item.crfFieldControl <= 8,
                    }"
                  >
                    <span v-if="item.isRequired === 1" class="isRequiredActive"
                      >*&nbsp;</span
                    >
                    <div v-html="item.fieldLabel" />
                  </div>
                  <div
                    v-if="item?.fieldDescription"
                    class="text-yellow-400 underline my-0.5"
                    @click="showFieldDescription(item.fieldDescription)"
                  >
                    填写指南
                  </div>
                  <!-- 3单行文本控件 -->
                  <van-field
                    v-if="item.crfFieldControl === 3"
                    v-model="item.fieldValue"
                    class="border-input"
                    :disabled="editable !== '1' || item?.isReadOnly > 0"
                    :placeholder="editable !== '1' ? '' : '请输入'"
                    maxlength="999"
                  >
                    <template #button>
                      <span
                        v-if="item?.dctQuestUnit"
                        v-html="item.dctQuestUnit"
                      />
                    </template>
                  </van-field>
                  <!-- 4多行文本控件 -->
                  <van-field
                    v-if="item.crfFieldControl === 4"
                    v-model="item.fieldValue"
                    maxlength="3999"
                    type="textarea"
                    class="border-input"
                    :disabled="editable !== '1' || item?.isReadOnly > 0"
                    autosize
                  />
                  <!-- 5数字控件 class="border-input"-->
                  <van-field
                    v-if="item.crfFieldControl === 5"
                    v-model="item.fieldValue"
                    :placeholder="item?.isReadOnly > 0 ? '' : '请输入'"
                    readonly
                    clickable
                    @focus="item.showNumberKeyboard = true"
                    maxlength="16"
                    :disabled="editable !== '1' || item?.isReadOnly > 0"
                    class="border-input"
                  >
                    <template #button>
                      <span
                        v-if="item?.dctQuestUnit"
                        v-html="item.dctQuestUnit"
                      />
                    </template>
                  </van-field>

                  <van-number-keyboard
                    v-model="item.numberFieldValue"
                    :show="item.showNumberKeyboard"
                    theme="custom"
                    extra-key="."
                    close-button-text="完成"
                    @input="
                      (e) => {
                        item.fieldValue += e;
                        keyUpNum(item, index);
                      }
                    "
                    @delete="
                      () => {
                        if (item?.fieldValue) {
                          item.fieldValue = item.fieldValue.slice(0, -1);
                        }
                      }
                    "
                    @blur="
                      item.showNumberKeyboard = false;
                      keyUpNum(item);
                    "
                  />

                  <!-- 6日期控件 -->
                  <div v-if="item.crfFieldControl === 6">
                    <!-- 值回显 -->
                    <van-field
                      v-model="item.fieldValue"
                      is-link
                      readonly
                      name="datetimePicker"
                      label="日期选择"
                      placeholder="点击选择日期"
                      @click="pickerDate(index)"
                    />
                    <van-popup
                      v-model:show="item.showDatas"
                      position="bottom"
                      style="min-height: 308px"
                    >
                      <van-picker
                        :columns="specificColumns"
                        @change="specificColumnsChange"
                        @confirm="
                          (e) => {
                            const startIndex = e.findIndex((e) => e === 'UK');
                            if (startIndex !== -1) {
                              e.fill('UK', startIndex + 1);
                            }
                            item.fieldValue = e[0] + '-' + e[1] + '-' + e[2];
                            item.showDatas = false;
                          }
                        "
                        @cancel="item.showDatas = false"
                      >
                        <template #title>
                          <div
                            class="ft-13-rem font-color-969799"
                            @click="
                              () => {
                                item.fieldValue = '';
                                item.showDatas = false;
                              }
                            "
                          >
                            清空
                          </div>
                        </template>
                      </van-picker>
                    </van-popup>
                  </div>
                  <!-- 7 = 时间控件, -->
                  <div v-if="item.crfFieldControl === 7">
                    <van-field
                      v-model="item.fieldValue"
                      is-link
                      readonly
                      name="datetimePicker"
                      label="时间选择"
                      placeholder="点击选择时间"
                      @click="pickerDay(index)"
                    />
                    <van-popup
                      v-model:show="item.showTimes"
                      position="bottom"
                      style="min-height: 308px"
                    >
                      <van-picker
                        :columns="timeColumns"
                        @change="columnsTimeChange"
                        @confirm="
                          (e) => {
                            const startIndex = e.findIndex((e) => e === 'UK');
                            if (startIndex !== -1) {
                              e.fill('UK', startIndex + 1);
                            }
                            item.fieldValue = e[0] + ':' + e[1];
                            item.showTimes = false;
                          }
                        "
                        @cancel="item.showTimes = false"
                      >
                        <template #title>
                          <div
                            class="ft-13-rem font-color-969799"
                            @click="
                              () => {
                                item.fieldValue = '';
                                item.showTimes = false;
                              }
                            "
                          >
                            清空
                          </div>
                        </template>
                      </van-picker>
                    </van-popup>
                  </div>
                  <!-- 8 = 日期时间控件 -->
                  <div v-if="item.crfFieldControl === 8">
                    <div class="date-and-time">
                      <!-- 日期 -->
                      <van-field
                        v-model="item.fieldValue[0]"
                        is-link
                        readonly
                        name="datetimePicker"
                        label="日期选择"
                        placeholder="点击选择日期"
                        @click="pickerDate(index)"
                      />
                      <van-popup
                        v-model:show="item.showDatas"
                        position="bottom"
                      >
                        <van-picker
                          :columns="specificColumns"
                          @change="specificColumnsChange"
                          @confirm="
                            (e) => {
                              const startIndex = e.findIndex((e) => e === 'UK');
                              if (startIndex !== -1) {
                                e.fill('UK', startIndex + 1);
                              }
                              item.fieldValue[0] =
                                e[0] + '-' + e[1] + '-' + e[2];
                              item.showDatas = false;
                            }
                          "
                          @cancel="item.showDatas = false"
                        >
                          <template #title>
                            <div
                              class="ft-13-rem font-color-969799"
                              @click="
                                () => {
                                  item.fieldValue[0] = '';
                                  item.showDatas = false;
                                }
                              "
                            >
                              清空
                            </div>
                          </template>
                        </van-picker>
                      </van-popup>
                      <!-- 时间 -->
                      <van-field
                        v-model="item.fieldValue[1]"
                        is-link
                        readonly
                        name="datetimePicker"
                        label="时间选择"
                        placeholder="点击选择时间"
                        @click="pickerDay(index)"
                      />
                      <van-popup
                        v-model:show="item.showTimes"
                        position="bottom"
                      >
                        <van-picker
                          :columns="timeColumns"
                          @change="columnsTimeChange"
                          @confirm="
                            (e) => {
                              const startIndex = e.findIndex((e) => e === 'UK');
                              if (startIndex !== -1) {
                                e.fill('UK', startIndex + 1);
                              }
                              item.fieldValue[1] = e[0] + ':' + e[1];
                              item.showTimes = false;
                            }
                          "
                          @cancel="item.showTimes = false"
                        >
                          <template #title>
                            <div
                              class="ft-13-rem font-color-969799"
                              @click="
                                () => {
                                  item.fieldValue[1] = '';
                                  item.showTimes = false;
                                }
                              "
                            >
                              清空
                            </div>
                          </template>
                        </van-picker>
                      </van-popup>
                    </div>
                  </div>
                  <!-- 9单选类型 direction="horizontal"水平-->
                  <div v-if="item.crfFieldControl === 9">
                    <van-radio-group
                      v-model="item.fieldValue"
                      :disabled="editable !== '1' || item?.isReadOnly > 0"
                    >
                      <van-radio
                        v-for="(radioItem, radioIndex) in item.fieldItems"
                        :key="radioIndex"
                        :name="radioItem.itemValue"
                        @click="invertSelectionFun(radioItem.itemValue, item)"
                        >{{ radioItem.itemName }}</van-radio
                      >
                      <!-- <van-radio name="2">单选框 2</van-radio> -->
                    </van-radio-group>
                  </div>
                  <!-- 10 = 多选控件 -->
                  <div v-if="item.crfFieldControl === 10">
                    <van-checkbox-group
                      v-model="item.fieldValue"
                      :disabled="editable !== '1' || item?.isReadOnly > 0"
                    >
                      <van-checkbox
                        v-for="(checkboxItem, checkboxIndex) in item.fieldItems"
                        :key="checkboxIndex"
                        :name="checkboxItem.itemValue"
                        @click="mutualExclusion(item, checkboxItem)"
                        >{{ checkboxItem.itemName }}</van-checkbox
                      >
                      <!-- <van-checkbox name="b">复选框 b</van-checkbox> -->
                    </van-checkbox-group>
                  </div>
                  <!-- 11年月控件 -->
                  <div v-if="item.crfFieldControl === 11">
                    <van-field
                      v-model="item.fieldValue"
                      is-link
                      readonly
                      label="年月选择"
                      placeholder="点击选择年月"
                      @click="pickerYearMonth(index)"
                    />
                    <van-popup
                      v-model:show="item.showYearMonth"
                      position="bottom"
                      style="min-height: 308px"
                    >
                      <van-picker
                        :columns="yearMonthColumns"
                        @change="yearMonthColumnsChange"
                        @confirm="
                          (e) => {
                            const startIndex = e.findIndex((e) => e === 'UK');
                            if (startIndex !== -1) {
                              e.fill('UK', startIndex + 1);
                            }
                            item.fieldValue = e[0] + '-' + e[1];
                            item.showYearMonth = false;
                          }
                        "
                        @cancel="item.showYearMonth = false"
                      >
                        <template #title>
                          <div
                            class="ft-13-rem font-color-969799"
                            @click="
                              () => {
                                item.fieldValue = '';
                                item.showYearMonth = false;
                              }
                            "
                          >
                            清空
                          </div>
                        </template>
                      </van-picker>
                    </van-popup>
                  </div>
                  <div
                    v-if="
                      questionObj?.dataClarification &&
                      item?.clarify?.clarifyCount
                    "
                    class="underline flex justify-end ft-13-rem"
                    style="color: #f19980"
                    @click="openDataClarification(item)"
                  >
                    数据澄清
                    <span v-if="item?.clarify?.clarifyCount">
                      （关闭{{ item?.clarify?.clarifyCloseCount }}/{{
                        item?.clarify?.clarifyCount
                      }}）
                    </span>
                  </div>
                </div>
              </div>

              <!-- 文件集合 -->
              <div
                v-if="
                  item.crfFieldType === 1 &&
                  index === questionIndex &&
                  item.refTypeShow !== 2 &&
                  item.refTypesShow !== 2 &&
                  editable === '1' &&
                  item?.isReadOnly === 0
                "
                class="pd-w10"
              >
                <div class="questionnaire-items-title">
                  <span v-if="item.isRequired === 1" class="isRequiredActive"
                    >*&nbsp;</span
                  >
                  <div v-html="item.fieldLabel" />
                </div>
                <div
                  v-if="item?.fieldDescription"
                  class="text-yellow-400 my-0.5 underline"
                  @click="showFieldDescription(item.fieldDescription)"
                >
                  填写指南
                </div>
                <div
                  style="
                    border-radius: 0.1rem;
                    background: #fff;
                    overflow: hidden;
                    margin-bottom: 0.2rem;
                  "
                >
                  <div class="upload-img">
                    <UploadImg
                      :questId="visitId || questionObj?.questId"
                      :postImgs="postQuestFile"
                      :deleteImg="deleteQuestFile"
                      :dctCodeItem="item"
                      :disabledSaveFalg="disabledSaveFalg"
                    />
                  </div>
                  <div
                    v-if="
                      questionObj?.dataClarification &&
                      item?.clarify?.clarifyCount
                    "
                    class="underline p-01rem mt-01-rem flex justify-end ft-13-rem"
                    style="color: #f19980"
                    @click="openDataClarification(item)"
                  >
                    数据澄清
                    <span v-if="item?.clarify?.clarifyCount">
                      （关闭{{ item?.clarify?.clarifyCloseCount }}/{{
                        item?.clarify?.clarifyCount
                      }}）
                    </span>
                  </div>
                </div>
              </div>
              <!-- 只读 -->
              <div
                v-if="
                  item.crfFieldControl === 2 &&
                  (editable === '0' || item?.isReadOnly > 0)
                "
                class="pd-w10"
              >
                <div class="questionnaire-items-title">
                  <span v-if="item.isRequired === 1" class="isRequiredActive"
                    >*&nbsp;</span
                  >
                  <div v-html="item.fieldLabel" />
                </div>
                <div
                  v-if="item?.fieldDescription"
                  class="text-yellow-400 my-0.5 underline"
                  @click="showFieldDescription(item.fieldDescription)"
                >
                  填写指南
                </div>
                <div
                  style="
                    border-radius: 0.1rem;
                    background: #fff;
                    overflow: hidden;
                    margin-bottom: 0.2rem;
                  "
                >
                  <div
                    v-if="item?.fileListUrl?.length"
                    class="upload-img upload-imgs"
                  >
                    <img
                      v-for="(fileItem, index) in item.fileListUrl"
                      :key="index"
                      :src="fileItem"
                      alt=""
                      @click="tab(item.fileListStr, index)"
                    />
                  </div>
                  <div v-else class="upload-img upload-imgs">
                    <p class="w-full centerflex">暂未上传</p>
                  </div>
                  <div
                    v-if="
                      questionObj?.dataClarification &&
                      item?.clarify?.clarifyCount
                    "
                    class="underline mt-01-rem flex justify-end ft-13-rem"
                    style="color: #f19980"
                    @click="openDataClarification(item)"
                  >
                    数据澄清
                    <span v-if="item?.clarify?.clarifyCount">
                      （关闭{{ item?.clarify?.clarifyCloseCount }}/{{
                        item?.clarify?.clarifyCount
                      }}）
                    </span>
                  </div>
                </div>
              </div>
              <!-- 列表类型 -->
              <QuestionList
                v-if="
                  item.crfFieldType === 3 &&
                  index === questionIndex &&
                  item.refTypeShow !== 2 &&
                  item.refTypesShow !== 2 &&
                  item?.children?.length
                "
                :listArrs="item.children"
                :viewItem="item"
                :addListFun="addListFun"
                :editListFun="editListFun"
                :deleteListFun="deleteListFun"
                :editFlag="
                  editable === '1' && item?.isReadOnly === 0 ? '1' : '0'
                "
              />
              <!-- xx类型 -->
            </div>
          </div>

          <!-- 问卷 4 = 上传图片 -->
          <div
            v-if="questionObj.questCrfType === 4 && editable === '1'"
            class="questionnaire-items"
          >
            <div
              v-for="(item, index) in questionObj.questCrfItemes"
              :key="item.id"
            >
              <!-- crfFieldType: 1 = 文件集合, 2 = 文件, 3 = 列表, 4 = 普通字段, 5 = 说明字段, 6 = 子问卷 -->
              <!-- 文件集合 -->
              <div
                v-if="item.crfFieldType === 1 && index === questionIndex"
                class="pd-w10"
              >
                <div class="questionnaire-items-title">
                  <span v-if="item.isRequired === 1" class="isRequiredActive"
                    >*&nbsp;</span
                  >
                  <div v-html="item.fieldLabel" />
                </div>
                <div
                  v-if="item?.fieldDescription"
                  class="text-yellow-400 my-0.5 underline"
                  @click="showFieldDescription(item.fieldDescription)"
                >
                  填写指南
                </div>

                <div
                  v-if="item?.isReadOnly === 0"
                  style="
                    border-radius: 0.1rem;
                    background: #fff;
                    overflow: hidden;
                    margin-bottom: 0.2rem;
                  "
                >
                  <div class="upload-img">
                    <UploadImg
                      :questId="visitId || questionObj?.questId"
                      :postImgs="postQuestFile"
                      :deleteImg="deleteQuestFile"
                      :dctCodeItem="item"
                      :disabledSaveFalg="disabledSaveFalg"
                    />
                  </div>
                  <div
                    v-if="
                      questionObj?.dataClarification &&
                      item?.clarify?.clarifyCount
                    "
                    class="underline mt-01-rem flex justify-end ft-13-rem"
                    style="color: #f19980"
                    @click="openDataClarification(item)"
                  >
                    数据澄清
                    <span v-if="item?.clarify?.clarifyCount">
                      （关闭{{ item?.clarify?.clarifyCloseCount }}/{{
                        item?.clarify?.clarifyCount
                      }}）
                    </span>
                  </div>
                </div>
                <div
                  v-else
                  style="
                    border-radius: 0.1rem;
                    background: #fff;
                    overflow: hidden;
                    margin-bottom: 0.2rem;
                  "
                >
                  <div
                    v-if="item?.fileListUrl?.length"
                    class="upload-img upload-imgs"
                  >
                    <img
                      v-for="(fileItem, index) in item.fileListUrl"
                      :key="index"
                      :src="fileItem"
                      alt=""
                      @click="tab(item.fileListStr, index)"
                    />
                  </div>
                  <div v-else class="upload-img upload-imgs">
                    <p class="w-full centerflex">暂未上传</p>
                  </div>
                  <div
                    v-if="
                      questionObj?.dataClarification &&
                      item?.clarify?.clarifyCount
                    "
                    class="underline mt-01-rem flex justify-end ft-13-rem"
                    style="color: #f19980"
                    @click="openDataClarification(item)"
                  >
                    数据澄清
                    <span v-if="item?.clarify?.clarifyCount">
                      （关闭{{ item?.clarify?.clarifyCloseCount }}/{{
                        item?.clarify?.clarifyCount
                      }}）
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 只读 -->
          <div
            v-if="questionObj.questCrfType === 4 && editable === '0'"
            class="pd-w10"
          >
            <div class="questionnaire-items-title">
              <span v-if="item.isRequired === 1" class="isRequiredActive"
                >*&nbsp;</span
              >
              <div v-html="item.fieldLabel" />
            </div>
            <div
              v-if="item?.fieldDescription"
              class="text-yellow-400 my-0.5 underline"
              @click="showFieldDescription(item.fieldDescription)"
            >
              填写指南
            </div>
            <div
              style="
                border-radius: 0.1rem;
                background: #fff;
                overflow: hidden;
                margin-bottom: 0.2rem;
              "
            >
              <div
                v-if="item?.fileListUrl?.length"
                class="upload-img upload-imgs"
              >
                <img
                  v-for="(fileItem, index) in item.fileListUrl"
                  :key="index"
                  :src="fileItem"
                  alt=""
                  @click="tab(item.fileListStr, index)"
                />
              </div>
              <div v-else class="upload-img upload-imgs">
                <p class="w-full centerflex">暂未上传</p>
              </div>
              <div
                v-if="
                  questionObj?.dataClarification && item?.clarify?.clarifyCount
                "
                class="underline mt-01-rem flex justify-end ft-13-rem"
                style="color: #f19980"
                @click="openDataClarification(item)"
              >
                数据澄清
                <span v-if="item?.clarify?.clarifyCount">
                  （关闭{{ item?.clarify?.clarifyCloseCount }}/{{
                    item?.clarify?.clarifyCount
                  }}）
                </span>
              </div>
            </div>
          </div>
        </div>
        <!-- 填写指南 -->
        <div
          v-show="questionIndex === -1 && myQuestType === '入排问卷'"
          class="questionnaire-module scrollnone"
        >
          <p
            v-if="questionObj.crfGuideline"
            v-html="questionObj.crfGuideline"
            class="fingerpost"
          />
        </div>
        <MyPopupShow
          :myPopupShow="myDeleteListPopupFlag"
          title="提示"
          texts="
          <div class='mb-01rem ft-13-rem'>是否确认删除？</div>
          <div class='text-red-400 ft-13-rem'>该数据已提交过，如需更新请点击编辑<div>"
          cancelText="取消"
          saveText="确认删除"
          cancelClass="text-black"
          saveClass="text-black"
          :handleSave="handleSaveDeleteList"
          :handleCancel="handleCancelDeleteList"
        />
      </div>
    </div>
    <!-- 按钮 -->
    <div class="w-full">
      <van-button
        v-if="questionIndex === -1 && questionObj.questCrfType === 2"
        class="next-btn questionnaire-common-btn"
        @click="saveQuestionnaire('start')"
        >开始</van-button
      >
      <!-- :loading="submitLoadingFlag" -->
      <van-button
        v-else-if="
          (questionIndex === questionObjLength - 1 ||
            questionObj.questCrfType === 1) &&
          editable === '1'
        "
        class="questionnaire-common-btn"
        type="primary"
        loading-text="提交"
        :disabled="disabledSaveFalg.disabledSaveFalg"
        @click="saveQuestionnaire"
        >提交</van-button
      >
      <van-button
        v-else-if="
          questionObj.questCrfType === 2 &&
          questionIndex < questionObjLength &&
          editable === '1'
        "
        class="questionnaire-common-btn"
        type="primary"
        @click="saveQuestionnaire('add')"
        >下一步</van-button
      >
    </div>
  </div>
  <!-- 逻辑核查页面 -->
  <VerificationRequiredModule
    ref="verificationRequiredModuleRef"
    :saveQuestionnaire="putSaveQuestionnaire"
  />
  <!-- 数据澄清页面 -->
  <DataClarificationModule
    ref="DataClarificationRef"
    :updateDataClarification="updateDataClarification"
  />
  <!-- 数据变动 DataChangeModule-->
  <DataChangeModule
    ref="DataChangeModuleRef"
    :saveDataChangeFun="saveDataChangeFun"
  />
  <!-- 计划外 -->
  <UnplannedModification
    ref="UnplannedModificationRef"
    :getQuestionnaireCustomize="getQuestionnaireCustomize"
  />
  <!-- 蒙层 -->
  <TransparentMask v-if="submitLoadingFlag" />
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount, getCurrentInstance, nextTick, watchEffect, watch } from 'vue';
import { useRoute } from "vue-router";
import { useStore } from "vuex";
import { Notify, ImagePreview, Dialog, Toast } from 'vant';
import UploadImg from "@/components/UploadImg.vue";
import {
  getAccessFile, getQuestInfo, posCheckVisit,
  postClearStagingData, putPatientQuestChangeData,
  postPatientQuestListCheck
} from '@/api/questionnaire'
import { deepClone, delay, } from '@trialdata/common-fun-css/index'
import QuestionList from "@/components/QuestionList.vue";
import DataClarificationModule from "@/components/DataClarificationModule.vue";
import { getDurgDataBeforehand } from '@/api/takeMedicineLog';
import {
  maintainedFun, monthArr,
  hourArr, minuteArr,
  dayArr, ThirtyDaysArr,
  isLeapYear, UKArr, returnPatientStatusUrl,
  parseTimeNYDHMS, nowDayArr, nowMonthArr
} from "@/utils/debounce";
import { putAutoPeriodicCompensationApply } from '@/api/compensationReimbursement';
// import { CommonQuestionStateInter } from "@/types/components";
import MyPopupShow from "@/components/MyPopupShow.vue";
import DataChangeModule from "@/components/DataChangeModule.vue";
import TransparentMask from "@/components/TransparentMask.vue";
import VerificationRequiredModule from "@/components/VerificationRequiredModule.vue";
import { getPatientStatus } from '@/api/user';
import { postConsentSupplementComfirm } from '@/api/questionnaire'
import UnplannedModification from "@/components/UnplannedModificationModule.vue";

export default defineComponent({
  name: "CommonQuestion", // 通用问卷
  components: {
    UploadImg,
    QuestionList,
    MyPopupShow,
    DataClarificationModule,
    DataChangeModule,
    TransparentMask,
    VerificationRequiredModule,
    UnplannedModification
  },
  props: {
    // 自义定-问卷类型
    myQuestType: {
      type: String,
      default: '随访问卷',
    },
    // 是否需要去除部分高度
    myNavBarHeight: {
      type: Number,
      default: 46,
    },
    getQuestView: {
      type: Function,
      default: () => { },
    },
    postQuestFile: {
      type: Function,
      default: () => { },
    },
    deleteQuestFile: {
      type: Function,
      default: () => { },
    },
    getQuestData: {
      type: Function,
      default: () => { },
    },
    putQuestData: {
      type: Function,
      default: () => { },
    },
    getTableList: {
      type: Function,
      default: () => { },
    },
    deleteTable: {
      type: Function,
      default: () => { },
    },
    // 埋点
    postICFGeneralInfoTime: {
      type: Function,
      default: () => { },
    },
    // 首次加载
    questionOnload: {
      type: Function,
      default: () => { },
    },
  },
  setup(props) {
    const regex = /(<([^>]+)>)/ig
    const proxy: any = getCurrentInstance()?.proxy
    const store = useStore();
    const route = useRoute();
    // CommonQuestionStateInter
    const state: any = reactive({
      // 数据变动返回
      backQuestionIndex: -1,
      // 消息模板打开
      routeOpenDataClarificationFlag: true,
      // 计划外
      UnplannedModificationRef: null,
      // 数据变动
      DataChangeModuleRef: null,
      openDataChangeModule: (item) => {
        if (item?.questCrfItemes.length && item?.questCrfItemes.filter((e) => e?.isDisplay).length) {
          item.questCrfItemes.map((e) => e.oldRemarksStr = e.remarksStr)
          state.DataChangeModuleRef.dataChangeModuleObj = deepClone(item)
          state.DataChangeModuleRef.dataChangeModuleFlag = true
        }
      },
      answerQuestdata: {
        answer: {},
        reocrd: {}
      },
      // 数据变动提交 保存后
      saveDataChangeFun: (quesDataObj) => {
        return new Promise<void>(async (resolve, reject) => {
          try {
            if (props.myQuestType === '用药记录') {
              const data = {
                answer: quesDataObj,
                reocrd: state.answerQuestdata.reocrd
              }
              await props.putQuestData(quesDataObj.questId, data)
              state.submitSaveLoadingFlag = true
              // console.log(store.state.routerInn);
              const routerInnBack = { ...store.state.routerInn[store.state.routerInn.length - 1] }
              Notify({ type: "success", message: "提交成功" });
              if (routerInnBack.backPath === '/newTakemedicineLog') {
                proxy.$routerBackFun()
              } else if (store.state.routerInn?.length > 1) {
                proxy.$routerBackFun(2)
              } else {
                proxy.$routerBackFun()
              }
              state.submitLoadingFlag = false
              Toast.clear();
            } else {
              const res = await props.putQuestData(quesDataObj.questId, quesDataObj)
              state.submitSaveLoadingFlag = true
              resolve()
              Notify({ type: "success", message: "提交成功" });
              store.dispatch('setGetOldQuestDataFlag', 0)
              if (props.myQuestType === '随访问卷') {
                if (store.state?.homeList?.dctPatientId) {
                  // 补偿报销申请创建 -> 访视问卷保存完调用
                  putAutoPeriodicCompensationApply(store.state.homeList.dctPatientId, quesDataObj?.questId)
                }
                if (store.state?.homeList?.patientStatus === 10) {
                  store.dispatch('setTaskText', state.questionObj.crfName)
                } else {
                  /* 筛选中的时候随访问卷保存需要调用 */
                  posCheckVisit(quesDataObj?.questId)
                }
              } else if (props.myQuestType === '知情问卷') {
                const url = returnPatientStatusUrl(res)
                if (url) {
                  proxy.$routerGoFun('replace', url)
                  return
                }
              }
              proxy.$routerBackFun()
            }
          } catch {
            reject()
          }
        })
      },
      // 数据澄清相关
      DataClarificationRef: null,
      openDataClarification: (item) => {
        state.DataClarificationRef.dataClarificationFlag = true
        state.DataClarificationRef.dataClarificationObj = deepClone(item)
        state.DataClarificationRef.questionObj = deepClone(state.questionObj)
        state.DataClarificationRef.onLoad()
      },
      //
      // refTypeShowFlag: false,
      // 用于列表 增删改触发 暂存
      rowChange: false, // 行号是否改变
      // 主问卷里的题目是否改变
      initialIsChange: false,
      //
      isChange: false,
      myDeleteListPopupFlag: false,
      myDeleteListPopupObj: {
        questId: "",
        pDctCode: "",
        rowId: ""
      },
      // 时分的 自定义
      timeColumns: [
        {
          values: hourArr,
          defaultIndex: 0,
        },
        // 第二列 月
        {
          values: [],
          defaultIndex: 0,
        },
      ],
      // 时变分变
      columnsTimeChange: (e, l) => {
        if (l === 0) {
          // 时UK则 - 分UK
          if (e[0] === "UK") {
            state.timeColumns[1].defaultIndex = 0;
            state.timeColumns[1].values = UKArr;
          } else if (state.timeColumns[1].values.length < 2) {
            const minuteOrUKArr = [...minuteArr]
            if (state.questionObj?.isAllowUK) {
              minuteOrUKArr.unshift('UK');
            }
            state.timeColumns[1].values = minuteOrUKArr;
          }
        }
      },
      // 时分定位当前值所在的下标
      setTimeDefaultIndex: (timeStr) => {
        if (timeStr) {
          const result = timeStr.split(':');
          if (result?.length && result[0] === 'UK') {
            state.timeColumns[1].values = UKArr
          }
          state.saveTimeDefaultIndex(result)
        } else {
          state.timeColumns[1].values = state.questionObj?.isAllowUK ? [...UKArr, ...minuteArr] : minuteArr
          // 没值定位到当前时分
          const dateMyTimes = new Date().toLocaleTimeString().split(':')
          state.saveTimeDefaultIndex(dateMyTimes)
        }
      },
      // 保存时分 下标
      saveTimeDefaultIndex: (result) => {
        state.timeColumns[0].values.forEach((el, index) => {
          if (el === result[0]) {
            state.timeColumns[0].defaultIndex = index
          }
        });
        state.timeColumns[1].values.forEach((el, index) => {
          if (el === result[1]) {
            state.timeColumns[1].defaultIndex = index
          }
        });
      },
      // 生成年数组
      setYearMonthColumns: (columns, min, max, dateValue = '') => {
        let dateArr = []
        if (dateValue) {
          dateArr = dateValue.split('-')
        } else {
          const data = parseTimeNYDHMS(new Date(), '{y}-{m}-{d}')
          dateArr = data.split('-')
        }
        const newColumnsArr = []
        let item = min
        for (let index = min; index <= max; index++) {
          const itemstr = item + ''
          newColumnsArr.push(itemstr)
          item += 1
        }
        if (state.questionObj?.isAllowUK) {
          newColumnsArr.unshift('UK');
        }
        const yearMonthColumnsValues = state.monthShiftChange(dateArr)
        if (columns) {
          columns[0].values = newColumnsArr
          columns[1].values = yearMonthColumnsValues
          if (columns.length === 3) {
            columns[2].values = state.dayShiftChange(dateArr)
          }
        }
      },
      // 年月定位当前值所在的下标
      setYearMonthDefaultIndex: (yearMonthStr) => {
        if (yearMonthStr) {
          const result = yearMonthStr.split('-');
          if (result?.length && result[0] === 'UK') {
            state.yearMonthColumns[1].values = UKArr
          }
          state.yearMonthColumns[0].values.forEach((el, index) => {
            if (el === result[0]) {
              state.yearMonthColumns[0].defaultIndex = index
            }
          });
          state.yearMonthColumns[1].values.forEach((el, index) => {
            if (el === result[1]) {
              state.yearMonthColumns[1].defaultIndex = index
            }
          });
        } else {
          const date = new Date();
          const year = date.getFullYear();
          const month = date.getMonth() + 1;
          state.yearMonthColumns[0].values.forEach((el, idx) => {
            if (el === year + '') {
              state.yearMonthColumns[0].defaultIndex = idx
            }
          });
          state.yearMonthColumns[1].values = state.monthShiftChange(1)
          if (state.yearMonthColumns[0].values[state.yearMonthColumns[0].defaultIndex] === 'UK') {
            state.yearMonthColumns[1].values = UKArr
          }
          state.yearMonthColumns[1].values.forEach((el, idx) => {
            if (el / 1 === month) {
              state.yearMonthColumns[1].defaultIndex = idx
            }
          });
        }
      },
      yearMonthColumns: [
        // 第一列 年 默认1900-2099
        {
          values: [],
          defaultIndex: 0,
        },
        // 第二列 月
        {
          values: [],
          defaultIndex: 0,
        },
      ],
      // 年变月变
      yearMonthColumnsChange: (e, l) => {
        if (l === 0) {
          // 年UK则 - 月UK
          if (e[0] === 'UK') {
            state.yearMonthColumns[1].defaultIndex = 0
            state.yearMonthColumns[1].values = UKArr
          } else {
            const yearMonthColumnsValues = state.monthShiftChange(e)
            state.yearMonthColumns[1].values = yearMonthColumnsValues
          }
        }
      },
      // 年月日 定位当前值所在的下标
      setSpecificDefaultIndex: (specificStr) => {
        if (specificStr) {
          const result = specificStr.split('-');
          if (result?.length && result[0] === 'UK') {
            state.specificColumns[1].values = UKArr
          }
          if (result?.length > 1 && result[1] === 'UK') {
            state.specificColumns[2].values = UKArr
          }
          state.specificColumns.forEach((item, idx) => {
            item.values.forEach((el, index) => {
              if (el === result[idx]) {
                state.specificColumns[idx].defaultIndex = index
              }
            })
          });
        } else {
          state.specificColumns[0].defaultIndex = 0
          state.specificColumns[1].values = state.monthShiftChange(1)
          state.specificColumns[2].values = state.dayShiftChange(1)
          const date = new Date();
          const year = date.getFullYear();
          const month = date.getMonth() + 1;
          const today = date.getDate();
          state.specificColumns[0].values.forEach((el, idx) => {
            if (el === year + '') {
              state.specificColumns[0].defaultIndex = idx
            }
          });
          // 年能定位到今天，不是UK的情况下
          if (state.specificColumns[0].values[state.specificColumns[0].defaultIndex] !== 'UK') {
            state.specificColumns[1].values = state.monthShiftChange(1)
            state.specificColumns[1].values.forEach((el, idx) => {
              if (el / 1 === month) {
                state.specificColumns[1].defaultIndex = idx
              }
            });
            state.specificColumns[2].values = state.dayShiftChange(1)
            state.specificColumns[2].values.forEach((el, idx) => {
              if (el / 1 === today) {
                state.specificColumns[2].defaultIndex = idx
              }
            });
          }
        }
      },
      // 年月日-控件
      specificColumns: [
        // 第一列 年 默认1900-2099
        {
          values: [],
          defaultIndex: 0,
        },
        // 第二列 月
        {
          values: [],
          defaultIndex: 0,
        },
        // 第3列 日 28 29 30 31
        {
          values: [],
          defaultIndex: 0,
        },
      ],
      // 年变 月变 日变
      specificColumnsChange(e, l) {
        if (l === 0) {
          // 年UK则 - 月UK
          if (e[0] === "UK") {
            state.specificColumns[1].defaultIndex = 0;
            state.specificColumns[2].defaultIndex = 0;
            state.specificColumns[1].values = UKArr;
            state.specificColumns[2].values = UKArr;
          } else {
            state.specificColumns[1].values = state.monthShiftChange(e)
            const newE = state.specificDayShiftChange(e)
            state.specificColumns[2].values = state.dayShiftChange(newE);
            if (e[1] === "UK") {
              state.specificColumns[2].defaultIndex = 0;
              state.specificColumns[2].values = UKArr;
            }
          }
        } else if (l === 1) {
          // 月UK 则 日UK
          if (e[1] === "UK") {
            state.specificColumns[2].defaultIndex = 0;
            state.specificColumns[2].values = UKArr;
          } else {
            state.specificColumns[2].values = state.dayShiftChange(e);
          }
        }
      },
      // 处理月份
      monthShiftChange: (e) => {
        let monthArrCpoy = monthArr
        if (!state.questionObj.isFutureDate && (e[0] / 1 === new Date().getFullYear() || e === 1)) {
          monthArrCpoy = nowMonthArr()
        }
        return state.questionObj?.isAllowUK ? [...UKArr, ...monthArrCpoy] : monthArrCpoy
      },
      // 处理日期change时天数问题
      specificDayShiftChange: (e) => {
        let data = e
        if (!state.questionObj.isFutureDate && e[0] / 1 === new Date().getFullYear()) {
          const newDate = new Date()
          if (e[1] / 1 <= (newDate.getMonth() + 1)) {
          } else {
            const date = parseTimeNYDHMS(new Date(), '{y}-{m}')
            data = date.split('-')
          }
        }
        return data
      },
      // 处理天
      dayShiftChange: (e) => {
        // e为1 就是直接走改变天数
        // 年动得时候处理日  月动
        const isAllowUKNum = state.questionObj?.isAllowUK ? 1 : 0
        let dayChangeArr = state.questionObj?.isAllowUK ? [...UKArr, ...dayArr] : [...dayArr];
        const newDate = new Date()
        if (!state.questionObj.isFutureDate &&
          (
            (
              (e[0] / 1 === newDate.getFullYear() &&
                e[1] / 1 === (newDate.getMonth() + 1))
            ) ||
            e === 1
          )
        ) {
          const dayArrCopy = nowDayArr()
          dayChangeArr = state.questionObj?.isAllowUK ? [...UKArr, ...dayArrCopy] : [...dayArrCopy];
        } else {
          if (ThirtyDaysArr.indexOf(e[1]) > -1) {
            dayChangeArr.length = 30 + isAllowUKNum;
          } else if (e[1] === "02") {
            dayChangeArr.length = (isLeapYear(Number(e[0])) ? 29 : 28) + isAllowUKNum;
          }
        }
        return dayChangeArr
      },
      // 
      outerHeight: document.documentElement.scrollHeight,
      // 初始 数据
      initialQuestionObj: {
        questCrfItemes: []
      },
      oldQuestionObj: {
        questCrfItemes: []
      }, // 旧问卷总对象-用于对比是否值更新
      questionObj: {
        questCrfItemes: []
      }, // 问卷总对象
      questionDataObj: {}, // 问卷回答对象questCrfItemes
      questionIndex: 0, // 当前回答问题的--下标
      questionObjLength: 0, // 问题总长
      yearMonthValue: new Date(),
      yearMonthIndex: -1, // 年月下标
      dateIndex: -1, // 日期下标
      editable: '',
      otherQuestionnaires: '',// 其他任务问卷
      // (将舍弃)
      questItem: {
        questDisplayType: '', // 4为上传问卷
        edcCrfId: 0,
        finishStatus: 1,
        questContentType: 1,
        questDescribe: '',
        questId: '',
        questName: ''
      },
      // postVisitFile, 
      // deleteVisitFile,
      visitId: '',
      dctCodeItem: {
        fileList: []
      }, // 获取到的图片列表
      disabledSaveFalg: {
        disabledSaveFalg: false,
      },
      visDate: '',
      visitName: '',
      myPreview: [],
      // 服药特殊字段
      durgDataBeforehandlist: null,
      submitLoadingFlag: false,
      // 逻辑核查
      verificationRequiredModuleRef: null,
      // 引导
      showFieldDescription: (fieldDescription) => {
        Dialog.alert({
          title: "填写指南",
          message: `${fieldDescription}`,
          confirmButtonText: '确定',
          confirmButtonColor: '#5860da',
          messageAlign: 'left',
          allowHtml: true,
        })
          .then(() => { })
      },
      // 图片预览
      tab: (idsArr, idx) => {
        if (idsArr?.length && idsArr?.length !== state.myPreview.length) {
          state.myPreview = []
          idsArr.forEach((item, index) => {
            // 获取图片原图
            getAccessFile(item).then((res) => {
              state.myPreview[index] = res.url
              if (index + 1 === idsArr.length && state.myPreview?.length) {
                setTimeout(() => {
                  ImagePreview({
                    images: state.myPreview,
                    startPosition: idx,
                    closeable: true
                  });
                }, 500)
              }
            });
          })
        } else {
          ImagePreview({
            images: state.myPreview,
            startPosition: idx,
            closeable: true
          });
        }
      },
      // 只能输入数字
      keyUpNum: (item) => {
        // item.fieldValue = item.fieldValue.replace(/[^\d]/g, "");
        item.fieldValue = item.fieldValue.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')
      },
      // 点击选择时间
      pickerDay: async (index) => {
        if (state.editable !== '1') {
          return
        }
        state.timeColumns = [
          {
            values: hourArr,
            defaultIndex: 0,
          },
          {
            values: [],
            defaultIndex: 0,
          },
        ]
        await nextTick()
        state.dateIndex = index;
        const quesObj = state.questionObj;
        if (!quesObj?.questCrfItemes[state.dateIndex].isReadOnly) {
          state.questionObj.isAllowUK = quesObj.questCrfItemes[index].isAllowUK
          state.timeColumns[0].values = state.questionObj?.isAllowUK ? [...UKArr, ...hourArr] : hourArr
          state.timeColumns[1].values = state.questionObj?.isAllowUK ? [...UKArr, ...minuteArr] : minuteArr
          if (quesObj.questCrfItemes[index].crfFieldControl === 8) {
            if (Array.isArray(quesObj.questCrfItemes[index].fieldValue) && quesObj.questCrfItemes[index].fieldValue?.length === 2) {
              state.setTimeDefaultIndex(quesObj.questCrfItemes[index].fieldValue[1])
            } else {
              state.setTimeDefaultIndex('')
            }
          } else {
            state.setTimeDefaultIndex(quesObj.questCrfItemes[index].fieldValue)
          }
          quesObj.questCrfItemes[state.dateIndex].showTimes = true;
        }
      },
      // 点击选择年月
      pickerYearMonth: async (index) => {
        if (state.editable !== '1') {
          return
        }
        state.yearMonthColumns = [
          // 第一列 年 默认1900-2099
          {
            values: [],
            defaultIndex: 0,
          },
          // 第二列 月
          {
            values: [],
            defaultIndex: 0,
          },
        ]
        await nextTick()
        state.yearMonthIndex = index;
        const quesObj = state.questionObj;
        if (!quesObj?.questCrfItemes[index].isReadOnly) {
          state.questionObj.isAllowUK = quesObj.questCrfItemes[index].isAllowUK
          state.questionObj.isFutureDate = quesObj.questCrfItemes[index]?.isFutureDate
          // 设置年月控件
          let max = quesObj.questCrfItemes[index]?.maximumYear || 0, min = quesObj.questCrfItemes[index]?.minimumYear || 0;
          if (!quesObj.questCrfItemes[index]?.isFutureDate) {
            const date = new Date();
            const year = date.getFullYear();
            // 最小值大于最大的了不成立直接为今年
            if (min > max) {
              min = max = year;
            } else {
              min = min && min <= year ? min : year
              max = max && max <= year ? max : year
            }
          }
          if (max && min && Number(max) > Number(min)) {
            state.setYearMonthColumns(state.yearMonthColumns, min, max, quesObj.questCrfItemes[index].fieldValue)
          } else {
            state.setYearMonthColumns(state.yearMonthColumns, 1900, 2099, quesObj.questCrfItemes[index].fieldValue)
          }
          state.setYearMonthDefaultIndex(quesObj.questCrfItemes[index].fieldValue)
          quesObj.questCrfItemes[index].showYearMonth = true;
        }
      },
      // 点击选择日期
      pickerDate: async (index) => {
        if (state.editable !== '1') {
          return
        }
        state.specificColumns = [
          // 第一列 年 默认1900-2099
          {
            values: [],
            defaultIndex: 0,
          },
          // 第二列 月
          {
            values: [],
            defaultIndex: 0,
          },
          // 第3列 日 28 29 30 31
          {
            values: [],
            defaultIndex: 0,
          },
        ]
        await nextTick()
        state.dateIndex = index;
        const quesObj = state.questionObj;
        if (!quesObj?.questCrfItemes[state.dateIndex].isReadOnly) {
          state.questionObj.isAllowUK = quesObj.questCrfItemes[index].isAllowUK
          state.questionObj.isFutureDate = quesObj.questCrfItemes[index]?.isFutureDate
          let max = quesObj.questCrfItemes[index]?.maximumYear || 0, min = quesObj.questCrfItemes[index]?.minimumYear || 0;
          if (!quesObj.questCrfItemes[index]?.isFutureDate) {
            const date = new Date();
            const year = date.getFullYear();
            // 最小值大于最大的了不成立直接为今年
            if (min > max) {
              min = max = year;
            } else {
              min = min && min <= year ? min : year
              max = max && max <= year ? max : year
            }
          }
          let dateValue = ''
          if (quesObj.questCrfItemes[index].crfFieldControl === 8) {
            if (Array.isArray(quesObj.questCrfItemes[index].fieldValue) && quesObj.questCrfItemes[index].fieldValue?.length) {
              dateValue = quesObj.questCrfItemes[index].fieldValue[0]
            } else {
              dateValue = ''
            }
          } else {
            dateValue = quesObj.questCrfItemes[index].fieldValue
          }
          if (max && min && Number(max) >= Number(min)) {
            state.setYearMonthColumns(state.specificColumns, min, max, dateValue)
          } else {
            state.setYearMonthColumns(state.specificColumns, 1900, 2099, dateValue)
          }
          state.setSpecificDefaultIndex(dateValue)
          quesObj.questCrfItemes[state.dateIndex].showDatas = true;
        }
      },
      // 删除 新增 某个列表
      handleSaveDeleteList: () => {
        const { questId, pDctCode, rowId } = state.myDeleteListPopupObj
        props.deleteTable(questId, pDctCode, rowId).then(() => {
          Notify({ type: "success", message: "删除成功" });
          state.upListData()
          state.myDeleteListPopupFlag = false
        })
      },
      handleCancelDeleteList: () => {
        state.myDeleteListPopupFlag = false
      },
      deleteListFun: (item, viewItem) => {
        const pDctCode = viewItem?.dctCode;
        if (item?.isSubmit) {
          // 如果已编辑
          state.myDeleteListPopupFlag = true
          state.myDeleteListPopupObj = {
            questId: item.questId,
            pDctCode,
            rowId: item.rowId
          }
          return
        }
        props.deleteTable(item.questId, pDctCode, item.rowId).then(() => {
          Notify({ type: "success", message: "删除成功" });
          state.upListData()
        })
      },
      addListFun: (item, viewItem) => {
        store.dispatch('setOldQuestDataIndex', state.questionIndex);
        store.dispatch('setGetOldQuestDataFlag', 1)
        store.dispatch('setInitialQuestionObj', state.initialQuestionObj);
        store.dispatch('setOldQuestData', state.questionObj);
        const pDctCode = viewItem?.dctCode;
        if (item && pDctCode) {
          const questItem = {
            questId: item.questId || '',
            questName: item?.tableName || '',
            pDctCode,
            rowId: '',
            fieldDescription: viewItem?.fieldDescription || ''
          }
          const query = {
            questItem: JSON.stringify(questItem),
            editFlag: '1',
            rowId: ''
          }
          // 区分 首次新增 和 二次新增
          if (!state.questionObj?.isStaging || state.questionObj?.isSubmit) {
            // (store.state.initialListArr.length === 0 || !store.state.initialListArr.find((e) => e.isSubmit))
            if (viewItem?.children?.length === 1 && !viewItem?.isSubmit) {
              query.rowId = item.rowId || ''
            } else {
              query.rowId = (Number(item.rowId) + 1) + ''
            }
          }
          store.dispatch('setMainQuestIsChange', true)
          const path = "/editQuestionnairetList"
          proxy.$routerGoFun('routerInnPush', path, query, route.path, { ...route.query, tableListBackFlag: 1 })
        }
      },
      signatureItem: {},
      editListFun: (item, editFlag, viewItem) => {
        store.dispatch('setOldQuestDataIndex', state.questionIndex);
        if (editFlag) {
          store.dispatch('setGetOldQuestDataFlag', 1)
          store.dispatch('setInitialQuestionObj', state.initialQuestionObj);
          store.dispatch('setOldQuestData', state.questionObj);
        } else {
          store.dispatch('setGetOldQuestDataFlag', 0)
        }
        const pDctCode = viewItem?.dctCode;
        // console.log('item && pDctCode',item, editFlag, viewItem);
        
        if (item && pDctCode) {
          const questItem = {
            questId: item.questId || '',
            questName: item?.tableName || '',
            pDctCode,
            rowId: item.rowId || '',
            fieldDescription: viewItem?.fieldDescription || '',
            dataClarification: state.questionObj?.dataClarification
          }
          const query = {
            questItem: JSON.stringify(questItem),
            editFlag: editFlag === '1' ? '1' : '0',
            questItemDataId: ''
          }
          store.dispatch('setMainQuestIsChange', true)
          const path = "/editQuestionnairetList"
          let backQuery: any = deepClone(route.query)
          // 编辑时 - 如果跳入需要显示某个题的数据澄清
          // && item?.clarify?.clarifyCount
          if (route.query?.questItemDataId
            && route.query?.clarificationRowId
            && state.questionObj?.isSubmit
          ) {
            query.questItemDataId = route.query.questItemDataId
            backQuery = { ...route.query, questItemDataId: '', clarificationRowId: '' }
          }
          backQuery.signatureItem = JSON.stringify(state.signatureItem)
          backQuery.editable = state.editable
          backQuery.tableListBackFlag = 1
          proxy.$routerGoFun('routerInnPush', path, query, route.path, backQuery)
        }
      },
      // 判断长度是否一样
      areChildrenLengthsEqual: (arr1, arr2) => {
        // console.log('arr1, arr2',arr1, arr2);
        
        // 如果两个数组的长度不同，直接返回 false
        if (arr1.length > 0 && arr1.length !== arr2.length) {
          return false;
        }
        // 遍历数组，比较每个元素的 children 长度和 rowId 累加和
        for (let i = 0; i < arr1.length; i++) {
          // 比较 children 长度
          // console.log(arr1[i].children.length, arr2[i].children.length,'arr2[i].children.length');
          if (arr1[i].children.length !== arr2[i].children.length) {
            return false;
          }
          // 计算 children 下的 rowId 累加和
          const sum1 = arr1[i].children.reduce((acc, child) => acc + Number(child.rowId), 0);
          const sum2 = arr2[i].children.reduce((acc, child) => acc + Number(child.rowId), 0);
          // console.log(sum1, sum2,'sum1');
          // 比较 rowId 累加和
          if (sum1 !== sum2) {
            return false;
          }
        }

        return true;
      },
      // 更新列表
      upListData: (flag = false) => {
        // console.log('upListData',store.state?.tableListAllList);
        state.tableListAllList = []
        state.questionObj.questCrfItemes.forEach(async (item) => {
          try {
            if (item.crfFieldType === 3) {
              const listRes = await props.getTableList(state.questionObj.questId, item.dctCode)
              state.rowChange = false
              if (listRes?.length && Array.isArray(listRes)) {
                // 比较tableListAllList数量是否一致
                item.children = listRes
                state.tableListAllList.push(item)
                // console.log(store.state?.tableListAllList, state.tableListAllList);
                state.rowChange = !state.areChildrenLengthsEqual(store.state?.tableListAllList, state.tableListAllList)
                // if (state.areChildrenLengthsEqual(store.state?.tableListAllList?.length, state.tableListAllList?.length)) {
                // store.state?.tableListArr.forEach((e, index) => {
                //   listRes.forEach((el, idx) => {
                //     if (idx === index && el.rowId !== e.rowId) {
                //       state.rowChange = true
                //     }
                //   });
                // });
                // } else {
                //   state.rowChange = true
                // }
                // console.log(store.state?.tableListAllList, state.tableListAllList, state.rowChange,'state.rowChange');
              }
              if (!flag && store?.state?.oldQuestDataIndex && state.questionObj?.questCrfType === 2) {
                state.questionIndex = store?.state?.oldQuestDataIndex
              }
            }
          } catch { }
        });
      },
      // 需完善弹框
      needImprove: (rest) => {
        // 需完善弹出
        Dialog.alert({
          title: "需完善",
          message: `${rest.warningContent || ''}`,
          confirmButtonColor: "#5860DA",
          // confirmButtonText: "确定",
          // cancelButtonText: "发起沟通",
        })
          .then(() => {
            return false;
          })
      },
      // 是否清除 暂存
      clearStagingFun: (questId) => {
        return postClearStagingData(questId || state.questionObj.questId)
      },
      // 是否在暂存
      isStagingQuestionnaireFlag: false,
      // 是否完成提交
      submitSaveLoadingFlag: false,
      // 暂存问卷
      saveTemporaryQuestionnaire: async (isStaging = true) => {
        try {
          state.isStagingQuestionnaireFlag = true
          const quesArr = deepClone(state.questionObj);
          const quesDataObj = deepClone(state.questionObj);
          // 更新提交--数据数组
          const quesDataObjs = !quesDataObj?.questCrfItemes?.length ? { ...quesArr } : quesDataObj
          quesArr.questCrfItemes.forEach((item) => {
            quesDataObjs.questCrfItemes.forEach((items) => {
              // 多选修改成数组
              if (item.crfFieldControl === 10) {
                if (Array.isArray(item.fieldValue)) {
                  item.fieldValue = item.fieldValue.join(",");
                }
              }
              if (item?.fieldValue) {
                item.fieldValue += '';
              } else {
                item.fieldValue = ''
              }
              // 合成字符串时间日期
              if (item.crfFieldControl === 8) {
                if (Array.isArray(item.fieldValue) && item?.fieldValue?.length) {
                  // if (!item?.fieldValue[0] && item?.dctDateControlDefault) {
                  //  item.fieldValue[0] = dateMYDay;
                  // }
                  let val = item.fieldValue[0]
                  if (item?.fieldValue?.length > 1) {
                    // if (!item?.fieldValue[1] && item?.dctDateControlDefault) {
                    //   item.fieldValue = dateMyTime;
                    // }
                    val += ' ' + item.fieldValue[1]
                  }
                  item.fieldValue = val;
                }
              }
              if (item.dctCode === items.dctCode) {
                items.fieldValue = item.fieldValue;
                items.fieldValueStr = item.fieldValueStr
              }
            });
          });
          const data = deepClone(quesDataObj);
          data.isStaging = isStaging
          // 这里调暂存接口 const res = 
          await props.putQuestData(quesArr.questId || quesDataObjs.questId, data)
          state.isStagingQuestionnaireFlag = false
        } catch {
          state.isStagingQuestionnaireFlag = false
        }
      },
      // 获取状态的请求
      getPatientStatusFun: () => {
        const icfStatementIdData = {
          icfStatementID:
            store.state?.userInformation?.icfStatementID ||
            store.state?.userInformation?.icfStementId,
        }
        return new Promise(async (resolve, reject) => {
          try {
            const patientStatus: any = await getPatientStatus(icfStatementIdData)
            if (patientStatus?.patientICFStatus === 404) {
              Notify({ type: 'danger', message: '已转线下知情' })
              proxy.$routerGoFun('replace', returnPatientStatusUrl(patientStatus), '')
              // router.replace(returnPatientStatusUrl(patientStatus));
              return
            }
            resolve(1)
          } catch (e) {
            reject(e)
          }
        })
      },
      // 运行异步worker
      workerScriptFun: (quesDataObj, questionObj) => {
        return new Promise(async (resolve) => {
          // 特殊逻辑核查 verificationRuleLevel 
          // type: 0, // 0 无需 1强制 2非强制
          const verificationRequiredObj = {
            type: 0,
            verificationRequiredList: []
          }
          try {
            const workerScript = `
              self.onmessage = function(e) {
                try {
                  const quesDataObj = JSON.parse(e.data);
                  const verificationRequiredObj = {
                    type: 0,
                    verificationRequiredList: []
                  }
                  const verificationList = []
                  // console.log('quesDataObj',quesDataObj)
                  quesDataObj.rules.map((e) => {
                    const ite = new Function('quesDataObj', e.verificationRuleCode)({...quesDataObj,verificationRuleLevel: e.verificationRuleLevel})
                    if (ite.type) {
                      if (ite.type === 1) {
                        verificationRequiredObj.type = 1 
                      } else if (verificationRequiredObj.type !== 1 ) {
                        verificationRequiredObj.type = ite.type
                      }
                      if (ite.type === verificationRequiredObj.type) {
                        verificationList.push(JSON.parse(JSON.stringify(ite)))
                      }
                    }
                  })
                  // 过滤
                  verificationList.map((e) => {
                    if (verificationRequiredObj.type === e.type) {
                      verificationRequiredObj.verificationRequiredList = verificationRequiredObj.verificationRequiredList.concat(e.verificationRequiredList)
                    }
                  })
                  self.postMessage(verificationRequiredObj);
                } catch (err) {
                  self.postMessage({
                    type: 0,
                    verificationRequiredList: []
                  });
                  // console.log(err,'err')
                }
              };
            `;
            // 创建一个Blob对象，将JavaScript字符串代码作为其内容
            const blob = new Blob([workerScript], { type: 'application/javascript' });
            // 创建一个Worker URL
            const workerUrl = URL.createObjectURL(blob);
            // 创建Web Worker实例
            const worker = new Worker(workerUrl);
            // questionObj.rules = [{
            //   verificationRuleCode: "const verificationRequired = { type: 0, verificationRequiredList: [] }; const fieldLabelFlag = quesDataObj.questCrfItemes.some(item => item.fieldLabel.includes('出现过潮热') && item.fieldValue === '1'); if (fieldLabelFlag) { const typeFlag = quesDataObj.questCrfItemes.filter(item => (item.fieldLabel.includes('轻度') || item.fieldLabel.includes('中度') || item.fieldLabel.includes('重度')) && (!item?.fieldValue || Number(item?.fieldValue) === 0)); if (typeFlag.length === 3) { verificationRequired.type =quesDataObj.verificationRuleLevel; typeFlag.map(e => e.hintText = `发生了潮热，轻度+中度+重度次数之和必须大于0。`); verificationRequired.verificationRequiredList = typeFlag; }} return verificationRequired;",
            //   isEnable: true,
            //   verificationRuleLevel: 1
            // }]
            // 发送参数给Worker
            worker.postMessage(JSON.stringify({
              questCrfItemes: quesDataObj.questCrfItemes,
              rules: questionObj?.rules?.length ? questionObj?.rules?.filter((e) => e?.isEnable && e.verificationRuleCode.includes('return')) : [],
              // questCrfType: quesDataObj.questCrfType
            }));
            // 接收Worker发送回来的消息
            worker.onmessage = function (e) {
              resolve(e.data)
              // console.log('Result Worker:', e.data);
            };
            // 清理：在适当的时候撤销Worker URL（例如，在Worker终止后）
            worker.onerror = function () {
              URL.revokeObjectURL(workerUrl);
            };
            // 可选：在不再需要Worker时终止它
            // worker.terminate();
          } catch (e) {
            resolve(verificationRequiredObj)
            // reject(e)
          }
        })
      },
      // judgeLimitIOSVersion: () => {
      //   try {
      //     const userAgent = navigator.userAgent || navigator.vendor || window.opera;
      //     // 匹配iOS版本的正则表达式
      //     const iOSRegex = /iPhone|iPad|iPod/i.test(userAgent) && !window.MSStream;
      //     if (!iOSRegex) {
      //       return false;
      //     }
      //     const versionRegex = /OS (\d+_\d+_\d+) like Mac OS X/i;
      //     const match = userAgent.match(versionRegex);
      //     if (match && match.length > 1) {
      //       // match 是第一个捕获组，即版本字符串 (e.g., "14_0_1")
      //       const versionString = match;
      //       // 将下划线替换为点号，得到标准的版本号格式 (e.g., "14.0.1")
      //       if (versionString[1] === '12_5_7') {
      //         return true;
      //       }
      //       return false;
      //     }
      //     return false;
      //   }
      //   catch { return false; }
      // },
      // 提交问卷
      putSaveQuestionnaire: async (typeFlag = 0) => {
        // typeFlag = 1跳过核查，
        state.submitLoadingFlag = true
        Toast.loading({
          duration: 300000,
          message: '提交中...',
          forbidClick: true,
        });
        if (state.isStagingQuestionnaireFlag) {
          setTimeout(() => {
            state.putSaveQuestionnaire(typeFlag)
          }, 1000)
          return
        }
        const quesArr = deepClone(state.questionObj);
        // const quesArrFlag = state.questionObj;
        const quesDataObj = state.questionDataObj;
        let isRequiredFlag = true; // 是否填完必填字段
        // 更新提交--数据数组
        const quesDataObjs = !quesDataObj?.questCrfItemes?.length ? { ...quesArr } : quesDataObj
        quesArr.questCrfItemes.forEach((item) => {
          quesDataObjs.questCrfItemes.forEach((items) => {
            // 多选修改成数组
            if (item.crfFieldControl === 10) {
              if (Array.isArray(item.fieldValue)) {
                item.fieldValue = item.fieldValue.join(",");
              }
            }
            // 数组没值 转字符会变成 ','
            // console.log(item.fieldValue,'item.fieldValue');

            if (item?.fieldValue) {
              item.fieldValue += '';
            } else {
              item.fieldValue = ''
            }
            // 合成字符串时间日期
            const { fieldLabel } = item
            const replaceFieldLabel = fieldLabel.replace(regex, '')
            if (
              item.isRequired === 1 &&
              !item.fieldValue &&
              item.refTypeShow !== 2 &&
              item.refTypesShow !== 2
            ) {
              Notify({ message: `请回答${replaceFieldLabel}`, type: "danger" });
              isRequiredFlag = false;
              return;
            }
            if (item.isRequired === 1 && item.crfFieldControl === 8 && item.refTypeShow !== 2 && item.refTypesShow !== 2) {
              // 字符串 用，分割
              const isRequiredArr = item.fieldValue.split(",");
              if (
                isRequiredArr.length < 2 ||
                isRequiredArr[0]?.length < 4 ||
                isRequiredArr[1]?.length < 4
              ) {
                Notify({
                  message: `请回答${replaceFieldLabel}`,
                  type: "danger",
                });
                isRequiredFlag = false;
                return;
              }
            }

            if (item.crfFieldControl === 8) {
              // console.log(item.fieldValue,'item.fieldValue');
              if (Array.isArray(item.fieldValue) && item?.fieldValue?.length) {
                // if (!item?.fieldValue[0] && item?.dctDateControlDefault) {
                //  item.fieldValue[0] = dateMYDay;
                // }
                let val = item.fieldValue[0]
                if (item?.fieldValue?.length > 1) {
                  // if (!item?.fieldValue[1] && item?.dctDateControlDefault) {
                  //   item.fieldValue = dateMyTime;
                  // }
                  val += ' ' + item.fieldValue[1]
                }
                item.fieldValue = val;
              }
            }
            // 日期控件需要默认值 dateMYDay dateMyTime
            // if (item?.dctDateControlDefault && (item?.crfFieldControl === 6 || item?.crfFieldControl === 8)) {
            //   if (!item?.fieldValue?.length || item.fieldValue.length < 3) {
            //     item.fieldValue = dateMYDay;
            //     if (item.crfFieldControl === 8) {
            //       // 日期时间控件默认值
            //       item.fieldValue += ','+dateMyTime;
            //     }
            //   }
            // }

            if (item.dctCode === items.dctCode) {
              items.fieldValue = item.fieldValue;
              items.fieldValueStr = item.fieldValueStr
              items.fieldLabel = item.fieldLabel
              items.refTypeShow = item?.refTypeShow || null
              items.refTypesShow = item?.refTypesShow || null
            }
            if (item.crfFieldControl === 5 && item.fieldValue) {
              const reg = /^[0-9]+.?[0-9]*$/;
              if (!reg.test(item.fieldValue)) {
                isRequiredFlag = false;
                Notify({
                  message: `${replaceFieldLabel}只可以填写数字哦`,
                  type: "danger",
                });
                return;
              }
            }
          });
        });
        // 必填项判断
        if (!isRequiredFlag) {
          state.submitLoadingFlag = false
          Toast.clear();
          return;
        }
        if (props.myQuestType === '随访问卷' || props.myQuestType === '用药记录') {
          const questInfoRes: any = await getQuestInfo(quesArr?.questId || quesDataObj?.questId || route.query?.questId)
          if (!questInfoRes?.isEditable) {
            state.editable = questInfoRes?.isEditable ? '1' : '0'
            Toast('不在窗口期内，无法提交')
            state.submitLoadingFlag = false
            setTimeout(() => {
              Toast.clear();
            }, 2000)
            return
          }
        }
        // 有列表的情况下 效验必填
        if (quesArr.questCrfItemes.some((questItem) => questItem.crfFieldType === 3)) {
          const resQuestListCheck: any = await postPatientQuestListCheck(quesArr.questId || quesDataObjs.questId, quesDataObjs)
          state.submitLoadingFlag = false
          Toast.clear();
          if (resQuestListCheck?.length && resQuestListCheck.some((e) => e?.children.length)) {
            Notify({
              message: `请完成列表问卷的必填项`,
              type: "danger",
            });
            return;
          }
        }
        const notice = await maintainedFun(route.path || '')
        if (notice && notice?.maintainedFlag) {
          state.submitLoadingFlag = false
          Toast.clear();
          return;
        }
        // 暂时还原》排除机型的核查
        if (quesArr?.rules?.length > 0) {
          const verificationRequiredObj = await state.workerScriptFun(quesDataObj, quesArr)
          // 进判断则打开逻辑核查页面
          if (typeFlag !== 1 && verificationRequiredObj?.type) {
            state.verificationRequiredModuleRef.verificationRequiredModuleObj = verificationRequiredObj
            state.verificationRequiredModuleRef.verificationRequiredModuleFlag = true
            state.submitLoadingFlag = false
            // console.log('verificationRequiredObj',verificationRequiredObj);
            return
          }
        }
        // 执行接口保存
        try {
          state.verificationRequiredModuleRef.verificationRequiredModuleFlag = false
          // if (props.myQuestType === '其它药物' || props.myQuestType === '不适记录') {
          //   await props.putQuestData(quesDataObj.questId, quesDataObj)
          //   proxy.$routerBackFun()
          //   Notify({ type: "success", message: "提交成功" });
          //   state.submitLoadingFlag = false
          //   Toast.clear();
          // } else
          if (props.myQuestType === '用药记录') {
            const reocrd = {
              dctDrugRecordId: '',
              patDrugRecordId: "", // 患者记录ID增时 为null
              drugId: route.query?.durgId || '', // 药物ID
              drugQuestTemplateId: '' // 问卷模板Id
            }
            if (route.query?.newTakemedicineLogItem) {
              const newTakemedicineLogItem = JSON.parse(route.query.newTakemedicineLogItem as string)
              reocrd.drugId = newTakemedicineLogItem.dctDrugId
              reocrd.patDrugRecordId = newTakemedicineLogItem.id
            } else if (route.query?.addTakemedicineLogItem) {
              const addTakemedicineLogItem = JSON.parse(route.query.addTakemedicineLogItem as string)
              reocrd.drugQuestTemplateId = addTakemedicineLogItem.questTemplateId
              reocrd.dctDrugRecordId = addTakemedicineLogItem.dctDrugRecordId
            } else {
              return
            }
            quesDataObjs.isStaging = false
            const answerQuestdata = {
              answer: quesDataObjs,
              reocrd
            }
            state.answerQuestdata = answerQuestdata
            //
            if (quesArr?.isSubmit || state.questionDataObj?.isSubmit) {
              // 打开-数据变动
              const res = await putPatientQuestChangeData(quesArr.questId || quesDataObjs.questId, quesDataObjs)
              if (res?.questCrfItemes.length && res?.questCrfItemes.filter((e) => e?.isDisplay).length) {
                state.openDataChangeModule(res)
                state.submitLoadingFlag = false
                if (state.backQuestionIndex > -1) {
                  state.questionIndex = state.backQuestionIndex
                }
                Toast.clear();
                return
              }
            }
            await props.putQuestData(quesArr.questId || quesDataObjs.questId, answerQuestdata)
            state.submitSaveLoadingFlag = true
            const routerInnBack = { ...store.state.routerInn[store.state.routerInn.length - 1] }
            if (routerInnBack.backPath === '/newTakemedicineLog') {
              proxy.$routerBackFun()
            } else {
              proxy.$routerBackFun(2)
            }
            Notify({ type: "success", message: "提交成功" });
            state.submitLoadingFlag = false
            Toast.clear();
          } else {
            quesDataObjs.isStaging = false
            if (props.myQuestType === '知情问卷') {
              quesDataObjs.icfStatementId = store.state?.userInformation?.icfStatementID || store.state?.userInformation?.icfStementId
              // 判断知情问卷是否为重新填写
              if (store.state?.userInformation?.patientICFStatus === 1002) {
                await postConsentSupplementComfirm({
                  icfStatementID: store.state?.userInformation?.icfStatementID || store.state?.userInformation?.icfStementId
                })
              }
            }
            // && !location.href.includes('otherQuestionnaires')
            if (props.myQuestType === '知情问卷' || props.myQuestType === '随访问卷' || props.myQuestType === '其它药物' || props.myQuestType === '不适记录') {
              if (quesArr?.isSubmit || state.questionDataObj?.isSubmit) {
                // 打开-数据变动
                let myRequestId, queryobj;
                if (props.myQuestType === '其它药物') {
                  queryobj = JSON.parse(route.query.CMsItem as string)
                  myRequestId = queryobj ? queryobj.rowId : ' ';
                } else if (props.myQuestType === '不适记录') {
                  queryobj = JSON.parse(route.query.discomfortItem as string)
                  myRequestId = queryobj ? queryobj.rowId : ' ';
                }
                const res = await putPatientQuestChangeData(quesArr.questId || quesDataObjs.questId, { ...quesDataObjs, rowNum: myRequestId })
                if (res?.questCrfItemes.length && res?.questCrfItemes.filter((e) => e?.isDisplay).length) {
                  state.openDataChangeModule(res)
                  state.submitLoadingFlag = false
                  if (state.backQuestionIndex > -1) {
                    state.questionIndex = state.backQuestionIndex
                  }
                  Toast.clear();
                  return
                }
              }
            }
            const res = await props.putQuestData(quesArr.questId || quesDataObjs.questId, quesDataObjs)
            state.submitSaveLoadingFlag = true
            Notify({ type: "success", message: "提交成功" });
            store.dispatch('setGetOldQuestDataFlag', 0)
            if (props.myQuestType === '随访问卷') {
              if (store.state?.homeList?.dctPatientId) {
                // 补偿报销申请创建 -> 访视问卷保存完调用
                putAutoPeriodicCompensationApply(store.state.homeList.dctPatientId, quesArr?.questId || quesDataObjs?.questId)
              }
              if (store.state?.homeList?.patientStatus === 10) {
                store.dispatch('setTaskText', state.questionObj.crfName)
              } else {
                /* 筛选中的时候随访问卷保存需要调用 */
                posCheckVisit(quesArr?.questId || quesDataObjs.questId)
              }
              proxy.$routerBackFun()
            } else if (props.myQuestType === '入排问卷') {
              // 首次提交都是审核中状态
              store.dispatch('setAuditFlag', false)
              proxy.$routerGoFun('replace', '/audit')
            } else if (props.myQuestType === '知情问卷') {
              const url = returnPatientStatusUrl(res)
              if (url) {
                proxy.$routerGoFun('replace', url)
              }
            } else if (props.myQuestType === '其它药物' || props.myQuestType === '不适记录') {
              proxy.$routerBackFun()
            }
            state.submitLoadingFlag = false
            Toast.clear();
            // console.log(quesDataObj); // 用于请求的数据
          }
        } catch {
          state.submitLoadingFlag = false
          Toast.clear();
          getPatientStatus({
            ICFStatementId: store.state?.userInformation?.icfStatementID || store.state?.userInformation?.icfStementId
          }).then((getPatientStatusRest) => {
            proxy.$routerGoFun('replace', returnPatientStatusUrl(getPatientStatusRest))
          })
        }
      },
      // 上下一题
      saveQuestionnaire: async (flag) => {
        if (state.disabledSaveFalg.disabledSaveFalg) {
          return
        }
        try {
          delay(async () => {
            if (props.myQuestType === '知情问卷') {
              await state.getPatientStatusFun()
            }
            const quesArr = deepClone(state.questionObj);
            const quesArrFlag = state.questionObj;
            if (flag === "add") {
              const quesObj = state.questionObj;
              // 是否填写 必填项？
              const crfFieldControl =
                quesObj?.questCrfItemes[state.questionIndex]?.crfFieldControl;
              const isRequired =
                quesObj?.questCrfItemes[state.questionIndex]?.isRequired;
              const fieldValue =
                quesObj?.questCrfItemes[state.questionIndex]?.fieldValue;
              // 下一步 当前是列表题 效验必填
              if (quesObj?.questCrfItemes[state.questionIndex]?.crfFieldType === 3) {
                const quesArr = deepClone(state.questionObj);
                const quesDataObj: any = state.questionDataObj;
                state.submitLoadingFlag = true
                Toast.loading({
                  duration: 300000,
                  message: '加载中...',
                  forbidClick: true
                });
                // 更新提交--数据数组
                const quesDataObjs: any = !quesDataObj?.questCrfItemes?.length ? quesArr : quesDataObj
                quesArr.questCrfItemes.forEach((item) => {
                  quesDataObjs.questCrfItemes.forEach((items) => {
                    // 多选修改成数组
                    if (item.crfFieldControl === 10) {
                      if (Array.isArray(item.fieldValue)) {
                        item.fieldValue = item.fieldValue.join(",");
                      }
                    }
                    if (item?.fieldValue) {
                      item.fieldValue += "";
                    } else {
                      item.fieldValue = "";
                    }
                    if (item.crfFieldControl === 8) {
                      if (
                        Array.isArray(item.fieldValue) &&
                        item?.fieldValue?.length
                      ) {
                        let val = item.fieldValue[0];
                        if (item?.fieldValue?.length > 1) {
                          val += " " + item.fieldValue[1];
                        }
                        item.fieldValue = val;
                      }
                    }
                    if (item.dctCode === items.dctCode) {
                      items.fieldValue = item.fieldValue;
                      items.fieldValueStr = item.fieldValueStr;
                      items.children = []
                    }
                  });
                });
                const resQuestListCheck: any = await postPatientQuestListCheck(quesArr.questId || quesDataObjs.questId, quesDataObjs)
                state.submitLoadingFlag = false
                Toast.clear();
                if (resQuestListCheck?.length && resQuestListCheck.some((e) => e.dctCode == quesObj?.questCrfItemes[state.questionIndex]?.dctCode && e?.children.length)) {
                  Notify({
                    message: `请完成列表问卷的必填项`,
                    type: "danger",
                  });
                  return;
                }
              }
              //
              const fieldItem = quesObj?.questCrfItemes[state.questionIndex]
              if (fieldItem.refTypeShow !== 2 && fieldItem.refTypesShow !== 2 &&
                isRequired === 1 && ((!fieldValue || !fieldValue?.length) ||
                  (crfFieldControl === 8 && (!fieldValue[0] || !fieldValue[1])))
              ) {
                Notify({ message: "请回答问题", type: "danger" });
                return;
              }
              if (fieldItem.refTypeShow !== 2 && fieldItem.refTypesShow !== 2 && crfFieldControl === 5 && fieldValue) {
                const reg = /^[0-9]+.?[0-9]*$/;
                if (!reg.test(fieldValue)) {
                  Notify({ message: "只可以填写数字哦", type: "danger" });
                  return;
                }
              }
              // 单选时
              if (
                quesArr.questCrfItemes[state.questionIndex]?.crfFieldControl === 9
              ) {
                quesArrFlag.questCrfItemes.forEach((showRelevanceItem) => {
                  state.showRelevance(showRelevanceItem);
                });
              } else if (
                quesArr.questCrfItemes[state.questionIndex]?.crfFieldControl === 10
              ) {
                quesArrFlag.questCrfItemes.forEach((showRelevanceItem) => {
                  state.showRelevances(showRelevanceItem);
                });
              }

              if (
                quesArrFlag.questCrfItemes[state.questionIndex + 1]
                  ?.refTypeShow === 2 ||
                quesArrFlag.questCrfItemes[state.questionIndex + 1]
                  ?.refTypesShow === 2
              ) {
                let num = 1;
                let subtractAddflag = true;
                quesArrFlag.questCrfItemes.forEach((elem, elemIndex) => {
                  if (
                    elemIndex >= state.questionIndex + 1 &&
                    (elem.refTypeShow === 2 ||
                      elem.refTypesShow === 2) &&
                    subtractAddflag
                  ) {
                    num += 1;
                  }
                  if (
                    quesArrFlag.questCrfItemes[state.questionIndex + num]
                      ?.refTypesShow !== 2 &&
                    quesArrFlag.questCrfItemes[state.questionIndex + num]
                      ?.refTypeShow !== 2 &&
                    subtractAddflag
                  ) {
                    subtractAddflag = false;
                  }
                });
                // 关联隐藏了n题 ，记录下用于数据变动返回时。
                if (num > 1) {
                  state.backQuestionIndex = state.questionIndex
                }
                state.questionIndex += num;
                if (state.questionIndex >= quesArrFlag.questCrfItemes.length) {
                  state.putSaveQuestionnaire();
                }
                // 随访问卷-》可编辑的 暂存。
                // else if (false) {
                //   state.saveTemporaryQuestionnaire()
                // }
                return;
              }
              state.questionIndex++;
              return;
            } else if (flag === "start") {
              // 开始按钮
              state.questionIndex = 0;
              return;
            } else if (flag === "subtract") {
              if (state.questionIndex > 0) {
                // 单选时
                // if(quesArr.questCrfItemes[state.questionIndex].crfFieldControl === 9)
                if (
                  quesArrFlag.questCrfItemes[state.questionIndex - 1]
                    ?.refTypesShow === 2 ||
                  quesArrFlag.questCrfItemes[state.questionIndex - 1]
                    ?.refTypeShow === 2
                ) {
                  let num = 1;
                  let myElemIndex = 0;
                  let subtractAddflag = true;
                  const questobj = deepClone(quesArrFlag);

                  for (let i = questobj.questCrfItemes.length - 1; i >= 0; i--) {
                    if (
                      (quesArrFlag.questCrfItemes[state.questionIndex - 1]
                        ?.crfItemId === questobj.questCrfItemes[i].crfItemId ||
                        myElemIndex) &&
                      (questobj.questCrfItemes[i].refTypeShow === 2 || questobj.questCrfItemes[i].refTypesShow === 2) &&
                      subtractAddflag
                    ) {
                      num += 1;
                      myElemIndex = i + num;
                    }
                    if (
                      myElemIndex &&
                      questobj.questCrfItemes[i]?.refTypesShow !== 2 &&
                      questobj.questCrfItemes[i]?.refTypeShow !== 2 &&
                      subtractAddflag
                    ) {
                      subtractAddflag = false;
                    }
                  }
                  state.questionIndex -= num;
                  return;
                }
                state.questionIndex--;
              }
              return;
            } else {
              // 保存
              state.putSaveQuestionnaire();
            }
          }, 200)
        } catch {
          state.submitLoadingFlag = false
        }
      },
      // 特殊字段赋值
      durgSpecialFieldTypeFz: (res) => {
        if (res?.specialFieldType > 10 && res?.specialFieldType < 15 && state.durgDataBeforehandlist) {
          state.durgDataBeforehandlist.forEach((durgItem) => {
            if (durgItem.dctCode === res.dctCode && !res?.fieldValue
              && res.refTypeShow !== 2 && res.refTypesShow !== 2) {
              res.fieldValue = durgItem.fieldValue
            }
          })
        }
      },
      updateDataClarification: () => {
        if (store.state.getQuestionnaiDataObj?.id) {
          state.getQuestionnaiDataObj = store.state.getQuestionnaiDataObj
        }
        const { id, params } = state.getQuestionnaiDataObj
        // 接口传参不同
        let getQuestViewFun
        if (props.myQuestType === '其它药物' ||
          props.myQuestType === '不适记录') {
          getQuestViewFun = props.getQuestView()
        } else if (props.myQuestType === '入排问卷'
          || props.myQuestType === '知情问卷') {
          getQuestViewFun = props.getQuestView({
            ICFStatementId: store.state?.userInformation?.icfStatementID || store.state?.userInformation?.icfStementId
          })
        }
        else if (props.myQuestType === '用药记录') {
          getQuestViewFun = props.getQuestView(id, params)
        }
        else {
          getQuestViewFun = props.getQuestView(id)
        }
        if (props?.getQuestView) {
          // 获取问题-数据
          getQuestViewFun.then((res) => {
            // 如果是消失的问卷 直接回首页
            let getQuestDataFun
            if (props.myQuestType === '其它药物' || props.myQuestType === '不适记录') {
              getQuestDataFun = props.getQuestData(id)
            } else if (props.myQuestType === '用药记录') {
              if (route.query?.durgId) {
                params.drugId = route.query.durgId
              } else if (route.query?.newTakemedicineLogItem) {
                const newTakemedicineLogItem = JSON.parse(route.query.newTakemedicineLogItem as string)
                params.drugId = newTakemedicineLogItem.dctDrugId
              }
              getQuestDataFun = props.getQuestData(id, params)
              // 获取特殊字段接口
            } else {
              getQuestDataFun = props.getQuestData(res.questId)
            }
            getQuestDataFun.then((rest) => {
              if (rest.questCrfType === 1 || rest.questCrfType === 2) {
                rest.questCrfItemes.forEach((item) => {
                  state.questionObj.questCrfItemes.forEach((items) => {
                    if (item.dctCode === items.dctCode) {
                      items.clarify = item.clarify;
                    }
                  });
                });
              }
            });
          });
        }
      },
      getQuestionnaiDataObj: {
        id: '',
        params: null
      },
      // 多个列表
      tableListAllList: [],
      // 检测是否需要去定制化问卷
      getQuestionnaireCustomize: (Flag) => {
        if (Flag === '1') {
          state.editable = '1'
        }
        // console.log('state.questionObj',state,state.questionObj);
        // state.getQuestViewObj
        if ((state.questionObj?.questCrfType === 3 && state.questionObj?.questUrl) || 
        (state.getQuestViewObj?.questCrfType === 3 && state.getQuestViewObj?.questUrl)) {
          const query = {
            ...route.query,
            questId: state.getQuestViewObj.questId || state.questionObj.questId,
            editable: state.editable,
            questionUrl: state.getQuestViewObj.questUrl || state.questionObj.questUrl,
          }
          proxy.$routerGoFun('replace', '/followurlquestionnaires', query, route.path)
        }
      },
      getQuestViewObj: {},
      // 初次获取数据
      getQuestionnaiData: (id, params) => {
        state.getQuestionnaiDataObj = {
          id,
          params
        }
        store.dispatch('setGetQuestionnaiDataObj', state.getQuestionnaiDataObj);
        // 接口传参不同
        let getQuestViewFun
        if (props.myQuestType === '其它药物' ||
          props.myQuestType === '不适记录') {
          getQuestViewFun = props.getQuestView()
        } else if (props.myQuestType === '入排问卷'
          || props.myQuestType === '知情问卷') {
          getQuestViewFun = props.getQuestView({
            ICFStatementId: store.state?.userInformation?.icfStatementID || store.state?.userInformation?.icfStementId
          })
        }
        else if (props.myQuestType === '用药记录') {
          getQuestViewFun = props.getQuestView(id, params)
        }
        else {
          getQuestViewFun = props.getQuestView(id)
        }
        if (props?.getQuestView) {
          // 获取问题-数据
          getQuestViewFun.then((res) => {
            state.getQuestViewObj = res
            // 如果是消失的问卷 直接回首页
            // if (res.xxx) {
            //   $back
            // }
            // 已审阅则只读
            // if (res.reviewStatus === 1) {
            //   state.editable = '0'
            // }
            // go定制化问卷 如果要先打开计划外则先不进
            if (res.questCrfType === 3 && !state.UnplannedModificationRef?.unplannedModificationModuleFlag) {
              const query = {
                ...route.query,
                questId: res.questId,
                editable: state.editable,
                questionUrl: res.questUrl,
              }
              proxy.$routerGoFun('replace', '/followurlquestionnaires', query, route.path)
              return
            }
            let getQuestDataFun
            if (props.myQuestType === '其它药物' || props.myQuestType === '不适记录') {
              getQuestDataFun = props.getQuestData(id)
            } else if (props.myQuestType === '用药记录') {
              if (route.query?.durgId) {
                params.drugId = route.query?.durgId || ''
              } else if (route.query?.newTakemedicineLogItem) {
                const newTakemedicineLogItem = JSON.parse(route.query.newTakemedicineLogItem as string)
                params.drugId = newTakemedicineLogItem.dctDrugId
              }
              getQuestDataFun = props.getQuestData(id, params)
              // 获取特殊字段接口
            } else {
              getQuestDataFun = props.getQuestData(res.questId)
            }
            getQuestDataFun.then((rest) => {
              if ((state.editable === '0' && rest.questCrfType === 2) || rest.questCrfType === 0) {
                res.questCrfType = 1
                rest.questCrfType = 1
              }
              if (rest?.dataClarification) {
                res.dataClarification = rest.dataClarification
                res.clarify = rest?.clarify
              }
              state.questionDataObj = rest;
              // 问题 和 答案 合并 
              if (rest.questCrfType === 1 || rest.questCrfType === 2) {
                let listFlag = false
                const count = rest.questCrfItemes.filter(items => items.crfFieldType === 3).length;
                let tableListCount = 0
                // 多个列表 数据
                const tableListAllList: any = []
                rest.questCrfItemes.forEach((item, index) => {
                  try {
                    res.questCrfItemes.forEach(async (items, indexs) => {
                      // 如果是列表
                      if (item.dctCode === items.dctCode && items.crfFieldType === 3) {
                        listFlag = true
                        const listRes = await props.getTableList(rest.questId, item.dctCode)
                        if (listRes?.length && Array.isArray(listRes)) {
                          // 存初始的列表值 用于 对比rowId是否变化
                          // store.dispatch('setTableListArr', listRes)
                          // store.dispatch('setInitialListArr', [])
                          items.children = listRes
                          // 存多个
                          tableListAllList.push(items)
                          if (!route.query?.tableListBackFlag) {
                            store.dispatch('setTableListAllList', deepClone(tableListAllList))
                            store.dispatch('setInitialAllListArr', tableListAllList)
                          }
                          state.tableListAllList = deepClone(tableListAllList)
                          tableListCount += 1
                          // 可能有多个
                          // console.log('cs',count === tableListCount);
                          if (count === tableListCount) {
                            state.fzFun(res)
                          }
                          // 消息模板进列表问卷的澄清
                          // && item?.clarify?.clarifyCount
                          // console.log('item.dctCode === route.query?.clarificationDctCode',item.dctCode === route.query?.clarificationDctCode);
                          // console.log(route.query?.questItemDataId
                          //   , route.query?.clarificationRowId
                          //   , rest?.isSubmit);
                          
                          if (route.query?.questItemDataId
                            && route.query?.clarificationRowId
                            && rest?.isSubmit
                            && item.dctCode === route.query?.clarificationDctCode
                          ) {
                            // if()遍历找 相同的rowId ,
                            listRes.map(async(viewItem) => {
                              // console.log(viewItem,route.query);
                              if (viewItem?.rowId && viewItem?.rowId === route.query?.clarificationRowId) {
                                // 关联条件结束后看/存在且没隐藏：则进列表问卷
                                // viewItem 是 listRes 下的某一条item
                                // console.log(items.refTypeShow !== 2 && items.refTypesShow !== 2,'items.refTypeShow !== 2 && items.refTypesShow !== 2');
                                const questInfoRes: any = await getQuestInfo(res.questId || route.query?.questId)
                                setTimeout(() => {
                                  if (items.refTypeShow !== 2 && items.refTypesShow !== 2) {
                                    state.questionIndex = index
                                    // ||  state.editable === '1'
                                    if (questInfoRes?.isEditable && items?.isReadOnly === 0) {
                                      state.editListFun(viewItem, '1', items)
                                    } else {
                                      state.editListFun(viewItem, '0', items)
                                    }
                                  }
                                }, 0);
                              }
                            }
                            )
                          }
                        }
                      }
                      // 传值
                      if (item.dctCode === items.dctCode) {
                        items.fieldValue = item.fieldValue;
                        items.numberFieldValue = item.fieldValue;
                        items.oldFieldValue = item.fieldValue;
                        items.questItemDataId = item.questItemDataId;
                        items.fieldValueStr = item.fieldValueStr
                        items.clarify = item?.clarify
                        // 如果消息模板 需要打开数据澄清
                        // 关联显示的话再打开
                        if (res?.dataClarification &&
                          items?.questItemDataId &&
                          items?.questItemDataId &&
                          items.questItemDataId === route.query?.questItemDataId &&
                          state.routeOpenDataClarificationFlag
                        ) {
                          state.routeOpenDataClarificationFlag = false
                          // 非列表
                          if (items.crfFieldType !== 3) {
                            setTimeout(() => {
                              if (items.refTypeShow !== 2 && items.refTypesShow !== 2) {
                                state.openDataClarification(items)
                              }
                            }, 0);
                          }
                        }
                        // 如果是文件集合
                        if (items.crfFieldType === 1) {
                          if (!items.fileList) {
                            // 为空定义
                            items.fileList = [];
                          }
                          items.fileList.push(item); // 持续赋值
                          items.fileList.forEach((fileListItem) => {
                            fileListItem.url = fileListItem.fieldValue;
                          });
                          // 转换图片地址数组
                          if (items.fieldValue && items.fieldValueStr) {
                            items.fileListUrl = items.fieldValue.split(',')
                            items.fileListStr = items.fieldValueStr.split(',')
                            items.fileListUrl.length -= 1
                            items.fileListStr.length -= 1
                          }
                        }
                        // 多选修改成数组
                        else if (items.crfFieldControl === 10) {
                          if (!items.fieldValue) {
                            items.fieldValue = [];
                          } else if (typeof items.fieldValue === "string") {
                            items.fieldValue = items.fieldValue.split(",");
                          }
                        }
                        else if (items.crfFieldControl === 6) {
                          // 6 = 日期控件, 7 = 时间控件
                          items.showDatas = false; // 显示隐藏当前的日期 时间组件
                        } else if (items.crfFieldControl === 7) {
                          items.showTimes = false;
                        }
                        // 拆分时间日期
                        else if (items.crfFieldControl === 8) {
                          items.showDatas = false; // 显示隐藏当前的日期 时间组件
                          items.showTimes = false;
                          if (typeof items.fieldValue === "string" && items.fieldValue) {
                            item.fieldValue = items.fieldValue.split(",")
                            items.fieldValue = items.fieldValue.split(",");
                          } else if (!items.fieldValue) {
                            items.fieldValue = ['', '']
                          }
                        } else if (items.crfFieldControl === 11) {
                          item.showYearMonth = false
                        }
                      }
                      // 赋值完成后 渲染
                      if (index + 1 === rest?.questCrfItemes?.length && indexs + 1 === res?.questCrfItemes?.length
                        && !listFlag) {
                        // state.questionIndex = res.crfGuideline ? -1 : 0;
                        // 获取特殊字段接口
                        if (props.myQuestType === '用药记录') {
                          getDurgDataBeforehand(id, params)
                            .then((durgDataBeforehandlist) => {
                              state.durgDataBeforehandlist = durgDataBeforehandlist
                              res.questCrfItemes.forEach(el => {
                                state.durgSpecialFieldTypeFz(el)
                              });
                              state.fzFun(res)
                            })
                        } else {
                          state.fzFun(res)
                        }
                        // console.log(state.questionObj); 获取到问题答案总对象
                      }
                    });
                  } catch { }
                });
              }
            });
          });
        }
      },
      fzFun: (res, initialQuestionObj) => {
        // console.log('fzFun',new Date());
        if (props.myQuestType === '入排问卷' && res.warningContent && res.finishStatus === 5) {
          // 需完善 弹框
          state.needImprove(res)
        }
        state.questionObj = res;
        // console.log(state.questionObj,'state.questionObj',state);
        state.questionObjLength = res.questCrfItemes.length;
        state.refreshRelevanceAll()
        // 埋点
        if (props.myQuestType === '知情问卷') {
          props.postICFGeneralInfoTime({
            icfStatementID: store.state?.userInformation?.icfStatementID || store.state?.userInformation?.icfStementId
          })
        }
        // console.log(res,initialQuestionObj,'initialQuestionObj');
        state.initialQuestionObj = initialQuestionObj ? deepClone(initialQuestionObj) : deepClone(res);
        state.oldQuestionObj = deepClone(res);
      },
      // 互斥
      mutualExclusion: (item, fildItem) => {
        state.showRelevances(item, fildItem)
        state.refreshRelevanceAll()
      },
      // 刷新 关联逻辑
      refreshRelevanceAll: () => {
        state.questionObj.questCrfItemes.forEach((resItems) => {
          state.showRelevance(resItems)
          if (resItems.crfFieldControl === 10) {
            state.showRelevances(resItems)
          }
        })
      },
      // 单选-反选
      invertSelectionFun: (e, item) => {
        if (state.editable !== '1' || item?.isReadOnly > 0) {
          return
        }
        if (!item.oldFieldValue) {
          item.oldFieldValue = item.fieldValue
        } else if (item?.oldFieldValue && item.oldFieldValue === item.fieldValue) {
          item.oldFieldValue = ''
          item.fieldValue = ''
        } else {
          item.oldFieldValue = e
        }
        state.refreshRelevanceAll()
      },
      // 是否关联显示
      showRelevance: (item) => {
        const questionObj = state.questionObj;
        questionObj.questCrfItemes.forEach(async (items) => {
          // code相同的是关联问题
          if (item.dctCode === items.refDctCode && item.crfFieldControl === 9) {
            // 初始化显示隐藏标识
            if (item.refTypeShow) {
              items.refTypeShow = item.refTypeShow
            }
            if (item.refTypesShow) {
              items.refTypesShow = item.refTypesShow
            }
            // refItemValue如果值为多个选 ,逗号分割 -- 需要拆分 数组再逐个匹配
            let relevanceFlag = false
            if (items?.refItemValue && items?.refItemValue.indexOf(',') > -1) {
              items.refItemValue.split(',').forEach(el => {
                if (el === item.fieldValue) {
                  relevanceFlag = true
                  return
                }
              });
            }
            // (items?.refItemValue === item.fieldValue || relevanceFlag)
            if (items?.refItemValue !== "" &&
              (items?.refItemValue === item.fieldValue || relevanceFlag) &&
              (item?.fieldValue || item?.fieldValue === 0)
            ) {
              if (items.refType === 1) {
                items.refTypeShow = 1;
                state.questionObj.questCrfItemes.forEach(el => {
                  state.durgSpecialFieldTypeFz(el)
                });
              } else if (items.refType === 2) {
                items.refTypeShow = 2;
                // if (Array.isArray(items.fieldValue)) {
                //   items.fieldValue = []
                //   items.oldFieldValue = []
                // } else {
                //   items.fieldValue = null
                //   items.oldFieldValue = null
                // }
                state.showRelevances(items)
              }
            } else if (item.fieldValue !== items.refItemValue) {
              if (items.refType === 1) {
                items.refTypeShow = 2;
                // if (Array.isArray(items.fieldValue)) {
                //   items.fieldValue = []
                //   items.oldFieldValue = []
                // } else {
                //   // else if(items.specialFieldType < 11 && items.specialFieldType > 14)
                //   items.fieldValue = null
                //   items.oldFieldValue = null
                // }
              } else if (items.refType === 2) {
                items.refTypeShow = 1;
                state.questionObj.questCrfItemes.forEach(el => {
                  state.durgSpecialFieldTypeFz(el)
                });
              }
            }
            // 如果父题隐藏则子关联题隐藏
            if (item.refTypeShow === 2) {
              items.refTypeShow = 2
            } else if (item.refTypesShow === 2) {
              items.refTypesShow = 2
            }
            // 如果是列表 被隐藏
            // store.state.initialListArr?.length !== 0 &&
            // store.state.initialListArr.find(item => item.isChange) &&
            // if (items.crfFieldType === 3 && 
            // !items.isReadOnly &&
            // props.myQuestType === '随访问卷' && 
            // state.editable === '1' &&
            // (items.refTypeShow === 2 || items.refTypesShow === 2)) {
            //   state.rowChange = false
            //   store.dispatch('setInitialListArr', [])
            //   state.refTypeShowFlag = true
            //   await postClearStagingData(state.questionObj?.questId, true)
            //   // 清空上次填写的列表问卷 ，再获取列表问卷
            //   state.upListData(true)
            // }
          }
        });
      },
      // 多选关联显示
      showRelevances: (item, fildItem) => {
        if (item?.isReadOnly > 0) {
          return
        }
        // mutualExclusion 互斥
        if (fildItem?.mutualExclusion) {
          if (fildItem?.itemValue && item?.fieldValue?.length && Array.isArray(item?.fieldValue)) {
            item.fieldValue = [fildItem?.itemValue || '']
          }
        }
        // 选中的有值 且 不支持反选
        else if (fildItem?.itemValue && !fildItem?.mutualExclusion) {
          item.fieldValue.forEach((element, idx) => {
            item.fieldItems.forEach(fieldItemsEl => {
              if (fieldItemsEl?.mutualExclusion && element === fieldItemsEl?.itemValue
                && element && fieldItemsEl?.itemValue) {
                item.fieldValue.splice(idx, idx + 1)
              }
            });
          });
        }
        if (item?.fieldValue?.length && Array.isArray(item.fieldValue)) {
          item.fieldValue = Array.from(new Set(item.fieldValue))
        }
        const questionObj = state.questionObj;
        questionObj.questCrfItemes.forEach(async (items) => {
          // code相同的是关联问题
          if (item.dctCode === items.refDctCode && item.crfFieldControl === 10) {
            // 初始化显示隐藏标识
            if (item.refTypeShow) {
              items.refTypeShow = item.refTypeShow
            }
            if (item.refTypesShow) {
              items.refTypesShow = item.refTypesShow
            }
            if (Array.isArray(item.fieldValue)) {
              let found = false
              item.fieldValue.forEach(i => {
                if (found) {
                  return
                }
                // items.refItemValue带i，是数组 - 拆分后判断
                // 字符串 === 判断
                if (items?.refItemValue?.includes(',')) {
                  const valueArr = items.refItemValue.split(',')
                  valueArr.forEach((val) => {
                    if (val === i)
                      found = true
                  });
                } else if (i === items.refItemValue) {
                  found = true
                }
              })
              if (found && items.refItemValue) {
                if (items.refType === 1) {
                  items.refTypesShow = 1;
                  state.questionObj.questCrfItemes.forEach(el => {
                    state.durgSpecialFieldTypeFz(el)
                  });
                } else if (items.refType === 2) {
                  items.refTypesShow = 2;
                  // if (Array.isArray(items.fieldValue)) {
                  //   items.fieldValue = []
                  //   items.oldFieldValue = []
                  // } else {
                  //   items.fieldValue = null
                  //   items.oldFieldValue = null
                  // }
                  state.showRelevance(items)
                }
              } else {
                if (items.refType === 1) {
                  items.refTypesShow = 2;
                  // if (Array.isArray(items.fieldValue)) {
                  //   items.fieldValue = []
                  //   items.oldFieldValue = []
                  // } else {
                  //   items.fieldValue = null
                  //   items.oldFieldValue = null
                  // }
                } else if (items.refType === 2) {
                  items.refTypesShow = 1;
                  state.questionObj.questCrfItemes.forEach(el => {
                    state.durgSpecialFieldTypeFz(el)
                  });
                }
              }
            } else {
              if (items.refType === 1) {
                items.refTypesShow = 2;
                // if (Array.isArray(items.fieldValue)) {
                //   items.fieldValue = []
                //   items.oldFieldValue = []
                // } else {
                //   items.fieldValue = null
                //   items.oldFieldValue = null
                // }
              } else if (items.refType === 2) {
                items.refTypesShow = 1;
                state.questionObj.questCrfItemes.forEach(el => {
                  state.durgSpecialFieldTypeFz(el)
                });
              }
            }
            // 如果父题隐藏则子关联题隐藏
            if (item.refTypeShow === 2) {
              items.refTypeShow = 2
            } else if (item.refTypesShow === 2) {
              items.refTypesShow = 2
            }
            // 如果是列表 被隐藏
            // if (items.crfFieldType === 3 && 
            // !items.isReadOnly &&
            // props.myQuestType === '随访问卷' && 
            // state.editable === '1' &&
            // (items.refTypeShow === 2 || items.refTypesShow === 2)) {
            //   state.rowChange = false
            //   store.dispatch('setInitialListArr', [])
            //   state.refTypeShowFlag = true
            //   await postClearStagingData(state.questionObj?.questId, true)
            //   // 清空上次填写的列表问卷 ，再获取列表问卷
            //   state.upListData(true)
            // }
          }
        });
      },
    });

    watch(state, (newState, oldState) => {
      // 1是完整问卷 2且可编辑，3且未提交 ,4随访问卷
      if (state.editable === '1' &&
        (newState?.questionObj?.questCrfType === 1 ||
          newState?.questionObj?.questCrfType === 2 ||
          newState?.questionObj?.questCrfType === 4
        ) &&
        newState.questionObj.questCrfItemes?.length) {
        // console.log(newState.questionObj,'newState',state.oldQuestionObj,'oldQuestionObj')
        delay(() => {
          // dctCode 相同 比较 fieldValue
          let isChange = newState.questionObj.questCrfItemes.some(item =>
            state.oldQuestionObj.questCrfItemes.some(oldItem =>
              item.dctCode === oldItem.dctCode && (item?.fieldValue || oldItem?.fieldValue || item?.fieldValue === 0 || oldItem?.fieldValue === 0) && (item.fieldValue !== oldItem.fieldValue && item.fieldValue + '' !== oldItem.fieldValue + '')
            )
          );

          if (Array.isArray(state.initialQuestionObj.questCrfItemes)) {
            // 暂时不能检测到 隐藏题列表问卷被改动 后 又给隐藏了
            // store.state.initialAllListArr 有 isChange 
            // state.questionObj.questCrfItemes.some 那题得是显示 refType !== 2 refTypeShow !== 2
            // store.state.initialAllListArr?.some(item => item.children.some(el => el?.isChange)))
            // console.log(state.initialQuestionObj.questCrfItemes,'state.initialQuestionObj.questCrfItemes');
            state.initialIsChange = state.questionObj.questCrfItemes.some(item =>
              state.initialQuestionObj.questCrfItemes.some(oldItem => item.crfFieldType !== 3 &&
                item.dctCode === oldItem.dctCode &&
                (item.refTypeShow !== oldItem.refTypeShow || (item.refTypeShow === oldItem.refTypeShow && oldItem.refTypeShow !== 2)) &&
                (item.refTypesShow !== oldItem.refTypesShow || (item.refTypesShow === oldItem.refTypesShow && oldItem.refTypesShow !== 2)) &&
                (item?.fieldValue?.length || oldItem?.fieldValue?.length || item?.fieldValue === 0 || oldItem?.fieldValue === 0) &&
                (item.fieldValue !== oldItem.fieldValue && item.fieldValue + '' !== oldItem.fieldValue + '')
              )
            );
            // console.log(state.initialIsChange,'state.initialIsChange',state.initialQuestionObj,state.questionObj,);
          }
          if (!state.submitSaveLoadingFlag && !state.submitLoadingFlag && isChange && state.initialIsChange) {
            state.isChange = true
            if (props.myQuestType === '随访问卷' && !newState?.questionObj?.isSubmit) {
              state.saveTemporaryQuestionnaire()
              state.oldQuestionObj.questCrfItemes = deepClone(newState.questionObj.questCrfItemes)
            }
            // console.log(state.oldQuestionObj,'oldQuestionObj2',newState.questionObj)
          } else if (props.myQuestType === '随访问卷' &&
            !newState?.questionObj?.isSubmit &&
            (state.isChange || store.state.initialAllListArr?.some(item => item.isSubmit))
            && !state.initialIsChange && !state.questionObj?.isStaging
            && !state.rowChange &&
            (store.state.initialAllListArr?.length === 0 || !store.state.initialAllListArr?.some(item => item.children.some(el => el?.isChange)))) {
            // 换成多列表的情况
            // store.state.initialAllListArr
            // 没变值 则 清空
            state.clearStagingFun()
          }
        }, 400);
      }
    })

    onBeforeMount(() => {
      props.questionOnload()
      setTimeout(() => {
        state.outerHeight = document.documentElement.scrollHeight;
      }, 800);
    });

    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
@import "@/style/questionList.less";
.questionnaire-container {
  width: 100%;
  height: 100vh;
  font-size: 0.16rem;
  color: #555;
  background: #f7f7f7;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  :deep(.van-cell::after) {
    border: none !important;
  }
  .questionnaire-common-btn {
    width: 100%;
    height: 50px;
    line-height: 1.2;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .questionnaire-form {
    overflow: auto;
    h4 {
      width: 100%;
    }
    .border-input {
      min-height: 0.6rem;
      border-radius: 0.06rem;
      :deep(.van-field__body) {
        position: relative !important;
        top: 0.1rem !important;
        border-bottom: #f0f0f0 0.5px solid;
      }
    }
    .van-picker,
    .van-datetime-picker {
      width: 100%;
      position: fixed;
      left: 0;
      bottom: 0;
      z-index: 99;
    }
    // 最上方标题语
    .questionnaire-title {
      width: 100%;
      min-height: 90px;
      padding: 0.2rem;
      box-sizing: border-box;
      color: #fff;
      background-image: linear-gradient(#3fa1fc, #5860da);
      .questionnaire-title-text {
        h4 {
          margin: 0 0 0.1rem 0;
        }
      }
      img {
        width: 1.32rem;
      }
    }
    .questionnaire-title-up {
      height: 0.95rem;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      font-size: 0.15rem;
      padding: 0 0 0 0.1rem;
      box-sizing: border-box;
      background: linear-gradient(180deg, #3fa1fc, #5860da);
      color: #fff;
      .questionnaire-title-text-up {
        width: 100%;
        p {
          &:nth-child(2) {
            color: rgb(236, 236, 236);
            margin-top: 0.05rem;
          }
        }
      }
    }
    // 上一题- 进度
    .questionnaire-schedule {
      padding: 0.2rem 0.1rem 0.1rem;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 0.12rem;
      color: var(--theme-color);
      .questionnaire-schedule-gotop {
        display: flex;
        align-items: center;
        :deep(.van-badge__wrapper, .van-icon, .van-icon-arrow-left) {
          color: var(--theme-color) !important;
        }
      }
      .questionnaire-schedule-back {
        width: 2.44rem;
        height: 0.04rem;
        border-radius: 0.1rem;
        overflow: hidden;
        background: #ebf0fa;
        .questionnaire-schedule-in {
          width: 10%;
          height: 100%;
          border-radius: 0.1rem;
          overflow: hidden;
          background: linear-gradient(90deg, #3fa1fc 0%, #5860da 100%);
        }
      }
      .questionnaire-schedule-need {
        width: 2rem;
      }
      // 需完善按钮
      .needImprove {
        padding: 0.02rem 0.07rem;
        white-space: nowrap;
        font-size: 0.1rem;
        color: var(--theme-color);
        border: 0.5px solid var(--theme-color);
        border-radius: 0.03rem;
      }
    }
    .questionnaire-body {
      width: 100%;
      box-sizing: border-box;
      .questionnaire-module {
        width: 100%;
        height: 100%;
        font-size: 0.13rem;
        box-sizing: border-box;
        // 填写指南
        .fingerpost {
          padding: 0.2rem;
          box-sizing: border-box;
          margin: 0.2rem 0 0 0;
          color: #333;
          font-size: 0.15rem;
          display: -webkit-box;
          word-break: break-all;
          word-wrap: break-word;
        }
        .questionnaire-items {
          width: 100%;
          margin: 0.1rem 0 0 0;
          padding: 0.1rem 0;
          box-sizing: border-box;
          border-radius: 0.1rem;
          .questionnaire-item-moduel {
            width: calc(100% - 0.4rem);
            padding: 0.1rem;
            box-sizing: border-box;
            margin: 0.1rem 0 0.2rem 0.2rem;
            border-radius: 0.1rem;
            background: #fff;
            box-shadow: 0 0.02rem 0.12rem rgba(0, 0, 0, 0.07);
            border-radius: 0.1rem;
          }
          .questionnaire-items-title {
            box-sizing: border-box;
            margin: 0.1rem 0;
            overflow: hidden;
            display: flex;
            .date-and-time {
              display: flex;
            }
          }
          // 上传照片
          .upload-img {
            padding: 0.1rem;
            height: 3.3rem;
            overflow: auto;
            box-sizing: border-box;
            border-radius: 0.1rem;
            background: #fff;
          }
          // 需完善按钮
          .needImprove {
            width: 0.3rem;
            margin: 0.1rem 0 0 0.2rem;
            padding: 0.02rem 0.07rem;
            white-space: nowrap;
            font-size: 0.1rem;
            color: var(--theme-color);
            border: 0.5px solid var(--theme-color);
            border-radius: 0.03rem;
          }
        }
      }
      :deep(.van-field__control:disabled) {
        color: #333;
        -webkit-text-fill-color: #333;
      }
    }
    // 拍照上传
    .uploadsheet-title {
      font-size: 0.15rem;
      margin: 0.2rem 0 0 0.1rem;
      color: #333;
    }
    // 只读
    .upload-imgs {
      width: 100%;
      height: 3.1rem;
      img {
        width: 0.9rem;
        height: 0.9rem;
        margin: 0 0 0.1rem 0.17rem;
      }
    }
    .uploadBtn {
      width: 90%;
      position: fixed;
      left: 0.2rem;
      bottom: 0.4rem;
    }
  }
}

.isRequiredActive {
  display: inline-block;
  color: red;
  font-size: 0.25rem;
  line-height: 0.27rem;
}
</style>
