<template>
  <div id="signature-container">
    <canvas id="signature-pad" width="400" height="400"/>
    <div @click="checkFullscreen" class="ft-12-rem">切换全屏</div>
    <div class="ft-12-rem">{{ windowOrientation }}</div>
    <div class="ft-12-rem" @click="quanpinfg">全屏赋值</div>
    <canvas id="smallScreenSignaturePad" width="400" height="200"/>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, nextTick, ref } from "vue";
import screenfull from "screenfull";
console.log(screenfull);

import SignaturePad from "signature_pad";
let signaturePad, smallScreenCanvasPad;

let windowOrientation = ref("");

function quanpinfg() {
  const fullScreenDataURL = signaturePad.toDataURL();
  const smallScreenCanvas = document.getElementById("smallScreenSignaturePad");
  var img = new Image();
  var ctx = smallScreenCanvas.getContext('2d');
  img.onload = function(){
    ctx.drawImage(img, 0, 0); 
  };
  img.src = fullScreenDataURL; 
  console.log(smallScreenCanvas);
  console.log(fullScreenDataURL);
}

function resizeCanvas() {
  const canvas = document.getElementById("signature-pad");
  const container = document.getElementById("signature-container");
  windowOrientation.value = window.orientation;
  setInterval(() => {
    windowOrientation.value = window.orientation;
  }, 2000);
  if (window.orientation === 90 || window.orientation === -90) {
    // 横屏
    canvas.width = container.offsetHeight;
    canvas.height = container.offsetWidth;
  } else {
    // 竖屏
    canvas.width = container.offsetWidth;
    canvas.height = container.offsetHeight;
  }
}

function checkFullscreen () {
    if (screenfull.isEnabled) {
        screenfull.toggle();
    }
}

window.addEventListener("orientationchange", resizeCanvas);

onMounted(() => {
  nextTick(() => {
    const canvas = document.getElementById("signature-pad");
    const smallScreenCanvas = document.getElementById("smallScreenSignaturePad");
    signaturePad = new SignaturePad(canvas);
    smallScreenCanvasPad = new SignaturePad(smallScreenCanvas);
    // resizeCanvas();
  });
});
</script>
