<template>
  <!-- 当前状态img+内容 -->
  <div class="myStateShow">
    <img v-if="imgSrc" :src="imgSrc" :class="imgClass"/>
    <div v-if="texts" v-html="texts"/>
    <slot name="mytexts"></slot>
  </div>
</template>
  
<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount } from "vue";

export default defineComponent({
  name: "MyStateShow", // 暂无数据
  props: {
    // 图片class
    imgClass: {
      type: String,
      default: "",
    },
    // 图片
    imgSrc: {
      type: String,
      default: "",
    },
    // 内容
    texts: {
      type: String,
      default: "",
    },
  },
  setup() {
    const state = reactive({
      //
    });

    onBeforeMount(() => {
      //
    });

    return {
      ...toRefs(state),
    };
  },
});
</script>
  
<style scoped lang='less'>
.myStateShow {
  width: 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  img {
    display: block;
    margin: 0 auto;
  }
  div {
    color: #9a9a9a;
    margin: 0.3rem 0 0 0;
    text-align: center;
    font-size: 0.16rem;
    font-family: PingFang SC;
    font-weight: 400;
  }
}
</style>
  