<template>
   <van-popup v-model:show="showMyDatePopup" position="bottom">
    <van-datetime-picker
      v-if="showDatetime"
      v-model="currentDate"
      type="date"
      title="选择日期"
      confirm-button-text="下一步"
      :min-date="minDate"
      :max-date="maxDate"
      :columns-order="['year', 'month', 'day']"
      @cancel="showMyDatePopup = false"
      @confirm="confirmValue"
    />
    <van-picker
      v-else-if="showTimePicker"
      title="选择时间段"
      :columns="timeColumns"
      @confirm="onConfirm"
      @cancel="onCancel"
    />
  </van-popup>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
import { year } from '@trialdata/common-fun-css/index'

export default defineComponent({
  name: "MyDateTimePopup", // 日期 时间段控件
  props: {
    getDateTimeValueFun: {
      type: Function,
      default: () => {},
    },
  },
  setup(props: any) {
    const state = reactive({
      // 日期相关控件
      myDateTimeValue: '',
      oldDateTimeValue: '',
      showMyDatePopup: false,
      showDatetime: true,
      showTimePicker: false,
      currentDate: new Date(),
      minDate: new Date(),
      maxDate: new Date(year/1 + 100, 12, 31),
      // 最早8:00-9:00，最晚19:00-20:00；
      timeColumns: [
        '08:00-09:00',
        '09:00-10:00',
        '10:00-11:00',
        '11:00-12:00',
        '12:00-13:00',
        '13:00-14:00',
        '14:00-15:00',
        '15:00-16:00',
        '16:00-17:00',
        '17:00-18:00',
        '18:00-19:00',
        '19:00-20:00'
      ],
      // 确认时间段
      onConfirm: (e) => {
        state.oldDateTimeValue += ` ${e}`
        state.myDateTimeValue = state.oldDateTimeValue
        props.getDateTimeValueFun(state.myDateTimeValue)
        state.showMyDatePopup = false
        state.showDatetime = true
        state.showTimePicker = false
      },
      // 取消时间段
      onCancel: () => {
        state.oldDateTimeValue = ''
        state.showDatetime = true
        state.showTimePicker = false
        state.showMyDatePopup = false
      },
      // 选择日期
      confirmValue: (val) => {
        const year = val.getFullYear();
        let month = val.getMonth() + 1;
        let day = val.getDate();
        let hour = val.getHours();
        let minute = val.getMinutes();
        if (month >= 1 && month <= 9) {
          month = `0${month}`;
        }
        if (day >= 1 && day <= 9) {
          day = `0${day}`;
        }
        if (hour >= 0 && hour <= 9) {
          hour = `0${hour}`;
        }
        if (minute >= 0 && minute <= 9) {
          minute = `0${minute}`;
        }
        let currentDates: any = state.currentDate;
        currentDates = `${year}-${month}-${day}`;
        // 日期控件时直接赋值
        state.oldDateTimeValue = currentDates
        state.showDatetime = false
        state.showTimePicker = true
      },
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>
