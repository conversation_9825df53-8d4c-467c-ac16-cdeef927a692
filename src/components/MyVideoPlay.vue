<template>
  <div v-if="fileUrl" class="videopl-body centerflex-h">
    <div class="videopl-modules">
      <video 
        ref="MyVideoPlayRef"
        playsinline
        webkit-playsinline
        x5-video-player-type="h5"
        x-webkit-airplay
        x5-video-orientation="portraint"
        class="video-js video-player vjs-custom-skin"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeUnmount, onMounted } from "vue";
import videojs from 'video.js';

export default defineComponent({
  name: "MyVideoPlay", // 封装视频播放
  props: {
    // 视频Url
    fileUrl: {
      type: String,
      default: '',
    },
    // 视频封面Url
    coverUrl: {
      type: String,
      default: '',
    },
    // 倍速
    playbackRates: {
      type: Array,
      default: () => [0.5, 1, 1.5, 2],
    },
    // 播放进行中
    onPlaying: {
      type: Function,
      default: () => {},
    },
    // 播放开始
    onPlay: {
      type: Function,
      default: () => {},
    },
    // 暂停回调
    onPause: {
      type: Function,
      default: () => {},
    },
    // 播放结束
    onEnded: {
      type: Function,
      default: () => {},
    },
  },
  setup(props) {
    const player: any = reactive({});
    const state = reactive({
      MyVideoPlayRef: null,
      // 暂停
      pause: () => {
        player.value?.pause()
      },
      // 播放
      play: () => {
        player.value?.play()
      },
      // 重播
      toReplay: () => {
        player.value.currentTime(0); // 将播放时间重置为开始
        player.value?.play()
      }
    });
    onMounted(() => {
      if (props?.fileUrl) {
        if (state.MyVideoPlayRef) {
          player.value = videojs(state.MyVideoPlayRef,{
            autoplay: false,
            controls: true,
            loop: false,
            fluid: true,
            playbackRates: props.playbackRates,
            controlsList: ['playToggle', 'currentTimeDisplay', 'timeDivider', 'durationDisplay', 'progressControl', 'fullscreenToggle'],
            sources: [{
                src: props.fileUrl + `&x=${new Date()}`,
                type: 'video/mp4'
              }],
            poster: props.coverUrl,
          });
          player.value.on('play', props.onPlay);
          player.value.on('playing', props.onPlaying);
          player.value.on('pause', props.onPause);
          player.value.on('ended', props.onEnded);
        }
        // 测试 暂停和播放
        // setTimeout(() => {
        //   // console.log(player.value?.play);
        //   // console.log(player.value?.pause); 
        //   player.value.pause();
        // }, 4000);
      }
    })
    onBeforeUnmount(() => {
      if (player.value) {
        // console.log(player.value,player.value.dispose,state.MyVideoPlayRef)
        player.value.dispose();
      }
      const videoPlayer = state.MyVideoPlayRef;
      if (videoPlayer && videoPlayer.srcObject) {
        const tracks = videoPlayer.srcObject.getTracks();
        tracks.forEach(track => {
          track.stop();
        });
      }
    })
    return {
      player,
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
// :deep(.vjs-poster) {
//   display: v-bind(display);
// }
/*画中画按钮隐藏 */
:deep(.vjs-picture-in-picture-control) {
  display: none !important;
}
.video-js {
  :deep(.vjs-big-play-button) {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
.videopl-body {
  height: calc(100vh - 46px); // 计算高度
}
.videopl-modules {
  width: 100%;
  height: 100%;
  // border-radius: 4px;
  overflow: auto;
  :deep(.vjs-fluid:not(.vjs-audio-only-mode)) {
    padding-top: 56.25% !important;
  }
  // :deep(.video-player.vjs-custom-skin) {
  //   height: 100% !important;
  // }
  :deep(.video-js.vjs-fluid) {
    height: 100% !important;
  }
}
</style>
