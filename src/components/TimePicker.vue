<template>
  <van-picker
    :columns="columns"
    @change="columnsChange"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <template #title>
      <div class="ft-13-rem font-color-969799" @click="handleClearCancel">
        清空
      </div>
    </template>
  </van-picker>
</template>

<script setup>
import { ref } from "vue";
// 时间UK控件 TimePicker
// hour minute

const hourArr = [
  "UK",
  "00",
  "01",
  "02",
  "03",
  "04",
  "05",
  "06",
  "07",
  "08",
  "09",
  "10",
  "11",
  "12",
  "13",
  "14",
  "15",
  "16",
  "17",
  "18",
  "19",
  "20",
  "21",
  "22",
  "23",
];
const minuteArr = [
  "UK",
  "00",
  "01",
  "02",
  "03",
  "04",
  "05",
  "06",
  "07",
  "08",
  "09",
  "10",
  "11",
  "12",
  "13",
  "14",
  "15",
  "16",
  "17",
  "18",
  "19",
  "20",
  "21",
  "22",
  "23",
  "24",
  "25",
  "26",
  "27",
  "28",
  "29",
  "30",
  "31",
  "32",
  "33",
  "34",
  "35",
  "36",
  "37",
  "38",
  "39",
  "40",
  "41",
  "42",
  "43",
  "44",
  "45",
  "46",
  "47",
  "48",
  "49",
  "50",
  "51",
  "52",
  "53",
  "54",
  "55",
  "56",
  "57",
  "58",
  "59",
];
//
const columns = ref([
  // 第一列 年 默认1900-2099
  {
    values: hourArr,
    defaultIndex: 0,
  },
  // 第二列 月
  {
    values: ["UK"],
    defaultIndex: 0,
  },
]);

// 改变后 小时 数组转换
function columnsChange(e, l) {
  /*e是完整数据如['00', '06',]
     l 是当前改变的第几项 0年 1月 2日
    */
  if (l === 0) {
    // 年UK则 - 月UK
    if (e[0] === "UK") {
      columns.value[1].defaultIndex = 0;
      columns.value[1].values = ["UK"];
    } else if (columns.value[1].values.length < 2) {
      columns.value[1].values = minuteArr;
    }
  }
}
// 确认
function handleConfirm() {
  return "";
}
// 取消
function handleCancel() {
  return "";
}
// 清空
function handleClearCancel() {
  return "";
}
</script>
