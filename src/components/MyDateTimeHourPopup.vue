<template>
   <van-popup v-model:show="showDatetimePicker" position="bottom">
    <van-datetime-picker
      v-if="showDatetime"
      v-model="currentDate"
      type="date"
      title="选择日期"
      confirm-button-text="下一步"
      :min-date="minDate"
      :max-date="maxDate"
      :columns-order="['year', 'month', 'day']"
      @cancel="showDatetimePicker = false"
      @confirm="confirmValue"
    />
    <van-datetime-picker
      v-else-if="showTimePicker"
      :filter="filterFun"
      v-model="currentTime"
      @confirm="onConfirm"
      @cancel="onCancel"
      type="time"
      title="选择时间"
      min-hour="08"
      max-hour="20"
    />
   </van-popup>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount, } from 'vue';
import { year } from '@trialdata/common-fun-css/index'

export default defineComponent({
  name: "MyDateTimePopup", // 日期 时间控件
  props: {
    getDateTimeValueFun: {
      type: Function,
      default: () => {
        //
      },
    },
    currentDateTime: {
      type: String,
      default: ""
    },
  },
  setup(props) {
    const state: any = reactive({
      // 日期相关控件
      currentTime: '',
      myDateTimeValue: '',
      oldDateTimeValue: '',
      showDatetimePicker: false,
      showDatetime: true,
      showTimePicker: false,
      currentDate: new Date(),
      minDate: new Date(),
      maxDate: new Date(year/1 + 100, 12, 31),
      // 最早8:00-9:00，最晚19:00-20:00；
      timeColumns: [
        '08:00-09:00',
        '09:00-10:00',
        '10:00-11:00',
        '11:00-12:00',
        '12:00-13:00',
        '13:00-14:00',
        '14:00-15:00',
        '15:00-16:00',
        '16:00-17:00',
        '17:00-18:00',
        '18:00-19:00',
        '19:00-20:00'
      ],
      filterFun: (type, val) => {
        if (type === 'minute') {
          return ['00','30'];
        }
        return val;
      },
      // 确认时间段
      onConfirm: (e) => {
        state.oldDateTimeValue += ` ${e}:00`
        state.myDateTimeValue = state.oldDateTimeValue
        props.getDateTimeValueFun(state.myDateTimeValue)
        state.showDatetimePicker = false
        state.showDatetime = true
        state.showTimePicker = false
      },
      // 取消时间段
      onCancel: () => {
        state.oldDateTimeValue = ''
        state.showDatetime = true
        state.showTimePicker = false
        state.showDatetimePicker = false
      },
      // 选择日期
      confirmValue: (val) => {
        const year = val.getFullYear();
        let month = val.getMonth() + 1;
        let day = val.getDate();
        let hour = val.getHours();
        let minute = val.getMinutes();
        if (month >= 1 && month <= 9) {
          month = `0${month}`;
        }
        if (day >= 1 && day <= 9) {
          day = `0${day}`;
        }
        if (hour >= 0 && hour <= 9) {
          hour = `0${hour}`;
        }
        if (minute >= 0 && minute <= 9) {
          minute = `0${minute}`;
        }
        let currentDates: string | Date = state.currentDate;
        currentDates = `${year}-${month}-${day}`;
        // 日期控件时直接赋值
        state.oldDateTimeValue = currentDates
        state.showDatetime = false
        state.showTimePicker = true
      },
      // 打开弹窗前回显日期时间
      showDatetimePickerFun: () => {
        state.showDatetimePicker = true
        if (props?.currentDateTime) {
          const dateTimeStr = props.currentDateTime.split('T')
          // console.log(dateTimeStr,new Date(dateTimeStr[0]))
          state.currentDate = new Date(dateTimeStr[0])
          state.currentTime = dateTimeStr[1]
        }
      }
    });
    onBeforeMount(() => {
      //
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>
