<template>
  <!-- vant上传图片 @delete="deleteImgFun"-->
  <div class="upload">
    <van-uploader
      :after-read="onRead"
      :before-read="beforeRead"
      :before-delete="beforeDelete"
      v-model="fileList"
      accept="image/*"
      :preview-options="{closeable: true}"
      :multiple="myMultiple"
      :max-size="maxSize"
      :max-count="maxCount"
    />
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  ref,
  toRefs,
  onMounted,
  nextTick,
  // watch,
  watchEffect
} from "vue";
// import { useStore } from "vuex";
import { Toast, Dialog, Notify } from "vant";
import { getAccessFile } from '@/api/questionnaire'

export default defineComponent({
  name: "UploadImg",
  props: {
    // 一次可上传多少张
    maxCount: {
      type: Number,
      default: 15,
    },
    // 最大多少MB-默认10MB
    maxSize: {
      type: String,
      default: "10240 * 1024",
    },
    // 上传的api
    apiUpload: {
      type: Function,
      // default: () => {}
    },
    // 超过多少MB压缩-默认2MB
    compressFileSize: {
      type: Number,
      default: 1048576 * 100,// 改为100
    },
    // 上传接口
    postImgs: {
      type: Function,
    },
    // 上传接口函数
    postImgFun: {
      type: Function,
    },
    // 删除的接口
    deleteImg: {
      type: Function,
    },
    // 上传对象 visitId 访视 questId 文件 dctCode
    dctCodeItem: {
      type: Object,
    },
    // 上传身份证件
    identityObj: {
      type: Object,
    },
    // questId 传ID
    questId: {
      type: String,
    },
    // 访视Id
    visitId: {
      type: String,
    },
    // 是否多选 :默认可以多选
    myMultiple: {
      type: Boolean,
      default: true
    },
    // 是否禁用保存问卷
    disabledSaveFalg:{
      type: Object,
    },
  },
  setup(props) {
    // const store = useStore();
    // console.log(props.dctCodeItem)
    const fileList = ref([
      // { url: 'https://img.yzcdn.cn/vant/leaf.jpg' },
    ]); // 回显图片

    const state = reactive({
      imagePath: [], // 上传图片路径

      // vant
      dataURLtoFile: (dataurl, filename) => {
        // 将base64转换为file文件
        const arr = dataurl.split(",");
        const mime = arr[0].match(/:(.*?);/)[1];
        const bstr = atob(arr[1]);
        let n = bstr.length;
        const u8arr = new Uint8Array(n);
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n);
        }
        return new File([u8arr], filename, { type: mime });
      },
      beforeRead(file) {
        // 上传之前检测图片类似返回true和false会影响到onRead函数
        const regex = /(.jpg|.jpeg|.png|.bmp|.gif|.jfif)$/;
        let beforeReadFalg = true
        if(Array.isArray(file)){
          // if(file.length > 15 || (fileList.value.length+file.length) > 15){
          //   Notify({ message: `最多支持15张图片`, type: "danger" });
          // }
          file.forEach((item) => {
            if (!regex.test(item.type)) {
              Toast("图片格式不支持上传");
              beforeReadFalg = false;
            }
          })
        }else{
          if (!regex.test(file.type)) {
            Toast("图片格式不支持上传");
            beforeReadFalg = false;
          }
        }
        if(beforeReadFalg) { // 如果可以上传, 那么我们现在就显示遮照
           // 注意这里和超时时间一样, 上传文件时间可能比较长, 这里设置超时时间为10分钟
          Toast.loading({duration: 60000*10, message: '上传中...',forbidClick: true,});
        }

        return beforeReadFalg
      },
      onRead: (file) => {
        if(Array.isArray(file)){
          props.disabledSaveFalg.disabledSaveFalg = true          
          file.map(async (item) => {
            // const flag = (index+1 === file.length)
            if (item.file.size > (props.compressFileSize)){
              await state.myCompress(item,fileList.value.length)
            }else{
              await state.uploadePrams(item.file,fileList.value.length)
            }
          })         
        }
        // 大于2MB的图片都缩小像素上传
        else if (file.file.size > props.compressFileSize) {
          state.myCompress(file)
        } else {
          //小于2MB直接上传
          // //上传接口visitId 访视 questId 文件 dctCode , 文件data
          state.uploadePrams(file.file)
        }
      },

      // 上传接口
      uploadePrams: (file,fileLength=1) => {
        if (props.questId) {
            props.postImgs(props.questId,props.dctCodeItem.dctCode,file)
            .then((res) => {
              if (props?.dctCodeItem?.fieldValue && props?.dctCodeItem?.fieldValueStr) {
                props.dctCodeItem.fieldValue += res.thumbUrl + ','
                props.dctCodeItem.fieldValueStr += res.dctPatientFileId+','
              } else if (!props?.dctCodeItem?.fieldValue || !props?.dctCodeItem?.fieldValueStr){
                props.dctCodeItem.fieldValue = res.thumbUrl + ','
                props.dctCodeItem.fieldValueStr = res.dctPatientFileId+','
              }             
              // fileList.value[fileList.value.length-1].url = res.thumbUrl
              const fieldValueStrArr = props.dctCodeItem.fieldValueStr.split(",")
              const fieldValueArr = props.dctCodeItem.fieldValue.split(",")
              // fieldValueStrArr.length -= 1
              // 对应加上id
              fileList.value.forEach((item,index: number) => {
                if(fieldValueStrArr[index]){
                  item.questItemDataId = fieldValueStrArr[index]
                  item.url = fieldValueArr[index]
                }            
              })            
              if(fieldValueStrArr.length > fileLength && fieldValueArr.length > fileLength ){
                props.disabledSaveFalg.disabledSaveFalg = false
                Toast.clear();

                Toast("上传成功");
              }
            })
            .catch(() => {
              props.disabledSaveFalg.disabledSaveFalg = false
              Toast.clear();

            })
          } else if(props.identityObj && props.postImgFun){
            props.postImgFun(file)
          }
      },
      // 压缩
      myCompress: (file) => {
          const canvas = document.createElement("canvas"); // 创建Canvas对象(画布)
          const context = canvas.getContext("2d");
          const img = new Image();
          img.src = file.content; // 指定图片的DataURL(图片的base64编码数据)
          img.onload = () => {
            canvas.width = 400;
            canvas.height = 300;
            context.drawImage(img, 0, 0, 400, 300);
            file.content = canvas.toDataURL(file.file.type, 0.92); // 0.92为默认压缩质量
            const files = state.dataURLtoFile(file.content, file.file.name);
            //上传 --压缩后格式会变 gif变为png
            state.uploadePrams(files)
          };
      },

      // beforeDelete 删除前
      beforeDelete: (e) => {
        if(props.disabledSaveFalg.disabledSaveFalg){              
          return
        }
        Dialog.confirm({
          title: "操作提示",
          message: "是否删除当前照片",
        })
          .then(() => {
            //上传
            if(props.disabledSaveFalg.disabledSaveFalg){
              Notify({ message: `不好意思，照片还未上传完成哦`, type: "danger" })              
              return
            }           
            props.disabledSaveFalg.disabledSaveFalg = true
            // if (props.visitId && e.dctPatientFileId) {
            //   props.deleteImg(props.visitId, e.dctPatientFileId).then(() => {
            //     //当前数组里删除
            //     fileList.value.forEach((item, index) => {
            //       if (item === e) {
            //         fileList.value.splice(index, 1);
            //       }
            //     });
            //     props.disabledSaveFalg.disabledSaveFalg = false
            //   }).catch(() => {
            //     props.disabledSaveFalg.disabledSaveFalg = false
            //   })
            // } else

            if (props?.identityObj?.statementFileId) {
              props.deleteImg(props.identityObj.statementFileId).then(() => {
                //当前数组里删除
                props.identityObj.identityCardFileUrl = ''
                fileList.value = []
                props.disabledSaveFalg.disabledSaveFalg = false
              }).catch(() => {
                props.disabledSaveFalg.disabledSaveFalg = false
              })
            }else
             if (props.questId && e.questItemDataId) {
               // 删除时自己一张一张删除照片, 不是上传文件的接口, 所以不会自己弹窗
              props.deleteImg(e.questItemDataId).then(() => {
                //当前数组里删除,
                fileList.value.forEach((item, index) => {
                  if (item === e) {
                    fileList.value.splice(index, 1);
                  }
                });
                // 排除已删除的
                props.dctCodeItem.fieldValue = ''
                props.dctCodeItem.fieldValueStr = ''
                if(fileList.value.length){              
                  fileList.value.forEach((listItem) => {
                    props.dctCodeItem.fieldValue += listItem?.url + ','
                    props.dctCodeItem.fieldValueStr += listItem.questItemDataId + ','
                  })
                }
                props.disabledSaveFalg.disabledSaveFalg = false
              }).catch(() => {
                props.disabledSaveFalg.disabledSaveFalg = false
              })
            }
          })
          .catch(() => {
            return false;
          });
      },
      // // 删除时
      // deleteImgFun:(deleteItem) => {
      //   console.log(deleteItem)
      //   // props.deleteImg().then((res) => {
      //   //   console.log(res)
      //   // })
      // }
    });

    onMounted(() => { 
      if (props.dctCodeItem) {
        // props.dctCodeItem.fieldValue = ''
        // props.dctCodeItem.fieldValueStr = ''
        // props.dctCodeItem.fileList[0].fieldValueStr = ''
        // props.dctCodeItem.fileList[0].fieldValue = ''
        nextTick(() => {
          const propsDctCodeItem = { ...props.dctCodeItem }
          if (propsDctCodeItem?.fileList.length === 1 && propsDctCodeItem?.fileList[0]?.fieldValueStr) {
            props.dctCodeItem.fieldValueStr = propsDctCodeItem?.fileList[0]?.fieldValueStr
          }
          const urlArr = propsDctCodeItem?.fieldValue?.split(",")
          if(Array.isArray(propsDctCodeItem.fileList)&&propsDctCodeItem.fileList.length
          &&(propsDctCodeItem.fileList?.length > urlArr?.length-1 || props?.visitId)){
            const myfileList = []
            propsDctCodeItem.fileList.forEach((item) => {
              const id =  item.questItemDataId || item.dctPatientFileId
              if (item.fieldValue) {
                getAccessFile(id).then((res) => {
                  item.url = res.url || item.thumbUrl
                  myfileList.push(item)
                });
              }
            })
            fileList.value = myfileList
          }else if (propsDctCodeItem.fieldValue) {
            //回显图片
            if (propsDctCodeItem?.fileList.length === 1 && propsDctCodeItem?.fileList[0]?.fieldValueStr) {
              props.dctCodeItem.fieldValueStr = propsDctCodeItem?.fileList[0]?.fieldValueStr
            }
            
            if(urlArr.length>1){
              const fileLists = []
              let idArr = []
              if(propsDctCodeItem?.fieldValueStr?.split(",")) {
                idArr = propsDctCodeItem?.fieldValueStr?.split(",")
              }if (propsDctCodeItem?.fileList?.length){
                propsDctCodeItem?.fileList.map((item) => {
                  idArr.push(item.questItemDataId)
                })
              }
              urlArr.length -= 1
              // idArr.length -= 1
              if (urlArr?.length) {
                props.dctCodeItem.fileList = []  
                urlArr.forEach((item,index) => {
                  fileLists.push({
                    url: item,
                    questItemDataId: idArr[index]
                  })
                  props.dctCodeItem.fileList.push({
                    url: item,
                    questItemDataId: idArr[index]
                  })
                })
              }
              
              fileLists.forEach((item) => {  
                const id =  item.questItemDataId || item.dctPatientFileId
                // console.log(id)
                // return
                getAccessFile(id).then((res) => {
                  item.url = res.url || item.thumbUrl
                });
              })
              fileList.value = fileLists; // newFileList.push({url: ''})  
            }else {
              fileList.value = []
            }
          }
        });
      }  
      
      
    });

    /*watch(
      props,
      () => {
        if (props?.identityObj?.identityCardFileUrl && props?.identityObj?.statementFileId && !fileList?.value?.length){
          fileList.value = [{
            url: props.identityObj.identityCardFileUrl,
            statementFileId: props.identityObj.statementFileId
          }]
        }
      },
      {
        immediate: props?.identityObj ? true : false, // 是否初始化立即执行一次, 默认是false
        deep: props?.identityObj ? true : false, // 是否是深度监视, 默认是false
      }
    )*/
    // 自动监听watchEffect回调里用到的所有数据
    watchEffect(() => {
      if (props?.identityObj?.identityCardFileUrl && props?.identityObj?.statementFileId && !fileList?.value?.length){
        fileList.value = [{
          url: props.identityObj.identityCardFileUrl,
          statementFileId: props.identityObj.statementFileId
        }]
      }
    })

    return {
      fileList,
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
:deep(.van-image) ,:deep(.van-uploader__preview-image) {
  width: 0.9rem;
  height: 0.9rem;
  margin: 0 0 0.1rem 0.13rem;
}

:deep(.van-uploader__upload) {
  width: 0.9rem;
  height: 0.9rem;
  margin: 0 0 0.1rem 0.13rem;
  .van-image{
    width: 0.9rem;
    height: 0.9rem;
  }
}
.upload {
  width: 100%;
  .van-uploader {
    width: 100%;
    :deep(.van-uploader__wrapper) {
      width: 100%;
    }
  }
}
</style>
