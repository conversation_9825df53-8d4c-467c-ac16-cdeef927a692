<template>
  <!-- vant上传图片 @delete="deleteImgFun"-->
  <div class="upload">
    <van-uploader
      v-if="!fileList[0]?.url"
      :after-read="onRead"
      :before-read="beforeRead"
      :before-delete="beforeDelete"
      v-model="fileList"
      accept="*"
      :preview-options="{closeable: true}"
      :multiple="myMultiple"
      :max-size="maxSize"
      :max-count="maxCount"
      @click-preview="previewVideo"
    />
    <div v-if="fileList[0]?.url" class="videoCoverUrlImg-body">
      <img v-if="fileList[0]?.url" :src="fileList[0].url" class="videoCoverUrlImg" alt="" @click="previewVideo">
      <div class="van-uploader__preview-delete" @click="beforeDelete">
        <i class="van-badge__wrapper van-icon van-icon-cross van-uploader__preview-delete-icon"></i>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  ref,
  toRefs,
  onMounted,
  nextTick,
  watch,
  getCurrentInstance
} from "vue";
import { Toast, Dialog, Notify } from "vant";
import { useRoute } from "vue-router";
// import { deepClone } from '@trialdata/common-fun-css/index';

export default defineComponent({
  name: "UploadVideo",
  props: {
    // 一次可上传多少张
    maxCount: {
      type: Number,
      default: 1,
    },
    //最大多少MB-默认100MB
    maxSize: {
      type: String,
      default: "102400 * 1024",
    },
    // 超过多少MB压缩-默认2MB
    compressFileSize: {
      type: Number,
      default: 102400 * 1024,// 改为100
    },
    // 上传接口
    postVideos: {
      type: Function,
      default: () => {}
    },
    // 删除接口
    deleteVideo: {
      type: Function,
      default: () => {}
    },
    // 上传对象
    videoItem: {
      type: Object,
    },
    // 是否多选 :默认可以多选
    myMultiple: {
      type: Boolean,
      default: false
    },
    // 是否禁用保存问卷
    disabledSaveFalg:{
      type: Object,
    },
  },
  setup(props) {
    const route = useRoute()
    const proxy: any = getCurrentInstance()?.proxy
    //console.log(props.videoItem)
    const fileList = ref([
      //{ url: 'https://img.yzcdn.cn/vant/leaf.jpg' },
    ]); // 回显

    const state = reactive({
      imagePath: [], // 上传视频路径

      //vant
      dataURLtoFile: (dataurl, filename) => {
        // 将base64转换为file文件
        const arr = dataurl.split(",");
        const mime = arr[0].match(/:(.*?);/)[1];
        const bstr = atob(arr[1]);
        let n = bstr.length;
        const u8arr = new Uint8Array(n);
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n);
        }
        return new File([u8arr], filename, { type: mime });
      },
      beforeRead(file) {
        // console.log(file)
        // 上传之前检测图片类似返回true和false会影响到onRead函数
        // const regex = /(.mp4 | .wmv|.asf|.asx | .rm| .rmvb | .3gp | .mov|.m4v | .avi|.dat|.mkv|.flv|.vob)$/;
        let beforeReadFalg = true
        if (file.size > props.compressFileSize){
          Toast("视频大小不可超过100MB");
          beforeReadFalg = false;
        }
        // if (!regex.test(file.type)) {
        //   Toast("视频格式不支持上传");
        //   beforeReadFalg = false;
        // }
        return beforeReadFalg
      },
      onRead: (file) => {
        file.status = 'uploading';
        file.message = '上传中...';
        props.postVideos(file.file,props.videoItem.statementFileId).then((res) => {
          file.status = 'done';
          file.message = '';
          props.videoItem.videoUrl = res.videoUrl
          props.videoItem.videoCoverUrl = res.videoCoverUrl
        }).catch(() => {
          file.status = 'done';
          file.message = '';
          props.videoItem.videoUrl = ''
          props.videoItem.videoCoverUrl = ''
          fileList.value = []
        })
        // state.uploadePrams(file.file)
      },
      // 点击播放视频 previewVideo: (file)
      previewVideo: () => {
        if (props.disabledSaveFalg.disabledSaveFalg) {            
          return
        }
        if (props?.videoItem?.videoCoverUrl && props?.videoItem?.videoUrl) {
          const query = {
            videoItem: JSON.stringify(props.videoItem as string)
          }
          const path = '/videoPlay'
          proxy.$routerGoFun('routerInnPush', path, query, route.path, route.query)
        }
      },
      // 上传接口uploadePrams: (file)
      uploadePrams: () => {
        //   console.log(file)
        //   setTimeout(() => {
        //     file.status = 'done';
        //     file.message = '';
        //   },2000)
          // if (props.visitId) {
          //   props.postImgs(props.visitId, file)
          //   .then(() => {
          //     Toast("上传成功");
          //     if(flag){
          //       props.disabledSaveFalg.disabledSaveFalg = false
          //     }
          //   }).catch(() => {
          //     if(flag){
          //       props.disabledSaveFalg.disabledSaveFalg = false
          //     }
          //   })
          // }
      },

      // beforeDelete 删除前
      beforeDelete: () => {
        if(props.disabledSaveFalg.disabledSaveFalg){              
          return
        }
        Dialog.confirm({
          title: "操作提示",
          message: "是否删除当前视频",
        })
          .then(() => {
            if(props.disabledSaveFalg.disabledSaveFalg){
              Notify({ message: `不好意思，视频还未上传完成哦`, type: "danger" })              
              return
            }
            props.deleteVideo(props.videoItem.statementFileId).then(() => {
              props.videoItem.videoUrl = ''
              props.videoItem.videoCoverUrl = ''
              fileList.value = []
            })
          })
          .catch(() => {
            return false;
          });
      },
      // //删除时
      // deleteImgFun:(deleteItem) => {
      //   console.log(deleteItem)
      //   // props.deleteImg().then((res) => {
      //   //   console.log(res)
      //   // })
      // }
    });

    onMounted(() => { 
      //
    });

    //监听 输入是否有值
    watch(
      props.videoItem,
      (val,newval) => {
        if (props.videoItem && newval) {
          nextTick(() => {
            const propsvideoItem = { ...props.videoItem }
            const { videoUrl, statementFileId, videoCoverUrl } = propsvideoItem
            if (videoCoverUrl && statementFileId && videoCoverUrl) {
              fileList.value = [
                {
                  url: propsvideoItem.videoCoverUrl,
                  statementFileId: propsvideoItem.statementFileId,
                  videoCoverUrl: propsvideoItem.videoCoverUrl,
                  videoUrl: videoUrl
                }
              ]
            }
          });
        }
      },
      {
        immediate: true, // 是否初始化立即执行一次, 默认是false
        deep: true, // 是否是深度监视, 默认是false
      }
    )
    return {
      fileList,
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
:deep(.van-uploader__upload) ,:deep(.van-uploader__preview) {
  width: 0.9rem;
  height: 0.9rem;
}
:deep(.van-uploader__file) {
  width: 0.9rem;
  height: 0.9rem;
}
.upload {
  width: 100%;
  .van-uploader {
    width: 100%;
    :deep(.van-uploader__wrapper) {
      width: 100%;
    }
  }
  .videoCoverUrlImg-body{
    width: 0.9rem;
    height: 0.9rem;
    position: relative;
    .videoCoverUrlImg{
      width: 100%;
      height: 100%;
    }
  }
}
</style>
