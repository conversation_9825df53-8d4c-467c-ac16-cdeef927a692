<template>
  <div
    v-show="verificationRequiredModuleFlag"
    class="w-full absolute overflow-hidden"
    :style="{
      height: outerHeight + 'px',
      left: '0',
      top: '0',
    }"
  >
    <van-nav-bar
      title="逻辑核查"
      left-text=""
      left-arrow
      @click-left="verificationRequiredModuleFlag = false"
    />
    <!-- 内容区 -->
    <div
      :style="{
        height: outerHeight - 96 + 'px',
      }"
      class="bg-F7F7F7 overflow-auto"
    >
      <div
        v-for="(
          item, index
        ) in verificationRequiredModuleObj.verificationRequiredList"
        class="p-01rem bg-white mb-01rem"
        :key="index"
      >
        <div class="ft-015rem">
          <div v-if="item?.fieldLabel" class="flex mb-006-rem">
            <div class="font-color-9A9A9A">题目：</div>
            <div class="font-color-333 flex-1" v-html="item.fieldLabel"></div>
          </div>
          <div class="flex mb-006-rem">
            <div class="font-color-9A9A9A">结果：</div>
            <div class="font-color-333 flex-1">{{ item?.fieldValue }}</div>
          </div>
          <div class="flex mb-006-rem">
            <div class="text-red-400">提示：</div>
            <div class="font-color-333 flex-1">{{ item?.hintText }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="flex">
        <van-button
            v-if="verificationRequiredModuleObj?.type === 2"
            style="height: 50px"
            class="w-full"
            type="danger"
            :loading="submitLoadingFlag"
            loading-text="我确认属实，继续提交"
            @click="saveQuestionnaire(1)"
        >我确认属实，继续提交</van-button>
        <van-button
            style="height: 50px"
            class="w-full"
            type="primary"
            loading-text="去修改"
            @click="verificationRequiredModuleFlag = false"
        >去修改</van-button>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from "vue";

export default defineComponent({
  name: "VerificationRequiredModule", // 逻辑核查
  props: {
    // 逻辑核查
    saveQuestionnaire: {
      type: Function,
      default: () => {},
    },
  },
  setup() {
    const state = reactive({
      outerHeight: document.documentElement.scrollHeight,
      verificationRequiredModuleFlag: false,
      verificationRequiredModuleObj: {
        type: 0,
        verificationRequiredList: [],
      },
      submitLoadingFlag: false,
    });

    setTimeout(() => {
      state.outerHeight = document.documentElement.scrollHeight;
    }, 1000);

    return {
      ...toRefs(state),
    };
  },
});
</script>
