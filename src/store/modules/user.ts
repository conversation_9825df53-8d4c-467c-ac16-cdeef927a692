//import { login, logout, getInfo } from '@/api/user'
//import { getToken, setToken, removeToken } from '@/utils/auth'

// const getDefaultState = () => {
//   return {
//    // token: getToken(),
//     titleName: '欢迎来到主页',
//     avatar: ''
//   }
// }

interface UserIState{
  usertitleName: string,
  avatar: string,
}

const state: UserIState = {
  usertitleName: '',
  avatar: '',
}

const mutations = {
  SET_NAME: (state, usertitleName: string) => {
    state.usertitleName = usertitleName
  },
  SET_AVATAR: (state, avatar: string) => {
    state.avatar = avatar
  },
}

const actions = {
  setUsertitleName: ({ commit }, usertitleName) => {
    commit('SET_NAME', usertitleName)
  },
  setAvatar: ({ commit }, avatar) => {
    commit('SET_AVATAR', avatar)
  },
  //例子:
  // async getCourseByToken(_, reqData) {
  //   let reqType = false;
  //   const resData = await toGetCourseByToken(reqData.tempNumb);
  //   if (resData) {
  //     reqType = true;
  //   }
  //   return { code: reqType, data: resData }
  // },

  // // 用户登录
  // login({ commit }, userInfo) {
  //   const { username, password } = userInfo
  //   let data = { username: username.trim(), password: password }
  //   // 判断是否开启验证码（0：开启；1：关闭）
  //   if (app.state.systemSetting.captchaStatus === '0') {
  //     data.verificationCode = userInfo.verificationCode
  //     data.verificationKey = userInfo.verificationKey
  //   }
  //   return new Promise((resolve, reject) => {
  //     login(data)
  //       .then(response => {
  //         const { data } = response
  //         commit('SET_TOKEN', data.accessToken)
  //         setToken(data.accessToken)
  //         resolve()
  //       })
  //       .catch(error => {
  //         reject(error)
  //       })
  //   })
  // },

  // // 获取用户信息
  // getInfo({ commit, state }) {
  //   return new Promise((resolve, reject) => {
  //     getInfo()
  //       .then(response => {
  //         const { data } = response

  //         if (!data) {
  //           return reject('验证失败，请重新登录')
  //         }
  //         const { realName, avatarUrl } = data
  //         commit('SET_NAME', realName)
  //         commit('SET_AVATAR', avatarUrl)
  //         resolve(data)
  //       })
  //       .catch(error => {
  //         reject(error)
  //       })
  //   })
  // },

  // // user logout
  // logout({ commit, state }) {
  //   return new Promise((resolve, reject) => {
  //     logout(state.token)
  //       .then(() => {
  //         removeToken()
  //         resetRouter()
  //         commit('RESET_STATE')
  //         resolve()
  //       })
  //       .catch(error => {
  //         reject(error)
  //       })
  //   })
  // },

  // // remove token
  // resetToken({ commit }) {
  //   return new Promise(resolve => {
  //     removeToken() // must remove  token  first
  //     commit('RESET_STATE')
  //     resolve()
  //   })
  // }

}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
