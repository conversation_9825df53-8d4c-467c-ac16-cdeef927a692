import { createStore } from 'vuex'
import getters from './getters'
import { IState } from '@/types/storeState'
// 因为我把模块拆分了，但是我又不想每次都导入，就通过这个自动导入modules目录下的模块
// const modulesFiles = require.context('./modules', true, /\.ts$/);
// const modules = modulesFiles.keys().reduce((modules, modulePath) => {
//   const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1')
//   const value = modulesFiles(modulePath)
//   modules[moduleName] = value.default
//   return modules
// }, {})

const state: IState = {
  appKey: '', // appKey
  appToken: '', // appToken
  routerInn: [],
  patientToken: '', // 用户标识
  auditFlag: true, // 是否获取审核状态
  userInformation: {
    token: '',
    expiredTime: '',
    patientStatus: 0,
    patientNum: '',
    avatarUrl: '',
    inGroupDay: '',
    auditComments: '',
    finishLanguage: '',
    icfStatementID: '',
    icfStementId: '',
    isFamilyMember: false,
    memberStatus: 0,
  }, // 用户所有信息
  homeList: {
    dctPatientId: '',
    dctSiteId: '',
    dctStudyId: '',
    displayTraining: false,
    patNumber: '',
    avatarUrl: '',
    memberAvatarUrl: '',
    greeting: '',
    currentStatusText: '',
    patientTrialStatusText: '',
    patientStatus: 0,
    visits: [],
    frequencyFeature: [],
    currentVisitId: '',
    currentVisitText: '',
    totalTask: 0,
    completedTask: 0,
    questionTaskStatus: 0,
    pictureTaskStatus: 0,
    otherTaskStatus: 0,
    visitMeetingList: [],
    dailyFeature: 0,
    inGroup: '',
    inGroupDay: '',
    medicationStatus: 0,
    patientICFStatus: 0,
    patientMenues: [],
    studyName: '',
    unReadChattingRecordsNum: 0,
    needInGroupGuidModal: 0,
    hasInteractiveCommunication: -1
  }, // 首页欢迎语等信息
  taskText: '', // 任务完成语
  getOldQuestDataFlag: 0,
  oldQuestData: {},
  oldQuestDataIndex: 0,
  storePlaceAnOrderObj: {
    expectPickupTimeDisplay: '',
    remark: ''
  },
  patientCode: '',
  continueStatusObj: {}, // 续方状态对象
  // 初始的主问卷数据
  initialQuestionObj: {},
  // 列表相关 暂存用到的
  tableListArr: [],
  // 多列表的数据
  tableListAllList: [],
  initialListArr: [],
  initialAllListArr: [],
  mainQuestIsChange: false,
  // 用于获取问卷数据
  getQuestionnaiDataObj: {},
}
const mutations = {
  SET_APPKEY: (state, appKey) => {
    state.appKey = appKey
  },
  SET_APPTOKEN: (state, appToken) => {
    state.appToken = appToken
  },
  SET_GET_QUESTIONNAI_DATA_OBJ: (state, data) => {
    state.getQuestionnaiDataObj = data
  },
  SET_TABLE_LIST_ARR: (state, data) => {
    state.tableListArr = data
  },
  SET_MAIN_QUEST_IS_CHANGE: (state, data) => {
    state.mainQuestIsChange = data;
  },
  SET_TABLE_LIST_ALL_ARR: (state, data) => {
    state.tableListAllList = data
  },
  SET_INITIAL_LIST_ARR: (state, data) => {
    state.initialListArr = data
  },
  SET_INITIAL_ALL_LIST_ARR: (state, data) => {
    state.initialAllListArr = data
  },
  //
  SET_INITIAL_QUESTIONOBJ: (state, data) => {
    state.initialQuestionObj = data
  },
  // 下单寄送的
  SET_STORE_PLACE_AN_ORDER_OBJ: (state, storePlaceAnOrderObj) => {
    state.storePlaceAnOrderObj = storePlaceAnOrderObj
  },
  SET_ROUTER_INN: (state, routerInn) => {
    state.routerInn = routerInn
  },
  SET_OLD_QUESTDATA_INDEX: (state, oldQuestDataIndex) => {
    state.oldQuestDataIndex = oldQuestDataIndex
  },
  SET_OLD_QUESTDATA: (state, oldQuestData) => {
    state.oldQuestData = oldQuestData
  },
  SET_GET_OLD_QUESTDATA_FLAG: (state, getOldQuestDataFlag) => {
    state.getOldQuestDataFlag = getOldQuestDataFlag
  },
  SET_USERINFORMATION: (state, userInformation) => {
    state.userInformation = userInformation
  },
  SET_TOKEN: (state, patientToken) => {
    state.patientToken = patientToken
  },
  // 首页用户信息
  SET_HOME_LIST: (state, homeList) => {
    state.homeList = homeList
  },
  // 任务完成
  SET_TASKTEXT: (state, taskText) => {
    state.taskText = taskText
  },
  // 是否获取审核状态
  SET_AUDIT_FLAG: (state, auditFlag) => {
    state.auditFlag = auditFlag
  },
  SET_PATIENTCODE: (state, patientCode) => {
    state.patientCode = patientCode
  },
  SET_CONTINUE_STATUS_OBJ: (state, data) => {
    state.continueStatusObj = data
  },
}

const actions = {
  setAppKey({ commit }, appKey) {
    commit('SET_APPKEY', appKey)
  },
  setAppToken({ commit }, appToken) {
    commit('SET_APPTOKEN', appToken)
  },
  setGetQuestionnaiDataObj: ({ commit }, data) => {
    commit('SET_GET_QUESTIONNAI_DATA_OBJ', data)
  },
  setMainQuestIsChange: ({ commit }, data) => {
    commit('SET_MAIN_QUEST_IS_CHANGE', data)
  },
  setTableListArr: ({ commit }, data) => {
    commit('SET_TABLE_LIST_ARR', data)
  },
  setTableListAllList: ({ commit }, data) => {
    commit('SET_TABLE_LIST_ALL_ARR', data)
  },
  setInitialListArr: ({ commit }, data) => {
    commit('SET_INITIAL_LIST_ARR', data)
  },
  setInitialAllListArr: ({ commit }, data) => {
    commit('SET_INITIAL_ALL_LIST_ARR', data)
  },
  //
  setInitialQuestionObj: ({ commit }, data) => {
    commit('SET_INITIAL_QUESTIONOBJ', data)
  },
  setPatientCode({ commit }, patientCode) {
    commit('SET_PATIENTCODE', patientCode)
  },
  setContinueStatusObj({ commit }, data) {
    commit('SET_CONTINUE_STATUS_OBJ', data)
  },
  setRouterInn({ commit }, routerInn) {
    commit('SET_ROUTER_INN', routerInn)
  },
  setToken({ commit }, token) {
    commit('SET_TOKEN', token)
  },
  setOldQuestDataIndex: ({ commit }, oldQuestDataIndex) => {
    commit('SET_OLD_QUESTDATA_INDEX', oldQuestDataIndex)
  },
  setOldQuestData: ({ commit }, oldQuestData) => {
    commit('SET_OLD_QUESTDATA', oldQuestData)
  },
  setGetOldQuestDataFlag: ({ commit }, getOldQuestDataFlag) => {
    commit('SET_GET_OLD_QUESTDATA_FLAG', getOldQuestDataFlag)
  },
  setUserInformation: ({ commit }, userInformation) => {
    commit('SET_USERINFORMATION', userInformation)
  },
  setHomeList: ({ commit }, homeList) => {
    commit('SET_HOME_LIST', homeList)
  },
  /*与研究者端不同的*/
  setStorePlaceAnOrderObj: ({ commit }, storePlaceAnOrderObj) => {
    commit('SET_STORE_PLACE_AN_ORDER_OBJ', storePlaceAnOrderObj)
  },
  setTaskText: ({ commit }, taskText) => {
    commit('SET_TASKTEXT', taskText)
  },
  setAuditFlag: ({ commit }, auditFlag) => {
    commit('SET_AUDIT_FLAG', auditFlag)
  },
}
// 调用createStore
export default createStore({
  getters,
  state,
  mutations,
  actions
})
