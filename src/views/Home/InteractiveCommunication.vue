<template>
  <div 
  class="interactiveCommunication-container"
  @touchstart.stop="() => {
    interactiveCommunicationList.forEach((item) => {
      if (item?.changan) {
        item.changan = false
      }
    })
  }">
    <van-nav-bar
      title="互动沟通"
      left-text=""
      left-arrow
      @click-left="$routerBackFun()"
    />
    <div class="takeMedicineLogList-nav">
      <img src="@/assets/baby/warningIcon.svg" alt="">
      <div>温馨提示：仅限项目相关交流，不涉及问诊及诊疗建议。</div>
    </div>
    <div ref="InteractiveCommunicationRef" class="interactiveCommunication-form scrollnone">
      <div
        v-for="(item, index) in interactiveCommunicationList"
        :key="index"
        class="interactiveCommunication-body"
      >
        <div v-if="item.displayChatTime" v-html="item.displayChatTime" class="displayChatTime"/>
        <!-- 客服回复 -->
        <div v-if="item.identity === 1" class="interactiveCommunication-module">
          <div
            v-if="item?.isWithdraw"
            class="centerflex text-[#9F9F9F]"
          >{{ item?.doctorName }}撤回了一条消息</div>
          <div v-else class="interactiveCommunication-items">
            <!-- 头像 -->
            <div class="doctor-module">
              <img v-if="item?.avatarUrl" class="info-header" :src="item.avatarUrl" alt="" />
              <img v-else src="@/assets/baby/userHeadPortrait.svg" class="info-header" />
              <div class="doctor-name">
                <div class="doctor-name-text">{{ item.doctorName || '' }}</div>
              </div>
            </div>
            <!-- 消息 -->
            <p 
              v-if="item.contentType === 3"
              class="w-[0.78rem] centerflex-h ft-12-rem"
              @click.stop="audioPlayPause(item)"
            >
              <div class="w-[0.17rem] mr-[0.05rem]">
                <div class="w-full overflow-hidden"
                :class="{'playdonghua': item.playdonghua}">
                  <img
                  class="w-[0.17rem] "
                  src="@/assets/baby/home/<USER>" alt="">
                </div>
              </div>
              {{ item?.audioDuration }}
            </p>
            <p v-else-if="item.contentType === 1">{{ item.content }}
            </p>
            <p v-else-if="item.contentType === 2">
              <img style="min-width: 100%"  :src="item.thumbnail" @click="handlePreviewPicture($event)" :data-msg="item.contentUrl"
              @load="loadImg"
              @error="loadImgErr">
            </p>
            <img
              v-if="!item?.converTextFlag && item.contentType === 3"
              class="w-[0.16rem] ml-[0.05rem]"
              src="@/assets/baby/home/<USER>"
              alt=""
              @click="getSpeechRecognitionFun(item)"
            >
          </div>
          
          <!-- 转换文字消息 -->
          <div v-if="item?.converTextFlag && !item?.isWithdraw && item?.content" class="flex">
            <div class="info-header"></div>
            <div class="dptext ft-13-rem">{{ item?.content }}</div>
          </div>
        </div>

        <!-- 我的发送内容 -->
        <div v-if="item.identity === 2" class="mysend-module">
          <div
            v-if="item?.isWithdraw"
            class="centerflex text-[#9F9F9F]"
          >你撤回了一条消息
          <span
            v-if="item.contentType === 1 && item.content && (new Date() - new Date(item.chatTime)) / (1000 * 60) <= 5"
            class="text-blue-300"
            @click="() => {
              sendMessagValue = item.content
            }"
          >&nbsp;重新编辑</span>
        </div>
          <div v-else class="mysend-items">
            <!-- 消息 -->
            <img
              v-if="!item?.converTextFlag && item.contentType === 3"
              class="w-[0.16rem] mr-[0.05rem]"
              src="@/assets/baby/home/<USER>"
              alt=""
              @click="getSpeechRecognitionFun(item)"
            >
            <p
              v-if="item.contentType === 3" 
              class="w-[0.78rem] centerflex-h ft-12-rem"
              v-longpress="() => showWithdrawTip(item)"
              @click.stop="audioPlayPause(item)"
            >
              <!-- contentUrl 播放地址 w-[0.06rem] w-[0.1rem]-->
              <div class="w-[0.17rem] mr-[0.05rem]">
                <div class="w-full overflow-hidden"
                :class="{'playdonghua': item.playdonghua}">
                  <img
                  class="w-[0.17rem] "
                  src="@/assets/baby/home/<USER>" alt="">
                </div>
              </div>
              {{ item?.audioDuration }}
              <div
                v-if="item.changan"
                class="tooltip" 
                :style="tooltipStyle"
                @touchstart.stop="revokeMessageSetItem(item)"
              >
                <img class="w-[0.2rem]" src="@/assets/baby/home/<USER>" alt="">
                <div>撤回</div>
              </div>
            </p>
            <p
             v-else-if="item.contentType === 1" 
             v-longpress="() => showWithdrawTip(item)"
            >{{ item.content }}
              <div
                v-if="item.changan"
                class="tooltip" 
                :style="tooltipStyle"
                @touchstart.stop="revokeMessageSetItem(item)"
              >
                <img class="w-[0.2rem]" src="@/assets/baby/home/<USER>" alt="">
                <div>撤回</div>
              </div>
            </p>
            <p
              v-else-if="item.contentType === 2"
              v-longpress="() => showWithdrawTip(item)"
            >
              <img
                style="min-width: 100%"
                :src="item.thumbnail"
                @click="handlePreviewPicture($event)"
                :data-msg="item.contentUrl"
                @load="loadImg"
                @error="loadImgErr"
              >
              <div
                v-if="item.changan"
                class="tooltip" 
                :style="tooltipStyle"
                @touchstart.stop="revokeMessageSetItem(item)"
              >
                <img class="w-[0.2rem]" src="@/assets/baby/home/<USER>" alt="">
                <div>撤回</div>
              </div>
            </p>
            
            <!-- 头像 -->
            <img v-if="item?.avatarUrl" class="info-header" :src="item.avatarUrl" alt="" />
            <img v-else-if="avatarUrl" class="info-header" :src="avatarUrl" alt="" />
            <img v-else src="@/assets/baby/userHeadPortrait.svg" class="info-header" />
          </div>
          <!-- 转换文字消息 -->
          <div v-if="item?.converTextFlag && !item?.isWithdraw && item?.content" class="flex justify-end">
            <div class="ptext ft-13-rem">{{ item.content }}</div>
            <div class="info-header"></div>
          </div>
        </div>
      </div>
    </div>
    <!-- 底部输入  @focus="sendMessagFocus" @blur="sendMessagBlur"-->
    <div class="sendmessage">
      <van-field
        v-show="!microphoneFlag"
        v-model.trim="sendMessagValue"
        class="sendmessage-input"
        maxlength="200"
        placeholder="请输入要咨询的问题"
      />
      <div
        v-show="microphoneFlag"
        class="w-full h-full centerflex pl-[0.1rem]"
      >
        <div
          class="text-[#333] ft-12-rem w-full h-[0.39rem] centerflex box-border"
          style="border: #E7E7E7 solid 0.5px;border-radius: 0.02rem;"
          @touchstart.stop="recOpen"
          @touchmove.stop="recMove"
          @touchend.stop="recStop"
        >按住说话</div>
      </div>
      <img
        v-show="sendOrAdd && !microphoneFlag"
        class="w-[0.3rem] ml-[0.04rem] mr-[0.1rem]"
        src="@/assets/baby/home/<USER>" alt=""
        @click="microphoneFlag = true"
      />
      <img
        v-show="sendOrAdd && microphoneFlag"
        class="w-[0.3rem] ml-[0.04rem] mr-[0.1rem]"
        src="@/assets/baby/home/<USER>" alt=""
        @click="microphoneFlag = false"
      />
      <van-uploader
        multiple
        :before-read="beforeRead"
        :after-read="afterRead"
        accept="image/*"
        max-size="'10240 * 1024'"
        :max-count="9"
      >
        <img
        v-show="sendOrAdd"
        class="w-[0.3rem] h-[0.3rem] pt-[0.04rem] pr-[0.1rem]"
        src="@/assets/baby/interactiveCommunicationAddIcon.svg" alt="" />
     </van-uploader>
      <p class="w-[0.7rem] text-center" v-show="!sendOrAdd" @click="addMyText">发送</p>
    </div>
    <!-- 语音 -->
    <div
      v-show="startFlag"
      ref="endRef"
      class="guanbiyuyin-icon absolute w-042rem text-white bg-4D4C4C h-042rem text-center"
      style="font-size: 0.35rem;border-radius: 50%;left: 0px;
      bottom: 21%;
      left: 50%;
      z-index: 9999;
      transform: translateX(-50%)
      "
      :class="{ 'bg-color-fff text-4D4C4C': isActive }"
      @click="recStopClick"
    />
    <!-- 自定义弹出窗 -->
    <div 
      v-if="startFlag"
      class="absolute w-100vw h-100vh flex items-end"
      style="background-color: rgba(0,0,0,0.5);
      left: 0px;
      top: 0;
      z-index: 999;"
    >
      <div class="w-full centerflex-w-wrap">
        <div
          class="w-full bg-yuyin"
          style="height: 1.355rem;"
        />
      </div>
    </div>
    <!-- 撤回确认 -->
    <van-action-sheet
      v-model:show="showSheet"
      :actions=" [
        { name: '撤回该条消息？', disabled: true },
        { name: '撤回', color: '#ee0a24' },
      ]"
      @select="(action, index) => {
        if (index === 1) {
          revokeMessage()
        }
      }"
      close-on-click-action
      :close-on-click-overlay="closeOnClickOverlay"
      cancel-text="取消"
    />
  </div>
</template>

<script lang="ts">
import {
  defineComponent, reactive, toRefs,
  onBeforeMount, watch, nextTick, ref,
  onBeforeUnmount
} from "vue";
import { Toast, Notify } from 'vant';
import { useStore } from "vuex";
import {
  getPatientChattingRecords,// 获取聊天记录
  postChatText,
  postChatImage,
  getSpeechRecognition,
  withdrawChatMessage,
  patientAudioChattingAsync,
  // 获取未读
  getNoReadDoctorChattingRecords
} from "@/api/chat"; // postChatAudio,
import { getPatient } from "@/api/home";
// 预览图片
import { ImagePreview } from 'vant';
import { homeListInter } from "@/types/storeState";
import Recorder from "recorder-core";
// 引入mp3格式支持文件；如果需要多个格式支持，把这些格式的编码引擎js文件放到后面统统引入进来即可
import "recorder-core/src/engine/mp3";
import "recorder-core/src/engine/mp3-engine";
// 录制wav格式的用这一句就行
import 'recorder-core/src/engine/wav'
// 可选的插件支持项，这个是波形可视化插件
// import "recorder-core/src/extensions/waveview";
import { HubConnectionBuilder } from "@microsoft/signalr";
import { deepClone } from '@trialdata/common-fun-css/index';

const longpress: Directive = {
  beforeMount: (el, binding) => {
    let pressTimer: any = null;
    const start = (e: any) => {
      if (e.type === 'click' && e.button !== 0) {
        return;
      }
      if (pressTimer === null) {
        pressTimer = setTimeout(() => {
          binding.value(e);
        }, 500);
      }
    };
    const cancel = () => {
      if (pressTimer !== null) {
        clearTimeout(pressTimer);
        pressTimer = null;
      }
    };
    el.addEventListener('mousedown', start);
    el.addEventListener('touchstart', start);
    el.addEventListener('click', cancel);
    el.addEventListener('mouseout', cancel);
    el.addEventListener('touchend', cancel);
    el.addEventListener('touchcancel', cancel);
  },
};

export default defineComponent({
  name: "InteractiveCommunication", // 互动沟通
  directives: {
    longpress
  },
  setup() {
    const store = useStore();
    const { homeList, userInformation } = store.state;
    const InteractiveCommunicationRef = ref(null)
    let tokenInterval: any = null
    // 构建websocket
    let str = location.origin + `/BackDoor/videotalk`;
    if (location.origin.includes("192.168")) {
      str = "https://dct.test.trialdata.cn/BackDoor/videotalk";
    }
    const connection = new HubConnectionBuilder().withUrl(str).build();
    let startConnectTimeout: any = null;
    // 连接服务器
    const startConnect = () => {
      connection
        .start()
        .then(() => {
          connection.invoke("JoinGroup", `IC_${store.state.userInformation.dctPatientId}`)
          // .then(() => {
          //   console.log('成功加入',store.state.userInformation.dctPatientId,);
          // })
        })
        .catch(() => {
          startConnectTimeout = setTimeout(() => {
            startConnect();
          }, 1000);
        });
    };

    // 断开连接
    const stopConnect = () => {
      // connection
      //   .start()
      //   .then(() => {
          
      //   })
      //   .catch((err) => console.error(err.toString()));
      state.oncloseFlag = false
      connection
      .invoke("LeaveGroup",`IC_${store.state.userInformation.dctPatientId}`)
      .then(() => {
        connection
        .stop()
        // .then(() => {
        //   console.log('stop');
        // })
        .catch(() => {});
      })
      .catch((err) => console.error(err.toString()));
    };

    // 接收消息
    connection.on("InteractiveChatReceiveMessage", (message) => {
      // let parseMsg = JSON.parse(message);
      // console.log('InteractiveChatReceiveMessage', message);
      // type: 1, // 1发送 2撤回
      if (message?.type === 2) {
        state.interactiveCommunicationList.map((it => {
          if (it.recordsId === message.recordsId) {
            it.isWithdraw = true
          }
        }))
      } else if (message?.type === 1 && message?.recordsId) { 
        // 判断当前消息是否来自于自己
        // message.sendToken !== store.state.doctorToken
        if (
          message.sendToken !== store.state.patientToken
        ) {
          getNoReadDoctorChattingRecords(
            homeList.dctSiteId,
            { recordsId: message.recordsId }
          )
          .then((res) => {
            // res 和 state.interactiveCommunicationList 通过recordsId来去重下
            // 提取 state.interactiveCommunicationList 中的 recordsId 到 Set
            const existingIds = new Set(state.interactiveCommunicationList.map(item => item.recordsId));
            const filteredRes = res.filter(item => !existingIds.has(item.recordsId));
            // console.log(filteredRes,'filteredRes');
            if (filteredRes.length > 0) {
              filteredRes.map((e) => {
                state.interactiveCommunicationList.push(e)
              })
              // = [...state.interactiveCommunicationList,...filteredRes]
            }
            // state.scrollAuto();
          })
        }
      }
    });

    // 发送消息
    const sendMessage = (entity) => {
      connection
        .invoke(
          "SendMessageToOthersInGroup",
          `IC_${store.state.userInformation.dctPatientId}`,
          entity
        )
        // .then(() => {
        //   console.log("发送成功",store.state.userInformation.dctPatientId);
        // })
        .catch(() => {
          // console.error(err.toString());
        });
    };

    // 断线重连
    connection.onclose(() => {
      if (state.oncloseFlag) {
        setTimeout(() => {
          // console.log('断线重连');
          startConnect();
        }, 1000);
      }
    });
    const state: any = reactive({
      avatarUrl: userInformation?.isFamilyMember ? homeList?.memberAvatarUrl : homeList?.avatarUrl,
      sendOrAdd: true, // 发送键是否展示
      oncloseFlag: true,
      interactiveCommunicationList: [
        // {
        //   recordsId: '',
        //   windowsId: '',
        //   siteId: '',
        //   patientId: '',
        //   patNumber: '',
        //   doctorId: '',
        //   avatarUrl: '',
        //   identity: 0,
        //   chatTime: '',
        //   contentType: 0,
        //   content: '',
        //   contentUrl: ''
        // }
      ],
      sendMessagValue: "", //输入的信息
      myPreview: [], // 预览数组
      loadImgNum: 0, // 总需要加载的图片
      endLoadImgNum: 0,// 总加载完成的图片
      showSheet: false, // 撤回确认框
      microphoneFlag: false, // 录音按钮是否展示
      tooltipStyle: {
        top: '',
        bottom: ''
      },
      // 播放或暂停音频
      audioPlayPause: (item) => {
        // console.log(item,'item.audioElement');
        if (!item.audioElement) {
          item.audioElement = new Audio(item.contentUrl);
          item.audioElement.onerror = () => {
            Toast('音频播放失败');
          };
          // 添加播放结束时的逻辑
          item.audioElement.onended = () => {
            item.playdonghua = false; // 播放自然结束，停止动画
          };
        }
        if (item.audioElement.paused) {
          item.audioElement.play();
          item.playdonghua = true; // 设置播放动画为true
        } else {
          item.audioElement.pause();
          item.audioElement.currentTime = 0; // 重置到开头
          item.playdonghua = false; // 设置播放动画为false
        }
      },
      // 展示撤回
      showTooltip: async() => {
        await nextTick();
        const tooltip = document.querySelector('.tooltip');
        // const messageEl = document.querySelector('.interactiveCommunication-form');
        const viewportHeight = window.innerHeight;
        const rect = tooltip.getBoundingClientRect();
        // const tooltipHeight = tooltip.offsetHeight;
        const tooltipTop = rect.top;
        // console.log(rect,tooltipTop > 140,tooltipTop ,viewportHeight,'tooltipTop < viewportHeight');
        // state.sendMessagValue= 'viewportHeight'+viewportHeight// 'tooltipTop'+tooltipTop
        const isInUpperHalf = tooltipTop > 150 || tooltipTop > viewportHeight / 2;
        let top = '';
        let bottom = '';
        // console.log(isInUpperHalf);
        
        if (isInUpperHalf) {
          // 如果在视口上半边，显示在下方
          state.tooltipStyle.top = '-0.6rem';
          state.tooltipStyle.bottom = ``;
        } else {
          // 否则显示在上方
          state.tooltipStyle.top = ''
          state.tooltipStyle.bottom = `-0.6rem`;
        }
        // isTooltipVisible.value = true;
      },
      // 获取语音识别文字
      getSpeechRecognitionFun: (item) => {
        if (!item?.speechRecognitionLoadingFlag) {
          item.speechRecognitionLoadingFlag = true
          getSpeechRecognition(item?.recordsId).then((res: any) => {
            if (res) {
              item.content = res
              item.converTextFlag = true
            } else {
              Toast('未识别到文字')
            }
            item.speechRecognitionLoadingFlag = false
          }).catch(() => {
            item.speechRecognitionLoadingFlag = false
          })
        }
      },
      revokeMessageItem: {}, // 撤回的item
      closeOnClickOverlay: false,
      revokeMessageSetItem: (item) => {
        state.closeOnClickOverlay = false
        item.changan = false
        state.revokeMessageItem = deepClone(item)
        state.showSheet = true
        setTimeout(() => {
          state.closeOnClickOverlay = true
        }, 300);
      },
      // 撤回消息
      revokeMessage: () => {
        // 你撤回了一条消息recordsId
        state.interactiveCommunicationList.some((e) => {
          if (e.recordsId && e.recordsId === state.revokeMessageItem.recordsId ){
            e.isWithdraw = true
            return true
          }
        })
        // 从数组里移除item
        // const index = state.interactiveCommunicationList.findIndex((ele: any) => ele.recordsId === item.recordsId);
        // state.interactiveCommunicationList.splice(index, 1);
        withdrawChatMessage(state.revokeMessageItem?.recordsId).then((res: any) => {
          // 发送长连接
          sendMessage({
            type: 2, // 1发送 2撤回
            recordsId: state.revokeMessageItem?.recordsId
          })
        }).catch(() => {
         // 撤回失败
         item.isWithdraw = false
        })
      },
      // 显示撤回提示框
      showWithdrawTip: (e) => {
        // console.log(e);
        const chatTime = new Date(e.chatTime);
        const currentTime = new Date();
        const timeDifference = currentTime - chatTime;
        const timeDifferenceInMinutes = timeDifference / (1000 * 60);
        // 超过2分钟不允许撤回
        if (timeDifferenceInMinutes > 2) {
          return
        }
        e.changan = true
        state.showTooltip()
      },
      // 打开语音
      startFlag: false,
      endRef: null,
      // x移入时
      isActive: false,
      // 测试录音功能
      cslyFlag: false,
      text: "",
      hasPermissionText: "",
      moveText: "",
      // 录音相关
      recwave: null,
      rec: null,
      wave: null,
      recCleRecorderEndFlag: 0,
      // 是否结束
      isEnd: true,
      // 是否授权
      isAuthorization: false,
      // 移入
      recMove: (e) => {
        const clientX = e?.changedTouches[0]?.clientX
        const clientY = e?.changedTouches[0]?.clientY
        const x = window.outerWidth / 2
        const y = window.outerHeight - window.outerHeight * 0.21
        const distanceFromCenterX = Math.abs(clientX - x);
        const distanceFromCenterY = clientY - y;
        // console.log(x , distanceFromCenterX, y, clientY, distanceFromCenterY);
        if (distanceFromCenterX <= 25 && distanceFromCenterY <= 0  ) {
          state.isActive = true
        } else {
          state.isActive = false
        }
      },
      // 创建录音对象
      recCleRecorder: () => {
        /*
        type: 'wav', // 设置音频格式为 WAV
        bitRate: 1411.2, // 设置比特率为 1411.2 kbps（CD 音质）
        sampleRate: 44100, // 设置采样率为 44100 Hz
        bitDepth: 16, // 设置位深度为 16 bit
        channels: 2, // 设置声道为立体声
        */
        state.rec = Recorder({
          type: 'wav', // 设置音频格式为 WAV
          bitDepth: 16, // 设置位深度为 16 bit
          channels: 2, // 设置声道为立体声
          // type: "mp3", // 录音格式，可以换成wav等其他格式
          sampleRate: 16000, // 录音的采样率，越大细节越丰富越细腻
          bitRate: 16, // 录音的比特率，越大音质越好
          onProcess: (
            buffers,
            powerLevel,
            bufferDuration,
            bufferSampleRate,
            newBufferIdx,
            asyncEnd
          ) => {
            if (bufferDuration && bufferDuration / 1000 >= 59) {
              state.recStop();
              // state.text = "录音时长"+ bufferDuration;
            }
            //录音实时回调，大约1秒调用12次本回调
            //可实时绘制波形，实时上传（发送）数据
            if (state.wave)
              state.wave.input(
                buffers[buffers.length - 1],
                powerLevel,
                bufferSampleRate
              );
          },
        });
      },
      recCle: () => {
        state.rec.open(
          () => {
            if (state.recwave) {
              // 创建音频可视化图形绘制对象
              state.wave = Recorder.WaveView({ elem: state.recwave });
            }
            // state.hasPermissionText += '创建'
            if (state.isAuthorization) {
              state.recStart();
            } else {
              state.isAuthorization = true
            }
          },
          (msg, isUserNotAllow) => {
            // 用户拒绝了录音权限，或者浏览器不支持录音
            // state.hasPermissionText += '-拒绝了录音权限'
            // state.isAuthorization = false
          }
        );
      },
      isAuthorizationNum: 0,
      // 按下提问
      recOpen: () => {
        if (!state.isAuthorization && navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
          navigator.mediaDevices
            .getUserMedia({ audio: true })
            .then((stream) => {
              state.isAuthorization = true
              // Toast('isAuthorization true')
              // console.log('state.isAuthorization true',state.isAuthorization);
            })
            .catch((error) => {
              state.isAuthorization = false
              // Toast('isAuthorization false')
              state.isAuthorizationNum += 1
              const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
              if (!isIOS && state.isAuthorizationNum > 1) {
                Toast('您已拒绝麦克风权限,请点击左上角关闭后重新打开')
              }
              // console.log('state.isAuthorization false',state.isAuthorization);
            });
        }
        if (state.isAuthorization && state.isEnd) {
          // iOS 对于 Web Audio 的使用有严格的限制，例如需要用户交互（如点击事件）才能激活音频上下文
          // 确保在生成语音之前已经通过用户交互事件（如点击按钮）初始化了音频上下文。
          const audioContext = new (window.AudioContext || window.webkitAudioContext)();
          if (audioContext.state === 'suspended') {
            audioContext.resume();
          }
          state.isEnd = false
          state.recCle()
        }
        // 判断是否有权限
        // if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        //   navigator.mediaDevices
        //     .getUserMedia({ audio: true })
        //     .then((stream) => {
        //       state.hasPermissionText += '-有权限'
        //       // state.recStart();
        //       // 授权和开启录音要一起执行
        //       setTimeout(() => {
        //         state.recStart();
        //       }, 1000);
        //     })
        //     .catch((error) => {
        //       // 打开录音，获得权限
        //       state.recCle()
        //       state.hasPermissionText += '无权限'
        //       // console.error("获取麦克风权限失败:", error);
        //     });
        // }
      },
      // 开始录音
      recStart: () => {
        if (!state.rec) {
          // state.hasPermissionText += '-未打开录音'
          return;
        }
        if (state.rec.start) {
          state.rec.start();
          state.startFlag = true
          // state.hasPermissionText = "已开始录音" // +state.rec.start;
        }
        //  else {
        //   state.hasPermissionText += '-打失败开录音'+state.rec
        // }
      },
      recStopClick: () => {
        state.startFlag = false
        if (!state.rec) {
          // console.error("未打开录音");
          state.isEnd = true
          return;
        }
        state.isActive = true
        state.rec.stop(
          (blob, duration) => {
            // 简单利用URL生成本地文件地址，此地址只能本地使用，比如赋值给audio.src进行播放，赋值给a.href然后a.click()进行下载（a需提供download="xxx.mp3"属性）
            // var localUrl = (window.URL || webkitURL).createObjectURL(blob);
            // console.log("录音成功", blob, localUrl, "时长:" + duration + "ms");
            state.rec.close(); // 关闭录音，释放录音资源，当然可以不释放，后面可以连续调用start
            state.rec = null;
            state.recCleRecorder();
            state.isActive = false
            state.isEnd = true
          },
          (err) => {
            // console.error("结束录音出错：" + err);
            state.rec.close(); //关闭录音，释放录音资源，当然可以不释放，后面可以连续调用start
            state.rec = null;
            state.recCleRecorder();
            state.isEnd = true
            state.isActive = false
          }
        );
      },
      // 停止录音
      recStop: (e) => {
        try {
        state.startFlag = false
        let clientX = e?.changedTouches[0]?.clientX
        let clientY = e?.changedTouches[0]?.clientY
        let x = window.outerWidth / 2
        let y = window.outerHeight - window.outerHeight * 0.21
        let distanceFromCenterX = Math.abs(clientX - x);
        let distanceFromCenterY = clientY - y;
        // x , distanceFromCenterX, 
        // console.log(y, clientY, distanceFromCenterY);
        if (!state.rec) {
          // console.error("未打开录音");
          state.isEnd = true
          return;
        }
        // state.text = "松开2";
        state.rec.stop(
          (blob, duration) => {
            // 简单利用URL生成本地文件地址，此地址只能本地使用，比如赋值给audio.src进行播放，赋值给a.href然后a.click()进行下载（a需提供download="xxx.mp3"属性）
            // var localUrl = (window.URL || webkitURL).createObjectURL(blob);
            // console.log("录音成功", blob, localUrl, "时长:" + duration + "ms");
            state.rec.close(); // 关闭录音，释放录音资源，当然可以不释放，后面可以连续调用start
            state.rec = null;
            state.recCleRecorder();
            if (distanceFromCenterX <= 25 && distanceFromCenterY <= 0) {
              state.isActive = false
              state.isEnd = true
              return
            }
            // state.text = "录音时长" + duration;
            const intDuration = duration / 1000
            if (intDuration < 60 && intDuration >= 1) {
              // blob就是我们要的录音文件对象，可以上传，或者本地播放
              state.recBlob = blob;
              state.upload(blob,intDuration); // 把blob文件上传到服务器
            } else if (intDuration < 1) {
              Toast.fail("录音时间太短");
            }
            state.isEnd = true
          },
          (err) => {
            // console.error("结束录音出错：" + err);
            state.rec.close(); //关闭录音，释放录音资源，当然可以不释放，后面可以连续调用start
            state.rec = null;
            state.recCleRecorder();
            state.isEnd = true
          }
        );
      } catch (error) {
          console.log(error,'error');
        }
      },
      // 上传
      upload: (blob, intDuration) => {
        // state.moveText += '1upload1'
        patientAudioChattingAsync(
          homeList?.dctSiteId || store.state.userInformation.dctSiteId,
          parseInt(intDuration),
          blob,
        )
        .then((res) => {
          state.interactiveCommunicationList.push(res);
          state.scrollAuto();
          // 发送长连接 通知接收消息
          sendMessage({
            type: 1, // 1发送 2撤回
            recordsId: res?.recordsId,
            sendToken: store.state.patientToken
          })
        })
      },
      // 图片加载完成
      loadImg: () => {
        state.endLoadImgNum++
        if (state.loadImgNum === state.endLoadImgNum)
        state.scrollAuto()
      },
      loadImgErr: () => {
        state.endLoadImgNum++
      },
      // 聊天内容一直滚动到最底部
      scrollAuto: () => {
        nextTick(() => {
          setTimeout(() => {
          // 滚动到底部
          const scrollerElement: any = InteractiveCommunicationRef.value;
          // console.log(scrollerElement);
          const nDivHeight = scrollerElement.clientHeight;
          const nScrollHeight = scrollerElement.scrollHeight;
          scrollerElement.scrollTop = nScrollHeight - nDivHeight;
          }, 500);
        })
      },
      dataURLtoFile: (dataurl, filename) => {
        // 将base64转换为file文件
        const arr = dataurl.split(",");
        const mime = arr[0].match(/:(.*?);/)[1];
        const bstr = atob(arr[1]);
        let n = bstr.length;
        const u8arr = new Uint8Array(n);
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n);
        }
        return new File([u8arr], filename, { type: mime });
      },
      beforeRead: (file) => {
        // 上传之前检测图片类似返回true和false会影响到onRead函数
        const regex = /(.jpg|.jpeg|.png|.bmp|.gif|.jfif)$/;
        let beforeReadFlag = true
        if (Array.isArray(file)) {
          file.forEach((item) => {
            if (!regex.test(item.type)) {
              Toast("图片格式不支持上传");
              beforeReadFlag = false;
            }
          })
          if (file.length > 9) {
            Notify({ type: 'warning', message: `一次最多只能上传9张图片` });
          }
        } else {
          if (!regex.test(file.type)) {
            Toast("图片格式不支持上传");
            beforeReadFlag = false;
          }
        }
        return beforeReadFlag
      },
      afterRead: (file) => {
        if (Array.isArray(file)) {
          file.map((item, idx) => {
            // if (item.file.size > (1048576 * 100)){
            //   state.myCompress(item)
            // }   
            state.sendImage(item.file, idx)
          })
        } else if (file.file.size > (1048576 * 100)) {
          state.myCompress(file)
        } else {
          state.sendImage(file.file)
        }
      },
      // 压缩
      myCompress: (file) => {
        const canvas = document.createElement("canvas"); // 创建Canvas对象(画布)
        const context: any = canvas.getContext("2d");
        const img = new Image();
        img.src = file.content; // 指定图片的DataURL(图片的base64编码数据)
        img.onload = () => {
          canvas.width = 400;
          canvas.height = 300;
          context.drawImage(img, 0, 0, 400, 300);
          file.content = canvas.toDataURL(file.file.type, 0.92); // 0.92为默认压缩质量
          const files = state.dataURLtoFile(file.content, file.file.name);
          state.sendImage(files)
        };
      },
      // 上传接口调用
      sendImage: (files, idx) => {
        // const communicationList = state.interactiveCommunicationList;
        postChatImage(homeList.dctSiteId,
        !idx,
        files).then((res: any) => {  
          const item = {
            ...res,
            identity: 2, // 2患者, 1自己
            contentType: 2, // 图片类型
            contentUrl: res.contentUrl, // 原图片地址
            thumbnail: res.thumbnail || res.contentUrl, // 缩略图
          }   
          state.interactiveCommunicationList.push(item)
          state.loadImgNum++
          state.scrollAuto();
          sendMessage({
            type: 1, // 1发送 2撤回
            recordsId: res?.recordsId,
            sendToken: store.state.patientToken
          })
        })
      },
      // 图片预览
      handlePreviewPicture: (e) => {
        const msg = e.target.dataset.msg;
        const myPreview: string[] = []
        if (state?.interactiveCommunicationList?.length) {
          state.interactiveCommunicationList.forEach((el) => {
            const item = el
            if (!item?.isWithdraw && item.contentUrl && item?.contentType === 2) {
              myPreview.push(item.contentUrl)
            }
          })
        }        
        if (state.interactiveCommunicationList?.length && myPreview?.length) {
          state.myPreview = [...myPreview]
          myPreview.forEach((item,index) => {
            if (item === msg) {
              setTimeout(() => {
                ImagePreview({
                  images: state.myPreview,
                  startPosition: index,
                  closeable: true
                });
              },500)
              return
            }
          })
        }
      },
      // 发送消息text
      addMyText: () => {
        if (state.sendMessagValue) {
          postChatText(
            homeList.dctSiteId,
            {
              "contentType": 1, //文本
              "content": state.sendMessagValue
            }
          ).then((res) => {
            state.interactiveCommunicationList.push(res);
            state.sendMessagValue = "";
            state.scrollAuto();
            sendMessage({
              type: 1, // 1发送 2撤回
              recordsId: res?.recordsId,
              sendToken: store.state.patientToken
            })
          });
        }
      },
      // 获取数据
      getPatientChattingRecord: (dctSiteId) => {
        if (store.state?.patientToken) {
          // 1,20 // 分页参数
          getPatientChattingRecords(dctSiteId,1,999)
          .then((res: any) => {
            if (res?.items && Array.isArray(res?.items)) {
              state.interactiveCommunicationList = res.items
              if (state.interactiveCommunicationList.length) {
                state.interactiveCommunicationList.forEach(item => {
                  if (item.contentType === 2) {
                    state.loadImgNum++
                  }
                });
                nextTick(() => {
                  state.scrollAuto();
                })
              }
            }
            startConnect();
          /*
            chatId	string :聊天记录的Id
            chatTime	string($date-time):聊天发生时间 具体到秒 如: 2020-12-07 12:15:13
            identity	number问题的方向 0 = 未知, 1 = 自己, 2 = 对方
            contentType 聊天问题 0 = 未知, 1 = 文本, 2 = 图片, 3 = 语音
            content	string: 我家宝宝最好了 如果聊天内容是文本, 则内容在这里
            contentUrl	string : http://abc.com/abc 如果聊天内容为图片或者语音, 则url在这里
            */
          });
        }
      }
    });
    // 监听 输入是否有值
    watch(
      state,
      () => {
        state.sendOrAdd = !state.sendMessagValue;
      },
      {
        immediate: true, // 是否初始化立即执行一次, 默认是false
        deep: true, // 是否是深度监视, 默认是false
      }
    );

    onBeforeMount(() => {
      let tokenFlag = true;
      tokenInterval = setInterval(async () => {
        const token = sessionStorage.getItem("patientToken");
        if (token && token !== "null" && token !== "undefined" && tokenFlag) {
          tokenFlag = false;
          if (homeList?.dctSiteId) {       
            state.getPatientChattingRecord(homeList.dctSiteId)
          } else {
            getPatient().then((res) => {
              const rest = res as homeListInter
              homeList.dctSiteId = rest.dctSiteId
              state.getPatientChattingRecord(rest.dctSiteId)
            });
          }
          clearInterval(tokenInterval);
        }
      }, 200);
      // 语音相关
      if (sessionStorage.getItem('isAuthorization')) {
        state.isAuthorization = true
      }
      // 禁用复制选中
      document.body.style.webkitUserSelect = "none";
      document.body.style.userSelect = "none";
      document.body.style.overscrollBehavior = 'none';
      document.body.addEventListener('scroll', function (event) {
        event._isScroller = true;
      }, { passive: true }
      );
      document.body.style.overflow = 'hidden';
      // 锁定页面当前位置
      document.body.style.position = 'fixed';
      document.body.style.width = '100%';
      state.recCleRecorder();
    });
    onBeforeUnmount(() => {
      clearInterval(startConnectTimeout);
      sessionStorage.setItem('isAuthorization', state.isAuthorization);
      stopConnect();
      clearInterval(tokenInterval);
    });
    return {
      InteractiveCommunicationRef,
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.playdonghua {
  animation: waveAnimation 1s infinite ease-in-out;
  // img {
  //   width: 100%;
  // }
}
@keyframes waveAnimation {
  0% {
    width: 0.06rem;
  }
  50% {
    width: 0.1rem;
  }
  100% {
    width: 100%;
  }
}
:deep(.van-popup--bottom) {
  min-height: 1.58rem !important;
}
.bg-yuyin {
  background: url('@/assets/baby/welcomeToJoinUs/yuyin.svg') no-repeat;
  background-size: 100% 100%;
}
.guanbiyuyin-icon::after {
  content: 'x';
  position: relative;
  top: -0.02rem;
}
.interactiveCommunication-container {
  height: 100vh;
  overflow: hidden;
  background: #F5F5F5;
  color: #333;
  font-size: 0.14rem;
  .interactiveCommunication-form {
    height: calc(100vh - 1.62rem);
    overflow: auto;
    .interactiveCommunication-body {
      width: 100%;
      padding: 0.2rem 0.1rem;
      box-sizing: border-box;
      .displayChatTime {
        width: 100%;
        text-align: center;
        color: #9F9F9F;
        margin: 0 0 0.1rem 0;
      }
      // 通用
      .info-header {
        width: 0.42rem;
        height: 0.42rem;
        border-radius: 50%;
        margin: 0 0.1rem 0 0;
      }
      .tooltip {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        width: 0.4rem;
        height: 0.4rem;
        background: #000;
        color: #fff;
        padding: 5px;
        border-radius: 5px;
        z-index: 100000000;
      }
      //
      .interactiveCommunication-module {
        width: 100%;
        box-sizing: border-box;
        .dptext {
            // width: 2.43rem;
            max-width: 2.43rem;
            padding: 0.1rem;
            box-sizing: border-box;
            border-radius: 0 0.1rem 0.1rem 0.1rem;
            word-break: break-all;
            word-wrap: break-word;
            background: #fff;
          }
        .interactiveCommunication-items {
          display: flex;
          // align-items: center;
          margin: 0 0 0.1rem 0;
          .doctor-module{
            width: 0.52rem;
            position: relative;
            .doctor-name{
              position: absolute;
              left: -0.08rem;
              .doctor-name-text{
                width: 0.55rem;
                color: #9F9F9F;
                text-align: center;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }
          }
          p {
            // width: 2.43rem;
            position: relative;
            max-width: 2.43rem;
            padding: 0.1rem;
            box-sizing: border-box;
            border-radius: 0 0.1rem 0.1rem 0.1rem;
            word-break: break-all;
            word-wrap: break-word;
            background: #fff;
          }
        }
      }
      //我的
      .mysend-module {
        width: 100%;
        box-sizing: border-box;
        .ptext {
            // width: 2.43rem;
            max-width: 2.43rem;
            padding: 0.1rem;
            box-sizing: border-box;
            border-radius: 0.1rem 0 0.1rem 0.1rem;
            word-break: break-all;
            word-wrap: break-word;
            color: #333;
            background: white;
          }
          .info-header {
            width: 0.42rem;
            height: 0.42rem;
            border-radius: 50%;
            margin: 0 0 0 0.1rem;
          }
        .mysend-items {
          display: flex;
          justify-content: flex-end;
          // align-items: center;
          margin: 0 0 0.1rem 0;
          p {
            position: relative;
            // width: 2.43rem;
            max-width: 2.43rem;
            padding: 0.1rem;
            box-sizing: border-box;
            border-radius: 0.1rem 0 0.1rem 0.1rem;
            word-break: break-all;
            word-wrap: break-word;
            color: #fff;
            background-image: linear-gradient(180deg, #3fa1fc, #5860da);
          }
        }
      }
    }
  }
  /*发送消息*/
  .sendmessage {
    width: 100%;
    height: 0.6rem;
    padding-bottom: 0.1rem;
    position: fixed;
    background: #fff;
    left: 0;
    bottom: 0rem;
    display: flex;
    // justify-content: space-around;
    align-items: center;
    .sendmessage-input {
      flex: -1;
      margin: 0 0 0 0.1rem;
      padding: 0.08rem 0.14rem;
      border-radius: 0.04rem;
      background: #F7F8FA;
    }
  }
}
</style>
