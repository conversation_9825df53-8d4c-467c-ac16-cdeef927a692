<template>
  <div class="maintained-container">
    <div class="ft-18-rem centerflex-wrap">维护公告</div>
    <div class="centerflex-wrap maintained-hurdle"/>
    <img src="@/assets/baby/home/<USER>" class="w-full" alt="" />
    <div class="maintained-texts whitespace-pre-wrap" v-html="maintainedHtml" />
  </div>
</template>

<script setup lang='ts'>
import { maintainedFun } from "@/utils/debounce";
import { onBeforeMount, ref } from "vue";

const maintainedHtml = ref('');
const getMaintainedHtml = async() => {
  try {
    const notice = await maintainedFun('/maintained')
    if (notice)
    maintainedHtml.value = notice?.maintainedHtml || ''
  } 
  catch (e) { console.log(e) } 
}
onBeforeMount(() => {
  getMaintainedHtml()
})
</script>

<style lang='less' scoped>
.maintained-container {
  padding: 20vh 0.1rem 0;
  color: #333;
  font-size: 0.15rem;
  overflow: auto;
  .maintained-hurdle:after {
    content: '';
    width: 0.37rem;
    height: 0.03rem;
    margin: 0.04rem 0 0.1rem 0;
    border-radius: 0.03rem;
    background: linear-gradient(#518bee 100%, #616dde 100%);
  }
  .maintained-texts {
    margin: 0.1rem 0 0 0;
  }
}
</style>