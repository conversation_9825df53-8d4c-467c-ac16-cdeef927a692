<template>
  <div class="followReminder">
    <van-nav-bar title="随访提醒" left-arrow @click-left="$routerBackFun()" />
    <div
      v-if="visitPlanObj.meetingStatus === 2"
      class="followReminder-take centerflex"
    >
      已接受本次随访
    </div>
    <div class="followReminder-date-module">
      <div class="flex justify-between">
        <div>{{visitPlanObj?.visitName || ''}}</div>
        <div v-if="visitPlanObj?.finishStatus === 3" class="followReminder-date-status">进行中</div>
        <div v-else-if="visitPlanObj?.finishStatus === 2" class="followReminder-date-status">已完成</div>
        <div v-else-if="visitPlanObj?.finishStatus === 1" class="followReminder-date-status-no">未完成</div>
        <div v-else-if="visitPlanObj?.finishStatus === 4" class="followReminder-date-status-no">待开始</div>
      </div>
      <div class="followReminder-date-start-stop">
        <div class="followReminder-date-start-stop-text">
          {{ visitPlanObj?.currentVisitText || '' }}
        </div>
      </div>
      <div>
        <div
          v-if="visitPlanObj?.visitRemindType"
          class="follow-hospital centerflex"
        >
          {{ visitPlanObj?.visitRemindType === 1 ? '到院' : '电话' }}随访
        </div>
      </div>
      <div class="followReminder-follow-date">
        <div>预约日期</div>
        <div>{{ visitPlanObj?.dateWeekText || '' }}</div>
      </div>
      <div class="mybr" />
      <div class="followReminder-follow-date">
        <div>预约时间</div>
        <div v-if="visitPlanObj?.timeText" v-html="visitPlanObj.timeText"/>
        <div v-else class="followReminder-follow-date-hint">
          可提前与研究人员预约
        </div>
      </div>
      <div class="mybr" />
    </div>
    <div class="remind-content">
      <div class="remind-content-title">提醒内容</div>
      <div v-if="visitPlanObj?.remindContent" v-html="visitPlanObj.remindContent" class="remind-content-body" />
    </div>
    <div
      v-if="visitPlanObj?.meetingStatus === 1 && visitPlanObj?.dateWeekText"
      class="followReminder-save-btn centerflex"
    >
      <van-button
        class="save-follow-btn"
        round
        type="primary"
        @click="saveFollow()"
        >接受本次随访</van-button
      >
    </div>
  </div>
</template>

<script lang='ts'>
import {
  defineComponent,
  onMounted,
  reactive,
  toRefs,
  getCurrentInstance,
} from "vue"
import { useRoute } from "vue-router"
import { getVisitPlan, postVisitPlanNotice } from '@/api/home'
import { Notify } from 'vant'
import { followReminderInter } from "@/types/home";
import { getVisitPlanInter } from '@/types/home';

export default defineComponent({
  name: "FollowReminder", // 随访提醒
  setup() {
    const route = useRoute()
    const proxy: any = getCurrentInstance()?.proxy
    const state: followReminderInter = reactive({
      visitPlanObj: {
        patientId: '',
        groupStatus: 0,
        patientStatus: 0,
        patientNo: '',
        avatarUrl: '',
        sex: 0,
        whetherToLayer: 0,
        group: '',
        statusText: '',
        inGroup: '',
        visitId: '',
        visitName: '',
        visitRemindType: 0,
        meetingDateTime: '',
        meetingStatus: 0,
        remindContent: '',
        currentVisitText: '',
        timeText: '',
        dateWeekText: '',
        finishStatus: 0,
        expectedTime: '',
        actualMeetingDateTime: '',
        visitBaseline: 0,
        doctorEditable: 0,
        hasExpectedDay: true
      },
      saveFollow: () => {
        if (route.query?.visitId) {
          postVisitPlanNotice(route.query.visitId)
            .then(() => {
              state.visitPlanObj.meetingStatus = 2
              Notify({ message: "成功接受本次随访", type: "success" })
              proxy.$routerBackFun()
            })
        }
      },
      onLoad: () => {
        if (route.query?.visitId) {
          getVisitPlan(route.query.visitId)
            .then((res) => {
              state.visitPlanObj = res as getVisitPlanInter
            })
        }
      },
    });

    onMounted(() => {
      state.onLoad()
    });

    return {
      ...toRefs(state),
    };
  },
});
</script>

<style lang='less' scoped>
.followReminder {
  height: 100vh;
  color: #333;
  font-size: 0.13rem;
  background: #f7f7f7;
  overflow: auto;
  .followReminder-take {
    width: 100%;
    height: 0.46rem;
    margin: 0 0 0.2rem 0;
    font-size: 0.15rem;
    color: #fff;
    background: #41b592;
  }
  .followReminder-date-module {
    width: 94%;
    margin: 0.25rem auto 0.2rem;
    padding: 0.14rem;
    box-sizing: border-box;
    border-radius: 0.06rem;
    background: #fff;
    .mybr {
      width: 100%;
      height: 0.5px;
      background: #eaeaea;
    }
    .followReminder-date-start-stop {
      margin: 0.06rem 0;
      .followReminder-date-start-stop-text {
        font-size: 0.12rem;
        color: #9a9a9a;
      }
    }
    .followReminder-date-status {
      color: #41b592;
    }
    .followReminder-date-status-no {
      color: #e26375;
    }
    .follow-hospital {
      width: 0.55rem;
      height: 0.19rem;
      font-size: 0.1rem;
      color: #5860da;
      border: #5860da 0.5px solid;
      border-radius: 0.02rem;
      background: #f2f3fa;
    }
    .follow-no-feedback {
      width: 0.45rem;
      height: 0.19rem;
      margin-left: 0.1rem;
      font-size: 0.1rem;
      color: #e26375;
      border: #e26375 0.5px solid;
      border-radius: 0.02rem;
      background: #ffedf0;
    }
    .follow-accepted {
      width: 0.45rem;
      height: 0.19rem;
      margin-left: 0.1rem;
      font-size: 0.1rem;
      color: #41b592;
      border: #41b592 0.5px solid;
      border-radius: 0.02rem;
      background: #effffb;
    }
    .followReminder-follow-date {
      margin: 0.17rem 0 0.08rem 0;
      display: flex;
      justify-content: space-between;
      .followReminder-follow-date-hint {
        color: #c3c3c3;
      }
    }
  }
  .remind-content {
    width: 94%;
    padding: 0.12rem;
    box-sizing: border-box;
    margin: 0.1rem auto 0.8rem;
    border-radius: 0.06rem;
    background: #fff;
    .remind-content-title {
      margin: 0 0 0.08rem 0;
    }
  }
  .followReminder-save-btn {
    width: 100%;
    position: fixed;
    bottom: 0.26rem;
    .save-follow-btn {
      width: 3.2rem;
      height: 0.455rem;
    }
  }
}
</style>