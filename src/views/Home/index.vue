<template>
  <div class="home-container">
    <van-nav-bar v-if="appToken == ''" title="首页" />
    <van-nav-bar v-else title="首页">
      <template #right>
        <van-icon
          name="https://dct-app.oss-cn-hangzhou.aliyuncs.com/icons/msg_1x.png"
          :badge="unreadMsgCount"
          size=".18rem"
          @click="openMsgDialog"
        />
      </template>
      <template #left>
        <van-icon
          name="https://dct-app.oss-cn-hangzhou.aliyuncs.com/icons/slide_1x.png"
          size=".18rem"
          @click="openLeftSlideDialog"
        />
      </template>
    </van-nav-bar>
    <div class="home-body scrollnone">
      <div class="home-push-message">
        <div class="home-message-body">
          <img
            v-if="homeList.avatarUrl"
            :src="homeList.avatarUrl"
            class="home-message-img"
            alt=""
          />
          <img
            v-else
            src="@/assets/baby/userHeadPortrait.svg"
            class="home-message-img"
            alt=""
          />
          <div class="home-message-detail">
            <h4>{{ homeList?.patNumber || "" }}</h4>
            <p
              v-if="homeList?.patientTrialStatusText"
              v-html="homeList?.patientTrialStatusText"
              :class="{ 'text-gray-400': homeList?.patientStatus !== 10 }"
            />
          </div>
        </div>
        <div
          v-for="(item, index) in homeList.visitMeetingList"
          :key="index"
          class="next-time-module centerflex-h-wrap"
          @click="routerGo('/followReminder', item.visitID)"
        >
          <div class="next-time-left">
            <div
              v-if="item.visitRemindType === 1 || item.visitRemindType === 2"
              class="next-time-left-phone centerflex-h"
            >
              <img
                v-if="item.visitRemindType === 2"
                src="@/assets/baby/home/<USER>"
                alt=""
              />
              <img v-else src="@/assets/baby/home/<USER>" alt="" />
              <span>
                {{ `下次${item.visitRemindType === 1 ? "到院" : "电话"}随访` }}
              </span>
            </div>
            <div class="next-time-left-date">
              {{ item?.meetingDateTime || "" }}
            </div>
          </div>
          <div class="next-time-right">
            <div>
              {{ item?.whichDayText || "" }}
              <span><van-icon name="arrow" /></span>
            </div>
          </div>
        </div>
      </div>
      <!-- 任务 totalTask	总任务数 completedTask 已完成-->
      <div class="swiper-title">
        <h5>当前任务</h5>
        <p v-if="false">{{ homeList.currentVisitText }}</p>
        <div v-if="false" class="schedule-all">
          <div class="schedule-back">
            <div v-if="homeList.totalTask === 0" class="schedule-in"></div>
            <div
              v-else
              :style="{
                width:
                  (homeList.completedTask / homeList.totalTask) * 100 + '%',
              }"
              class="schedule-in"
            ></div>
          </div>
          <span class="schedule-text-activ">{{ homeList.completedTask }}</span>
          <span>/{{ homeList.totalTask }}</span>
        </div>
      </div>
      <!-- questionTaskStatus pictureTaskStatus otherTaskStatus
    0 = 未知, 1 = 无需填写, 2 = 未完成, 3 = 部分完成, 4 = 已完成  -->
      <div class="home-tasks">
        <div class="home-tasks-module">
          <div class="home-tasks-item">
            <div
              v-for="(homeTasksItem, homeTasksIdx) in homeList.patientMenues"
              :key="homeTasksIdx"
            >
              <!-- 填写问卷 -->
              <div
                v-if="homeTasksItem.routerUrl === '/questionnairestaskcale'"
                class="home-tasks-item1 home-tasks-item-all"
                :class="{ 'home-prohibit': homeList.questionTaskStatus === 1 }"
                @click="
                  routerGo(
                    -1,
                    '/questionnairestaskcale',
                    homeList.questionTaskStatus === 1
                  )
                "
              >
                <div class="home-tasks-item-le">
                  <img
                    v-if="homeTasksItem.imgUrl"
                    :src="homeTasksItem.imgUrl"
                    alt=""
                  />
                  <img
                    v-else-if="homeList.questionTaskStatus === 1"
                    src="@/assets/baby/p1.svg"
                    alt=""
                  />
                  <img
                    v-else
                    src="@/assets/baby/addQuestionnaireIcon.svg"
                    alt=""
                  />
                  <p v-html="homeTasksItem.title" />
                </div>
                <div class="home-tasks-item-ri">
                  <!-- questionTaskStatus 1无需填写 2 = 未完成, 3 = 部分完成, 4 = 已完成 -->
                  <p v-if="homeList.questionTaskStatus === 1">无需填写</p>
                  <p v-else-if="homeList.questionTaskStatus === 2">待完成</p>
                  <p v-else-if="homeList.questionTaskStatus === 3">部分完成</p>
                  <p v-else-if="homeList.questionTaskStatus === 4">已完成</p>
                  <van-icon name="arrow" />
                </div>
              </div>
              <!-- 拍照上传 pictureTaskStatus -->
              <div
                v-if="homeTasksItem.routerUrl === '/uploadthetestsheetTask'"
                class="home-tasks-item2 home-tasks-item-all"
                @click="
                  routerGo(
                    -1,
                    '/uploadthetestsheetTask',
                    homeList.pictureTaskStatus === 1
                  )
                "
                :class="{ 'home-prohibit': homeList.pictureTaskStatus === 1 }"
              >
                <div class="home-tasks-item-le">
                  <img
                    v-if="homeTasksItem.imgUrl"
                    :src="homeTasksItem.imgUrl"
                    alt=""
                  />
                  <img
                    v-else-if="homeList.pictureTaskStatus === 1"
                    src="@/assets/baby/p2.svg"
                    alt=""
                  />
                  <img v-else src="@/assets/baby/addPictureIcon.svg" alt="" />
                  <p v-html="homeTasksItem.title" />
                </div>
                <div class="home-tasks-item-ri">
                  <p v-if="homeList.pictureTaskStatus === 1">无需上传</p>
                  <p v-else-if="homeList.pictureTaskStatus === 2">待上传</p>
                  <p v-else-if="homeList.pictureTaskStatus === 3">部分完成</p>
                  <p v-else-if="homeList.questionTaskStatus === 4">已完成</p>
                  <van-icon name="arrow" />
                </div>
              </div>
              <!-- 服药日志 Status待接口添加参数 /newTakemedicineLog-->
              <!-- 窗口期内有值就高亮， 窗口期内都已完成-高亮显示已完成， 仅提醒-高亮  其他置灰 -->
              <div
                v-if="homeTasksItem.routerUrl === '/takemedicineLog'"
                class="home-tasks-item4 home-tasks-item-all"
                @click="routerGo(-1, '/takemedicineLog')"
                :class="{
                  'home-prohibit':
                    homeList.extendStatus === 3 || homeList.extendStatus === 0,
                }"
              >
                <div class="home-tasks-item-le">
                  <img
                    v-if="homeTasksItem.imgUrl"
                    :src="homeTasksItem.imgUrl"
                    alt=""
                  />
                  <img
                    v-else-if="
                      homeList.extendStatus === 3 || homeList.extendStatus === 0
                    "
                    src="@/assets/baby/p3.svg"
                    alt=""
                  />
                  <img v-else src="@/assets/baby/medicineIcon.svg" alt="" />
                  <p v-html="homeTasksItem.title" />
                </div>
                <div class="home-tasks-item-ri">
                  <!-- <p v-if="homeList.medicationStatus === 1">无需填写</p>
                  <p v-else-if="homeList.medicationStatus === 0">遵从医嘱服药</p> -->
                  <p
                    v-if="
                      (homeList?.medicationStatus === 2 ||
                        homeList?.medicationStatus === 3) &&
                      homeList.extendStatus === 2
                    "
                  >
                    待完成
                  </p>
                  <p
                    v-else-if="
                      homeList?.medicationStatus === 4 &&
                      homeList.extendStatus === 2
                    "
                  >
                    已完成
                  </p>
                  <van-icon name="arrow" />
                  <!-- <img
                    v-if="homeList.medicationStatus === 4"
                    src="@/assets/baby/accomplishIcon4.svg"
                    alt=""
                  /> -->
                </div>
              </div>
              <!-- 学习资料 -->
              <div
                v-if="homeTasksItem.routerUrl === '/learningMaterials'"
                class="home-tasks-item5 home-tasks-item-all"
                :class="{ 'home-prohibit': !homeList?.displayTraining }"
                @click="routerGo(-1, '/learningMaterials')"
              >
                <div class="home-tasks-item-le">
                  <img
                    v-if="homeTasksItem.imgUrl"
                    :src="homeTasksItem.imgUrl"
                    alt=""
                  />
                  <img
                    v-else-if="homeList?.displayTraining"
                    src="@/assets/baby/home/<USER>"
                    alt=""
                  />
                  <img
                    v-else
                    src="@/assets/baby/home/<USER>"
                    alt=""
                  />
                  <!-- <img v-else src="@/assets/baby/addPictureIcon.svg" alt="" /> -->
                  <p v-html="homeTasksItem.title" />
                </div>
                <div class="home-tasks-item-ri">
                  <p
                    v-if="trainingNotice > 0 && homeList?.displayTraining"
                    class="home-tasks-item-study-num centerflex"
                  >
                    {{ trainingNotice }}
                  </p>
                  <van-icon name="arrow" />
                </div>
              </div>
              <!-- 其他任务 -->
              <div
                v-if="homeTasksItem.routerUrl === '/other'"
                class="home-tasks-item-other-background home-tasks-item-all"
                :class="{ 'home-prohibit': homeList.otherTaskStatus <= 1 }"
                @click="routerGo(-1, '/other', homeList.otherTaskStatus === 0)"
              >
                <div class="home-tasks-item-le-other centerflex-h">
                  <div class="other-img-module centerflex">
                    <img
                      v-if="homeTasksItem.imgUrl"
                      :src="homeTasksItem.imgUrl"
                      alt=""
                    />
                    <img
                      v-else
                      src="@/assets/baby/home/<USER>"
                      alt=""
                    />
                  </div>
                  <p v-html="homeTasksItem.title" />
                </div>
                <div class="home-tasks-item-ri">
                  <p v-if="homeList.otherTaskStatus === 1">无需填写</p>
                  <p v-else-if="homeList.otherTaskStatus === 2">待完成</p>
                  <p v-else-if="homeList.otherTaskStatus === 3">部分完成</p>
                  <p v-else-if="homeList.otherTaskStatus === 4">已完成</p>
                  <van-icon v-if="homeList.otherTaskStatus" name="arrow" />
                </div>
              </div>
              <!-- 其它记录 -->
            </div>
          </div>
        </div>
      </div>
      <!-- 其它药物-不适记录 下单寄送 -->
      <div class="myeveryday-module">
        <div class="centerflex-h-wrap">
          <div
            v-for="(myeverydaItem, myeverydaIdx) in homeList.patientMenues"
            :key="myeverydaIdx"
          >
            <div
              v-if="
                (myeverydaItem.routerUrl === '/meeting' && conferenceFlag) ||
                myeverydaItem.routerUrl === '/drugcombination' ||
                myeverydaItem.routerUrl === '/discomfort' ||
                myeverydaItem.routerUrl === '/placeanorder' ||
                myeverydaItem.routerUrl ===
                  '/compensationReimbursement/compensationReimbursementPage' ||
                myeverydaItem.routerUrl ===
                  '/digitalSignature/digitalSignaturePage'
              "
              class="myeveryday-items"
              @click="routerGo(-1, myeverydaItem.routerUrl)"
            >
              <p class="myeveryday-items-title" v-html="myeverydaItem.title" />
              <p
                class="myeveryday-items-title-en"
                v-html="myeverydaItem.enTitle"
              />
              <img
                v-if="myeverydaItem?.imgUrl"
                :src="myeverydaItem.imgUrl"
                class="myeveryday-item-img1"
                alt=""
              />
              <img
                v-else-if="myeverydaItem.routerUrl === '/drugcombination'"
                src="@/assets/baby/takeMedicineLogIcon.svg"
                class="myeveryday-item-img1"
                alt=""
              />
              <img
                v-else-if="myeverydaItem.routerUrl === '/discomfort'"
                src="@/assets/baby/AE.svg"
                class="myeveryday-item-img2"
                alt=""
              />
              <img
                v-else-if="
                  myeverydaItem.routerUrl ===
                  '/digitalSignature/digitalSignaturePage'
                "
                src="@/assets/baby/home/<USER>"
                class="w-[0.45rem]"
                alt=""
              />
              <img
                v-else-if="myeverydaItem.routerUrl === '/placeanorder'"
                src="@/assets/baby/placeanorderIcon.svg"
                class="myeveryday-item-img3"
                alt=""
              />
              <img
                v-else-if="
                  myeverydaItem.routerUrl ===
                  '/compensationReimbursement/compensationReimbursementPage'
                "
                src="@/assets/baby/home/<USER>"
                class="myeveryday-item-img2"
                alt=""
              />
              <!-- 会议 -->
              <img
                v-else-if="myeverydaItem.routerUrl === '/meeting'"
                src="@/assets/baby/home/<USER>"
                class="myeveryday-item-meeting"
                alt=""
              />
            </div>
          </div>
        </div>
      </div>
      <!-- 广告位 -->
      <div class="administration mb-[0.4rem]">
        <!-- cardSize 0 = 未知, 1 = 大, 2 = 中, 3 = 小-->
        <div
          v-for="(ie, idx) in studyAdvertisingList"
          :key="idx"
          :class="{
            'w-full h-2rem': ie.cardSize === 1,
            'w-full h-1rem': ie.cardSize === 2,
            'w-1/2 h-1rem': ie.cardSize === 3,
          }"
          class="pb-[0.2rem] overflow-auto scrollnone"
        >
          <img
            class="w-full h-full"
            :src="ie.bannerPicAccessUrl"
            alt=""
            @click="routerGo('banner', ie)"
          />
        </div>
      </div>
    </div>
    <!-- 底部tabbar -->
    <MyTabbar :propsActive="0" />
    <!-- 点赞图 遮罩层 -->
    <div v-if="showPopupFlag" class="popup" @click="showPopup()">
      <div class="popup-modules">
        <img class="popup-good-img" src="@/assets/baby/homeGood.svg" alt="" />
        <p>恭喜完成{{ showPopupText }}任务!</p>
        <van-button class="end-task-btn" round>确定</van-button>
      </div>
    </div>
    <!-- 弹窗-等待中心工作人员审核入组 -->
    <MyPopupShow :myPopupShow="popupShowFlag" title="">
      <template v-slot:bodyslot>
        <div class="undone-tips">
          <div class="w-full undone-tips-img">
            <img class="w-full" src="@/assets/baby/undoneTips.svg" alt="" />
          </div>
          <div class="undone-tips-writing">
            <div>尊敬的用户您好！</div>
            <div>请耐心等待中心工作人员审核入组~</div>
          </div>
        </div>
        <div class="centerflex" @click="popupShowFlag = false">
          <div class="undone-close">关闭</div>
        </div>
      </template>
    </MyPopupShow>
  </div>
</template>

<script lang='ts'>
import { defineComponent, onBeforeMount, reactive, toRefs, getCurrentInstance, inject, onActivated } from 'vue';
import { getPatient, getTrainingNotice } from '@/api/home';
import { useStore } from "vuex";
import MyTabbar from "@/components/Tabbar.vue";
// import { homeInter } from '@/types/home';
import { maintainedFun, returnPatientStatusUrl, toWildfire } from '@/utils/debounce';
import { useRoute } from 'vue-router';
import MyPopupShow from "@/components/MyPopupShow.vue";
import { homeListInter } from '@/types/storeState';
import { getStudyConference } from '@/api/home';
import { Toast } from 'vant';
import { getStudyAdvertising } from '@/api/home';
import { getUnReadCount } from '@/api/app'

export default defineComponent({
  name: "Home",
  components: {
    MyTabbar,
    MyPopupShow
  },
  setup() {
    const fireConferenceObj: any = inject('fireConferenceObj')
    const proxy: any = getCurrentInstance()?.proxy
    const store = useStore()
    const route = useRoute()
    const state: any = reactive({
      unreadMsgCount: '', // 未读消息数量
      appToken: store.state.appToken,
      conferenceFlag: false,
      homeList: {
        dctPatientId: '',
        dctSiteId: '',
        dctStudyId: '',
        displayTraining: false,
        patNumber: '',
        avatarUrl: '',
        greeting: '',
        currentStatusText: '',
        patientTrialStatusText: '',
        patientStatus: 0,
        visits: [],
        frequencyFeature: [],
        currentVisitId: '',
        currentVisitText: '',
        totalTask: 0,
        completedTask: 0,
        questionTaskStatus: 0,
        pictureTaskStatus: 0,
        otherTaskStatus: 0,
        visitMeetingList: [],
        dailyFeature: 0,
        inGroup: '',
        inGroupDay: '',
        medicationStatus: 0,
        patientICFStatus: 0,
        patientMenues: [],
        studyName: '',
        unReadChattingRecordsNum: 0,
        needInGroupGuidModal: 0,
        memberAvatarUrl: '',
        hasInteractiveCommunication: 0
      },
      showPopupFlag: false, // 弹出框任务成
      showPopupText: "",
      trainingNotice: 0,
      // 首页模块配置显示
      // {
      //   title: '填写问卷',
      //   enTitle: '',
      //   routerUrl: '/questionnairestaskcale',
      //   sort: 1,
      //   imgUrl: ''
      // },
      popupShowFlag: false,
      studyAdvertisingList: [], // 广告
      // 关闭弹出层
      showPopup: () => {
        state.showPopupFlag = false;
        store.dispatch('setTaskText', '')
      },
      // 获取首页数据
      getMyHomeDatas: async () => {
        const notice = await maintainedFun(route.path || '')
        getPatient().then((res) => {
          // res.patientMenues.push({...res.patientMenues[0],routerUrl: '/digitalSignature/digitalSignaturePage'})
          state.homeList = res as homeListInter;
          getStudyAdvertising(store.state.userInformation?.dctStudyId || state.homeList?.dctStudyId).then((res) => {
            state.studyAdvertisingList = res?.items;
          }).catch(() => {
            // console.error('获取广告位信息失败:', error);
          });
          state.homeList.currentVisitText = state.homeList.currentVisitText
            .split("--")
            .join(" ~ ");
          store.dispatch('setHomeList', state.homeList);
          // needInGroupGuidModal 1 筛选期任务完成
          if (state.homeList?.needInGroupGuidModal === 1 && !notice?.maintainedFlag && (state.homeList.patientStatus === 33 || state.homeList.patientStatus === 34)) {
            const initialEntry = sessionStorage.getItem("initialEntry") || '';
            if (!initialEntry) {
              sessionStorage.setItem("initialEntry", '1');
              state.popupShowFlag = true
            }
          }
          getStudyConference(state.homeList?.dctStudyId).then(res => {
            state.conferenceFlag = res as boolean;
          })
        });
        getTrainingNotice()
          .then((res) => {
            state.trainingNotice = res as number || 0
          })
      },
      // 替换
      replaceTemplateLiterals: (urlTemplate, variables) => {
        return urlTemplate.replace(/\$\{([^}]+)\}/g, (match, key) => {
          if (variables.hasOwnProperty(key)) {
            return variables[key];
          }
          // throw new Error(`Variable ${key} is not defined`);
        });
      },
      // 跳转
      routerGo: (index, url, flag) => {
        // 判断当前环境是否为app，如果是app则跳转app页面
        if (store.state.appToken != '' && url === '/meeting') {
          const msgData = {
            data: {
              action: "manageConference",
              payload: {
                StudyID: store.state.userInformation?.dctStudyId || state.homeList?.dctStudyId,
                PatientID: store.state.userInformation?.dctPatientId || state.homeList?.dctPatientId,
                SiteID: store.state.userInformation?.dctSiteId || state.homeList?.dctSiteId
              }
            }
          };
          // 如果是iOS
          if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.jumpToNative) {
            window.webkit.messageHandlers.jumpToNative.postMessage(msgData);
          }
          // 如果是非iOS环境(即uniapp、安卓)
          uni.postMessage(msgData);
          return;
        }
        if (flag) return
        if (index === 'banner') {
          // const url = 'https://lingxi.test.trialdata.cn/ui/summary?token=${token}&studyId=${studyId}&siteId=${siteId}&backUrl=${backUrl}'
          const variables = {
            token: store.state.patientToken || sessionStorage.getItem('patientToken'),
            studyId: store.state.userInformation?.dctStudyId || state.homeList?.dctStudyId,
            siteId: store.state.userInformation?.dctSiteId || state.homeList?.dctSiteId,
            patientId: store.state.userInformation?.dctPatientId || state.homeList?.dctPatientId,
            backUrl: encodeURIComponent(location.href)
          };
          const resultUrl = state.replaceTemplateLiterals(url?.redirectUrl, variables);
          // console.log(resultUrl);
          // return
          if (url?.redirectTargetDisplay === "H5") {
            location.replace(resultUrl)
          }
          return
        }
        let query = {}
        if (index === '/followReminder') {
          query = {
            visitId: url
          }
          proxy.$routerGoFun('routerInnPush', index, query, '/')
        } else if (url === '/meeting') {
          if (fireConferenceObj.src) {
            Toast.fail('当前已经在会议中')
            return
          }
          // 跳转到会议
          // console.log(state.homeList,state.homeList.patNumber);
          const { dctPatientId, patNumber } = state.homeList
          const token = store?.state?.patientToken || sessionStorage.getItem('patientToken')
          // console.log(toWildfire(token, dctPatientId, store.state?.userInformation?.icfStementId, false, '', patNumber));
          fireConferenceObj.src = toWildfire(token, dctPatientId, store.state?.userInformation?.icfStementId, false, '', patNumber)
        }
        else if (url) {
          if (url === '/placeanorder') {
            url = `/temporarilynotopened`
          } else if (url === '/takemedicineLog' &&
            state.homeList.studyName !== `甲状腺片与左甲状腺素钠片对照治疗分化型甲状腺癌术后患者的有效性与安全性临床研究`) {
            url = `/newTakemedicineLog`
          }
          proxy.$routerGoFun('routerInnPush', url, query, '/')
        }
      },
      // 打开APP消息列表
      openMsgDialog: () => {
        const msgData = {
          data: {
            action: 'msgList',
            payload: store.state.appToken
          }
        }
        // 如果是iOS
        if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.jumpToNative) {
          window.webkit.messageHandlers.jumpToNative.postMessage(msgData);
        }
        // 如果是非iOS环境(即uniapp、安卓)
        uni.postMessage(msgData);
      },
      // 打开侧边栏
      openLeftSlideDialog: () => {
        const msgData = {
          data: {
            action: 'showLeftSlide',
            payload: store.state.appToken
          }
        }
        // 如果是iOS
        if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.jumpToNative) {
          window.webkit.messageHandlers.jumpToNative.postMessage(msgData);
        }
        // 如果是非iOS环境(即uniapp、安卓)
        uni.postMessage(msgData);
      },
    });

    onBeforeMount(() => {
      // console.log(store.state.userInformation?.dctStudyId,'1', state.homeList?.dctStudyId);
      if (store.state.appToken != '') {
        getUnReadCount().then(res => {
          state.unreadMsgCount = res;
        });
      }
      // 人脸后直接进野火
      if (location.hash.includes('Conference=true')) {
        sessionStorage.removeItem('conferenceIMNum')
        // 获取哈希值
        const hash = location.hash + '';
        // 去除哈希前的'#'符号
        const hashContent = hash.slice(1);
        // 解析哈希中的查询参数
        const hashParams = new URLSearchParams(hashContent);
        const serialNo = hashParams.get('serialNo');
        const bgStudyId = hashParams.get('bgStudyId');
        const conferenceId = hashParams.get('conferenceId');
        if (bgStudyId) {
          store.dispatch('setUserInformation', {
            ...store.state.userInformation,
            dctStudyId: bgStudyId
          })
        }
        const { dctPatientId, icfStementId, patientNum, dctStudyId } = store.state.userInformation;
        let info = {
          token: store?.state?.patientToken || sessionStorage.getItem('patientToken'),
          patientId: dctPatientId,
          icfStatementID: icfStementId,
          makeReservation: false,
          host: location.host,
          pathname: location.pathname,
          code: sessionStorage.getItem("code"),
          documentTitle: document.title,
          patientName: '',
          patientNo: patientNum,
          studyId: bgStudyId || state?.homeList?.dctStudyId || dctStudyId || '',
          serialNo: serialNo || '',
          conferenceId: conferenceId || '',
        }
        const userInfo = JSON.stringify(info)
        let path = `https://${location.host}/patientui/conference/unpackage/dist/build/h5/index.html#/?userInfo=${userInfo}`
        // path = `http://localhost:5173/?userInfo=${userInfo}`
        fireConferenceObj.src = path
        if (store.state.userInformation.patientStatus === 4) {
          proxy.$routerGoFun('replace', returnPatientStatusUrl(store.state.userInformation))
        } else if (store.state.userInformation?.patientStatus === 10 ||
          store.state.userInformation?.patientStatus === 17 ||
          store.state.userInformation?.patientStatus >= 32) {
          proxy.$routerGoFun('replace', '/')
        }
      }
      state.getMyHomeDatas()
      store.commit('SET_GET_OLD_QUESTDATA_FLAG', 0)
      // 如果有任务完成
      if (store.state?.taskText) {
        state.showPopupText = store.state.taskText;
        state.showPopupFlag = true;
      }
    });

    onActivated(() => {
      state.getMyHomeDatas()
    })

    return {
      ...toRefs(state),
    };
  },
});
</script>

<style lang='less' scoped>
.home-container {
  font-size: 0.14rem;
  height: 100vh;
  width: 100%;
  overflow-x: hidden;
  h4,
  h5 {
    margin: 0;
  }
  .administration {
    width: calc(100% - 0.2rem);
    // margin: 0 0.1rem 0.1rem;
    margin: 0 auto 0.4rem;
    // width: 3.27rem;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    .h-1rem {
      height: 1rem;
    }
    .h-2rem {
      height: 2rem;
    }
  }
  .home-body {
    // height: 86vh;
    height: calc(100vh - 46px - 56px);
    overflow: auto;
    .home-push-message {
      width: 100%;
      padding: 0.2rem 0.1rem;
      box-sizing: border-box;
      .home-message-body {
        min-height: 1rem;
        display: flex;
        align-items: center;
        background: #fff;
        border-radius: 0.1rem;
        box-shadow: 0 0.02rem 0.26rem rgba(0, 0, 0, 0.07);
        .home-message-img {
          width: 0.6rem;
          height: 0.6rem;
          border-radius: 50%;
          margin: 0 0.1rem;
        }
        .home-message-detail {
          margin: 0 0 0 0.1rem;
          h4 {
            margin: 0.1rem 0 0 0;
            font-size: 0.17rem;
          }
          h5,
          p {
            padding: 0;
            margin: 0;
            font-size: 0.15rem;
          }
        }
      }
      .next-time-module {
        margin: 0.1rem 0 0 0;
        padding: 0.15rem;
        box-sizing: border-box;
        min-height: 0.63rem;
        color: var(--theme-color);
        background: #fff;
        border-radius: 0.1rem;
        border: var(--theme-color) 0.5px solid;
        box-shadow: 0 0.02rem 0.26rem rgba(88, 96, 218, 0.2);
        .next-time-left {
          width: 50%;
          .next-time-left-phone {
            margin: 0 0 0.04rem 0;
            font-size: 0.13rem;
            img {
              margin: 0 0.1rem 0 0;
              width: 0.1645rem;
            }
          }
          .next-time-left-date {
            font-size: 0.12rem;
          }
        }
        .next-time-right {
          width: 50%;
          display: flex;
          justify-content: flex-end;
          font-size: 0.15rem;
          .van-badge__wrapper.van-icon.van-icon-arrow {
            color: var(--theme-color) !important;
          }
          span {
            margin: 0 0 0 0.1rem;
          }
        }
      }
    }
    // 本次任务
    .swiper-title {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 0.1rem 0;
      padding: 0 0.1rem;
      box-sizing: border-box;
      color: #000;
      h5 {
        font-size: 0.15rem;
      }
      p {
        font-size: 0.1rem;
      }
      .schedule-all {
        display: flex;
        align-items: center;
        .schedule-text-activ {
          color: rgba(88, 96, 218, 1);
        }
        .schedule-back {
          width: 1.13rem;
          height: 0.09rem;
          background: rgba(239, 239, 239, 1);
          border-radius: 0.2rem;
          overflow: hidden;
          margin: 0 0.05rem 0 0;
        }
        .schedule-in {
          height: 100%;
          border-radius: 0.2rem;
          // 渐变
          background: linear-gradient(
            90deg,
            rgba(63, 161, 252, 1) 100%,
            rgba(88, 96, 218, 1) 100%
          );
        }
      }
    }
    // 我的 任务--
    .home-tasks {
      width: 100%;
      .home-tasks-module {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        .home-tasks-item {
          width: 100%;
          overflow-x: hidden;
          color: #fff;
          /* 从 Vue.js 2.6.0 版本开始，>>> /deep/ ::v-deep已经被弃用了，
          官方建议改用 :deep() 函数来代替它。*/
          :deep(.van-icon) {
            color: #fff !important;
            margin: 0 0 0 0.1rem;
          }
          .home-tasks-item-all {
            width: calc(100% - 0.2rem);
            margin: 0 0.1rem 0.1rem;
            padding: 0.1rem;
            box-sizing: border-box;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.15rem;
            border-radius: 0.1rem;
            .home-tasks-item-le {
              display: flex;
              align-items: center;
              img {
                width: 0.5rem;
                height: 0.5rem;
                margin: 0 0.1rem 0 0;
              }
            }
            .home-tasks-item-le-other {
              .other-img-module {
                width: 0.5rem;
                height: 0.5rem;
                margin: 0 0.1rem 0 0;
                background: rgba(167, 185, 239, 0.5);
                border-radius: 0.1rem;
                img {
                  width: 0.2315rem;
                  height: 0.2315rem;
                }
              }
            }
            .home-tasks-item-ri {
              display: flex;
              align-items: center;
              .home-tasks-item-study-num {
                padding: 0.01rem 0.04rem;
                padding-top: 0;
                font-size: 0.1rem;
                color: #41b592;
                border-radius: 0.08rem;
                background: #daf0ea;
                box-sizing: border-box;
                min-width: 0.23rem;
              }
              img {
                width: 0.8rem;
                height: 0.35rem;
              }
            }
          }
          // 置灰
          .home-prohibit {
            width: calc(100% - 0.2rem);
            margin: 0 0.1rem;
            border-radius: 0.1rem;
            padding: 0.1rem;
            box-sizing: border-box;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.15rem;
            color: #fff;
            background: rgba(195, 195, 195) !important;
            overflow: hidden;
            border-radius: 0.1rem;
            margin-bottom: 0.1rem;
            .other-img-module {
              background: rgba(170, 170, 170, 1) !important;
            }
            .home-tasks-item-le {
              display: flex;
              align-items: center;
              img {
                display: inline-block;
                width: 0.5rem;
                height: 0.5rem;
                margin: 0 0.1rem 0 0;
              }
            }
            .home-tasks-item-ri {
              display: flex;
              align-items: center;
              img {
                width: 0.8rem;
                height: 0.35rem;
              }
            }
          }
          .home-tasks-item1 {
            background: #8d94ff;
          }
          .home-tasks-item2 {
            background: #3fa1fc;
          }
          // 其他任务
          .home-tasks-item-other-background {
            background: #1d40a5;
          }
          .home-tasks-item3 {
            background: #ec8ea1;
          }
          .home-tasks-item4 {
            background: #ec8ea1;
          }
          .home-tasks-item5 {
            background: #41b592;
          }
          img {
            width: 100%;
          }
        }
      }
    }
    // 其它药物 - 不适记录 -下单寄送
    .myeveryday-module {
      margin: 0.1rem 0 0.1rem 0;
      .myeveryday-items {
        width: 1.04rem;
        min-height: 1.1rem;
        box-sizing: border-box;
        padding: 0.1rem 0.1rem;
        margin: 0.05rem 0.1rem;
        position: relative;
        background: #fff;
        border-radius: 0.04rem;
        box-shadow: 0 0.02rem 0.26rem rgba(0, 0, 0, 0.07); // 阴影
        .myeveryday-items-title {
          font-size: 0.14;
          font-weight: 700;
          color: rgba(88, 96, 217, 1);
          margin: 0.04rem 0 0 0;
        }
        .myeveryday-items-title-en {
          margin: 0.06rem 0;
          font-size: 0.08rem;
          color: rgba(118, 118, 118, 1);
        }
        img {
          position: absolute;
          right: 0.04rem;
          bottom: 0.04rem;
        }
        .myeveryday-item-img1 {
          width: 0.41rem;
          margin: 0 0 0 50%;
        }
        .myeveryday-item-img2 {
          width: 0.4rem;
          margin: 0 0 0 50%;
        }
        .myeveryday-item-meeting {
          width: 0.45rem;
          margin: 0 0 0 50%;
        }
        .myeveryday-item-img3 {
          width: 0.6rem;
          margin: 0 0 0 30%;
        }
      }
    }
  }
  // 遮罩
  .popup {
    width: 100%;
    height: 100vh;
    position: fixed;
    top: 0;
    z-index: 999;
    color: #fff;
    background: rgba(0, 0, 0, 0.3);
    .popup-modules {
      width: 2.81rem;
      height: 3.3rem;
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      margin: auto;
      border-radius: 0.1rem;
      background: linear-gradient(
        180deg,
        rgba(63, 161, 252, 1),
        rgba(88, 96, 218, 1)
      );
      p {
        width: 100%;
        text-align: center;
      }
      .popup-good-img {
        width: 1.55rem;
        height: 1.95rem;
      }
      .end-task-btn {
        width: 2.37rem;
        background: #fff;
        height: 0.35rem;
        color: rgba(88, 96, 218, 1);
      }
    }
  }
  // tips
  .undone-tips {
    padding: 0.3rem 0.3rem 0.2rem;
    box-sizing: border-box;
    border-bottom: 0.5px solid #f6f6f6;
    font-size: 0.15rem;
    .undone-tips-img {
      padding: 0 0.37rem;
      box-sizing: border-box;
    }
    .undone-tips-writing {
      margin-top: 0.2rem;
      color: #333;
      text-align: center;
    }
  }
  .undone-close {
    padding: 0.15rem 0;
  }
}
</style>