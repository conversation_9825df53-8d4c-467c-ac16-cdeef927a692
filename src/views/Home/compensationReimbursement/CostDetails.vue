<template>
  <div v-show="showCostBreakdownFlag" class="h-100-vh font-color-333 ft-15-rem bg-color-F5F5F5 overflow-hidden">
    <van-nav-bar title="详情" left-arrow @click-left="$routerBackFun()" />
    <div
      class="cost-details-body overflow-auto"
      :class="{ 'cost-details-body-edit': !editFlag }"
    >
      <div class="patient-num-module patient-num-module-title p-01rem">
        <div>{{applyInfoObj?.patientNumber || ''}}</div>
        <p class="my-01rem ft-14-rem font-light">
          <span v-if="applyInfoObj.status === 0 && applyInfoObj?.id" class="text-yellow-400">待确认</span>
          <span v-else-if="applyInfoObj.status === 4" class="font-color-666">已退回</span>
          <span v-else-if="applyInfoObj.status === 2" class="font-color-72AE32">已处理</span>
          <span v-else-if="applyInfoObj.status === 3" class="font-color-E02020">已拒绝</span>
          <span v-else-if="applyInfoObj.status === 1" class="text-yellow-400">处理中</span>
        </p>
        <div
        v-if="applyInfoObj?.triggreType > 0 && applyInfoObj.status === 0 && applyInfoObj?.id"
        class="ft-14-rem font-color-999 font-light"
        >
          <span v-if="applyInfoObj.triggreType === 1">访视任务完成时触发，创建于{{applyInfoObj.createTimeStr}}</span>
          <span v-else>录入实际随访日期时触发{{applyInfoObj.createTimeStr}}</span>
        </div>
        <div v-else-if="applyInfoObj?.applyNumber" class="font-color-999 font-light">单号{{applyInfoObj.applyNumber}}</div>
      </div>
      <div class="patient-num-module my-01rem centerflex-h justify-between px-01rem">
        <div>关联访视</div>
        <div class="w-2/3 font-normal centerflex-h justify-end" @click="showInterviewPopup = editFlag">
          <span v-if="!applyInfoObj?.relationVisitName && editFlag" class="ft-13-rem font-color-666">请选择访视</span>
          <div v-else class="ft-13-rem none-warp-text-auto">{{applyInfoObj?.relationVisitName || ''}}</div>
          <van-icon v-if="editFlag" class="ml-01rem" name="arrow" />
        </div>
      </div>
      <div class="bg-color-fff mb-01rem">
        <div class="patient-num-module centerflex-h px-01rem">费用明细</div>
        <div class="px-01rem font-color-5995FF ft-14-rem mb-02rem">
          合计（元）：{{applyInfoObj?.compensationAmount || ''}}
        </div>
        <div class="px-01rem" :class="{ 'pb-01rem': !editFlag }">
          <div
            v-for="(item, index) in applyInfoObj.applySubjects"
            :key="index"
            class="commmon-card-module ft-14-rem mb-01rem"
          >
            <div class="mb-01rem centerflex-h justify-between">
              <div>科目：{{item?.subjectName || ''}}</div>
              <img
                v-if="editFlag"
                class="w-0165rem translate-y-005rem"
                src="@/assets/baby/editAddressIcon.svg"
                alt=""
                @click="setCostBreakdown('edit',item,index)"
              />
              <img
                v-else
                class="w-0215rem translate-y-005rem"
                src="@/assets/baby/compensationReimbursement/readOnlyEyesIcon.svg"
                alt=""
                @click="setCostBreakdown('readonly',item,index)"
              />
            </div>
            <div class="my-01rem">金额（元）：{{item?.amount || ''}}</div>
            <div class="my-01rem none-warp-text-auto">
              备注：{{item?.remark || ''}}
            </div>
            <div class="my-01rem">附件：
              <span v-if="item?.attachmentEditViewModels?.length > 0">{{item.attachmentEditViewModels.length}}</span>
            </div>
          </div>
        </div>
        <div
          v-if="editFlag"
          class="patient-num-module centerflex ft-14-rem"
          @click="setCostBreakdown('push')"
        >
        <span class="font-color-5995FF">+ 添加</span>
        </div>
      </div>
      <div class="bg-color-fff">
        <div class="patient-num-module centerflex-h px-01rem">流程</div>
        <!-- 流程轴 -->
        <div v-for="(item,index) in applyInfoObj.runtimeLog"
        :key="index"
        class="common-date-navs-module flex pb-02rem">
          <div class="date-navs-items-left">
            <div v-if="item?.aprStatus || item?.detailName === '发起人'" class="date-bule-icon bg-color-5995FF" />
            <div v-else-if="!item?.aprStatus" class="date-bule-icon bg-color-dfdfdf" />
            <div v-if="applyInfoObj.runtimeLog.length > 1 && index !== applyInfoObj.runtimeLog.length -1" class="date-after" />
            <!-- aprStatus
            Approved 审批通过/Rejected 审批拒绝/Processing 待审批 -->
            <img
              v-if="item?.aprStatus === 'Approved'"
              src="@/assets/baby/compensationReimbursement/correctIcon.svg" 
              alt=""
              class="w-015rem absolute z-2"
            />
            <img
              v-else-if="item?.aprStatus === 'Rejected' && item?.aprBizStatus === 'Returned'"
              src="@/assets/baby/compensationReimbursement/leftArrowIcon.svg" 
              alt=""
              class="w-015rem absolute z-2"
            />
            <img
              v-else-if="item?.aprStatus === 'Rejected' && item?.aprBizStatus === 'Rejected'"
              src="@/assets/baby/compensationReimbursement/rejectedIcon.svg" 
              alt=""
              class="w-015rem absolute z-2"
            />
            <img
              v-else-if="item?.aprStatus === 'Processing'"
              src="@/assets/baby/compensationReimbursement/alarmClockIcon.svg" 
              alt=""
              class="w-015rem absolute z-2"
            />
          </div>
          <div class="date-navs-items-infos font-normal">
            <div class="flex justify-between mb-01rem">
              <div class="w-1/2">
                <div v-html="item.detailName" class="none-warp-text-auto"/>
                <div v-if="item?.remark" v-html="item.remark" class="ft-12-rem font-color-999 mt-01-rem"
                  :class="{
                    'w-3-rem': !(((!item?.aprStatus && item?.detailName !== '发起人') || item?.aprStatus !== 'Processing') && !item?.aprStatus && item?.approver)
                  }"
                />
              </div>
              
              <div v-if="((!item?.aprStatus && item?.detailName !== '发起人') || item?.aprStatus !== 'Processing') && !item?.aprStatus && item?.approver" class="pr-01-rem max-w-40-percent max-h-055rem centerflex-h-wrap justify-end">
                <div class="approver-img mb-006rem centerflex">
                  <img src="@/assets/baby/compensationReimbursement/approverIcon.svg" class="w-018rem" alt="">
                </div>
                <div class="w-full centerflex-h justify-end ft-11-rem font-color-999">
                  <div class="truncate">{{item.approver}}</div>
                </div>
              </div>
              <div v-else-if="((!item?.aprStatus && item?.detailName !== '发起人') || item?.aprStatus !== 'Processing') && item?.requestDate" v-html="item.requestDate" class="ft-11-rem font-color-999"/>
            </div>

            <div v-if="item?.comment && item?.aprStatus !== 'Processing'" class="ft-11-rem font-color-999 flex">
              <div class="triangle-left"/>
              <div v-html="item.comment" class="w-full p-01rem bg-color-F5F5F5 break-all"/>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="editFlag"
      class="commmon-bottom-add-btn centerflex mt-01-rem"
      @click="costApplyForSave"
    >
      提交
    </div>
    <van-popup v-model:show="showInterviewPopup" round position="bottom">
      <van-picker
        title="选择访视"
        :columns="applyInfoObj.visits"
        :columnsFieldNames="{text: 'visitTemplateName'}"
        :default-index="defaultInterviewIndex"
        @confirm="onConfirmInterview"
        @cancel="showInterviewPopup = false"
      >
      <template #title>
        <div class="font-color-969799"
        @click="() => {
          applyInfoObj.relationVisitName = ''
          applyInfoObj.relationVisitId = ''
          defaultInterviewIndex = 0
          showInterviewPopup = false
        }">
          清空
        </div>
      </template>
    </van-picker>
    </van-popup>
    <MyPopupShow 
      :myPopupShow="excessAmountPopupShow"
      title="提示"
      :texts="excessAmountText"
      cancelText="取消"
      saveText="继续"
      cancelClass="font-color-333"
      saveClass="font-color-333"
      :handleSave="continueSave"
      :handleCancel="() => { excessAmountPopupShow = false }"
    />
  </div>
  <CostBreakdown
  v-show="!showCostBreakdownFlag"
  ref="myCostBreakdownRef"
  :backCostBreakdown="backCostBreakdown"
  :changeCostBreakdownItem="changeCostBreakdownItem"
  />
</template>

<script lang="ts">
import { Dialog, Notify, Toast } from "vant";
import {
  defineComponent,
  onBeforeMount,
  reactive,
  toRefs,
  getCurrentInstance,
} from "vue";
import { useRoute } from "vue-router";
// import { Toast } from "vant";
import MyPopupShow from "@/components/MyPopupShow.vue";
import {
  getPeriodicCompensationApplyListInfo,
  putValidatePeriodicCompensationApply,
  putPeriodicCompensationApply
} from '@/api/compensationReimbursement';
import { useStore } from "vuex";
import CostBreakdown from "@/views/Home/compensationReimbursement/CostBreakdown.vue";
import { deepClone, guid } from '@trialdata/common-fun-css/index';
import { CostDetailsStateInter, GetPeriodicCompensationApplyItemInter } from '@/types/home';

export default defineComponent({
  name: "CostDetails", // 详情（费用）
  components: {
    MyPopupShow,
    CostBreakdown
  },
  setup() {
    const route = useRoute();
    const store = useStore()
    const proxy: any = getCurrentInstance()?.proxy;
    const state: CostDetailsStateInter = reactive({
      colorfff: '#fff',
      showCostBreakdownFlag: true, // 是否显示费用明细模块
      myCostBreakdownRef: null,
      showInterviewPopup: false,
      defaultInterviewIndex: 0,// 默认选中的是
      excessAmountPopupShow: false,
      excessAmountText: ``,
      applyInfoObj: {
        id: '',
        studyId: '',
        siteId: '',
        patientId: '',
        createTimeStr: '',
        applyNumber: '',
        patientNumber: '',
        siteName: '',
        relationVisitName: '',
        patientName: '',
        compensationAmount: 0,
        status: 0,
        statusStr: '',
        lastStatus: 0,
        source: 0,
        triggreType: 0,
        relationVisitId: '',
        creator: '',
        creatorId: '',
        applyTime: '',
        applyTimeStr: '',
        createTime: '',
        lastUpdater: '',
        lastUpdateTime: '',
        lastUpdateTimeStr: '',
        applyStatus: 0,
        applyMsg: '',
        realName: '',
        isDeleted: true,
        remark: '',
        nextApprovers: '',
        handledByMe: true,
        commitByMe: true,
        currentRuntimeId: 0,
        applySubjects: [],
        visits: [],
        runtimeLog: [],
        users: [],
        subjects: '',
      },
      editFlag: false, // 是否可编辑
      submitLoadingFlag: true,
      // 初始对象
      initCostBreakdownItem: {
        subjectId: '',
        subjectName: '',
        amount: '',
        remark: '',
        attachmentEditViewModels: []
      },
      setDefaultInterviewIndexFun: () => {
        // 匹配到已选择的index
        if (state?.applyInfoObj?.relationVisitId && state?.applyInfoObj?.visits?.length) {
          state.applyInfoObj.visits.forEach((el,idx) => {
            if (el?.visitTemplateId && el.visitTemplateId === state.applyInfoObj.relationVisitId) {
              state.defaultInterviewIndex = idx
            }
          });
        }
      },
      // 确认访视
      onConfirmInterview: (e) => {
        state.applyInfoObj.relationVisitName = e.visitTemplateName
        state.applyInfoObj.relationVisitId = e.visitTemplateId
        state.setDefaultInterviewIndexFun()
        state.showInterviewPopup = false
      },
      // 继续提交
      continueSave: () => {
        state.saveEnd(false)
      },
      // 从明细页 回主页
      backCostBreakdown: () => {
        state.showCostBreakdownFlag = true
      },
      // 打开费用明细
      setCostBreakdown: (flag,item,index) => {
        const addInitCostBreakdownItem = deepClone(state.initCostBreakdownItem)
        addInitCostBreakdownItem.guId = guid()
        if (state.applyInfoObj?.subjects) {
          const subjects: any = state.applyInfoObj.subjects.split(',')
          state.myCostBreakdownRef.subjectOptions = state.myCostBreakdownRef.subjectOptions.filter(item => subjects.includes(item.subjectId))
        }
        state.myCostBreakdownRef.operationCostBreakdownItem = flag !== 'push' ? deepClone(item) : deepClone(addInitCostBreakdownItem)
        if (state.applyInfoObj?.subjects && state.myCostBreakdownRef.subjectOptions?.length === 1 && !state.myCostBreakdownRef.operationCostBreakdownItem?.subjectId && flag !== 'readonly' ) { // 默认给第一个值
          state.myCostBreakdownRef.operationCostBreakdownItem.subjectId = state.myCostBreakdownRef.subjectOptions[0].subjectId
          state.myCostBreakdownRef.operationCostBreakdownItem.subjectName = state.myCostBreakdownRef.subjectOptions[0].subjectName
        }
        state.myCostBreakdownRef.defaultSubjectIndex = 0
        state.myCostBreakdownRef.setDefaultIndexFun()
        state.myCostBreakdownRef.editFlag = flag !== 'readonly'
        if (flag !== 'push') {
          state.myCostBreakdownRef.operationItemObjIndex = index
        } else if (state.applyInfoObj?.applySubjects[0]?.subjectId) {
          state.myCostBreakdownRef.operationItemObjIndex = state.applyInfoObj?.applySubjects?.length || 1
        }
        state.myCostBreakdownRef.applySubjectsLength = state.applyInfoObj?.applySubjects?.length || 1
        state.myCostBreakdownRef.operationFlag = flag
        state.showCostBreakdownFlag = false
      },
      // 明细中 删除或者保存
      changeCostBreakdownItem: (flag,index,item) => {
        if (flag === 'push') {
          if (state.applyInfoObj?.applySubjects[0]?.amount) {
            state.applyInfoObj.applySubjects.push(item)
          } else {
            state.applyInfoObj.applySubjects[0] = item
          }
        } else if (flag === 'edit') {
          state.applyInfoObj.applySubjects[index] = item
        } else if (flag === 'delete') {
          const applySubjectsArr = [...state.applyInfoObj.applySubjects]
          applySubjectsArr.forEach((el: any,index) => {
            if (item?.id && el?.id === item.id) {
              state.applyInfoObj.applySubjects.splice(index,1)
            } else if (item?.guId && el?.guId === item.guId) {
              state.applyInfoObj.applySubjects.splice(index,1)
            }
          })
        }
        // 计算
        state.applyInfoObj.compensationAmount = 0
        state.applyInfoObj.applySubjects.forEach((el) => {
          if (el?.amount) {
            state.applyInfoObj.compensationAmount += Number(el.amount)
          }
        })
      },
      // 提交
      costApplyForSave: () => {
        if (state.submitLoadingFlag) {
          state.submitLoadingFlag = false
        const paramsObj = deepClone(state.applyInfoObj)
        paramsObj.studyId = store.state.homeList.dctStudyId
        paramsObj.siteId = store.state.homeList.dctSiteId
        paramsObj.patientId = store.state.homeList.dctPatientId
        paramsObj.lastStatus = paramsObj.status
        if (!route.query?.itemId || (paramsObj?.commitByMe && paramsObj?.status === 4)) {
          paramsObj.status = 1 // 首次添加设为状态 处理中
        }
        if (!state.applyInfoObj?.compensationAmount) {
          paramsObj.compensationAmount = 0
        }
        if (!state.applyInfoObj?.applySubjects[0].amount) {
          paramsObj.applySubjects[0].amount = 0
        }
        putValidatePeriodicCompensationApply(paramsObj)
         .then((rest) => {
           const res = rest as GetPeriodicCompensationApplyItemInter
            if (res?.applyStatus === 0) {
              state.saveEnd(true)
              return
            } 
            if (res?.applyStatus === 1) {
              Dialog.alert({
                title: '提示',
                confirmButtonText: '关闭',
                confirmButtonColor: '#333',
                message: res?.applyMsg ? `<div class="ft-14-rem">${res.applyMsg}</div>` : '',
                allowHtml: true,
              }).then(() => {
                // console.log('关闭')
              });
            } else if (res?.applyStatus === 2) {
              state.excessAmountText = res?.applyMsg || ''
              state.excessAmountPopupShow = true
            } else if (res?.applyStatus === 3) {
              Toast(res?.applyMsg || '')
            }
            state.submitLoadingFlag = true
         }).catch(() => {state.submitLoadingFlag = true})
        }
      },
      saveEnd: (flag) => {
        if (state.submitLoadingFlag || flag) {
        const paramsObj = deepClone(state.applyInfoObj)
        paramsObj.studyId = store.state.homeList.dctStudyId
        paramsObj.siteId = store.state.homeList.dctSiteId
        paramsObj.patientId = store.state.homeList.dctPatientId
        paramsObj.lastStatus = paramsObj.status
        // if (!route.query?.itemId) {
        paramsObj.status = 1 // 首次添加设为状态 处理中
        // }
        if (!state.applyInfoObj?.compensationAmount) {
          paramsObj.compensationAmount = 0
        }
        if (!state.applyInfoObj?.applySubjects[0].amount) {
          paramsObj.applySubjects[0].amount = 0
        }
        putPeriodicCompensationApply(paramsObj)
         .then(() => {
            Notify({ type: "success", message: "提交成功" })
            proxy.$routerBackFun()
            state.submitLoadingFlag = true
         }).catch(() => {state.submitLoadingFlag = true})
        }
      },
      onLoad: () => {
        // ApplyId = id
        const paramsObj = {
          applyId: route.query?.itemId || '',
          patientId: store.state?.homeList?.dctPatientId || ''
        }
        getPeriodicCompensationApplyListInfo(paramsObj)
          .then((rest) => {
            const res = rest as GetPeriodicCompensationApplyItemInter
            if (!res?.patientNumber) {
              res.patientNumber = store.state.homeList.patNumber
            }
            if (!route.query?.itemId) {
              const addInitCostBreakdownItem = deepClone(state.initCostBreakdownItem)
              addInitCostBreakdownItem.guId = guid()
              res.applySubjects = [addInitCostBreakdownItem]
            }
            state.applyInfoObj = res
            state.setDefaultInterviewIndexFun()
            // 4退回 commitByMe是否为申请人
            if ((res?.status === 0 && !res?.id) || (res?.status === 4 && res?.commitByMe)) {
              state.editFlag = true
            }
          })
      },
    });
    onBeforeMount(() => {
      state.onLoad();
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.cost-details-body {
  height: calc(100vh - 46px - 0.65rem);
  .patient-num-module {
    font-weight: 500;
    font-size: 0.15rem;
    color: #333;
    min-height: 0.51rem;
    /*使用v-bind绑定state中的变量*/
    background: v-bind('colorfff');
  }
  .patient-num-module-title {
    min-height: 0.2rem;
  }
}
.cost-details-body-edit {
  height: calc(100vh - 46px - 0.1rem);
}
</style>
