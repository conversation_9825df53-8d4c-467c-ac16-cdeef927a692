<template>
  <div class="h-100-vh
  ft-13-rem
  font-color-fff
  bg-color-F5F5F5
  overflow-hidden">
    <van-nav-bar title="补偿/报销" left-arrow @click-left="$routerBackFun()" />
    <div class="compensation-reimbursement-body">
      <div class="compensation-reimbursement-header bg-size-100">
        <div class="centerflex-h h-full py-01rem box-border">
          <!-- 遍历对象v-for="(value, key) in myObject" :key="key" -->
          <p class="w-1/3 h-full centerflex-wrap">
            <span class="w-full centerflex ft-18-rem">¥ {{summaryObj.applyAmount}}</span>
            <span class="w-full centerflex">申请中</span>
          </p>
          <p class="w-1/3 h-full centerflex-wrap border-br">
            <span class="w-full centerflex ft-18-rem">¥ {{summaryObj.dealedAmount}}</span>
            <span class="w-full centerflex">已处理</span>
          </p>
          <p class="w-1/3 h-full centerflex-wrap">
            <span class="w-full centerflex ft-18-rem">¥ {{summaryObj.withdrawalAmount}}</span>
            <span class="w-full centerflex">已提现</span>
          </p>
        </div>
      </div>
      <MyStateShow v-if="!compensationReimbursementList?.length" :imgSrc="noDatasImg" texts="暂无数据" imgClass="max-w-3-1rem"/>
      <div v-else
      ref="compensationReimbursementListRef"
      class="compensation-reimbursement-list-module p-01rem box-border ft-14-rem
      font-color-333
      overflow-auto scrollnone"
      @scroll="scrollChage"
      >
        <div
          v-for="(item, index) in compensationReimbursementList"
          :key="index"
          class="commmon-card-module mb-01rem"
          @click="routerGo('/compensationReimbursement/costDetails', item)"
        >
          <div class="mb-01rem centerflex-h justify-between">
            <div v-html="item.patientNumber" class="ft-15-rem"/>
            <div class="centerflex-h">
              <span v-if="item.status === 0" class="ft-13-rem text-yellow-400">待确认</span>
              <span v-else-if="item.status === 4" class="ft-13-rem font-color-666">已退回</span>
              <span v-else-if="item.status === 2" class="ft-13-rem font-color-72AE32">已处理</span>
              <span v-else-if="item.status === 3" class="ft-13-rem font-color-E02020">已拒绝</span>
              <span v-else-if="item.status === 1" class="ft-13-rem text-yellow-400">处理中</span>
              <van-icon class="ml-01rem" name="arrow" />
            </div>
          </div>
          <div class="my-hr w-full028rem translate-x014rem"/>
          <div v-if="item?.applyTimeStr && item.status === 0" class="my-01rem font-color-666">创建时间：{{item?.createTimeStr || ''}}</div>
          <div v-else-if="item?.applyTimeStr" class="my-01rem font-color-666">申请时间：{{item?.applyTimeStr || ''}}</div>
          <div class="my-01rem">总金额（元）：{{item?.compensationAmount || ''}}</div>
          <div class="my-01rem">关联访视：
            <span>
            {{item?.relationVisitName || ''}}
            </span>
          </div>
        </div>
        <div class="ft-14-rem font-color-666 text-center">没有更多数据了～</div>
      </div>
    </div>
    <div v-if="patientAuth" class="commmon-bottom-add-btn centerflex mt-01-rem" @click="routerGo('/compensationReimbursement/costDetails')">添加申请</div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  onBeforeMount,
  reactive,
  toRefs,
  getCurrentInstance,
} from "vue";
import { useRoute } from "vue-router";
import { Toast } from "vant";
import MyStateShow from "@/components/MyStateShow.vue";
import noDatasImg from '@/assets/baby/compensationReimbursement/noDatasImg.svg';
import { 
  getPeriodicCompensationApplyList,
  getCompensationAmountSummary,
  getPeriodicCompensationApplyAuth } from '@/api/compensationReimbursement';
import { useStore } from "vuex";
import { compensationReimbursementPageStateInter, 
  GetPeriodicCompensationApplyListInter,
  getCompensationAmountSummaryInter,
  GetPeriodicCompensationApplyAuthInter } from '@/types/home';

export default defineComponent({
  name: "CompensationReimbursementPage", // 补偿/报销compensationReimbursement
  components: {
    MyStateShow
  },
  setup() {
    const route = useRoute();
    const store = useStore()
    const proxy: any = getCurrentInstance()?.proxy;
    const state: compensationReimbursementPageStateInter = reactive({
      noDatasImg,
      patientAuth: false,
      summaryObj: {
        applyAmount: 0,
        dealedAmount: 0,
        withdrawalAmount: 0
      },
      compensationReimbursementList: [],
      compensationReimbursementListRef: null,
      pageIndex: 1,
      pageSize: 20,
      totalItemCount: 0,
      scrollChage: () => {
        const { scrollHeight, scrollTop, clientHeight } = state.compensationReimbursementListRef
        const scrollMoveHeight = parseInt(scrollHeight - scrollTop + '')
        const clientHeightArr = [clientHeight, clientHeight - 1, clientHeight + 1]
        if (clientHeightArr.includes(scrollMoveHeight) && state.compensationReimbursementList?.length < state.totalItemCount) {
          state.pageIndex += 1
          state.onLoad()  
        }
      },
      routerGo: (path, item) => {
        // 跳转/compensationReimbursement/costDetails
        const query = {
          itemId: item?.id || ''
        };
        proxy.$routerGoFun("routerInnPush", path, query, route.path);
      },
      onLoad: () => {
        Toast.loading({
          duration: 300000,
          message: "加载中...",
          forbidClick: true
        });
        const { pageIndex, pageSize } = state;
        if (store.state?.homeList?.dctStudyId,store.state?.homeList?.dctSiteId) {
          getPeriodicCompensationApplyAuth(store.state.homeList.dctStudyId,store.state.homeList.dctSiteId)
          .then((res) => {
            const rest = res as GetPeriodicCompensationApplyAuthInter
            state.patientAuth = rest?.patientAuth
          })
        }
        if (store.state?.homeList?.dctPatientId) {
          getCompensationAmountSummary(store.state.homeList.dctPatientId).then((rest) => {
            if (rest) {
              state.summaryObj = rest as getCompensationAmountSummaryInter
            }
          })
        }
        getPeriodicCompensationApplyList({ pageIndex, pageSize,patientId: store.state.homeList.dctPatientId, status: -1, isPatient: true })
          .then((rest) => {
            const res = rest as GetPeriodicCompensationApplyListInter
            if (pageIndex > 1) {
              state.compensationReimbursementList =
                state.compensationReimbursementList.concat(res.items);
            } else if (res?.items?.length) {
              state.compensationReimbursementList = res.items;
            }
            state.totalItemCount = res?.totalItemCount || 0;
            Toast.clear();
          })
          .catch(() => {
            Toast.clear();
          });
      },
    });
    onBeforeMount(() => {
      state.onLoad();
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.compensation-reimbursement-body {
  height: calc(100vh - 46px - 0.65rem);
  .compensation-reimbursement-header {
    height: 0.835rem;
    background-image: url(@/assets/baby/compensationReimbursement/compensationNavBg.svg);
    .border-br {
      border-right: 1px solid #85B2FF;
      border-left: 1px solid #85B2FF;
    }
  }
  .compensation-reimbursement-list-module {
    max-height: calc(100vh - 46px - 1.485rem);
  }
}
</style>
