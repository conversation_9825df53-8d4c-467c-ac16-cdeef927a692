<template>
  <div class="ft-15-rem overflow-hidden bg-color-F5F5F5"
  :style="{'height': outerHeight ? outerHeight + 'px' : '100vh'}">
    <van-nav-bar title="费用明细" left-arrow @click-left="backCostBreakdown()" />
    <div :style="{'height': outerHeight ? `calc(${outerHeight}px - 46px - 0.6rem)` : 'calc(100vh - 46px - 0.6rem)'}"
    class="overflow-auto">
      <div class="p-01rem font-color-999">当前{{editFlag ? '编辑' : '查看'}}的是第{{operationItemObjIndex+1}}行</div>
      <div class="bg-color-fff">
        <div class="p-01rem centerflex-h">
          <img class="w-008rem" src="@/assets/baby/equiredIcon.svg" alt="" />
          <div>&nbsp;科目</div>
        </div>
        <div class="p-01rem centerflex-h justify-between"
        @click="showSubjectPopup = editFlag">
          <div v-if="!operationCostBreakdownItem?.subjectName " class="ft-14-rem font-color-999">
            &nbsp;&nbsp;&nbsp;请选择
          </div>
          <div v-else class="ft-14-rem">
            &nbsp;&nbsp;&nbsp;{{
              operationCostBreakdownItem?.subjectName || ''
            }}
          </div>
          <van-icon v-if="editFlag" class="ml-01rem" name="arrow" />
        </div>
        <div class="my-hr" />
        <div class="p-01rem centerflex-h">
          <img class="w-008rem" src="@/assets/baby/equiredIcon.svg" alt="" />
          <div>&nbsp;金额（元）</div>
        </div>
        <div class="centerflex-h">
          &nbsp;&nbsp;
          <van-field
            v-model="operationCostBreakdownItem.amount"
            label=""
            maxlength="15"
            :disabled="!editFlag"
            :placeholder="editFlag ? '请输入' : ''"
            @change="amountUpNum"
          />
        </div>
        <div class="my-hr" />
        <div class="p-01rem">
          <div>&nbsp;&nbsp;&nbsp;备注</div>
        </div>
        <div class="flex">
          &nbsp;&nbsp;
          <van-field
            v-model="operationCostBreakdownItem.remark"
            label=""
            type="textarea"
            autosize
            :disabled="!editFlag"
            :placeholder="editFlag ? '请输入' : ''"
          />
        </div>
        <div class="my-hr" />
        <div class="p-01rem centerflex-h justify-between">
          <div class="ft-14-rem">
            &nbsp;&nbsp;&nbsp;附件
            <span class="font-color-999">（支持上传图片）</span>
          </div>
          <van-uploader
            v-if="editFlag"
            :before-read="beforeRead"
            :after-read="afterRead"
            accept="image/*"
            :max-size="maxSize"
            :max-count="maxCount"
          >
            <img
              src="@/assets/baby/compensationReimbursement/addImgIcon.svg"
              class="w-0175rem"
              alt=""
            />
          </van-uploader>
        </div>
        <!--上传的所有图片-->
        <div class="w-full centerflex-h-wrap px-02rem pb-02rem box-border">
          <div
            v-for="(
              item, index
            ) in operationCostBreakdownItem.attachmentEditViewModels"
            class="w-096rem h-090rem relative mb-01rem"
            :class="{ 'mx-023rem': index === 1 || index % 3 === 1 }"
            :key="index"
          >
            <img
              class="w-full h-full"
              :src="item.thumbnailUrl"
              alt=""
              @click.stop="handleImgPreview(index)"
            />
            <img
              v-if="editFlag"
              class="w-02rem absolute r-0 t-0"
              src="@/assets/baby/compensationReimbursement/deleteAttachmentIcon.svg"
              alt=""
              @click.stop="handleDeleteImg(item)"
            />
          </div>
        </div>
      </div>
    </div>
    <div v-if="editFlag" class="mt-01-rem flex ft-18-rem h-05rem">
      <div
        v-if="applySubjectsLength > 1"
        class="w-full h-full centerflex font-color-666 bg-color-fff"
        @click="handleDelete()"
      >
        删除
      </div>
      <div
        class="w-full h-full centerflex font-color-fff bg-color-5995FF"
        @click="saveCostBreakdown()"
      >
        保存
      </div>
    </div>
    <div v-if="showSubjectPopup">
      <van-popup v-model:show="showSubjectPopup" round position="bottom">
        <van-picker
          title="选择科目"
          :columns="subjectOptions"
          :columnsFieldNames="{text: 'subjectName'}"
          :default-index="defaultSubjectIndex"
          @confirm="onConfirmSubject"
          @cancel="showSubjectPopup = false"
        />
      </van-popup>
    </div>
  </div>
</template>

<script lang="ts">
import { ImagePreview, Notify, Toast } from "vant";
import {
  defineComponent,
  onBeforeMount,
  reactive,
  toRefs,
} from "vue";
import { useStore } from "vuex";
import { postCommonFile } from '@/api/compensationReimbursement';
import { CostBreakdownStateInter, PostCommonFileInter, FileAttachment } from "@/types/home";
import { getPatient } from '@/api/home';

export default defineComponent({
  name: "CostBreakdown", // 费用明细/compensationReimbursement/CostBreakdown
  props: {
    backCostBreakdown: {
      type: Function,
      default: () => {},
    },
    changeCostBreakdownItem: {
      type: Function,
      default: () => {},
    },
  },
  setup(props) {
    const store = useStore()
    const state: CostBreakdownStateInter = reactive({
      maxCount: 6,
      maxSize: "102400 * 1024",
      outerHeight: window.outerHeight,
      editFlag: false,
      showSubjectPopup: false,
      myPreview: [], // 预览图
      operationFlag: '', // 操作- 添加 编辑 只读
      operationItemObjIndex: 0,// 父组件传来的对象的下标
      defaultSubjectIndex: 0,// 默认选中的是
      applySubjectsLength: 1,// 父组件的数组长度
      operationCostBreakdownItem: {
        subjectId: '',
        subjectName: '',
        amount: '',
        remark: '',
        attachmentEditViewModels: [],
        id: '',
        applyId: '',
        compensationConfigId: '',
      },
      // 科目
      subjectOptions: [
        {
          subjectId: "1",
          subjectName: "营养费",
        },
        {
          subjectId: "2",
          subjectName: "交通费",
        },
        {
          subjectId: "3",
          subjectName: "检查费",
        },
        {
          subjectId: "4",
          subjectName: "住宿费",
        },
        {
          subjectId: "5",
          subjectName: "抽血补偿",
        },
        {
          subjectId: "6",
          subjectName: "访视补助",
        },
        {
          subjectId: "7",
          subjectName: "治疗费",
        },
        {
          subjectId: "8",
          subjectName: "误工费",
        },
        {
          subjectId: '10',
          subjectName: '药物费',
        },
        {
          subjectId: "9",
          subjectName: "其他",
        },
      ],
      amountUpNum: () => {
        const e = state.operationCostBreakdownItem.amount
        state.operationCostBreakdownItem.amount = e.replace(/[^\d.]/g, '')
        .replace(/^\./, '')
        .replace(/(\.[^\.]*)\./g, '$1')
        .replace(/^0+(\d)/, '$1')
        .replace(/^(\d+\.\d\d).*$/, '$1')
      },
      // 确认科目
      onConfirmSubject: (e) => {
        // e.subjectId e.subjectName
        state.operationCostBreakdownItem.subjectId = e.subjectId
        state.operationCostBreakdownItem.subjectName = e.subjectName
        state.showSubjectPopup = false
        state.setDefaultIndexFun()
      },
      // 图片预览
      handleImgPreview: (idx) => {
        // 获取图片原图
        const myPreview: string[] = []
        state.operationCostBreakdownItem.attachmentEditViewModels.forEach((el: FileAttachment) => {
          myPreview.push(el.fileUrl)
        })
        ImagePreview({
          images: myPreview,
          startPosition: idx,
          closeable: true,
        });
      },
      // 删除单张图片
      handleDeleteImg: (item) => {
        const attachmentEditViewModels = [...state.operationCostBreakdownItem.attachmentEditViewModels]
        attachmentEditViewModels.forEach((el,index) => {
          if (el.id === item.id) {
            state.operationCostBreakdownItem.attachmentEditViewModels.splice(index,1)
          }
        })
      },
      beforeRead: (file) => {
        // 上传之前检测图片类似返回true和false会影响到onRead函数
        const regex = /(.jpg|.jpeg|.png|.bmp|.gif|.jfif)$/;
        let beforeReadFlag = true;
        if (Array.isArray(file)) {
          file.forEach((item) => {
            if (!regex.test(item.type)) {
              Toast("图片格式不支持上传");
              beforeReadFlag = false;
            }
          });
        } else {
          if (!regex.test(file.type)) {
            Toast("图片格式不支持上传");
            beforeReadFlag = false;
          }
        }
        return beforeReadFlag;
      },
      afterRead: (file) => {
        if (file.file.size > 1048576 * 100) {
          Toast("图片大小不支持上传100MB以上的");
        } else {
          state.setAttachmentImage(file.file);
        }
      },
      // 上传图片
      setAttachmentImage: (file) => {
        if (store?.state?.homeList?.dctPatientId) {
          state.setAttachmentSaveImage(store.state.homeList.dctPatientId,file)
        } else {
          getPatient().then((res: any) => {
            state.setAttachmentSaveImage(res.dctPatientId,file)
          })
        }
      },
      setAttachmentSaveImage: (dctPatientId,file) => {
        postCommonFile(dctPatientId, 2, 1, file)
        .then((rest) => {
          const res = rest as PostCommonFileInter
          const item: any = {
            fileUrl: res.fileUrl,
            id: res.id,
            thumbnailUrl: res.thumbnailUrl,
          }
          state.operationCostBreakdownItem.attachmentEditViewModels.push(item)
        })
      },
      // 删除
      handleDelete: () => {
        if (state.operationFlag === "edit") {
          // 可以编辑时删除当前对象(下标2, 删除几个1)
          props.changeCostBreakdownItem('delete',state.operationItemObjIndex,state.operationCostBreakdownItem)
        }
        props.backCostBreakdown();
      },
      // 保存
      saveCostBreakdown: () => {
        const regex = /^\d+(\.\d{1,2})?$/;
        if (!state.operationCostBreakdownItem?.subjectId) {
          Toast(`请选择科目`);
        } else if (!state.operationCostBreakdownItem?.amount) {
          Toast(`请输入金额`);
        } else if (!regex.test(state.operationCostBreakdownItem?.amount)) {
          Toast(`请输入正确的金额`);
        } else {
          // 先存对象 push 还是编辑
          props.changeCostBreakdownItem(state.operationFlag,state.operationItemObjIndex,state.operationCostBreakdownItem)
          Notify({ type: 'success', message: '保存成功' });
          props.backCostBreakdown();
        }
      },
      // 设置初始已选中的科目
      setDefaultIndexFun: () => {
        // 匹配到已选择的index
        if (state?.operationCostBreakdownItem?.subjectId && state?.subjectOptions?.length) {
          state.subjectOptions.forEach((el,idx) => {
            if (el?.subjectId && el.subjectId === state.operationCostBreakdownItem?.subjectId) {
              state.defaultSubjectIndex = idx
            }
          });
        }
      },
      onLoad: () => {
        setTimeout(() => {
          state.outerHeight = window.outerHeight;
        }, 0);
      },
    });
    onBeforeMount(() => {
      state.onLoad();
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang="less">
:deep(.van-field__control:disabled) {
  color: #333 !important;
  -webkit-text-fill-color: #333 !important;
}
</style>