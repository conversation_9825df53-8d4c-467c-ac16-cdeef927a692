<template>
  <div
    v-show="!openDigitalSignatureDetailFlag"
    class="digitalSignaturePage-container h-[100vh] text-[0.14rem]"
  >
    <van-nav-bar
      title="数据签署"
      left-text=""
      left-arrow
      @click-left="$routerBackFun()"
    />
    <div
      ref="DigitalSignaturePageRef"
      class="pb-[0.2rem] box-border overflow-auto scrollnone"
      @scroll="signaturesListScroll"
      style="height: calc(100vh - 0.5rem - 46px)"
    >
      <div
        v-for="(item, index) in signaturesList"
        :key="index"
        class="ft-13-rem w-[92%] ml-[4%] overflow-hidden mt-[0.15rem]"
        style="border-radius: 0.06rem; box-shadow: 0 0.02rem 0.12rem rgba(0, 0, 0, 0.07);"
        @click="() => {
          if (signaturesFlag) {
            openDigitalSignatureDetails(item)
          } else {
            item.checked = !item?.checked
          }
        }"
      >
        <div class="px-[0.1rem] flex justify-between items-center bg-white">
          <img
            v-if="!signaturesFlag && item?.checked"
            class="w-015rem mr-[0.1rem]"
            src="@/assets/baby/home/<USER>"
          />
          <img
            v-else-if="!signaturesFlag"
            class="w-015rem mr-[0.1rem]"
            src="@/assets/baby/home/<USER>"
          />
          <div class="flex-1">
            <div class="my-[0.1rem] text-[#F19980]">
              <span>待签署
                <span>（提交人）</span>
                <!-- <span v-if="item?.userName">（{{item.userName}}）</span> -->
              </span>
              <span
                v-if="item?.questMideleTableStatus === 2"
                class="rounded-[0.04rem] ml-[0.05rem] ft-10-rem p-[0.04rem] bg-[#FFEDF0] text-[#E26375]"
                >已退回，需重新签署</span
              >
            </div>
            <div class="h-[0.5px] bg-[#E2E2E2] mb-[0.1rem]"></div>
            <!-- <div v-if="item?.templateType" class="wrap1 mb-[0.1rem]">
              {{templateTypes[item?.templateType]}}
            </div> -->
            <div class="wrap1 mb-[0.1rem]">
              {{item?.questTypeName}}
            </div>
            <div v-if="item?.questName" class="wrap1 mb-[0.1rem]">
              {{item?.questName}}
            </div>
            <div class="mb-[0.1rem] text-[#7f7f7f]">
              提交时间：{{item?.submitTime}}
            </div>
            <div class="mb-[0.1rem] text-[#7f7f7f]">
              提交人：{{item?.submitUserName}}
            </div>
          </div>
          <van-icon
            v-if="signaturesFlag"
            name="arrow"
            class="ml-[0.1rem]"
          />
        </div>
      </div>
      <!-- <div v-if="totalItemCount > 0 && signaturesList?.length === totalItemCount" class="item-nos mt-1">没有更多了 ~</div> -->
    </div>
    <myStateShow
      v-if="totalItemCount === 0 && signaturesList?.length === 0"
      :imgSrc="noinformedImgSrc"
      imgClass="max-w-[1.36rem]"
      texts="暂无数据"
    />
    <div v-if="signaturesList?.length" class="flex">
      <van-button
        v-if="signaturesFlag"
        type="primary"
        class="flex-1 h-[0.5rem] rounded-[0] ft-15-rem !bg-[#5860DA]"
        @click="handleSignaturesChange"
        >批量签署</van-button
      >
      <van-button
        v-if="!signaturesFlag"
        class="w-full h-[0.5rem] border-0 text-white rounded-[0] ft-15-rem bg-[#C3C3C3]"
        @click="
          () => {
            signaturesFlag = 1;
            signaturesList = []
            pageIndex = 1
            pageSize = 20
            onLoad();
          }
        "
        >取消批量操作</van-button
      >
      <van-button
        v-if="!signaturesFlag"
        class="w-full h-[0.5rem] border-[#5860da] border-[0.5px] rounded-[0] ft-15-rem theme-color"
        @click="
          () => {
            signaturesList.map((e) => (e.checked = true));
          }
        "
        >全选</van-button
      >
      <van-button
        v-if="!signaturesFlag"
        type="primary"
        class="w-full h-[0.5rem] rounded-[0] ft-15-rem !bg-[#5860DA]"
        @click="handleSignatures"
        >批量签署</van-button
      >
    </div>
  </div>
  <DigitalSignatureDetails
    v-show="openDigitalSignatureDetailFlag"
    ref="DigitalSignatureDetailsRef"
    :backFun="(flag) => {
        openDigitalSignatureDetailFlag = false
        if (flag) {
          signaturesFlag = 1
          openDigitalSignatureDetailFlag = false
          signaturesList = []
          pageIndex = 1
          pageSize = 20
          onLoad();
        }
      }"
    />
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  onBeforeMount,
  getCurrentInstance,
} from "vue";
import { useRoute } from "vue-router";
import myStateShow from "@/components/MyStateShow.vue";
import noinformedImgSrc from "@/assets/baby/noinformed.svg";
import { getTobeSignedQuest } from "@/api/digitalSignature";
import { Toast } from "vant";
import { useStore } from "vuex";
import DigitalSignatureDetails from "@/views/Home/digitalSignature/DigitalSignatureDetails.vue";
import { deepClone } from '@trialdata/common-fun-css/index';

export default defineComponent({
  name: "DigitalSignaturePage", // 数据签署
  components: {
    DigitalSignatureDetails,
    myStateShow,
  },
  setup() {
    const proxy: any = getCurrentInstance()?.proxy;
    const route = useRoute();
    const store = useStore();
    const state: any = reactive({
      //0 = 未知, 1 = 普通问卷, 2 = 不良事件, 3 = 合并用药, 4 = 入排标准, 5 = 访视日期, 6 = 初筛问卷, 7 = 随机问卷, 8 = 量表问卷, 9 = 图片问卷, 10 = 知情问卷, 11 = 服药问卷, 12 = 自定义问卷
      // templateTypes: ['',
      // '普通问卷', '不良事件', '合并用药', '入排标准', '访视日期',
      // '初筛问卷', '随机问卷', '量表问卷',
      // '图片问卷', '知情问卷', '服药问卷',
      // '自定义问卷'
      // ],
      noinformedImgSrc,
      DigitalSignaturePageRef: null,
      // 订单=> 列表
      signaturesList: [],
      totalItemCount: 0,
      pageIndex: 1,
      pageSize: 20,
      signaturesFlag: 1,
      openDigitalSignatureDetailFlag: false,
      DigitalSignatureDetailsRef: null,
      openDigitalSignatureDetails: (item) => {
        state.openDigitalSignatureDetailFlag = true;
        state.DigitalSignatureDetailsRef.signatureItem = deepClone(item)
      },
      // 点击批量
      handleSignaturesChange: () => {
        state.signaturesFlag = 0;
        state.signaturesList = []
        state.pageIndex = 1
        state.pageSize = 20
        state.onLoad();
      },
      // 批量签署
      handleSignatures: () => {
        const list = state.signaturesList.filter((e) => e?.checked);
        if (list?.length) {
          state.routerGo("/digitalSignature/signatureConfirmation", list);
        } else {
          Toast("至少选择一条数据");
        }
      },
      // 下拉加载更多
      signaturesListScroll: () => {
        const scroll = state.DigitalSignaturePageRef;
        if (
          scroll.scrollHeight / 1 - Math.ceil(scroll.scrollTop / 1) ===
          scroll.clientHeight / 1
        ) {
          // 触底
          if (state.signaturesList.length < state.totalItemCount) {
            state.pageIndex += 1;
            state.onLoad();
          }
        }
      },
      onLoad: () => {
        if (store.state?.userInformation?.dctPatientId) {
          Toast.loading({
            duration: 300000,
            message: "加载中...",
            forbidClick: true,
          });
          getTobeSignedQuest(store.state.userInformation.dctPatientId, {
            pageIndex: state.pageIndex,
            pageSize: state.pageSize,
            filter: !state.signaturesFlag,
          })
            .then((res: any) => {
              if (res?.items) {
                if (route.query?.signatureItem) {
                  state.openDigitalSignatureDetails(JSON.parse(route.query.signatureItem as string));
                }
                const oldOrderList = [...state.signaturesList];
                state.signaturesList = oldOrderList.concat(res.items);
              }
              state.totalItemCount = res.totalItemCount;
              Toast.clear();
            })
            .catch(() => {
              Toast.clear();
            });
        } else {
          setTimeout(() => {
            state.onLoad();
          }, 2000);
        }
      },
      routerGo: (path: string, list) => {
        const query = {
          list: JSON.stringify(list),
        };
        proxy.$routerGoFun(
          "routerInnPush",
          path,
          query,
          route.path,
          route.query
        );
      },
    });

    onBeforeMount(() => {
      state.onLoad();
    });

    return {
      ...toRefs(state),
    };
  },
});
</script>
