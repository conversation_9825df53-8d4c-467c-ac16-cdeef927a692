<template>
  <div
    class="overflow-hidden font-color-555 ft-15-rem"
    :style="{ height: outerHeight ? outerHeight + 'px' : '100vh' }"
  >
    <van-nav-bar title="签署" left-arrow @click-left="$routerBackFun" />
    <div
      class="w-full bg-color-F5F5F5 overflow-auto scrollnone"
      :style="{ height: `calc(${outerHeight}px - 316px - 0.94rem)` }"
    >
      <div class="mb-[0.1rem]">
        <div class="bg-color-fff px-015rem py-015rem">
          <div class="mb-01rem ft-13-rem text-[#333]">为确保本次签署的效力，请输入以下声明：</div>
          <div class="mb-01rem text-[#F19980]">{{questSigntureSet?.signatureRemark}}</div>
          <van-form class="bg-[#F3F3F3] rounded-[0.06rem]">
            <van-cell-group inset>
              <!-- :rules="[{ required: true, message: '请输入' }]" -->
              <van-field
                v-model.trim="form.remark"
                type="textarea"
                placeholder="请输入抄写内容"
                maxlength="3999"
              />
            </van-cell-group>
            <div class="pl-[0.1rem] pb-[0.1rem]">
              <div class="centerflex w-[0.8rem] h-[0.3rem] ft-13-rem bg-[#D4D6EF] text-[#5860DA] rounded-[0.2rem]"
              @click="form.remark = questSigntureSet.signatureRemark">一键输入</div>
            </div>
          </van-form>
          <div class="mt-01-rem ft-10-rem text-[#E26375]">
            <span v-if="clickFlag && form.remark !== questSigntureSet.signatureRemark">抄写内容有误，可点“一键输入”快速更正</span>
            <span v-else class="opacity-0">抄写内容有误，可点“一键输入”快速更正</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 手写板 -->
    <div>
      <div
        class="p-01rem h-04rem box-border ft-12-rem text-center font-color-F4A22E bg-color-fff"
      >
        请正楷签署您的姓名，避免错别字或过于潦草
      </div>
      <div
        v-if="outerWidth"
        class="relative"
        @touchstart="touchstartFlag = false"
      >
        <canvas ref="signatureCanvas" :width="outerWidth" height="270"></canvas>
        <span
          v-if="touchstartFlag"
          class="absolute-center ft-24-rem font-color-999 z--9"
          >请 在 此 处 签 名</span
        >
      </div>
      <div class="flex">
        <van-button
          block
          @click="clearSignature"
          class="box-border h-051rem ft-15-rem bg-color-fff border-[#5860da] border-[0.5px] theme-color"
        >
          重写
        </van-button>
        <van-button
          block
          :loading="submitLoadingFlag"
          loading-text="确认签署"
          @click="onSubmit"
          class="h-051rem border-0 rounded-none ft-15-rem !bg-[#5860DA] font-color-fff"
        >
        确认签署
        </van-button>
      </div>
    </div>
    <!-- 弹窗 -->
    <MyPopupShow
      :myPopupShow="myPopupShowFlag"
      title="提示"
      texts="
      <div class='mb-01rem ft-13-rem'>您的签名可能有错别字或过于潦草，建议正楷签署您的姓名。</div>
      "
      cancelText="重写"
      saveText="继续提交"
      cancelClass="text-black"
      saveClass="text-black"
      :handleSave="commonSubmit"
      :handleCancel="
        () => {
          myPopupShowFlag = false;
          clearSignature()
        }
      "
    />
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  onBeforeMount,
  getCurrentInstance,
  nextTick
} from "vue";
import { Toast } from "vant";
import { useStore } from "vuex";
import { useRoute } from "vue-router";
import SignaturePad from "signature_pad";
import { getQuestSigntureSet, psotValidSignaturePic } from "@/api/digitalSignature";
// import { psotQuestSigntureAndReview } from "@/api/digitalSignature";
import MyPopupShow from "@/components/MyPopupShow.vue";

export default defineComponent({
  name: "SignatureConfirmation", // 签署
  components: {
    MyPopupShow
  },
  setup() {
    const proxy: any = getCurrentInstance()?.proxy;
    const store = useStore();
    const route = useRoute();
    const state = reactive({
      patientSignatureType: 1, // 1短信验证 2 验证码 3手写
      touchstartFlag: true,
      clickFlag: false,
      signaturePad: null,
      signatureCanvas: null,
      outerWidth: window.outerWidth,
      outerHeight: window.outerHeight,
      questSigntureSet: {
        signatureRemark: "",
      },
      form: {
        remark: "",
      },
      submitLoadingFlag: false,
      caSignatureStrategyNum: -1,
      // 清空手写板->重写
      clearSignature: () => {
        state.touchstartFlag = true;
        state.signaturePad.clear();
      },
      // 点击确认签署
      onSubmit: async () => {
        state.clickFlag = true
        // 会效验签名是否能识别，不能识别则弹窗 => 是否确认签署？
        if (state.form.remark === state.questSigntureSet.signatureRemark) {
          // 获取签署的Base64数据
          const base64File = state.signaturePad.toDataURL();
          // 获取是否为空板 true = 未填写，false 已填写。
          if (!state.signaturePad.isEmpty()) {
            // 先检测签名是否能识别
            state.submitLoadingFlag = true
            // 1-患者，2-医生
            const res = await psotValidSignaturePic({
              userId: store.state.userInformation.dctPatientId,
              userType: 1,
              base64File,
            });
            if (res?.isMatched) {
              state.commonSubmit(base64File)
            } else {
              // 弹窗
              state.myPopupShowFlag = true
              state.submitLoadingFlag = false
            }
          } else {
            Toast('请签署姓名')
          }
        }
      },
      myPopupShowFlag: false,
      commonSubmit: (base64File) => {
        base64File = state.signaturePad.toDataURL()
        // 先跳转核验 手机号/人脸 通过后签署
        const lists = JSON.parse(route.query.list as string)
        const list: any = []
        lists.forEach((item: any) => {
          list.push({
            questMideleTableStatus: 3,
            rowNumber: item.rowNum,
            remark: state.form.remark,
            purposeOfQuestMiddleTable: 1,
            questId: item.questId,
          })
        })
        localStorage.setItem('signatureBase64File', base64File)
        proxy.$routerGoFun('routerInnPush', '/identityVerification', {verificationType: '17', list: JSON.stringify(list)}, route.path, route.query)
        state.myPopupShowFlag = false
        state.submitLoadingFlag = false
        // state.submitLoadingFlag = true
        // psotQuestSigntureAndReview({
        //   base64File,
        //   list
        // })
        // .then(() => {
        //   Notify({ type: "success", message: "签署成功" });
        //   proxy.$routerBackFun()
        //   state.submitLoadingFlag = false;
        // }).catch(() => {state.submitLoadingFlag = false})
      },
    });
    const handleResize = () => {
      // 处理 window.outerHeight 变化的逻辑
      state.outerHeight = window.outerHeight;
      state.outerWidth = window.outerWidth;
    };

    onBeforeMount(async () => {
      getQuestSigntureSet(store.state.userInformation.dctStudyId,{
        PurposeOfSignature: 1,
      }).then((res) => {
        if (res?.length && Array.isArray(res)) {
          const arr = res.filter((e) => e.questTemplateType === 1)
          if (arr?.length) {
            state.questSigntureSet = arr?.[0];
          }
        }
      });
      nextTick(() => {
        const canvas = state.signatureCanvas;
        if (canvas) {
          state.signaturePad = new SignaturePad(canvas, {
            minWidth: 3,
            maxWidth: 4,
            // penColor: "rgb(66, 133, 244)"
          });
          handleResize()
        }
      });
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang="less">
.van-cell {
  background: #f3f3f3;
  padding-left: 0;
}
</style>
