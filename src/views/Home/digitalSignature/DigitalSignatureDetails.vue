<template>
  <div
    class="digitalSignaturePage-container h-[100vh] text-[0.14rem] bg-[#f7f7f7]"
  >
    <van-nav-bar
      title="详情"
      left-text=""
      left-arrow
      @click-left="() => {
        signatureItem = {}
        backFun()
      }"
    />
    <div
      ref="DigitalSignatureDetailsRef"
      class="overflow-auto scrollnone"
      style="height: calc(100vh - 0.5rem - 46px)"
    >
      <!-- 问卷查看态 -->
      <CommonQuestion
        v-if="signatureItem.questId"
        ref="FollowQuestionnaireRef"
        :myQuestType="myQuestTypeText[signatureItem.templateType]"
        :postQuestFile="postQuestFile"
        :deleteQuestFile="deleteQuestFile"
        :getQuestView="getQuestViewList[signatureItem.templateType]"
        :getQuestData="getQuestDataList[signatureItem.templateType]"
        :getTableList="getTableList"
        :deleteTable="deleteTable"
        :putQuestData="putQuestData"
        :questionOnload="questionOnload"
      />
    </div>
    <van-button
      v-if="(FollowQuestionnaireRef?.questionDataObj?.questSigntrueStatus === 1 ||
      FollowQuestionnaireRef?.questionDataObj?.questSigntrueStatus === 2)
       && signatureItem?.submitUserId === dctPatientId"
      type="primary"
      class="w-full !bg-[#5860DA] !border-[#5860DA] h-[0.5rem] rounded-[0] ft-15-rem"
      @click="handleSignatures"
      >签署</van-button
    >
    <MyPopupShow
      :myPopupShow="showPopup"
      title="已退回，需重新签署"
      saveText="关闭"
      saveClass="!w-full text-black"
      :handleSave="handleClosePopup"
    >
      <template v-slot:bodyslot>
        <div
          class="text-[#333] px-03rem ft-14-rem box-border my-2 overflow-auto max-h-45vh"
        >
        原因：{{ signatureItem?.questRemark }}
        </div>
      </template>
    </MyPopupShow>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  onBeforeMount,
  getCurrentInstance,
  nextTick,
} from "vue";
import { useRoute } from "vue-router";
import MyPopupShow from '@/components/MyPopupShow.vue'
import CommonQuestion from "@/components/CommonQuestion.vue";
import {
  getQuestView, // 访视可以用到此接口
  postQuestFile,
  deleteQuestFile,
  getQuestData,
  putQuestData,
  getTableList,
  deleteTable,
  getQuestInfo
} from "@/api/questionnaire";
import { useStore } from "vuex";
//
import { getCM, getCMData } from '@/api/drugCombination'
import { getAdverseEvent, getAdverseEventData } from "@/api/discomfort";
import { getQuestICFGeneralInfo } from "@/api/informed";
import { getDurgData, getDurgView } from "@/api/takeMedicineLog";
import { deepClone } from '@trialdata/common-fun-css/index';

export default defineComponent({
  name: "DigitalSignatureDetails",
  props: {
    backFun: {
      type: Function,
      default: () => {
        //
      },
    },
  },
  components: {
    CommonQuestion,
    MyPopupShow
  },
  setup() {
    const store = useStore();
    const proxy: any = getCurrentInstance()?.proxy;
    const route = useRoute();
    const state: any = reactive({
      postQuestFile,
      deleteQuestFile,
      getQuestData,
      putQuestData,
      getTableList,
      deleteTable,
      getQuestInfo,
      // templateType:
      // 0 = 未知, 1 = 普通问卷, 2 = 不良事件,
      // 3 = 合并用药, 4 = 入排标准, 5 = 访视日期,
      // 6 = 初筛问卷, 7 = 随机问卷, 8 = 量表问卷,
      // 9 = 图片问卷, 10 = 知情问卷getQuestICFGeneralInfo, 
      // 11 = 服药问卷getDurgView, 12 = 自定义问卷
      myQuestTypeText: [
        '随访问卷','随访问卷','不适记录','其它药物','随访问卷',
        '随访问卷','随访问卷','随访问卷','随访问卷','随访问卷',
        '知情问卷','用药记录','随访问卷',
      ],
      getQuestViewList: [
        getQuestView,
        getQuestView,
        getAdverseEvent,
        getCM,
        getQuestView,
        getQuestView,
        getQuestView,
        getQuestView,
        getQuestView,
        getQuestView,
        getQuestICFGeneralInfo, 
        getDurgView,
        getQuestView,
      ],
      getQuestDataList: [
        () => {},
        getQuestData,
        getAdverseEventData,
        getCMData,
        getQuestData,
        getQuestData,
        getQuestData,
        getQuestData,
        getQuestData,
        getQuestData,
        getQuestICFGeneralInfo, 
        getDurgData,
        getQuestData,
      ],
      dctPatientId: store.state.userInformation.dctPatientId,
      FollowQuestionnaireRef: null,
      showPopup: false,
      // 批量签名
      handleSignatures: () => {
        state.routerGo("/digitalSignature/signatureConfirmation", {list: JSON.stringify([state.signatureItem])},route.path,)  ;
      },
      signatureItem: {},
      // 问卷首次加载
      questionOnload: () => {
        nextTick(async () => {
          // console.log(state.signatureItem.questTypeName);
          // state.signatureItem.questTypeName
          const QuestionnaireRef: any = state.FollowQuestionnaireRef;
          const questId = state.signatureItem.questId
          if (state.signatureItem?.questRemark && state.signatureItem?.questMideleTableStatus === 2) {
            state.showPopup = true
          }
          try {
            const res: any = await getQuestInfo(questId)
            if (state?.FollowQuestionnaireRef) {
              // QuestionnaireRef.questItem = route?.query?.questItem
              //   ? JSON.parse(route?.query?.questItem as string)
              //   : {};
              // QuestionnaireRef.visDate = route.query?.visDate as string;
              // QuestionnaireRef.visitName = route.query?.visitName as string;
              QuestionnaireRef.visitId = questId as string;
              // if (store.state?.getOldQuestDataFlag && store.state?.oldQuestData) {
              //   QuestionnaireRef.questionObj = store.state.oldQuestData;
              //   if (
              //     store?.state?.oldQuestDataIndex &&
              //     QuestionnaireRef.questionObj?.questCrfType === 2
              //   ) {
              //     QuestionnaireRef.questionIndex = store.state.oldQuestDataIndex;
              //   }
              //   QuestionnaireRef.questionObjLength =
              //     store.state.oldQuestData.questCrfItemes.length;
              //   QuestionnaireRef.fzFun(
              //     QuestionnaireRef.questionObj,
              //     store.state.initialQuestionObj
              //   );
              //   QuestionnaireRef.upListData();
              //   QuestionnaireRef.isChange = store.state?.mainQuestIsChange
              //   store.dispatch("setGetOldQuestDataFlag", 0);
              // } else
               if (questId) {
                state.FollowQuestionnaireRef.signatureItem = deepClone(state.signatureItem)
                let qId = questId
                if (state.signatureItem?.templateType === 2 || state.signatureItem?.templateType === 3) {
                  qId = state.signatureItem?.rowNum
                }
                if (res?.isTable && res?.isSubmit) {
                  await QuestionnaireRef.clearStagingFun(questId);
                  QuestionnaireRef.getQuestionnaiData(qId);
                } else {
                  QuestionnaireRef.getQuestionnaiData(qId);
                }
              }
              QuestionnaireRef.editable = "0"
            }
          } catch {
            if (QuestionnaireRef && questId) {
              QuestionnaireRef.getQuestionnaiData(questId);
            }
          }
        });
      },
      routerGo: (path: string, query) => {
        proxy.$routerGoFun(
          "routerInnPush",
          path,
          query,
          route.path,
          // {signatureItem: JSON.stringify(state.signatureItem)}
        );
      },
      handleClosePopup: () => {
        state.showPopup = false;
      },
    });

    onBeforeMount(() => {
      // state.onLoad();
    });

    return {
      ...toRefs(state),
    };
  },
});
</script>

<!-- <style lang="less" scoped>
:deep(.questionnaire-title-up) {
  display: none !important;
}
</style> -->