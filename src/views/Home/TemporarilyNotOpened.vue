<template>
  <div class="h-100-vh overflow-hidden bg-color-fff font-color-555 ft-16-rem centerflex-w-wrap">
    <img src="@/assets/baby/auditAdd.svg" class="w-80-percent" alt="" />
    <p class="w-full text-center font-bold">暂未开放</p>
    <van-button class="w-90-percent fixed b-02-rem" round type="primary" @click="$routerBackFun()">返回</van-button>
  </div>
</template>

<script lang="ts" setup>
// 暂未开放
// import { ref, } from "vue";
// const showImg = ref(false)
</script>