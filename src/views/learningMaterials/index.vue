<template>
  <div class="learningMaterials-container">
    <van-nav-bar
      title="学习资料"
      left-text=""
      left-arrow
      @click-left="$routerBackFun"
    />
    <van-dropdown-menu active-color="#5860da">
      <van-dropdown-item
        v-model="tagIds" 
        :options="tagIdList" 
        @change="() => {
          learningMaterialsList = []
          pageIndex = 1
          pageSize = 20
          onLoad()
        }"
      />
    </van-dropdown-menu>
    <div v-if="learningMaterialsList?.length" class="learningMaterials-form">
      <div v-show="!seFlag" class="learningMaterials-body">
        <!-- 表 -->
        <div
          class="learningMaterials-module scrollnone"
          ref="learningMaterialsListRef"
          @scroll="learningMaterialsListScroll"
        >
          <!-- 1 = 未完成, 2 = 已完成, 3 = 部分完成但完成关键问卷, 4 = 部分完成且未完成关键问卷, 5 = 医生要求重填 -->
          <div v-for="(item,index) in learningMaterialsList"
            :key="index"
            class="learningMaterials-items"
            @click="routerGo('/learningMaterialsInfo',item)"
            >
            <!-- 判断 -->
            <div v-if="item.finishStatus === 1" class="learningMaterials-items-nole">
              未学习
            </div>
            <div class="learningMaterials-items-le flex-1">
              <img src="@/assets/baby/studyIcon.svg" alt="" />
              <div class="flex items-center flex-1 flex-wrap">
                <p v-if="item?.fileType || item?.trainingType" class="w-full wrap2">{{item?.trianingTitle || ''}}</p>
                <p class="max-h-[0.18rem] flex flex-wrap overflow-hidden">
                  <div
                   v-for="e in item.trainingDocumentTags"
                   :key="e"
                   class="mb-[0.1rem] mr-[0.08rem] ft-10-rem py-[0.02rem] px-[0.04rem] text-[#333] bg-[#F3F3F3] wrap1"
                   style="border-radius: 0.04rem;"
                   :style="{ maxWidth: item?.trainingDocumentTags?.length < 3 ? '1rem' : '0.7rem' }"
                  >{{e}}</div>
                </p>
              </div>
            </div>
            <!-- <img v-if="item.finishStatus === 2" src="@/assets/baby/completionTaskIcon.svg"
            class="learningMaterials-items-ri-img" alt=""> -->
            <van-icon name="arrow" />
          </div>
          <div v-if="learningMaterialsList?.length === totalItemCount" class="item-nos">没有更多了 ~</div>
        </div>
      </div>
    </div>
    <div v-else class="nocases nocases-pd2">
      <!-- 自写状态组件 -->
      <myStateShow :imgSrc="noinformedImgSrc" texts="暂无数据" />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount, getCurrentInstance } from "vue";
import { useRoute } from 'vue-router';
import { getTrainingDocumentTag, getTrainings } from '@/api/learningMaterials';
import myStateShow from "@/components/MyStateShow.vue";
// 在相应的文件中通过import的方式把图片引入进来否则img标签的src属性动态绑定本地图片路径无效
import noinformedImgSrc from '@/assets/baby/noinformed.svg';
import { getTrainingsInter } from '@/types/learningMaterials';
import { useStore } from "vuex";

export default defineComponent({
  name: "LearningMaterials", // 学习资料
  components: {
    myStateShow,
  },
  setup() {
    const route = useRoute()
    const proxy: any = getCurrentInstance()?.proxy
    const store = useStore()
    const state: any = reactive({
      noinformedImgSrc,
      learningMaterialsList: [],
      learningMaterialsListRef: null,
      totalItemCount: 0,
      // 筛选Id
      tagIds: '',
      tagIdList: [
        { text: '全部', value: '' }
      ],
      pageIndex: 1,
      pageSize: 20,
      // 筛选标识
      seFlag: false,
      // 下拉加载更多
      learningMaterialsListScroll: () => {
        const scroll = state.learningMaterialsListRef
        if((scroll.scrollHeight/1) - Math.ceil(scroll.scrollTop/1) === (scroll.clientHeight/1)){
          // 触底
          if(state.learningMaterialsList.length < state.totalItemCount){
            state.pageIndex += 1
            state.onLoad()       
          }
        } 
      },
      routerGo: (path,item) => {
        if (item) {
          const query = { learningMaterialsItem: JSON.stringify(item) }
          const backQuery = state?.tagIds ? {
            tagIds: state.tagIds
          } : {}
          proxy.$routerGoFun('routerInnPush', path, query, route.path, backQuery)
        }        
      },
      onLoad: () => {
        const {
          pageIndex,
          pageSize,
          tagIds
        } = state
        getTrainings({
          pageIndex,
          pageSize,
          tagIds
        })
        .then((rest) => {
          const res = rest as getTrainingsInter
          if (res?.items) {
            const oldLearningMaterialsList = [...state.learningMaterialsList]
            state.learningMaterialsList = oldLearningMaterialsList.concat(res.items)
          }
          state.totalItemCount = res?.totalItemCount
        })
      }
    });
    onBeforeMount(() => {
      getTrainingDocumentTag(store.state.userInformation?.dctStudyId).then((res) => {
        if (Array.isArray(res) && res?.length) {
          res.forEach((item) => {
            item.value = item.id
            item.text = item.tagName
          })
          state.tagIdList = [...state.tagIdList,...res]
        }
        if (route.query?.tagIds) {
          state.tagIds = route.query.tagIds
        }
        state.onLoad()
      }).catch(() => {
        state.onLoad()
      })
    })
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.learningMaterials-container {
  height: 100vh;
  overflow: hidden;
  color: #333;
  font-size: 0.16rem;
  background: #f7f7f7;
  // .rotated-element {
  //   transform: rotate(180deg);
  //   transition: transform 0.5s ease; /* 添加过渡效果 */
  // }
  :deep(.van-ellipsis) {
    font-size: 0.13rem;
    font-weight: 400;
  }
  :deep(.van-badge__wrapper.van-icon.van-icon-success.van-dropdown-item__icon) {
    color: #5860da !important;
  }
  // :deep(.van-popup.van-popup--top.van-dropdown-item__content) {
  //   width: calc(100% - 0.2rem);
  //   margin-left: 0.1rem;
  //   border-radius: 0.06rem;
  //   overflow: hidden;
  // }
  :deep(.van-dropdown-menu__bar) {
    background: #f7f7f7;
    box-shadow: none;
    .van-dropdown-menu__item.van-haptics-feedback {
      justify-content: flex-start;
    }
    .van-ellipsis{
      color: #333;
    }
    .van-dropdown-menu__title {
      padding-left: 0.2rem;
      &::after{
        right: -8px;
        margin-top: -7px;
        border: 4px solid;
        border-color: transparent transparent #333 #333;
      }
    }
    .van-dropdown-menu__title.van-dropdown-menu__title--down.van-dropdown-menu__title--active {
      &::after{
        margin-top: -1px;
      }
    }
  }
  
  .learningMaterials-form {
    .learningMaterials-body {
      width: 100%;
      height: 100%;
      .learningMaterials-module {
        width: 100%;
        height: calc(100vh - 94px);
        overflow: auto;
        .learningMaterials-items {
          height: 0.7rem;
          padding: 0.1rem;
          box-sizing: border-box;
          margin: 0 0.2rem 0.2rem 0.2rem;
          display: flex;
          justify-content: space-between;
          align-items: center;
          background: #fff;
          border-radius: 0.1rem;
          position: relative;
          .learningMaterials-items-nole {
            position: absolute;
            font-size: 0.1rem;
            width: 0.45rem;
            height: 0.19rem;
            background-color: #C3C3C3;
            top: 0;
            left: 0;
            color: #fff;
            border-radius: 0.05rem 0 0.05rem 0;
            text-align: center;
            line-height: 0.19rem;
          }
          .learningMaterials-items-le {
            height: 100%;
            display: flex;
            div {
              height: 100%;
              display: flex;
              align-items: center;
              // padding: 0.04rem 0 0 0;
            }
            img {
              width: 0.2332rem;
              margin: 0 0.16rem 0 0;
            }
          }
          .learningMaterials-items-ri-img {
            width: 0.54rem;
            height: 0.54rem;
          }
          p{
            font-size: 0.13rem;
          }
        }
      }
    }
  }
}
</style>
