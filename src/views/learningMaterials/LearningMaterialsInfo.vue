<template>
  <div
    class="learningMaterialsInfo-container learningMaterialsInfo-pdf-container"
  >
    <div class="learningMaterialsInfo-form scrollnone">
      <van-nav-bar
        title="学习资料"
        left-arrow
        left-text=""
        @click="$routerBackFun()"
      />
      <div
        class="learningMaterialsInfo-body"
        :class="{
          'learningMaterialsInfo-pdf-body':learningMaterialsInfoIcfObj?.fileType === 3
        }"
      >
        <div
          v-if="learningMaterialsInfoIcfObj?.fileType === 3  && learningMaterialsInfoIcfObj?.trainingType === 1"
          class="learningMaterialsInfo-pdf-module"
        >
          <PdfOpen :myPdfurl="learningMaterialsInfoIcfObj.trainingFileUrl" />
          <!-- <iframe :src="learningMaterialsInfoIcfObj.trainingFileUrl" class="pdf_iframe"></iframe> -->
        </div>
        <div class="img-preview" v-if="learningMaterialsInfoIcfObj?.fileType === 1 && learningMaterialsInfoIcfObj?.trainingType === 1">
          <van-image class="w-full" @click="imgClick()" :src="learningMaterialsItem.trainingFileUrl" />
          <van-image-preview v-model:show="imgPreviewShow" :images="[learningMaterialsItem.trainingFileUrl]" :max-zoom="100" closeable />
        </div>
        <!-- 视频 -->
        <MyVideoPlay v-else-if="learningMaterialsInfoIcfObj?.fileType === 4 && learningMaterialsInfoIcfObj?.trainingType === 1 && learningMaterialsItem?.trainingFileUrl"
          :fileUrl="learningMaterialsItem.trainingFileUrl"
          :coverUrl="learningMaterialsItem.videoCoverUrl"
          :onPlaying="onPlaying"
          :onPause="onPause"
          :onEnded="onEnded"
        />
    
         <!-- 富文本 -->
         <!-- trainingType是1 的话 走 fileType的判断类型 -->
        <div v-else-if="learningMaterialsInfoIcfObj?.trainingType === 2">
          <div class="learningMaterialsInfo-container-fwb" v-if="learningMaterialsInfoIcfObj.trainingText" v-html="learningMaterialsInfoIcfObj.trainingText"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount, onBeforeUnmount, } from "vue";
import { useRoute } from "vue-router";
import PdfOpen from "@/components/PdfOpen.vue";
import { learningMaterialsInfoInter, getTrainingsDetailsInter, getTrainingsStartTimeInter } from '@/types/learningMaterials';
import { getTrainingsStartTime, getTrainingsEndTime, getTrainingsDetails} from '@/api/learningMaterials'
import MyVideoPlay from "@/components/MyVideoPlay.vue";

export default defineComponent({
  name: "LearningMaterialsInfo", // 学习资料
  components: {
    PdfOpen,
    MyVideoPlay
  },
  setup() {
    const route = useRoute();

    const state: learningMaterialsInfoInter = reactive({
      TIMEOUT: null,
      learningMaterialsInfoVideoPlayerRef: null,
      learningMaterialsInfoIcfObj: {},
      learningMaterialsItem: {},
      recordId: '',
      imgPreviewShow: false,
      imgClick: () => {
        state.imgPreviewShow = true
      },
      // 暂停回调
      onPause: () => {
        if (state.TIMEOUT) {
          clearInterval(state.TIMEOUT)
        }
      },
      // 播放完回调
      onEnded: () => {
        if (state.TIMEOUT) {
          clearInterval(state.TIMEOUT)
        }
      },
      // 已开始播放回调
      onPlaying: () => {
        state.getTrainingsStartTime()
      },
      // 结束时间
      getTrainingsEndTime: () => {
         if (state.TIMEOUT) {
          clearInterval(state.TIMEOUT)
        }
        state.TIMEOUT = setInterval(() => {
            if (state?.recordId)
            getTrainingsEndTime(state.recordId)
          }, 5000);
      },
      // 开始时间
      getTrainingsStartTime: () => {
        getTrainingsStartTime(state.learningMaterialsInfoIcfObj.id, state.learningMaterialsInfoIcfObj.ruleId)
        .then((rest) => {
          const res = rest as getTrainingsStartTimeInter
          state.recordId = res?.id || ''
          state.getTrainingsEndTime()
        })
      },
    });
    onBeforeMount(() => {
      if (route.query?.learningMaterialsItem) {
        const learningMaterialsItem = JSON.parse(route.query.learningMaterialsItem as string);
        state.learningMaterialsItem = learningMaterialsItem
        state.learningMaterialsInfoIcfObj = learningMaterialsItem;
        if (state.learningMaterialsInfoIcfObj?.trainingType === 2) {
          // 只获取富文本的值
          getTrainingsDetails(learningMaterialsItem?.id)
          .then((rest)=>{
            const res = rest as getTrainingsDetailsInter
            state.learningMaterialsInfoIcfObj.trainingText = res?.trainingText
            if (res?.trainingText) {
              state.learningMaterialsInfoIcfObj.trainingText = res?.trainingText.replace(/<img/gi, "<img class='learningMaterialsInfo-fwb-img'");
            }
          })
        }
        /*
          trainingFileUrl	string
          访问路径
          videoCoverFilePath	string
          知情视频的 封面路径
          videoCoverFileName	string
          知情视频的 封面文件名字
          videoCoverUrl	string
          知情视频的 封面URL
        */
      }
      // pdf和富文本，进入页面就开始计时
      if (((state.learningMaterialsInfoIcfObj?.fileType === 3 || state.learningMaterialsInfoIcfObj?.fileType === 1) && state.learningMaterialsInfoIcfObj?.trainingType === 1) || state.learningMaterialsInfoIcfObj?.trainingType === 2) {
        state.getTrainingsStartTime()
      }
    });
    onBeforeUnmount(() => {
      if (state.TIMEOUT) {
        clearInterval(state.TIMEOUT)
      }
    })
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
:deep(.vjs-control-bar) {
  bottom: 0.3rem;
}
:deep(.vjs-tech) {
  height: calc(100% - 0.3rem);
}
.learningMaterialsInfo-container {
  height: 100vh;
  overflow: hidden;
  .learningMaterialsInfo-form {
    .learningMaterialsInfo-body {
      width: 100%;
    }
  }
}
.learningMaterialsInfo-pdf-container {
  background: #fff;
  :deep(.pdfjs) ,
  :deep(.pdfViewer) {
    padding: 0;
  }
  .learningMaterialsInfo-form {
    margin: 0;
    .learningMaterialsInfo-pdf-body {
      margin: 0;
      .learningMaterialsInfo-pdf-module {
        margin: 0;
        padding: 0;
        box-shadow: 0 0.02rem 0.12rem rgba(0, 0, 0, 0.07); // 阴影
        overflow: hidden !important;
        :deep(.pinch-zoom-container) {
          height: calc(100vh - 44px) !important;
          max-height: calc(100vh - 44px) !important;
        }
      }
    }
  }
}
:deep(.van-button__text) {
  font-size: 0.13rem;
}
.learningMaterialsInfo-container-fwb {
  word-break:break-all;
  word-wrap:break-word;
  height: calc(100vh - 46px);
  overflow-y: scroll;
  font-size: 0.12rem;
  padding: 10px 20px;
  box-sizing: border-box;
}
:deep(.learningMaterialsInfo-fwb-img) {
  width: 100%  !important;
}
.img-preview {
  height: calc(100vh - 46px);
  overflow-y: scroll;
}
</style>
