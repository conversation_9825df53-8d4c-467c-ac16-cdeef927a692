<template></template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs, onBeforeUnmount } from "vue";
import { useRouter } from "vue-router";
import { useStore } from "vuex";

export default defineComponent({
  name: "kb1", // 人脸识别
  components: {},
  setup() {
    const router = useRouter();
    const store = useStore();
    const state: facialRecognitionInter = reactive({
        popstateFunction: () => {
            history.pushState(null, '', document.URL)
            // history.replaceState(history.state, '', url);
        },
        onLoad: () => {
            history.pushState(null, '', document.URL)
            window.addEventListener('popstate', state.popstateFunction)
            store.dispatch('setGoFrequency', 1)
            router.replace('/facialRecognition')
        },
        
    });
    onMounted(() => {
      state.onLoad();
    });
    onBeforeUnmount(() => {
      window.removeEventListener('popstate',state.popstateFunction)
    })
    return {
      ...toRefs(state),
    };
  },
});
</script>