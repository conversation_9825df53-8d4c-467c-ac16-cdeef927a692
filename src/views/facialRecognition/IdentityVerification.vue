<template>
  <div
    v-show="identityVerificationModuleFlag"
    class="identity-verification bg-white h-[100vh] t-0 w-full absolute overflow-hidden"
  >
    <div
      :style="{
        height: outerHeight ? outerHeight - 50 + 'px' : 'calc(100vh - 50px)',
        overflow: 'auto',
      }"
    >
      <van-nav-bar
        title="身份核验"
        :left-arrow="identityConfigRes?.verificationType == 17"
        @click-left="routerBack"
      />
      <div v-if="identityForm?.mode == 2">
        <div style="padding: 0.4rem 0.2rem; font-size: 0.15rem; color: #3e3e3e">
          根据临床研究相关法规及指导原则要求，本次操作需核验您的身份，我们将对个人信息严格保密。
        </div>
        <div
          style="
            text-align: center;
            font-weight: bold;
            padding: 0px 0.2rem 0.4rem 0.2rem;
            font-size: 0.2rem;
            color: #333333;
          "
        >
          您是{{ identityForm?.identity || "" }}
        </div>
        <van-form>
          <van-cell-group inset class="form-modules-body">
            <div class="mobile-inputs">
              <img src="@/assets/baby/common/name-icon.svg" alt="" />
              <van-field
                v-model.trim="identityForm.name"
                :disabled="!!identityConfigRes?.name"
                placeholder="姓名"
                maxlength="20"
                :rules="[{ required: true, message: '请填写姓名' }]"
              />
            </div>
            <div class="mobile-inputs">
              <img src="@/assets/baby/common/identityCard.svg" alt="" />
              <van-field
                v-model.trim="identityForm.identityCardNo"
                :disabled="
                  !!identityConfigRes?.name &&
                  !!identityConfigRes.identityCardNo
                "
                placeholder="身份证号"
                maxlength="20"
                :rules="[{ required: true, message: '请填写身份证号' }]"
              />
            </div>
          </van-cell-group>
        </van-form>
      </div>
      <!-- 手机验证 -->
      <div v-else>
        <div style="padding: 0.4rem 0.2rem; font-size: 0.15rem; color: #3e3e3e">
          根据临床研究相关法规及指导原则要求，本次操作需核验您的身份，我们将对个人信息严格保密。
        </div>
        <div
          style="
            text-align: center;
            font-weight: bold;
            padding: 0px 0.2rem 0.4rem 0.2rem;
            font-size: 0.2rem;
            color: #333333;
          "
        >
          您是{{ identityForm?.identity || "" }}
        </div>
        <van-form class="form-modules">
          <!-- 手机号 验证登录 left-icon="phone-circle-o"-->
          <van-cell-group inset style="position: relative">
            <div class="mobile-inputs">
              <img src="@/assets/baby/mobileIcon.svg" alt="" />
              <van-field
                v-model.trim="identityForm.mobile"
                :disabled="!!identityConfigRes.mobile"
                placeholder="手机号"
                :rules="[
                  {
                    // 自定义校验规则
                    validator: (value) => {
                      return /^[1][3-9][0-9]{9}$/.test(value);
                    },
                    message: '请输入正确的手机号',
                    trigger: 'onBlur',
                  },
                ]"
              />
            </div>
            <!-- 输入验证码 left-icon="comment-o"-->
            <div class="mobile-inputs">
              <img src="@/assets/baby/verifyCode.svg" alt="" />
              <van-field
                v-model.trim="identityForm.verifyCode"
                maxlength="6"
                placeholder="验证码"
                :rules="[{ required: true, message: '请写填验证码' }]"
              />
            </div>

            <div class="getverify" @click="getVerifycode">
              |&nbsp;&nbsp;{{ verifyTime > 59 ? "获取验证码" : verifyTime }}
            </div>
          </van-cell-group>
        </van-form>
      </div>
    </div>
    <van-button
      :loading="confirmLoadingFlag"
      loading-text="确认"
      type="primary"
      block
      class="h-[50px]"
      @click="handleConfirm"
    >
      确认
    </van-button>
  </div>
  <!-- 结果 -->
  <div
    v-show="!identityVerificationModuleFlag"
    class="facialRecognition ft-14-rem bg-color-F5F5F5"
  >
    <van-nav-bar title="身份认证结果" />
    <div>
      <div
        v-if="whetherPsaa !== 0"
        class="facialRecognition-body flex flex-col items-center overflow-auto scrollnone"
      >
        <div class="facial-img mt-095-rem">
          <img
            v-if="whetherPsaa === 1"
            src="@/assets/baby/home/<USER>"
            class="w-full"
            alt=""
          />
          <img
            v-else-if="whetherPsaa === 2"
            src="@/assets/baby/home/<USER>"
            class="w-full"
            alt=""
          />
        </div>
        <div class="ft-18-rem mt-040-rem mb-020-rem font-bold">
          <span v-if="whetherPsaa === 1">通过人脸认证</span>
          <span v-else-if="whetherPsaa === 2">未通过人脸认证</span>
        </div>
        <div v-if="whetherPsaa === 1" class="ft-15-rem">即将自动跳转</div>
        <!-- <div v-if="whetherPsaa === 2" class="ft-15-rem">请确保为 {{ certName }} 本人操作</div> -->
        <div v-if="whetherPsaa === 2" class="ft-15-rem">请确保网络环境</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  getFacialIdentityResult,
  getPatientStatus,
  getQuestResult,
  postIdentityVerificationSentVerifyCode,
  postVerifyFace,
  //  postVerifyIdentityCard,
  postVerifyMobile
} from "@/api/user";
import { returnPatientStatusUrl } from "@/utils/debounce";
import { Notify, Toast } from "vant";
import { defineComponent, getCurrentInstance, inject, onBeforeMount, reactive, toRefs } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useStore } from "vuex";
// import { Toast, Notify } from 'vant';
import { deepClone } from '@trialdata/common-fun-css/index';
import { psotQuestSigntureAndReview } from "@/api/digitalSignature";

export default defineComponent({
  name: "IdentityVerification",
  props: {
    close: {
      type: Function,
      default: () => { },
    },
  },
  setup() {
    const fireConferenceObj: any = inject('fireConferenceObj')
    const route = useRoute();
    const myreg = /^[1][3-9][0-9]{9}$/;
    const store = useStore();
    const router = useRouter();
    const proxy: any = getCurrentInstance()?.proxy
    const state: any = reactive({
      outerHeight: document.documentElement.scrollHeight,
      verifyTime: 60,
      // 1 = 手机验证码, 2 = 人脸识别
      identityConfigRes: {
        skipVerification: false,
        mode: 1,
        identity: '',
        mobile: '',
        name: '',
        identityCardNo: '',
        verificationType: 16 //  16 首次访问 17数据签署
      },
      identityForm: {
        skipVerification: false,
        mode: 1,
        identity: '',
        mobile: '',
        name: '',
        identityCardNo: '',
        verificationType: 16 //  16 首次访问 17数据签署
      },
      identityVerificationModuleFlag: 1, // 0 身份认证结果 1身份核验
      // 身份认证结果相关
      certName: '',
      whetherPsaa: 0,
      // 确认按钮loading
      confirmLoadingFlag: false,
      handleConfirm: () => {
        const bizType = route.query?.verificationType
        // 手机
        if (state.identityConfigRes.mode == 1) {
          const { mobile } = state.identityForm;
          if (!myreg.test(mobile) || !mobile) {
            Notify({ type: "danger", message: "请输入正确的手机号" });
            return;
          }
          if (!state.identityForm.verifyCode) {
            return
          }
          state.confirmLoadingFlag = true
          postVerifyMobile(store.state?.userInformation.dctStudyId,
            {
              ...state.identityForm,
              bizType
            }).then(() => {
              // 根据状态流跳转
              if (route.query?.verificationType === '16' || route.query?.verificationType === '19') {
                state.continueStatus(store.state.continueStatusObj)
              } else {
                psotQuestSigntureAndReview({
                  base64File: localStorage.getItem('signatureBase64File'),
                  list: JSON.parse(route.query.list as string)
                })
                  .then(() => {
                    Notify({ type: "success", message: "签署成功" });
                    proxy.$routerBackFun(2)
                    state.confirmLoadingFlag = false
                  }).catch(() => {
                    state.confirmLoadingFlag = false;
                    proxy.$routerBackFun()
                  })
              }

            }).catch(() => {
              state.confirmLoadingFlag = false
            })
        } else if (state.identityForm.identityCardNo && state.identityForm.name) {
          // 人脸识别
          // console.log('人脸识别');
          state.confirmLoadingFlag = true
          let returnUrl = location.href.replace(/(\?|&)serialNo=[^&]*/, '')
          returnUrl = location.href.replace(/(\?|&)list=[^&]*/, '')
          if (!returnUrl.includes('&getFacialIdentityResult=1')) {
            returnUrl += '&getFacialIdentityResult=1'
          }
          localStorage.setItem('patientRouterInnLists', JSON.stringify(store.state.routerInn))
          localStorage.setItem('identityLists', route.query.list as string)
          // route.query?.verificationType === '17'
          const dataObj = {
            name: state.identityConfigRes.name && state.identityConfigRes.identityCardNo ? '' : state.identityForm.name,
            identityCardNo: state.identityConfigRes.name && state.identityConfigRes.identityCardNo ? '' : state.identityForm.identityCardNo,
            bizType: route.query?.verificationType === '17' && store.state?.userInformation?.isFamilyMember ? 21 : bizType,
            patientId: store.state?.userInformation.dctPatientId,
            conferenceId: "",
            patientQuestId: "",
            rowNumber: "",
            returnUrl: returnUrl
          }
          // 判断是否为app入会前校验
          if (store.state?.appKey?.toLowerCase() === 'app' && route.query?.triggerEvent === '2') {
            dataObj["code"] = route.query?.code;
            dataObj["conferenceId"] = route.query?.conferenceId;
          }
          state.postVerifyFaceFun(dataObj)
        }
      },
      // 人脸识别跳入
      postVerifyFaceFun: (dataObj) => {
        postVerifyFace(store.state?.userInformation.dctStudyId, dataObj)
          .then((res) => {
            // 序列号
            // res?.businessId
            if (res?.certUrl) {
              location.replace(res.certUrl)
            }
            state.confirmLoadingFlag = false
          }).catch(() => {
            state.confirmLoadingFlag = false
          })
      },
      getVerifycode: () => {
        if (state.verifyTime / 1 < 60) return;
        const { mobile } = state.identityForm;
        if (!myreg.test(mobile) || !mobile) {
          Notify({ type: "danger", message: "请输入正确的手机号" });
          return;
        }
        state.verifyTime = 59
        postIdentityVerificationSentVerifyCode({
          mobile: state.identityForm.mobile,
        })
        const TIMEOUT = setInterval(() => {
          state.verifyTime--;
          if (state.verifyTime / 1 === 0) {
            state.verifyTime = 60;
            clearInterval(TIMEOUT);
          }
        }, 1000);
      },
      // 继续流转
      continueStatus: (continueStatusObj) => {
        if (!continueStatusObj?.res || !continueStatusObj?.token || !continueStatusObj?.myLocation) {
          continueStatusObj = JSON.parse(localStorage.getItem('continueStatusObj') as string)
        }
        continueStatusObj.myLocation.hash = decodeURIComponent(continueStatusObj.myLocation.hash)
        continueStatusObj.urls = continueStatusObj.urls ? decodeURIComponent(continueStatusObj.urls) : ''
        continueStatusObj.url = continueStatusObj.url ? decodeURIComponent(continueStatusObj.url) : ''
        state.netxFun({ ...continueStatusObj })
      },
      //
      routerBack: () => {
        proxy.$routerBackFun()
      },
      netxFun: ({
        ICFStatementId,
        res,
        token,
        urls,
        url,
        myLocation,
      }) => {
        if (ICFStatementId) {
          const { userInformation } = store.state
          if (ICFStatementId) {
            userInformation.icfStementId = ICFStatementId
            userInformation.icfStatementID = ICFStatementId
          }
          store.dispatch('setUserInformation', userInformation)
          // 获取状态请求
          getPatientStatus({ ICFStatementId })
            .then((res: any) => {
              proxy.$routerGoFun('replace', returnPatientStatusUrl(res) || url)
            })
          return
        }
        // 消息模板直接进野火
        if (myLocation.hash.includes('Conference=true')) {
          sessionStorage.removeItem('conferenceIMNum')
          // 获取哈希值
          const hash = myLocation.hash + '';
          // 去除哈希前的'#'符号
          const hashContent = hash.slice(1);
          // 解析哈希中的查询参数
          const hashParams = new URLSearchParams(hashContent);
          const serialNo = hashParams.get('serialNo');
          const conferenceId = hashParams.get('conferenceId');
          const { dctPatientId, icfStementId, patientNum, dctStudyId } = store.state.userInformation;
          let info = {
            token: res.token,
            patientId: dctPatientId,
            icfStatementID: icfStementId,
            makeReservation: false,
            host: myLocation.host,
            pathname: myLocation.pathname,
            code: sessionStorage.getItem("code"),
            documentTitle: document.title,
            patientName: '',
            patientNo: patientNum,
            studyId: dctStudyId || '',
            serialNo: serialNo || '',
            conferenceId: conferenceId || '',
          }
          // 消息模板时 - 进到会议列表 不进到详情
          // if (res.patientICFStatus === 401 && res.patientStatus === 4) {
          //   info.backUrl = '/waitExplainInformed'
          // } else {
          //   info.backUrl = url
          // }
          const userInfo = JSON.stringify(info)
          let path = `https://${location.host}/patientui/conference/unpackage/dist/build/h5/index.html#/?userInfo=${userInfo}`
          // path = `http://localhost:5173/?userInfo=${userInfo}`
          fireConferenceObj.src = path
          // location.replace(path)
          // return
        }
        // 消息模板跳转互动沟通 状态在知情 有3个页面会有
        if (urls.includes('interactivecommunication')) {
          // 使用异步回调函数获取最新状态
          if (token) {
            const routerInn = [...store.state.routerInn]
            routerInn.push({
              backQuery: null,
              backPath: url,
              path: urls,
              query: null
            })
            store.dispatch('setRouterInn', routerInn)
            router.replace(urls)
            return
          }
        }
        if (urls !== '/informedList' &&
          (res?.patientStatus === 16 ||
            res?.patientStatus === 37 ||
            res?.patientStatus === 15 ||
            res?.patientStatus === 14 ||
            res?.patientStatus === 13 ||
            res?.patientStatus === 11 ||
            res?.patientStatus === 7)) { // 不走下边了
          proxy.$routerGoFun('replace', url)
          return
        }
        // 主流程
        if (urls === '/informedList' && token && token !== 'null' && token !== 'undefined') {
          proxy.$routerGoFun('replace', urls)
        } else {
          proxy.$routerGoFun('replace', url)
        }
        // 筛选期 成功入组 待入组
        if ((res.patientStatus === 10 || res.patientStatus == 33 || res.patientStatus == 34) && urls && url !== '/informedList') {
          if ((urls !== '/' && urls !== '/home') && urls !== '/my' && urls !== '/datelog') {
            setTimeout(() => {
              // 使用异步回调函数获取最新状态
              if (store.state?.patientToken) {
                // proxy.$routerGoFun('routerInnPush', urls, '', '/')
                const routerInn = [...store.state.routerInn]
                routerInn.push({
                  backQuery: null,
                  backPath: '/',
                  path: urls,
                  query: null
                })
                store.dispatch('setRouterInn', routerInn)
                router.replace(urls)
              }
            }, 500)
          }
        }
        state.confirmLoadingFlag = false
      },
      onLoad: () => {
        // 人脸识别 结果展示
        if (route.query.getFacialIdentityResult === '1') {
          const hash = location.hash;
          // 去除哈希前的'#'符号
          const hashContent = hash.slice(1);
          // 解析哈希中的查询参数
          const hashParams = new URLSearchParams(hashContent);
          const serialNo = hashParams.get('serialNo');
          const l = localStorage.getItem('patientRouterInnLists')
          if (l) {
            const lis = JSON.parse(l)
            if (Array.isArray(lis) && lis.length > 0) {
              store.dispatch('setRouterInn', lis)
            }
          }
          getFacialIdentityResult({
            studyId: store.state?.userInformation.dctStudyId,
            businessId: serialNo
          }).then((res: any) => {
            // 判断当前是否是APP
            if (store.state?.appKey?.toLowerCase() === 'app') {
              const msgData = {
                data: {
                  action: 'facialResult',
                  payload: {
                    isSuccess: res.isSuccess,
                    isComplete: res.isComplete,
                    isExpired: res.isExpired
                  }
                }
              }
              // 如果是iOS
              if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.jumpToNative) {
                window.webkit.messageHandlers.jumpToNative.postMessage(msgData);
              }
              // 如果是非iOS环境(即uniapp、安卓)
              uni.postMessage(msgData);
              return;
            }

            state.identityVerificationModuleFlag = 0
            state.whetherPsaa = res.isSuccess ? 1 : 2
            if (route.query?.verificationType === '16' || route.query?.verificationType === '19') {
              setTimeout(() => {
                if (res.isSuccess) {
                  state.continueStatus(store.state.continueStatusObj)
                } else {
                  state.identityVerificationModuleFlag = 1
                }
              }, 2500)
            } else {
              const identityLists = localStorage.getItem('identityLists')
              // 通过后去完成签署
              localStorage.removeItem('patientRouterInnLists')
              if (res.isSuccess && identityLists) {
                const list = JSON.parse(identityLists as string)
                psotQuestSigntureAndReview({
                  base64File: localStorage.getItem('signatureBase64File'),
                  list
                })
                  .then(() => {
                    setTimeout(() => {
                      Notify({ type: "success", message: "签署成功" });
                      proxy.$routerBackFun(2)
                      localStorage.removeItem('signatureBase64File')
                      localStorage.removeItem('identityLists')
                    }, 2500)
                  }).catch(() => {
                    setTimeout(() => {
                      proxy.$routerBackFun()
                    }, 2500)
                  })
              } else {
                setTimeout(() => {
                  // digitalSignature/digitalSignaturePage
                  proxy.$routerBackFun()
                }, 2500)
              }
            }
            // isComplete是否认证完成
            // isExpired是否认证过期或失效
            // 是否认证通过isSuccess
            // businessType 0 = 未知, 1 = 物资回收, 2 = 费用补偿,
            //  3 = 余额提现, 4 = 项目款, 5 = 知情同意, 6 = 知情签名,
            //   7 = 知情声明, 8 = 身份认证, 9 = 家属银行卡, 10 = 线下知情文件,
            //    11 = 研究者入会, 12 = 受试者入会, 13 = 受试家属入会, 
            //    14 = 修改问卷澄清PDF, 15 = 广告位,
            //  16 = 受试者访问患者端, 17 = 受试者签署问卷, 18 = 研究者审阅问卷
            // bizSourceId 业务唯一ID
          })
        }
        // 签署 的身份核验
        if (route.query?.verificationType === '17') {
          getQuestResult({
            studyId: store.state?.userInformation.dctStudyId,
            bizType: store.state?.userInformation?.isFamilyMember ? 21 : 17 // 21 家属
          }).then((res: any) => {
            res.verificationType = route.query.verificationType
            state.identityConfigRes = deepClone(res)
            state.identityForm = deepClone(res)
          })
        } else {
          state.identityConfigRes = deepClone(route.query);
          state.identityForm = deepClone(route.query);
        }
        setTimeout(() => {
          state.outerHeight = document.documentElement.scrollHeight;
        }, 0);
      },
    });
    onBeforeMount(() => {
      state.onLoad();
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style lang="less" scoped>
.mobile-inputs {
  width: 100%;
  display: flex;
  align-items: center;
  background: #f7f7f7;
  height: 0.46rem;
  border-radius: 100px;
  overflow: hidden;
  margin: 0 0 0.16rem 0;
  :deep(.van-field__control) {
    font-size: 14px;
  }
  .van-field--disabled {
    :deep(.van-field__control) {
      color: #323233 !important;
      -webkit-text-fill-color: #323233 !important;
    }
  }
  img {
    width: 0.15rem;
    margin: 0.1rem 0.1rem 0.1rem 0.15rem;
  }
}
.van-cell {
  background: #f7f7f7;
}
.getverify {
  position: absolute;
  font-size: 0.14rem;
  right: 0.24rem;
  top: 0.12rem;
  color: var(--theme-color);
  display: flex;
  align-items: center;
}
</style>
