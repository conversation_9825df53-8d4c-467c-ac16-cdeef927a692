<template>
  <div class="facialRecognition ft-14-rem bg-color-F5F5F5">
    <van-nav-bar :title="myTitle" />
    <div v-if="iosShow === 0">
      <div v-if="whetherPsaa !== 0" class="facialRecognition-body flex flex-col items-center overflow-auto scrollnone">
        <div class="facial-img mt-095-rem">
          <img v-if="whetherPsaa === 1" src="@/assets/baby/home/<USER>" class="w-full" alt="">
          <img v-else-if="whetherPsaa === 2" src="@/assets/baby/home/<USER>" class="w-full" alt="">
        </div>
        <div class="ft-18-rem mt-040-rem mb-020-rem font-bold">
          <span v-if="whetherPsaa === 1">通过人脸认证</span>
          <span v-else-if="whetherPsaa === 2">未通过人脸认证</span>
        </div>
        <div v-if="whetherPsaa === 1" class="ft-15-rem">即将自动跳转</div>
        <div v-if="whetherPsaa === 2" class="ft-15-rem">请确保为 {{ certName }} 本人操作</div>
        <div v-if="whetherPsaa === 2" class="ft-15-rem">请确保网络环境</div>
      </div>
      <div v-if="whetherPsaa === 2" class="facial-btn ft-16-rem centerflex mt-01-rem h-05rem" @click="handleRecognition">
        重新认证
      </div>
    </div>
    <!-- ios版本小于14.3得页面 -->
    <div v-else-if="iosShow === 1">
      <div class="flex items-center flex-col recognition-box">
        <div class="recognition-img">
          <img class="w-full h-full" src="@/assets/baby/home/<USER>" alt="">
        </div>
        <div class="ft-18-rem font-bold">
          iOS版本过低，无法使用人脸认证
        </div>
        <div class="ft-15-rem mt-02-rem">
          请升级iOS版本至14.3以上再试
        </div>
      </div>
      <div class="flex items-center flex-col recognition-footer">
        <div class="recognition-line">目前无法升级，我确保信息真实</div>
        <div class="recognition-line" @click="nextInformedClick">跳过认证 》</div>
      </div>
    </div>
  </div>
</template>

<script lang='ts'>
import { 
  getFacialIdentity, getFacialIdentityResult,
  getFacialIdentitySkip, postUpdateICFStatus,
  getWithoutICFFacialIdentity,
  getWithoutICFFacialIdentityResult
} from '@/api/home'
import { defineComponent, onMounted, reactive, toRefs, } from 'vue';
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
// import { facialRecognitionResponse } from '@/types/facialRecognition';
import { returnPatientStatusUrl } from '@/utils/debounce';
import { Notify, Toast } from 'vant';
import { getPatientStatus } from '@/api/user';

export default defineComponent({
  name: 'FacialRecognition', // 人脸识别
  // components: {},
  setup() {
    const router = useRouter()
    const route = useRoute()
    const store = useStore()
    const state: any = reactive({
      whetherPsaa: 0,
      certName: '',
      iosShow: 0, // 下期用
      loading: false,
      myTitle: '',
      handleRecognition: async() => {
        await state.getPatientStatusFun() 
        state.result()
      },
      // 获取状态的请求
      getPatientStatusFun: () => {
        const icfStatementIdData = {
          icfStatementID:
            store.state?.userInformation?.icfStatementID ||
            store.state?.userInformation?.icfStementId,
        }
        return new Promise(async(resolve, reject) => {
          try {
            const patientStatus: any = await getPatientStatus(icfStatementIdData)
            if (patientStatus?.patientICFStatus === 404) {
              Notify({ type: 'danger', message: '已转线下知情' })
              router.replace(returnPatientStatusUrl(patientStatus));
              return
            }
            resolve(1)
          } catch (e) {
            reject(e)
          }
        })
      },
      nextInformedClick: async() => {
        await state.getPatientStatusFun() 
        // 跳过人脸
        if (state.loading) {
          return
        }
        state.loading = true
        const params = {
          withoutICF: !!store.state.userInformation?.withoutICF,
          ICFStatementId: store.state?.userInformation?.icfStatementID || store.state?.userInformation?.icfStementId
        }
        getFacialIdentitySkip(params).then((res) => {
          state.loading = false
          state.routerGo(res, 1)
        }).catch(() => {
          state.loading = false
        })
      },
      routerGo: (res, index) => {
        const url = returnPatientStatusUrl(res)
        if (url) {
          if (index === 1) {
            router.replace(url)
          } else {
            setTimeout(() => {
              router.replace(url)
            }, 2000)
          }
        }
      },
      result: () => {
        if (state.loading) {
          return
        }
        const params = {
          code: store.state.patientCode,
          ICFStatementId: store.state?.userInformation?.icfStatementID || store.state?.userInformation?.icfStementId
        }
        state.loading = true
        const getFacialIdentityResultFun = !(route.query?.ICFStatementId || route.query?.icfStatementId || store.state?.userInformation?.icfStatementID || store.state?.userInformation?.icfStementId) && store.state.userInformation?.withoutICF ? getWithoutICFFacialIdentityResult : getFacialIdentityResult
        localStorage.setItem('patientUserInformation', JSON.stringify(store.state.userInformation))
        getFacialIdentityResultFun(params).then((res: any) => {
          if (!(route.query?.ICFStatementId || route.query?.icfStatementId || store.state?.userInformation?.icfStatementID || store.state?.userInformation?.icfStementId) && store.state.userInformation?.withoutICF && res?.patientICFStatus_Other){
            res.patientICFStatus = res?.patientICFStatus_Other
          }
          state.loading = false
          state.certName = res?.certName || ''
          if (res?.patientICFStatus === 63 || res?.patientICFStatus === 61 
          || res?.patientICFStatus === 3001 || res?.patientICFStatus === 3003) {
            const show = state.verifyIOS(res, 2)
            if (show) return
            if (res?.certUrl) {
              window.location.replace(res.certUrl)
            }
          } else {
            state.routerGo(res, 0)
          }
        }).catch(() => {
          state.loading = false
        })
      },
      verifyIOS: (res, index) => {
        if ((index === 1 && res?.patientICFStatus === 61 || res?.patientICFStatus === 3001) || (index === 2 && (res?.patientICFStatus === 61 || res?.patientICFStatus === 63 || res?.patientICFStatus === 3001 || res?.patientICFStatus === 3003))) {
          const userAgent: any = navigator.userAgent
          if (userAgent && /iPhone/i.test(userAgent) && userAgent.match(/iPhone\sOS\s([\d_]+)/)[1]) {
            const str = userAgent.match(/iPhone\sOS\s([\d_]+)/)[1]
            if (Number(str.split('_')[0]) < 14 || (Number(str.split('_')[0]) === 14 && Number(str.split('_')[1]) < 3)) {
              state.myTitle = '身份认证'
              state.iosShow = 1
            }
          }
        }
        return state.iosShow === 1 || false
      },
      // 进入页面加载，写在了onMounted中
      onLoad: () => {
        const userInformationStr = localStorage.getItem('patientUserInformation')
        let userInformation = store.state.userInformation
        // 判断userInformationStr是不是JSON对象
        if (userInformationStr && !userInformation?.dctStudyId &&
          userInformationStr.startsWith('{') && userInformationStr.endsWith('}')) {
          userInformation = JSON.parse(userInformationStr)
          store.dispatch('setUserInformation',userInformation)
        }
        if (userInformation?.dctStudyId && (store?.state?.patientCode || sessionStorage.getItem("code"))) {
          if (route.query?.icfStatementId || route.query?.ICFStatementId) {
            userInformation.icfStatementID = route.query?.ICFStatementId || route.query.icfStatementId
            userInformation.icfStementId = route.query?.ICFStatementId || route.query.icfStatementId
            store.dispatch('setUserInformation',userInformation)
          }
          const getFacialIdentityFun = !(route.query?.ICFStatementId || route.query?.icfStatementId || store.state?.userInformation?.icfStatementID || store.state?.userInformation?.icfStementId) && userInformation?.withoutICF ? getWithoutICFFacialIdentity : getFacialIdentity
          if (userInformation?.withoutICF || (route.query?.ICFStatementId || route.query?.icfStatementId || store.state?.userInformation?.icfStatementID || store.state?.userInformation?.icfStementId)) {
            const params = {
              code: store?.state?.patientCode || sessionStorage.getItem("code"),
              ICFStatementId: route.query?.ICFStatementId || route.query?.icfStatementId || store.state?.userInformation?.icfStatementID || store.state?.userInformation?.icfStementId
            }
            getFacialIdentityFun(params).then((res: any) => {
              const show = state.verifyIOS(res, 1)
              if (show) return
              state.certName = res?.certName || ''
              if (!(route.query?.ICFStatementId || route.query?.icfStatementId || store.state?.userInformation?.icfStatementID || store.state?.userInformation?.icfStementId) && userInformation?.withoutICF && res?.patientICFStatus_Other){
                res.patientICFStatus = res.patientICFStatus_Other
              }
              if (res?.patientICFStatus === 61 || res?.patientICFStatus === 3001) {
                if (res?.certUrl) {
                  window.location.replace(res.certUrl)
                }
              } else if (res?.patientICFStatus === 62 || res?.patientICFStatus === 3002) {
                state.myTitle = '身份认证结果'
                state.whetherPsaa = 1
                // 已知是通过 直接流转状态跳转即可。
                if ((route.query?.ICFStatementId || route.query?.icfStatementId || store.state?.userInformation?.icfStatementID || store.state?.userInformation?.icfStementId) || !userInformation?.withoutICF) {
                  postUpdateICFStatus({
                    "icfStatementID": route.query?.ICFStatementId || route.query?.icfStatementId || store.state?.userInformation?.icfStatementID || store.state?.userInformation?.icfStementId,
                    "icfStatus": res?.patientICFStatus
                  }).then((rest) => {
                    state.routerGo(rest, 0)
                  })
                } else {
                  getWithoutICFFacialIdentityResult(params).then((rest) => {
                    state.routerGo(rest, 0)
                  })
                }
              } else if (res?.patientICFStatus === 63 || res?.patientICFStatus === 3003) {
                state.myTitle = '身份认证结果'
                state.whetherPsaa = 2
              } else {
                state.routerGo(res, 0)
              }
              localStorage.removeItem('patientUserInformation')
            })
          }
        } else {
          Toast.loading({
            duration: 300000,
            message: "加载中...",
            forbidClick: true,
          });
          setTimeout(() => {
            state.onLoad()
            Toast.clear()
          }, 2000)
        }
      }
    })
    onMounted(() => {
      state.onLoad()
    })
    return {
      ...toRefs(state)
    }
  }
})
</script>
