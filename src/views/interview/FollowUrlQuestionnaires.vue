<template>
  <div
    v-if="questionUrlSrc"
    v-show="!DataChangeModuleRef?.dataChangeModuleFlag"
    class="overflow-hidden"
    :style="{ height: outerHeight ? outerHeight + 'px' : '100vh' }"
  >
    <van-nav-bar
      v-if="myTitle !== '不适记录' && pageHide"
      title="随访内容"
      left-text=""
      left-arrow
      @click-left="$routerBackFun()"
    />
    <!-- src="./SF-36v2-English.html" src="./GDMT.html"-->
    <iframe
      src="./GDMT.html"
      id="ifrQuestion"
      name="ifrQuestion"
      ref="ifrQuestion"
      scrolling="yes"
      class="iframe w-screen"
      :class="
        myTitle === '不适记录' || !pageHide ? 'iframe-first' : 'iframe-height'
      "
    >
      <p>您的手机不支持此问卷</p>
    </iframe>
  </div>
  <!-- 数据变动 -->
  <div class="ft-16-rem">
    <DataChangeModule
      ref="DataChangeModuleRef"
      :saveDataChangeFun="saveDataChangeFun"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, getCurrentInstance, onMounted, ref, onBeforeUnmount } from 'vue';
import { useRoute } from "vue-router";
import { useStore } from "vuex";
import { putAutoPeriodicCompensationApply } from '@/api/compensationReimbursement';
import { posCheckVisit, putQuestData } from "@/api/questionnaire";
import { deepClone } from '@trialdata/common-fun-css/index';
import { Notify } from "vant";
import DataChangeModule from "@/components/DataChangeModule.vue"

export default defineComponent({
  name: "FollowUrlQuestionnaires", // 定制化url问卷
  components: {
    DataChangeModule
  },
  setup() {
    const store = useStore();
    const route = useRoute();
    const proxy: any = getCurrentInstance()?.proxy
    const state: any = reactive({
      myTitle: '',
      pageHide: true,
      type: '',
      questionUrlSrc: '',// 'http://oldstory.backgroundserver.trialdata.cn/quest2.html', // 这个可以没有, 或者放一张欢迎页? 以免后端太慢?
      ifrQuestion: ref(null),
      outerHeight: window.outerHeight,
      // 数据变动Ref
      DataChangeModuleRef: null,
      // 数据变动提交 保存后
      saveDataChangeFun: (quesDataObj) => {
        return new Promise<void>(async (resolve, reject) => {
          try {
            // if (props.myQuestType === '用药记录') {
            //   const data = {
            //     answer: quesDataObj,
            //     reocrd: state.answerQuestdata.reocrd
            //   }
            //   await props.putQuestData(quesDataObj.questId, data)
            //   const routerInnBack = { ...store.state.routerInn[store.state.routerInn.length - 1] }
            //   if (routerInnBack.backPath === '/newTakemedicineLog') {
            //     proxy.$routerBackFun()
            //   } else {
            //     proxy.$routerBackFun(2)
            //   }
            //   Notify({ type: "success", message: "提交成功" });
            //   state.submitLoadingFlag = false
            //   Toast.clear();
            // } else {
              await putQuestData(quesDataObj.questId, quesDataObj)
              resolve()
              Notify({ type: "success", message: "提交成功" });
              store.dispatch('setGetOldQuestDataFlag', 0)
              // if (props.myQuestType === '随访问卷') {
                if (store.state?.homeList?.dctPatientId) {
                  // 补偿报销申请创建 -> 访视问卷保存完调用
                  putAutoPeriodicCompensationApply(store.state.homeList.dctPatientId, quesDataObj?.questId)
                }
                if (store.state?.homeList?.patientStatus === 10) {
                  store.dispatch('setTaskText', quesDataObj.crfName)
                } else {
                  /* 筛选中的时候随访问卷保存需要调用 */
                  posCheckVisit(quesDataObj?.questId)
                }
              // }
              proxy.$routerBackFun()
            // }
          } catch {
            reject()
          }
        })
      },
    });
    // 这个是给iframe发消息, 传送一些相关信息, 应该是对应的, 比如token, visitid, crfId, patientId, doctorId等等, 这样
    // 自定义问卷可以保存数据, 访问后端等等
    const sendMessage = (msgObj) => {
      if (state.ifrQuestion) {
        // 表示当前的类型时医生还是患者, 运营端传的应该根据情况不同调整
        msgObj.sourceType = "patient"
        const msg = JSON.stringify(msgObj)
        const ifr: any = state.ifrQuestion
        const win = ifr?.contentWindow
        // let win2 : any =  state.ifrWindow
        if (win) {
          win?.postMessage(msg, "*");
        }
      }
    };

    const handleMessage = (event) => {
      if (event?.data && typeof event.data === 'string' && event.data?.includes("dataChangeModuleObj")) {
        const res = JSON.parse(event.data.split('dataChangeModuleObj=')[1])
        res.questCrfItemes.map((e) => e.oldRemarksStr = e.remarksStr)
        state.DataChangeModuleRef.dataChangeModuleObj = deepClone(res)
        state.DataChangeModuleRef.dataChangeModuleFlag = true
      } else if (event.data === "getQuestInfo") {
        // 获取问卷信息
        if (state.myTitle === '不适记录') {
          let getViewUrl = ''
          let postViewUrl = ''
          if (state.type === '编辑') {
            getViewUrl = `${window.TrialData_UI_Config.VUE_APP_API_BASE_URL}api/Patient/AdverseEvent/view`
          } else if (state.type === '新建') {
            postViewUrl = `${window.TrialData_UI_Config.VUE_APP_API_BASE_URL}api/Patient/AdverseEvent`
          }
          // 分新建还是编辑
          sendMessage({
            questId: route.query.questId,
            isEditable: 0,
            token: sessionStorage.getItem('patientToken'),
            getViewUrl: getViewUrl,
            getDataUrl: `${window.TrialData_UI_Config.VUE_APP_API_BASE_URL}api/Patient/AdverseEvent/${route.query.aeId || ' '}/Data`,
            putUrl: `${window.TrialData_UI_Config.VUE_APP_API_BASE_URL}api/Patient/AdverseEvent/${route.query.questId}/Data`,
            postViewUrl: postViewUrl,
            outerHeight: state.outerHeight
          })
        } else {
          sendMessage({
            questId: route.query.questId,
            isEditable: route.query.editable,
            token: sessionStorage.getItem('patientToken'),
            // 数据变动接口
            postDataChangeUrl: `${window.TrialData_UI_Config.VUE_APP_API_BASE_URL}api/Patient/Quest/${route.query.questId}/PatientQuestChangeData`,
            // 保存问卷接口
            putUrl: `${window.TrialData_UI_Config.VUE_APP_API_BASE_URL}api/Patient/Quest/${route.query.questId}/Data`,
            getViewUrl: `${window.TrialData_UI_Config.VUE_APP_API_BASE_URL}api/Patient/Quest/${route.query.questId}/View`,
            getDataUrl: `${window.TrialData_UI_Config.VUE_APP_API_BASE_URL}api/Patient/Quest/${route.query.questId}/Data`,
            getImgUrl: `${window.TrialData_UI_Config.VUE_APP_API_BASE_URL}api/Patient/Quest/File/${route.query.questId}`,
            deleteImgUrl: `${window.TrialData_UI_Config.VUE_APP_API_BASE_URL}api/Patient/Quest/File/`,
            outerHeight: state.outerHeight,
            dctUrlApi: window.TrialData_UI_Config?.VUE_APP_API_BASE_URL
          })
        }
      } else if (event.data === "saveQuestInfo") { // 保存成功后, url问卷会推的消息
        store.dispatch('setGetOldQuestDataFlag', 0)
        Notify({ type: "success", message: "提交成功" });
      } else if (event.data === "goLastPage") {
        // 受试者补偿需求之后不用这个了
        if (route.query?.goPath && store.state.routerInn[store.state.routerInn.length - 1]?.path !== route.query?.goPath) {
          if (state.myTitle !== '不适记录' && route.query?.questId && state.pageHide) {
            putAutoPeriodicCompensationApply(store.state.homeList.dctPatientId, route.query.questId)
          }
          proxy.$routerBackFun()
        }
      } else if (event.data === "goLastPageNew") {
        // 以后保存完调用这个接口返回
        if (state.myTitle !== '不适记录' && route.query?.questId) {
          putAutoPeriodicCompensationApply(store.state.homeList.dctPatientId, route.query.questId)
        }
        proxy.$routerBackFun()
      } else if (event.data === "previousPage") {
        proxy.$routerBackFun()
      } else if (event?.data === "pagehide") {  // 隐藏全部用的
        state.pageHide = false
      }
    };

    onMounted(() => {
      window.addEventListener("message", handleMessage); // 接受iframe的消息
      state.questionUrlSrc = route.query.questionUrl as string
      state.myTitle = route.query?.myTitle as string || ''
      state.type = route.query?.type as string || '编辑'
      setTimeout(() => {
        state.outerHeight = window.outerHeight;
      }, 0)
      // state.questionUrlSrc = 'http://oldstory.backgroundserver.trialdata.cn/quest2.html' // 测试目的
    });

    onBeforeUnmount(() => {
      window.removeEventListener("message", handleMessage)
    })

    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped>
/*暂时不删除不更改，以免以前的定制化问卷有用到这些class名*/
.iframe {
  border: 0;
}
.iframe-first {
  min-height: 100vh;
}
.iframe-height {
  min-height: calc(100vh - 46px);
}
</style>