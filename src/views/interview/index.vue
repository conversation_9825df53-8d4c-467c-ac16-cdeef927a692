<template>
  <div class="interview-container">
    <van-nav-bar
      title="您的访视任务"
      left-text=""
      left-arrow
      @click-left="$routerGoFun('replace','/')"
    />
    <div class="interview-body scrollnone">
      <div class="interview-module">
        <div class="interview-items">
          <img src="@/assets/baby/editAddressIcon.svg" alt="" />
          <div class="interview-items-title">
            <h4>上传检查单</h4>
            <p>2021-01-10 - 2021-11-10</p>
          </div>
          <img src="@/assets/baby/editAddressIcon.svg" alt="" />
        </div>
      </div>

      <div class="interview-module">
        <div class="interview-items">
          <img src="@/assets/baby/editAddressIcon.svg" alt="" />
          <div class="interview-items-title">
            <h4>xx量表</h4>
            <p>2021-01-10 - 2021-11-10</p>
          </div>
          <img src="@/assets/baby/editAddressIcon.svg" alt="" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// import { defineComponent } from "vue";
// defineComponent({ name: 'Interview' })
</script>

<style scoped lang='less'>
.interview-container {
  height: 100vh;
  overflow: hidden;
  background: #f0f0f0;
  color: #555;
  font-size: 0.14rem;
  .interview-body {
    width: 100%;
    height: 70vh;
    padding: 0.2rem;
    margin: 0.1rem 0 0 0;
    box-sizing: border-box;
    .interview-module {
      width: 100%;
      min-height: 0.9rem;
      padding: 0.1rem 0.1rem;
      margin: 0 0 0.1rem 0;
      box-sizing: border-box;
      background: #fff;
      border-radius: 0.1rem;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      .interview-items {
        display: flex;
        align-items: center;
        img {
          width: 0.5rem;
          height: 0.5rem;
        }
        .interview-items-title {
          display: flex;
          justify-content: center;
          flex-wrap: wrap;
          h4 {
            margin: 0 0 0.1rem 0;
          }
        }
      }
      h4 {
        min-width: 0.7rem;
      }
      p {
        margin: 0 0 0 0.1rem;
        word-break: break-all;
        word-wrap: break-word;
      }
    }
  }
}
</style>
