<template>
  <div class="w-full font-color-555 ft-16-rem bg-color-f7f7f7">
    <van-nav-bar
      title="随访内容"
      left-text=""
      left-arrow
      @click-left="routerBack"
    />
    <CommonQuestion
      ref="FollowQuestionnaireRef"
      :postQuestFile="postQuestFile"
      :deleteQuestFile="deleteQuestFile"
      :getQuestView="getQuestView"
      :getQuestData="getQuestData"
      :getTableList="getTableList"
      :deleteTable="deleteTable"
      :putQuestData="putQuestData"
      :questionOnload="questionOnload"
    />

    <MyPopupShow
      :myPopupShow="immediateWithdrawalFlag"
      title="提示"
      texts="
      <div class='mb-01rem ft-13-rem'>有未提交的数据，请确认。</div>
      <div class='text-red-400 ft-13-rem'>确认退出：将不会暂存未提交的数据<div>"
      cancelText="继续填写"
      saveText="确认退出"
      cancelClass="text-black"
      saveClass="text-black"
      :handleSave="handleCenterBtn"
      :handleCancel="
        () => {
          immediateWithdrawalFlag = false;
        }
      "
    />
    <!-- 不暂存并退出 -->
    <MyPopupShow
      :myPopupShow="temporaryExitFlag"
      title="提示"
      texts="
      <div class='mb-01rem ft-13-rem'>有未提交的数据，请按需操作。</div>
      <div class='text-red-400 ft-13-rem'>不暂存并退出：将不会暂存未提交的数据<div>
      <div class='text-red-400 ft-13-rem'>暂存并退出：将会暂存未提交的数据3天<div>"
      cancelText="继续填写"
      centerBtnText="不暂存并退出"
      saveText="暂存并退出"
      cancelClass="text-black"
      centerBtnClass="text-black"
      saveClass="text-black"
      :handleSave="handleSave"
      :handleCenterBtn="handleCenterBtn"
      :handleCancel="
        () => {
          temporaryExitFlag = false;
        }
      "
    />
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  nextTick,
  getCurrentInstance,
} from "vue";
import { useRoute } from "vue-router";
import { useStore } from "vuex";
import {
  getQuestView, // 访视可以用到此接口
  postQuestFile,
  deleteQuestFile,
  getQuestData,
  putQuestData,
  getTableList,
  deleteTable,
  getQuestInfo,
  getQuestUnPlannedWindow
} from "@/api/questionnaire";
import CommonQuestion from "@/components/CommonQuestion.vue";
import MyPopupShow from "@/components/MyPopupShow.vue";
import { Toast } from "vant";

export default defineComponent({
  name: "FollowQuestionnaires", // 随访内容（crf问卷）
  components: {
    CommonQuestion,
    MyPopupShow,
  },
  setup() {
    const store = useStore();
    const route = useRoute();
    const proxy: any = getCurrentInstance()?.proxy;
    const state: any = reactive({
      FollowQuestionnaireRef: null,
      // All接口
      postQuestFile,
      deleteQuestFile,
      getQuestView,
      getQuestData,
      putQuestData,
      getTableList,
      deleteTable,
      immediateWithdrawalFlag: false,
      temporaryExitFlag: false,
      editable: false, // 从接口里取的 是否可编辑
      handleSave: () => {
        state.FollowQuestionnaireRef.saveTemporaryQuestionnaire();
        proxy.$routerBackFun();
      },
      handleCenterBtn: async () => {
        try {
          Toast.loading({
            duration: 300000,
            message: "加载中...",
            forbidClick: true,
          });
          await state.FollowQuestionnaireRef.clearStagingFun();
          proxy.$routerBackFun();
          Toast.clear();
        } catch {
          proxy.$routerBackFun();
          Toast.clear();
        }
      },
      routerBack: () => {
        const questionItems = state.FollowQuestionnaireRef?.questionObj.questCrfItemes || [];
        const allListItems = store.state.initialAllListArr;

        const hasMatchingDctCode = questionItems.some(questionItem =>
          questionItem.crfFieldType === 3 &&
          questionItem.refTypesShow !== 2 &&
          questionItem?.refTypeShow !== 2 &&
          allListItems.some(allListItem =>
            allListItem.dctCode === questionItem.dctCode &&
            allListItem.children.some(childItem => childItem?.isChange)
          )
        );

        // console.log(
        //   hasMatchingDctCode,'hasMatchingDctCode',
        //   store.state.initialAllListArr,
        //   state.editable,
        //   state.FollowQuestionnaireRef?.initialQuestionObj?.isSubmit,
        //   state.FollowQuestionnaireRef?.initialIsChange,
        //   state.FollowQuestionnaireRef?.rowChange,
        //   store.state.initialAllListArr?.some(item => item.children.some(el => el?.isChange))
        // );
        // 未提交过的： 继续填写 不暂存并退出 暂存并退出。（要值更新过 才弹窗）
        if (
          state.editable &&
          (
            state.FollowQuestionnaireRef?.initialQuestionObj?.isStaging ||
            state.FollowQuestionnaireRef?.rowChange ||
            state.FollowQuestionnaireRef?.initialIsChange ||
            hasMatchingDctCode
          ) &&
          !state.FollowQuestionnaireRef?.initialQuestionObj?.isSubmit
        ) {
          state.temporaryExitFlag = true;
          return;
        } else if (
          state.editable &&
          state.FollowQuestionnaireRef?.initialQuestionObj?.isSubmit &&
          (
            state.FollowQuestionnaireRef?.initialIsChange ||
            state.FollowQuestionnaireRef?.rowChange ||
            hasMatchingDctCode
          )
        ) {
          // 提交过的： 继续填写 确认退出 （一定弹出）
          state.immediateWithdrawalFlag = true;
          return;
        }
        proxy.$routerBackFun();
      },
      // 问卷首次加载
      questionOnload: () => {
        nextTick(async () => {
          if (sessionStorage.getItem('patientToken') || store?.state?.patientToken) {
            const QuestionnaireRef: any = state.FollowQuestionnaireRef;
            try {
              const res: any = await getQuestInfo(route.query.questId)
              state.editable = res?.isEditable
              // 计划外的查看
              const unPlannedRes: any = await getQuestUnPlannedWindow(route.query.questId)
              // console.log(store.state?.getOldQuestDataFlag, QuestionnaireRef.UnplannedModificationRef);
              // QuestionnaireRef.UnplannedModificationRef.unplannedModificationModuleFlag = true
              if (unPlannedRes?.id && !(store.state?.getOldQuestDataFlag && store.state?.oldQuestData)) {
                /*
                  有计划id （注意忽略从列表问卷回来的时候）
                  1.isFeedBack  false未反馈 （isUpdateUser是修改人 ：同意 不同意 按钮） （不是修改人下一页）
                  2.isFeedBack true 已反馈 isAgree已经同意 （下一页）
                  3.isAgree false 不同意 （忽略 正常留在问卷）
                  《注意 url问卷最后还要 同意按钮 和 下一页按钮 跳转进入》
                */
                if (!unPlannedRes?.isFeedBack || (unPlannedRes?.isFeedBack && unPlannedRes?.isAgree )) {
                  QuestionnaireRef.UnplannedModificationRef.unplannedModificationModuleFlag = true
                  QuestionnaireRef.UnplannedModificationRef.unplannedModificationModuleObj = unPlannedRes
                }
              }
              if (state?.FollowQuestionnaireRef) {
                QuestionnaireRef.questItem = route?.query?.questItem
                  ? JSON.parse(route?.query?.questItem as string)
                  : {};
                QuestionnaireRef.visDate = route.query?.visDate as string;
                QuestionnaireRef.visitName = route.query?.visitName as string;
                QuestionnaireRef.visitId = route.query.questId as string;
                if (store.state?.getOldQuestDataFlag && store.state?.oldQuestData) {
                  QuestionnaireRef.questionObj = store.state.oldQuestData;
                  if (
                    store?.state?.oldQuestDataIndex &&
                    QuestionnaireRef.questionObj?.questCrfType === 2
                  ) {
                    QuestionnaireRef.questionIndex = store.state.oldQuestDataIndex;
                  }
                  QuestionnaireRef.questionObjLength =
                    store.state.oldQuestData.questCrfItemes.length;
                  QuestionnaireRef.fzFun(
                    QuestionnaireRef.questionObj,
                    store.state.initialQuestionObj
                  );
                  QuestionnaireRef.upListData();
                  QuestionnaireRef.isChange = store.state?.mainQuestIsChange
                  store.dispatch("setGetOldQuestDataFlag", 0);
                } else if (route.query.questId) {
                  if (res?.isTable && res?.isSubmit) {
                    await QuestionnaireRef.clearStagingFun(route.query.questId);
                    QuestionnaireRef.getQuestionnaiData(route.query.questId);
                  } else {
                    QuestionnaireRef.getQuestionnaiData(route.query.questId);
                  }
                }
                // 以接口取到的是否可编辑 为准 || route.query?.editable === "1" ? "1" : "0";
                QuestionnaireRef.editable = res?.isEditable ? "1" : "0"
                if (res?.visitName) {
                  QuestionnaireRef.visitName = res?.visitName;
                }
                if (res?.beginDate) {
                  QuestionnaireRef.visDate = res?.beginDate + ' ~ ' + res?.endDate
                }
              }
            } catch {
              if (QuestionnaireRef && route.query.questId) {
                QuestionnaireRef.getQuestionnaiData(route.query.questId);
              }
            }
          } else {
            setTimeout(() => {
              state.questionOnload()
            }, 500);
          }
        });
      },
    });

    return {
      ...toRefs(state),
    };
  },
});
</script>
