<template>
  <div class="otherTaskDetails-container">
    <van-nav-bar
      title="任务详情"
      left-text=""
      left-arrow
      @click-left="$routerBackFun()"
    />
    <div class="otherTaskDetails-body scrollnone">
      <div class="otherTaskDetails-header">
        <div class="otherTaskDetails-header-title none-warp-text-auto" v-html="otherTaskDetailsObj.taskName"/>
        <div class="none-warp-text-auto otherTaskDetails-auto">{{otherTaskDetailsObj.statusText}}</div>
        <div v-if="otherTaskDetailsObj?.publishDate" class="none-warp-text-auto otherTaskDetails-push">推送于{{otherTaskDetailsObj.publishDate}}</div>
      </div>
      <div class="otherTaskDetails-module">
        <div v-for="(item,index) in subTasksDetailsObj"
        :key="index"
        @click="routerGo(item)"
        class="bg-white relative question-placeAnOrder-list centerflex-h justify-between">
          <div class="w-full centerflex-h other-task-question-name">
            <img v-if="item.subtaskType === 1" src="@/assets/baby/other/freightIcon.svg" alt="">
            <img v-else-if="item.subtaskType === 2" src="@/assets/baby/other/otherTaskDetailsIcon.svg" alt="">
            <div v-if="item.subtaskType === 1 || item.subtaskType === 2" class="none-warp-text-auto">{{item.subtaskName}}</div>
          </div>
          <van-icon v-if="!otherTaskDetailsObj?.isExpired && !item?.isFinished" name="arrow" class="question-placeAnOrder-list-arrow" />
          <img v-else-if="item.isFinished" src="@/assets/baby/completionTaskIcon.svg" class="undone-or-done-img" alt="">
          <img v-else-if="otherTaskDetailsObj?.isExpired && !item.isFinished" src="@/assets/baby/other/unfinishedIcon.svg" class="undone-or-done-img" alt="">
          <div v-if="!item?.isValidPeriod && item?.isStaging && !item?.isFinished && !item?.isSubmit"
          class="absolute w-04rem h-019rem centerflex radius-0025rem font-color-9A9A9A bg-F8F8F8 ft-10-rem"
          style="right: 0px;top: 0px;border: 0.5px solid #E6E6E6"
          >暂存</div>
          <div
            v-if="item?.toBeClarified && item.subtaskType === 2"
            class="absolute centerflex ft-10-rem"
            style="top: 0px;
            left: 0;
            width: 0.48rem;
            color: #F19980;
            background-color: #FFEBE6;
            border-radius: 0.06rem 0 0.06rem 0;"
          >待澄清</div>
        </div>
        <div v-if="otherTaskDetailsObj?.taskDescription" class="mt-1 mb-10-px font-bold">说明</div>
        <div v-if="otherTaskDetailsObj?.taskDescription" v-html="otherTaskDetailsObj.taskDescription" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount, getCurrentInstance } from "vue";
import { useRoute } from "vue-router";
import { getTaskDetails, getTaskSubTasksDetails } from '@/api/tasks';
import { otherTaskDetailsStateInter, getTaskSubTasksDetailsInter, getTaskDetailsInter } from "@/types/interview";
import { Toast } from "vant";

export default defineComponent({
  name: "OtherTaskDetails", // 任务详情
  setup() {
    const route = useRoute()
    const proxy: any = getCurrentInstance()?.proxy
    const state: otherTaskDetailsStateInter = reactive({
      otherTaskDetailsObj: {
        id: '',
        taskName: '',
        taskDescription: '',
        patientId: '',
        finishedCount: 0,
        totalCount: 0,
        status: 0,
        isExpired: true,
        statusText: '',
        publishDate: ''
      },
      subTasksDetailsObj: [], // 子任务
      // 下单寄送 - 我的订单
      routerGo: (item) => {
        // 下单 已过期，未完成，不支持跳转
        // 问卷 已过期，未完成，支持查看 (进去只读)
        if (item.unPlannedWindow) {
          // 计划外继续
        } else if ((item.subtaskType === 1 && state.otherTaskDetailsObj?.isExpired && !item.isFinished) ||
        (item.subtaskType === 2 && !state.otherTaskDetailsObj?.isValidPeriod && !item?.isFinished)
        ) {
          if (item.subtaskType === 1) {
            Toast('未下单')
          } else {
            Toast('未填写')
          }
          return
        }
        let query: any = null
        let path = ''
        if (item.subtaskType === 1) {
          /* 新建下单寄送
          ①未过期，未完成，支持创建订单；
          ②未过期，已完成，跳转订单列表；
          ③已过期，未完成，不支持跳转订单列表；
          ④已过期，已完成，跳转订单列表；
          */
          if (item.isFinished) {
            path = '/my/myorder'
          } else if (!state.otherTaskDetailsObj?.isExpired && !item.isFinished) {
            path = '/placeanorder'
            query = {
              taskId: route.query?.taskId as string || ''
            }
          }
        } else if (item.subtaskType === 2) {
          /* 问卷
          ①未过期，未完成/已完成，支持编辑；
          ②已过期，未完成，不支持查看；
          ③已过期，已完成，支持查看；
          */
          path = '/other/otherQuestionnaires'
          query = {
            editable: '0',
            questId: item.associatedNumber // 问卷ID
          }
          if (!state.otherTaskDetailsObj?.isExpired) {
            query.editable = '1'
          } else {
            query.editable = '0'
          }
        } else {
          return
        }
        const backQuery = route.query
        backQuery.questId = ''
        proxy.$routerGoFun('routerInnPush', path, query, route.path, backQuery)
      },
      onLoad: () => {
        if (route.query?.taskId) {
          getTaskSubTasksDetails(route.query.taskId)
            .then((res) => {
              if (route.query?.questId && Array.isArray(res) && res?.length) {
                for (let i = 0; i < res.length; i++) {
                  if (res[i].associatedNumber === route.query?.questId) {
                    state.routerGo(res[i]);
                    break;
                  }
                }
              }
              state.subTasksDetailsObj = res as getTaskSubTasksDetailsInter[]
            })
          getTaskDetails(route.query.taskId)
          .then((res) => {
            state.otherTaskDetailsObj = res as getTaskDetailsInter
          })
        }
      },
    });
    onBeforeMount(() => {
      state.onLoad()
    })
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.otherTaskDetails-container {
  height: 100vh;
  overflow: hidden;
  color: #333;
  font-size: 0.13rem;
  .otherTaskDetails-body {
    width: 100%;
    // height: calc(100vh - 113.5px);
    height: calc(100vh - 46px);
    overflow: auto;
    .otherTaskDetails-header {
      padding: 15px 10px;
      box-sizing: border-box;
      color: #fff;
      // 渐变常用
      background: linear-gradient(
        180deg,
        #3FA1FC,
        #5860DA 100%
      );
      overflow: hidden;
      .otherTaskDetails-header-title {
        font-size: 0.15rem;
        margin: 0 0 0.1rem 0;
      }
      .otherTaskDetails-auto {
        margin: 0 0 0.1rem 0;
      }
      .otherTaskDetails-push {
        font-size: 0.12rem;
        text-align: right;
      }
    }
    .otherTaskDetails-module {
      min-height: calc(100% - 95px);
      padding: 20px 10px;
      box-sizing: border-box;
      background: #F7F7F7;
      .question-placeAnOrder-list {
        margin: 0 0 0.2rem 0;
        padding: 0.23rem 0.15rem;
        box-sizing: border-box;
        border-radius: 0.04rem;
        .other-task-question-name {
          width: calc(100% - 0.32rem);
        }
        .question-placeAnOrder-list-arrow {
          margin: 0.02rem 0 0 0;
        }
        .undone-or-done-img {
          width: 0.28rem;
        }
        .other-task-question-name {
          img {
            width: 0.25rem;
            margin: 0 0.2rem 0 0;
          }
        }
      }
    }
  }
}
</style>
