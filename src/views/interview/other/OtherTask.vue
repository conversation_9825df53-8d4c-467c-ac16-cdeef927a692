<template>
  <div class="otherTask-container">
    <van-nav-bar
      title="其它任务"
      left-text=""
      left-arrow
      @click-left="$routerBackFun()"
    />
    <div class="otherTask-body">
      <div ref="otherTaskRef" class="otherTask-module scrollnone"
      @scroll="scrollChage">
        <div class="otherTask-items-box"
          v-for="(item,index) in otherTaskList"
          :key="index"
          @click="routerGo('/other/otherTaskDetails',item)">
          <div class="otherTask-items">
            <div class="otherTask-items-le centerflex-h">
              <div class="other-img-module centerflex">
                <img src="@/assets/baby/home/<USER>" alt="" />
              </div>
              <div>
                <h4 class="none-warp-text-auto">{{item?.taskName || ''}}</h4>
                <p :class="(item?.status === 1 || item?.status === 2) && !item.isExpired ? 'otherTask-color' : ''" class="wrap2">{{item?.statusText || ''}}</p>
              </div>
            </div>
            <img v-if="item?.status === 3" src="@/assets/baby/completionTaskIcon.svg" 
            class="otherTask-items-ri-img" alt="">
            <van-icon v-else name="arrow" />
          </div>
          <div v-if="item?.publishDate" class="push-writing">
            推送于{{ item.publishDate || ''}}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount, getCurrentInstance } from 'vue';
import { useRoute } from 'vue-router';
import { getTasks } from '@/api/tasks';
import { Toast } from 'vant';
import { otherTaskStateInter, getTasksInter } from '@/types/interview';

export default defineComponent({
  name: "OtherTask", // 其他任务
  setup() {
    const route = useRoute()
    const proxy: any = getCurrentInstance()?.proxy
    const state: otherTaskStateInter = reactive({
      otherTaskRef: null,
      otherTaskList: [],
      pageIndex: 1,
      pageSize: 20,
      totalItemCount: 0,
      routerGo: (path: string,item) => {
        if (item) {
          const query = { taskId: item.id }
          proxy.$routerGoFun('routerInnPush', path, query, route.path)
        }
      },
      scrollChage: () => {
        const scroll = state.otherTaskRef
        const scrollHeight = parseInt(scroll.scrollHeight - scroll.scrollTop)
        if( scrollHeight === scroll.clientHeight || scrollHeight === scroll.clientHeight + 1 || scrollHeight === scroll.clientHeight - 1) {
          // 触底
          if(state.otherTaskList.length < state.totalItemCount){
            state.pageIndex += 1
            state.onLoad({pageIndex:state.pageIndex,pageSize:state.pageSize})
          }
        } 
      },
      onLoad: (params) => {
        Toast.loading({
          duration: 300000,
          message: '加载中...',
          forbidClick: true,
        });
        getTasks(params).then((rest) => {
          const res = rest as getTasksInter
          if (params.pageIndex > 1) {
            state.otherTaskList = state.otherTaskList.concat(res.items)
          } else if(res?.items) {
            state.otherTaskList = res.items
          }
          state.totalItemCount = res?.totalItemCount || 0
          Toast.clear()
         }).catch(() => { Toast.clear() })
      }
    });
    onBeforeMount(() => {
      const { pageIndex, pageSize} = state
      state.onLoad({pageIndex,pageSize})
    })
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.otherTask-container {
  height: 100vh;
  overflow: hidden;
  color: #333;
  font-size: 0.13rem;
  .otherTask-body {
    padding: 0.25rem 0;
    box-sizing: border-box;
    height: calc(100vh - 46px);
    .otherTask-module {
      height: 100%;
      overflow: auto;
      .otherTask-items-box {
        margin: 0 0.16rem 0.15rem;
        .push-writing {
          margin-top: 0.05rem;
          font-size: 0.11rem;
          text-align: right;
          color: #999;
        }
      }
      .otherTask-items {
        height: 0.9rem;
        padding: 0.1rem;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #fff;
        border-radius: 0.1rem;
        box-shadow: 0 0.02rem 0.12rem rgba(0, 0, 0, 0.07); // 阴影
        .otherTask-items-le {
          .other-img-module{
            width: 0.5rem;
            height: 0.5rem;
            margin: 0 0.1rem 0 0;
            background: rgba(167, 185, 239, 1);
            border-radius: 0.1rem;
            img {
              width: 0.2315rem;
              height: 0.2315rem;
            }
          }
          h4 {
            margin: 0 0 0.1rem 0;
          }
          p {
            color: #767676;
            font-size: 0.11rem;
          }
          .otherTask-color {
            color: #E5B119;
          }
        }
        .otherTask-items-ri-img {
          width: 0.54rem;
          height: 0.54rem;
        }
      }
    }
  }
}
</style>
