<template>
  <div class="other-questionnaires-container">
    <van-nav-bar
      :title="myTitle"
      left-text=""
      left-arrow
      @click-left="routerBack"
    />
    <CommonQuestion
      ref="otherQuestionnairesRef"
      :postQuestFile="postQuestFile"
      :deleteQuestFile="deleteQuestFile"
      :getQuestView="getQuestView"
      :getQuestData="getQuestData"
      :getTableList="getTableList"
      :deleteTable="deleteTable"
      :putQuestData="putQuestData"
      :questionOnload="questionOnload"
    />
    <MyPopupShow
      :myPopupShow="immediateWithdrawalFlag"
      title="提示"
      texts="
      <div class='mb-01rem ft-13-rem'>有未提交的数据，请确认。</div>
      <div class='text-red-400 ft-13-rem'>确认退出：将不会暂存未提交的数据<div>"
      cancelText="继续填写"
      saveText="确认退出"
      cancelClass="text-black"
      saveClass="text-black"
      :handleSave="handleCenterBtn"
      :handleCancel="
        () => {
          immediateWithdrawalFlag = false;
        }
      "
    />
    <!-- 不暂存并退出 -->
    <MyPopupShow
      :myPopupShow="temporaryExitFlag"
      title="提示"
      texts="
      <div class='mb-01rem ft-13-rem'>有未提交的数据，请按需操作。</div>
      <div class='text-red-400 ft-13-rem'>不暂存并退出：将不会暂存未提交的数据<div>
      <div class='text-red-400 ft-13-rem'>暂存并退出：将会暂存未提交的数据3天<div>"
      cancelText="继续填写"
      centerBtnText="不暂存并退出"
      saveText="暂存并退出"
      cancelClass="text-black"
      centerBtnClass="text-black"
      saveClass="text-black"
      :handleSave="handleSave"
      :handleCenterBtn="handleCenterBtn"
      :handleCancel="
        () => {
          temporaryExitFlag = false;
        }
      "
    />
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  nextTick,
  getCurrentInstance,
} from "vue";
import { useRoute } from "vue-router";
import { useStore } from "vuex";
import {
  getQuestView,
  postQuestFile,
  deleteQuestFile,
  getQuestData,
  putQuestData,
  getTableList,
  deleteTable,
  getQuestInfo,
  getQuestUnPlannedWindow,
} from "@/api/questionnaire";
import CommonQuestion from "@/components/CommonQuestion.vue";
// import { commonQuestionnaireInter } from '@/types/welcomeToJoinUs';
import MyPopupShow from "@/components/MyPopupShow.vue";
import { Toast } from "vant";

export default defineComponent({
  name: "OtherQuestionnaires", // 自定义任务问卷
  components: {
    CommonQuestion,
    MyPopupShow,
  },
  setup() {
    const store = useStore();
    const route = useRoute();
    const proxy: any = getCurrentInstance()?.proxy;
    const state: any = reactive({
      otherQuestionnairesRef: null,
      myTitle: "编辑",
      // All接口
      postQuestFile,
      deleteQuestFile,
      getQuestView,
      getQuestData,
      putQuestData,
      getTableList,
      deleteTable,
      immediateWithdrawalFlag: false,
      temporaryExitFlag: false,
      editable: false,
      handleSave: () => {
        state.otherQuestionnairesRef.saveTemporaryQuestionnaire();
        proxy.$routerBackFun();
      },
      handleCenterBtn: async () => {
        try {
          Toast.loading({
            duration: 300000,
            message: "加载中...",
            forbidClick: true,
          });
          await state.otherQuestionnairesRef.clearStagingFun();
          proxy.$routerBackFun();
          Toast.clear();
        } catch {
          proxy.$routerBackFun();
          Toast.clear();
        }
      },
      routerBack: () => {
        // console.log(state.otherQuestionnairesRef?.editable,'state.otherQuestionnairesRef?.editable')
        if (state.otherQuestionnairesRef?.editable !== "1") {
          proxy.$routerBackFun();
          return;
        }
        // 未提交过的： 继续填写 不暂存并退出 暂存并退出。（要值更新过 才弹窗）
        if (
          (state.editable || route?.query?.editable === "1") &&
          (state.otherQuestionnairesRef?.initialQuestionObj?.isStaging ||
            state.otherQuestionnairesRef?.rowChange ||
            state.otherQuestionnairesRef?.initialIsChange ||
            store.state.initialListArr?.find((item) => item.isChange)) &&
          !state.otherQuestionnairesRef?.initialQuestionObj?.isSubmit
        ) {
          state.temporaryExitFlag = true;
          return;
        } else if (
          (state.editable || route?.query?.editable === "1") &&
          state.otherQuestionnairesRef?.initialQuestionObj?.isSubmit &&
          (state.otherQuestionnairesRef?.initialIsChange ||
            state.otherQuestionnairesRef?.rowChange ||
            store.state.initialListArr?.find((item) => item.isChange))
        ) {
          // 提交过的： 继续填写 确认退出 （一定弹出）
          state.immediateWithdrawalFlag = true;
          return;
        }
        proxy.$routerBackFun();
      },
      // 问卷首次加载
      questionOnload: () => {
        nextTick(async () => {
          if (
            sessionStorage.getItem("patientToken") ||
            store?.state?.patientToken
          ) {
            const QuestionnaireRef: any = state.otherQuestionnairesRef;
            try {
              if (state?.otherQuestionnairesRef) {
                const res: any = await getQuestInfo(route.query.questId);
                if (res?.isOffline) {
                  proxy.$routerBackFun();
                  return;
                }
                state.editable = res?.isEditable;
                state.myTitle = res?.isEditable ? "编辑" : "查看";
                QuestionnaireRef.editable = res?.isEditable ? "1" : "0";
                QuestionnaireRef.questItem = route?.query?.questItem
                  ? JSON.parse(route?.query?.questItem as string)
                  : {};
                QuestionnaireRef.otherQuestionnaires = "1";
                QuestionnaireRef.visitId = route.query.questId as string;
                if (
                  store.state?.getOldQuestDataFlag &&
                  store.state?.oldQuestData
                ) {
                  QuestionnaireRef.questionObj = store.state.oldQuestData;
                  if (
                    store?.state?.oldQuestDataIndex &&
                    QuestionnaireRef.questionObj?.questCrfType === 2
                  ) {
                    QuestionnaireRef.questionIndex =
                      store.state.oldQuestDataIndex;
                  }
                  QuestionnaireRef.questionObjLength =
                    store.state.oldQuestData.questCrfItemes.length;
                  QuestionnaireRef.fzFun(
                    QuestionnaireRef.questionObj,
                    store.state.initialQuestionObj
                  );
                  QuestionnaireRef.upListData();
                  QuestionnaireRef.isChange = store.state?.mainQuestIsChange;
                  store.dispatch("setGetOldQuestDataFlag", 0);
                } else if (route.query?.questId) {
                  const unPlannedRes: any = await getQuestUnPlannedWindow(
                    route.query.questId
                  );
                  if (
                    unPlannedRes?.id &&
                    !(
                      store.state?.getOldQuestDataFlag &&
                      store.state?.oldQuestData
                    )
                  ) {
                    if (
                      !unPlannedRes?.isFeedBack ||
                      (unPlannedRes?.isFeedBack && unPlannedRes?.isAgree)
                    ) {
                      QuestionnaireRef.UnplannedModificationRef.unplannedModificationModuleFlag =
                        true;
                      QuestionnaireRef.UnplannedModificationRef.unplannedModificationModuleObj =
                        unPlannedRes;
                    }
                  }
                  //（如果没有列表问卷其实可以不清空）
                  if (res?.isTable && res?.isSubmit) {
                    await QuestionnaireRef.clearStagingFun(route.query.questId);
                    QuestionnaireRef.getQuestionnaiData(route.query.questId);
                  } else {
                    QuestionnaireRef.getQuestionnaiData(route.query.questId);
                  }
                }
              }
            } catch {
              if (QuestionnaireRef && route.query.questId) {
                QuestionnaireRef.getQuestionnaiData(route.query.questId);
              }
            }
          } else {
            setTimeout(() => {
              state.questionOnload();
            }, 500);
          }
        });
      },
    });

    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang="less">
.other-questionnaires-container {
  font-size: 0.16rem;
  color: #555;
  background: #f7f7f7;
}
</style>
