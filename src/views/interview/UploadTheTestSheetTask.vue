<template>
  <div class="UploadTheTestSheetTask-container">
    <van-nav-bar
      title="拍照上传"
      left-text=""
      left-arrow
      @click-left="$routerBackFun()"
    />
    <div class="UploadTheTestSheetTask-body">
      <div
        v-if="questionnairesTaskList?.length"
        ref="uploadTheTestSheetTaskRef"
        class="UploadTheTestSheetTask-module scrollnone"
        @scroll="scrollChage"
      >
        <div v-for="(taskItem,taskIndex) in questionnairesTaskList"
        :key="taskIndex">
          <div v-if="taskItem?.visitName && taskItem?.patientQuests?.length" v-html="taskItem.visitName" class="theme-color none-warp-text-auto mx-1"/>
          <!-- finishStatus 1 = 未完成, 2 = 已完成, 3 = 部分完成但完成关键问卷, 4 = 部分完成且未完成关键问卷, 5 = 医生要求重填 6 = 提前开始 -->
          <div 
            v-for="(item,index) in taskItem.patientQuests"
            :key="index"
            class="UploadTheTestSheetTask-items relative" 
            @click="routerGo(item)"
            >
            <div class="UploadTheTestSheetTask-items-le centerflex-h">
              <img src="@/assets/baby/addPictureIcon.svg" alt="" />
              <div>
                <h4 v-if="item?.questName" v-html="item.questName" class="wrap1"/>
                <p v-if="item?.finishStatusText" v-html="item.finishStatusText" class="wrap2"/>
              </div>
            </div>
            <img v-if="item.finishStatus === 2" src="@/assets/baby/completionTaskIcon.svg" 
            class="UploadTheTestSheetTask-items-ri-img" alt="">
            <van-icon v-else name="arrow" />
            <div v-if="item?.isOffline && item?.isStaging && item?.finishStatus !== 2 && !item?.isSubmit"
            class="absolute w-04rem h-019rem centerflex radius-0025rem font-color-9A9A9A bg-F8F8F8 ft-10-rem"
            style="right: 0px;top: 0px;border: 0.5px solid #E6E6E6"
            >暂存</div>
            <div
              v-if="item?.isSubmit"
              class="absolute flex"
              style="left: -0.1rem;top: 0px;"
            >
            <!-- <div
              v-if="item?.reviewStatus === 1"
              class="ml-01rem centerflex ft-10-rem w-045rem h-019rem"
              style="color: #41B592;
              border: 0.5px #41B592 solid;
              background-color: #EFFFFB;
              border-radius: 0.02rem;"
            >已审阅</div> -->
            <div
              v-if="item?.toBeClarified"
              class="ml-01rem centerflex ft-10-rem"
              style="
              width: 0.48rem;
              color: #F19980;
              background-color: #FFEBE6;
              border-radius: 0.06rem 0 0.06rem 0;"
            >待澄清</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onBeforeMount, reactive, toRefs, getCurrentInstance } from "vue";
import { useRoute } from "vue-router";
import { getVisits } from '@/api/tasks'
import { Toast } from 'vant';
import { uploadTheTestSheetTaskStateInter, questionnairesTaskListInter } from "@/types/interview";

export default defineComponent({
  name: "UploadTheTestSheetTask", // 拍照上传任务

  setup() {
    const route = useRoute()
    const proxy: any = getCurrentInstance()?.proxy
    const state: uploadTheTestSheetTaskStateInter = reactive({
      questionnairesTaskList: [],
      uploadTheTestSheetTaskRef: null,
      pageIndex: 1,
      pageSize: 20,
      totalItemCount: 0,
      routerGo: (item) => {
        if (item) {
          if (item?.isOffline) {
            Toast('无法查看，该任务在线下完成')
            return
          }
          let path = '/followquestionnaires'
          // 如果是url问卷, 把url替换掉成另外一个组件.
          // if (item.questDisplayType === 3) {
          //   path = '/followurlquestionnaires'
          // }
          const query = { questId: item.questId, editable: '1', questionUrl: item.questionUrl }
          proxy.$routerGoFun('routerInnPush', path, query, route.path)
        }
      },
      scrollChage: () => {
        const scroll = state.uploadTheTestSheetTaskRef
        const scrollHeight = Math.floor(scroll.scrollHeight - scroll.scrollTop)
        if (scrollHeight === scroll.clientHeight || scrollHeight === scroll.clientHeight + 1 || scrollHeight === scroll.clientHeight - 1) {
          // 触底
          if (state.questionnairesTaskList.length < state.totalItemCount) {
            state.pageIndex += 1
            state.onLoad()
          }
        } 
      },
      onLoad: () => {
        Toast.loading({
          duration: 300000,
          message: '加载中...',
          forbidClick: true,
        });
        const { pageIndex, pageSize } = state
        getVisits({ pageIndex, pageSize, questContentType: 2 })
          .then((rest) => {
            const res = rest as questionnairesTaskListInter
            if (pageIndex > 1) {
              state.questionnairesTaskList = state.questionnairesTaskList.concat(res.items)
            } else if (res?.items?.length) {
              state.questionnairesTaskList = res.items
            }
            state.totalItemCount = res?.totalItemCount || 0
            Toast.clear()
          }).catch(() => { Toast.clear() })
      }
    });
    
    onBeforeMount(() => {
      state.onLoad()
    })
    
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.UploadTheTestSheetTask-container {
  height: 100vh;
  overflow: hidden;
  color: #333;
  font-size: 0.16rem;
  .UploadTheTestSheetTask-body {
    padding: 0.25rem 0;
    box-sizing: border-box;
    height: calc(100vh - 46px);
    .UploadTheTestSheetTask-module {
      height: 100%;
      overflow: auto;
      .UploadTheTestSheetTask-items {
        height: 0.9rem;
        padding: 0.1rem;
        box-sizing: border-box;
        margin: 0.16rem 0.15rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #fff;
        border-radius: 0.06rem;
        box-shadow: 0 0.02rem 0.12rem rgba(0, 0, 0, 0.07); // 阴影
        .UploadTheTestSheetTask-items-le {
          h4 {
            font-size: 0.13rem;
            margin: 0 0 0.1rem 0;
          }
          p {
            color: #767676;
            font-size: 0.11rem;
          }
          img {
            width: 0.4rem;
            height: 0.4rem;
            margin: 0 0.1rem 0 0;
          }
        }
        .UploadTheTestSheetTask-items-ri-img {
          width: 0.54rem;
          height: 0.54rem;
        }
        p {
          font-size: 0.13rem;
        }
      }
    }
  }
}
</style>
