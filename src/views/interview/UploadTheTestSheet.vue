<template>
  <div class="uploadsheet-container">
    <van-nav-bar
      title="拍照上传"
      left-text=""
      left-arrow
      @click-left="routerGo('/')"
    />
    <div class="uploadsheet-title">
      <div style="width: 50%">请上传</div>
      <img src="@/assets/baby/backQuestionnaire.svg" alt="" />
    </div>

    <div class="uploadsheet-form scrollnone">
      <div class="uploadsheet-body">
        <div class="uploadsheet-module">
          <div v-if="showUploadFalg" class="uploadsheet-items">
             <UploadImg 
                :visitId="visitId"
                :postImgs="postVisitFile"
                :deleteImg="deleteVisitFile"
                :dctCodeItem="dctCodeItem"
                :disabledSaveFalg="disabledSaveFalg"
                />
          </div>
        </div>
      </div>
    </div>
    <!-- 完成按钮 -->
    <van-button
      class="btn" 
      round type="primary"
      :disabled="disabledSaveFalg.disabledSaveFalg"
      @click="routerGo('/')"
    >
      完成
    </van-button>
  </div>
</template>

<script lang="ts">
import { defineComponent, onBeforeMount, reactive, toRefs } from "vue";
import { useRouter } from "vue-router";
import { useStore } from "vuex";
import { getVisitFile,postVisitFile, deleteVisitFile } from "@/api/tasks";
import UploadImg from '@/components/UploadImg.vue';
import { Toast } from "vant";

export default defineComponent({
  name: "UploadTheTestSheet", //拍照任务
 components: {
    UploadImg
  },
  setup() {
    const router = useRouter();
    const store = useStore();
    const state = reactive({
      postVisitFile, 
      deleteVisitFile,
      visitId: '',
      showUploadFalg: false,
      // 获取到的图片列表
      dctCodeItem: {
        fileList: []
      },
      disabledSaveFalg: {
        disabledSaveFalg: false,
      },
      routerGo: (path: string) => {
        if (state.disabledSaveFalg.disabledSaveFalg) {
          return
        }
        // 完成时
        getVisitFile(state.visitId).then((res: any) => {
          if (res?.length) {
            store.dispatch('setTaskText','拍照上传')
            router.replace(path);
          } else {
            Toast('请上传图片~')
            return
          }
        });
      },
    });
    onBeforeMount(() => {
      state.visitId = store.state.homeList.currentVisitId
      getVisitFile(state.visitId).then((res: any) => {
        res.forEach((item) => {
          item.url = item.thumbUrl;
        });
        state.dctCodeItem.fileList = res; // thumbUrl
        state.showUploadFalg = true // 请求完成 再显示组件
      });
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.uploadsheet-container {
  height: 96vh;
  overflow: hidden;
  color: #333;
  font-size: 0.16rem;
  .uploadsheet-title {
    height: 1.26rem;
    display: flex;
    justify-content: space-around;
    align-items: center;
    flex-wrap: wrap;
    font-size: 0.15rem;
    background: linear-gradient(180deg, #3fa1fc, #5860da);
    color: #fff;
    img {
      width: 1.32rem;
    }
  }
  .btn {
    width: 90%;
    position: fixed;
    left: 0.2rem;
    bottom: 0.4rem;
  }
  //
  .uploadsheet-form {
    .uploadsheet-body {
      width: 100%;
      // padding: 0 0.1rem;
      box-sizing: border-box;
      .uploadsheet-module {
        width: 100%;
        display: flex;
        justify-content: center;
        width: 100%;
        padding: 0 0.1rem;
        box-sizing: border-box;
        .uploadsheet-items {
          width: 100%;
          height: 3.52rem;
          padding: 21px 0.1rem 11px;
          box-sizing: border-box;
          margin: 0.2rem 0 0 0;
          background: #fff;
          border-radius: 0.1rem;
          box-shadow: 0 0.02rem 0.26rem rgba(0, 0, 0, 0.07); // 阴影
          overflow: auto;
        }
      }
    }
  }
}
</style>
