<template>
  <div class="orderDetails-container">
    <van-nav-bar
      title="订单详情"
      left-text=""
      left-arrow
      @click-left="$routerBackFun()"
    />
    <div class="orderDetails-status centerflex-h justify-between"
    :class="{'canceled': orderDetailsObj?.state === 4}">
      <!-- 已下单/待取件 配送中 已签收 已取消 -->
      <div>
        <span>{{ orderDetailsObj?.orderStateDisplay || '' }}</span>
      </div>
      <div v-if="orderDetailsObj?.state === 3 && orderDetailsObj?.signTime">签收时间：{{orderDetailsObj?.signTime || ''}}</div>
      <div v-else-if="orderDetailsObj?.state === 4 && orderDetailsObj?.lastUpdateTimeDisplay">取消时间：{{orderDetailsObj.lastUpdateTimeDisplay}}</div>
    </div>
    <div class="orderDetails-body scrollnone">
      <div class="orderDetails-module">
        <div class="orderDetails-module-infos">创建时间：{{orderDetailsObj.createTimeDisplay}}</div>
        <div class="orderDetails-module-infos">订单编号：{{orderDetailsObj.orderNo}}</div>
        <div class="orderDetails-module-infos">快递单号：{{orderDetailsObj?.waybillNo || '尚未生成'}}</div>
        <div v-if="routeOrderItem?.orderType === 1" class="orderDetails-module-infos">物品信息：{{orderDetailsObj?.goodsTypeName || ''}}</div>
        <div v-else-if="routeOrderItem?.orderType === 2" class="orderDetails-module-infos">物流公司：{{orderDetailsObj?.logisticsProvider || '尚未生成'}}</div>
      </div>
      <!-- 寄收 信息 -->
      <div v-if="routeOrderItem?.orderType === 1" class="orderDetails-module">
        <div class="orderDetails-title">
          <div class="flex">
            <img src="@/assets/baby/sendIcon.svg" alt="">
            <div>
              <div class="centerflex-h">
                <h4 v-html="orderDetailsObj.shipperName"/>
                <div v-html="orderDetailsObj.shipperPhoneNo" class="orderDetails-title-info-text"/>
              </div>
              <div class="mt-5-px orderDetails-title-info-text">
                {{orderDetailsObj.shipperProvinceName+orderDetailsObj.shipperCityName+orderDetailsObj.shipperAreaName+orderDetailsObj.shipperAddressDetail}}
              </div>
            </div>
          </div>
        </div>
        <div style="marginTop: 20px"/>
        <div class="orderDetails-title">
          <div class="flex">
            <img src="@/assets/baby/collectIcon.svg" alt="">
            <div>
              <div class="centerflex-h">
                <h4 v-html="orderDetailsObj.rcptName"/>
                <div v-html="orderDetailsObj.rcptPhoneNo" class="orderDetails-title-info-text"/>
              </div>
              <div class="mt-5-px orderDetails-title-info-text">
                {{orderDetailsObj.rcptProvinceName+orderDetailsObj.rcptCityName+orderDetailsObj.rcptAreaName+orderDetailsObj.rcptAddressDetail}}
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 寄件方式 -->
      <div v-if="routeOrderItem?.orderType === 1" class="orderDetails-module">
        <div class="orderDetails-module-infos">寄件方式：{{orderDetailsObj.shippingMethod}}</div>
        <div class="orderDetails-module-infos">期望上门时间：{{orderDetailsObj.expectPickupTimeDisplay}}</div>
      </div>
      <!-- 物资信息 -->
      <div v-if="routeOrderItem?.orderType === 2 && orderDetailsObj?.materials?.length">
        <div v-for="(item, index) in orderDetailsObj.materials" :key="index"
          class="good-message centerflex-h" 
          :class="orderDetailsObj.materials?.length === (index+1) ? '' : 'good-message-bb'">
          <div class="good-message-img">
            <img class="w-full" v-if="item.materialPic" :src="item.materialPic" alt="">
            <img class="w-full" v-else src="@/assets/baby/materialsIcon.svg" alt="">
          </div>
          <div class="good-message-box">
            <div class="good-message-name wrap2">
              {{ item.materialName }}
            </div>
            <div class="good-message-pic flex justify-between">
              <span>{{ item.materialSpecs }}</span>
              <span v-if="item?.number > 0">x{{ item.number }}</span>
            </div>
            <div class="mt-01-rem good-message-pic">
              <div v-for="(e,idx) in item.itemList" :key="idx"
              class="wrap1"
              >{{ e?.materialNumber || '' }}</div>
            </div>
          </div>
        </div>
      </div>
      <!-- 物资-收件人信息 -->
      <div v-if="routeOrderItem?.orderType === 2" class="mt-01-rem">
        <div class="placeanorder-module">
          <div class="placeanorder-title">
            <img src="@/assets/baby/collectIcon.svg" alt="">
            <h4>收件人信息</h4>
          </div>
          <van-cell-group inset>
            <van-field v-model="orderDetailsObj.rcptName" readonly label="联系人" />
          </van-cell-group>
          <van-divider />
          <van-cell-group inset>
            <van-field v-model="orderDetailsObj.rcptPhoneNo" readonly label="联系电话" />
          </van-cell-group>
          <van-divider />
          <!-- 地区 is-link箭头是否需要 @click="showCitys = true"-->
          <van-field
            v-model="orderDetailsObj.cityCollectValue"
            readonly
            label="所在地区"
            placeholder=""
          />
          <!-- 详细地址 -->
          <van-cell-group inset>
            <van-field
              readonly
              v-model="orderDetailsObj.rcptAddressDetail"
              name="详细地址"
              label="详细地址"
              placeholder=""
              type="textarea"
              autosize
            />
          </van-cell-group>
          <van-divider />
        </div>
      </div>
      <div class="orderDetails-module">
        <div class="orderDetails-module-infos">备注：
          <span v-if="orderDetailsObj?.remark" v-html="orderDetailsObj.remark"/>
        </div>
      </div>
      <!-- 底部按钮 -->
      <div class="bottom-save-btn">
        <!-- <van-button v-if="orderDetailsObj?.state === 0 || orderDetailsObj?.state === 1" class="save-btn" round type="primary" @click="savePlaceAnOrderForm('/placeanorder')"
        >修改订单</van-button> -->
        <van-button v-if="orderDetailsObj?.state === 2 || orderDetailsObj?.state === 3" class="save-btn" round type="primary" @click="savePlaceAnOrderForm('/my/logisticsdetails')"
        >运单详情</van-button>
        <van-button v-else-if="orderDetailsObj?.state === 0 || orderDetailsObj?.state === 1" class="save-btn ml-16-px" round type="danger" @click="savePlaceAnOrderForm('cancel')"
        >取消订单</van-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onMounted, getCurrentInstance, } from 'vue';
import { useRoute } from "vue-router";
import { Dialog, Toast } from 'vant';
import { getLogisticsOrder, putCancelLogisticsOrder, putCancelMaterialOrderOrder, getMaterialOrder } from '@/api/myOrder';
import { orderDetailsStateInter } from '@/types/my';

export default defineComponent({
  name: "OrderDetails", // 订单详情
  setup() {
    const route = useRoute()
    const proxy: any = getCurrentInstance()?.proxy
    const state: orderDetailsStateInter = reactive({
      routeOrderItem: {
        orderType: 0,
      },
      orderDetailsObj: {
        id: '',
        orderNo: '',
        waybillNo: '',
        creator: '',
        createTime: '',
        createTimeDisplay: '',
        lastUpdator: '',
        lastUpdateTime: '',
        lastUpdateTimeDisplay: '',
        state: -1,
        // 0 = 已下单, 1 = 待取件, 2 = 配送中, 3 = 已签收, 4 = 已取消
        waybillRecords: [],
        patientId: '', // 患者编号
        dctStudyId: '',
        dctSiteId: '',
        omsProjectId: '',
        customTaskId: '', // 自定义任务Id
        goodsTypeName: '',
        logisticsProvider: '', // 物流供应商名称
        shipperName: '',// 发件人姓名
        shipperPhoneNo: '', // 发件人手机号
        shipperProvinceCode: '',
        shipperProvinceName: '',
        shipperCityCode: '',
        shipperCityName: '',
        shipperAreaCode: '',
        shipperAreaName: '',
        shipperAddressDetail: '',
        rcptName: '',// 收件人
        rcptPhoneNo: '',// 收件人手机号
        rcptProvinceCode: '',
        rcptProvinceName: '',
        rcptCityCode: '',
        rcptCityName: '',
        rcptAreaCode: '',
        rcptAreaName: '',
        rcptAddressDetail: '',
        shippingMethod: '',// 寄件方式
        expectPickupTime: '',// 期望上门取件时间
        expectPickupTimeDisplay: '',
        remark: '',
        cityCollectValue: '',
        materials: [],
        signTime: '',
        orderStateDisplay: '',
      },
      // 提交订单
      savePlaceAnOrderForm: (path: string) => {
        let query = {}
        if (path === 'cancel') {
          Dialog.confirm({
            title: '提示',
            confirmButtonColor: '#333',
            message: `是否确认取消订单？`
          })
            .then(() => {
              const putCancelFun = state.routeOrderItem?.orderType === 1 ? putCancelLogisticsOrder : putCancelMaterialOrderOrder
              putCancelFun(state.orderDetailsObj.id)
                .then(() => {
                  Toast('取消成功')
                  state.onLoad()
                })
              // 订单状态已变更，取消失败
            })
            .catch(() => {
              // on cancel
            });
          return
        } else if (path === '/my/logisticsdetails' || path === '/placeanorder') {
          query = {
            orderItem: route.query.orderItem
          }
        }
        proxy.$routerGoFun('routerInnPush', path, query, route.path, route.query)
      },
      onLoad: () => {
        if (route.query?.orderItem) {
          const params = JSON.parse(route.query.orderItem as string)
          state.routeOrderItem = params
          //  1 = 物流订单, 2 = 物资订单
          if (params.orderType === 1 && params?.id) {
            getLogisticsOrder(params.id)
              .then((res: any) => {
                state.orderDetailsObj = res
              })
          } else if (params.orderType === 2 && params?.id) {
            getMaterialOrder(params.id)
              .then((res: any) => {
                state.orderDetailsObj = res
                state.orderDetailsObj.cityCollectValue = (res?.rcptProvinceName || '') + (res?.rcptCityName || '') + (res?.rcptAreaName || '')
              })
          }
        }
      },
    });
    onMounted(() => {
      state.onLoad()
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.orderDetails-container {
  height: 100vh;
  overflow: hidden;
  background: #f0f0f0;
  color: #555;
  font-size: 0.13rem;
  .orderDetails-status {
    padding: 0 10px;
    box-sizing: border-box;
    height: 42px;
    color: #fff;
    background: var(--theme-color);
  }
  .canceled {
    background: #9A9A9A;
  }
  .orderDetails-body {
    width: 100%;
    height: calc(100vh - 154px);
    overflow: auto;
    padding: 0.1rem;
    margin: 0.1rem 0 0 0;
    box-sizing: border-box;
    .orderDetails-module {
      width: 100%;
      padding: 0.1rem 0.1rem;
      margin: 0 0 0.1rem 0;
      box-sizing: border-box;
      background: #fff;
      border-radius: 0.1rem;
      .orderDetails-module-infos {
        margin: 0.1rem 0;
        font-size: 0.13rem;
        color: #333;
      }
      .van-divider {
        margin: 0 0.2rem 0 0.16rem;
        width: 90%;
      }
      img {
        width: 0.35rem;
        height: 0.35rem;
        margin: 0 0.15rem 0 0;
      }
      .orderDetails-title {
        margin: 0 0 0.1rem 0;
        h4 {
          margin: 0 0.1rem 0 0;
        }
        .orderDetails-title-info-text {
          color:#6A6A6A;
        }
      }
    }
    // 底部 按钮
    .bottom-save-btn {
      width: 100%;
      height: 66px;
      padding: 0 0.15rem;
      box-sizing: border-box;
      display: flex;
      justify-content: end;
      align-items: center;
      background: #fff;
      position: fixed;
      left: 0;
      bottom: 0;
      .save-btn {
        width: 1.09rem;
        height: 0.35rem;
      }
    }
    // 物资信息
    .good-message-bb {
      border-bottom: 0.5px solid #e5e5e5;
    }
    .good-message {
      margin: 0 0 0.01rem 0;
      padding: 0.2rem 0.15rem 0.2rem 0.1rem;
      box-sizing: border-box;
      border-radius: 0.1rem;
      background: #fff;
      .good-message-img {
        margin-right: 0.15rem;
        width: 0.4rem;
        height: 0.4rem;
      }
      .good-message-box {
        flex: 1;
        .good-message-name {
          margin-bottom: 0.1rem;
          max-height: 0.32rem;
        }
        .good-message-pic {
          color: #666;
        }
      }
    }
  }
}
</style>
