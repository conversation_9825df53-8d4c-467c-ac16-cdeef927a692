<template>
  <div class="myaddress-container">
    <van-nav-bar
      title="我的地址"
      left-text=""
      left-arrow
      @click-left="$routerBackFun()"
    />
    <div class="myaddress-body">
      <div class="myaddress-module">
        <div class="myaddress-title centerflex-h justify-between">
          <h4>我的地址</h4>
          <img
            src="@/assets/baby/editAddressIcon.svg"
            alt=""
            @click="routerGo('/my/editaddress')"
          />
        </div>
        <van-cell-group inset>
          <van-field
            v-model.trim="myAddressForm.name"
            label-class="label-text"
            :disabled="disabled"
            name="姓名:"
            label="姓名:"
          />
        </van-cell-group>
        <van-divider />
        <van-cell-group inset>
          <van-field
            v-model.trim="myAddressForm.mobile"
            label-class="label-text"
            :disabled="disabled"
            name="手机号:"
            label="手机号:"
          />
        </van-cell-group>
        <van-divider />
        <!-- 地区 -->
        <van-cell-group inset>
          <van-field
            v-model.trim="myAddressForm.cityValue"
            label-class="label-text"
            :disabled="disabled"
            name="省市区:"
            label="省市区:"
          />
        </van-cell-group>
        <van-divider />
        <!-- 详细地址 -->
        <van-cell-group inset>
          <van-field
            v-model.trim="myAddressForm.addressDetail"
            label-class="label-text"
            :disabled="disabled"
            type="textarea"
            name="详细地址:"
            label="详细地址:"
            autosize
          />
        </van-cell-group>
        <van-divider />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount, getCurrentInstance } from 'vue';
import { getMyAddress } from '@/api/my'

export default defineComponent({
  name: "MyAddress", // 我的地址
  setup() {
    const proxy: any = getCurrentInstance()?.proxy
    const state = reactive({
      disabled: true,
      myAddressForm: {
        cityValue: '', // 城市
        province: '',
        city: '',
        area: '',
        name: '',
        mobile: '',
        addressDetail: ''
      },
      routerGo: (path: string) => {
        proxy.$routerGoFun('routerInnPush', path, '', '/my/myaddress',)
      },
    });
    onBeforeMount(() => {
      getMyAddress()
        .then((res) => {
          if (Array.isArray(res) && res.length) {
            state.myAddressForm = res[0]
            state.myAddressForm.cityValue = `${res[0]?.province || ''} ${res[0]?.city || ''} ${res[0]?.area || ''}`
          }
        })
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.myaddress-container {
  height: 100vh;
  color: #555;
  font-size: 0.14rem;
  overflow: hidden;
  background: #f0f0f0;
  .myaddress-body {
    height: 80vh;
    padding: 0.1rem;
    margin: 0.25rem 0 0 0;
    box-sizing: border-box;
    .myaddress-module {
      padding: 0.1rem;
      box-sizing: border-box;
      border-radius: 0.1rem;
      background: #fff;
      .myaddress-title {
        margin: 0 0 0.2rem 0;
        h4 {
          font-size: 0.13rem;
        }
        img {
          width: 0.16rem;
          height: 0.16rem;
        }
      }
      .van-divider {
        width: 90%;
        margin: 0 0.2rem 0 0.16rem;
      }
      :deep(.van-cell-group,
      van-cell-group--inset) {
        padding: 0;
        margin: 0;
      }
      :deep(.van-field__control:disabled) {
        -webkit-text-fill-color: #000;
        color: #000;
      }
      :deep(.van-cell__title,
      .van-field__label,
      .label-text) {
        width: 0.6rem;
        -webkit-text-fill-color: #000;
        color: #000;
      }
    }
  }
}
</style>
