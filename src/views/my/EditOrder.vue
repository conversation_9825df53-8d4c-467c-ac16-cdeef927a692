<template>
  <div class="placeanorder-container">
    <van-nav-bar
      title="样本寄送"
      left-text=""
      left-arrow
      @click-left="routerGo('/')"
    />
    <div class="placeanorder-form scrollnone">
      <div class="placeanorder-body">
        <div class="placeanorder-module">
          <!-- 寄件人地址 -->
          <div class="placeanorder-title">
            <img src="@/assets/baby/sendIcon.svg" alt="" />
            <h4>寄件人地址</h4>
          </div>
          <van-cell-group inset>
            <van-field v-model.trim="sendForm.userName" :disabled="disabled" label="姓名" />
          </van-cell-group>
          <van-divider />
          <van-cell-group inset>
            <van-field v-model.trim="sendForm.mobile" :disabled="disabled" label="联系方式" />
          </van-cell-group>
          <van-divider />
          <!-- 地区 -->
          <van-cell-group inset>
            <van-field v-model.trim="sendForm.cityValue" :disabled="disabled" label="省市区" />
          </van-cell-group>
          <van-divider />
          <!-- 详细地址 -->
          <van-cell-group inset>
            <van-field
              type="textarea"
              v-model.trim="sendForm.addressSpecific"
              :disabled="disabled"
              name="详细地址"
              label="详细地址"
              autosize
            />
          </van-cell-group>
        </div>
        <!-- 收件人地址 -->
        <div class="placeanorder-module">
          <div class="placeanorder-title">
            <img src="@/assets/baby/collectIcon.svg" alt="" />
            <h4>收件人地址</h4>
          </div>

          <van-cell-group inset>
            <van-field v-model.trim="collectForm.userName" label="姓名" :disabled="disabled" />
          </van-cell-group>
          <van-divider />
          <van-cell-group inset>
            <van-field v-model.trim="collectForm.mobile" label="联系方式" :disabled="disabled" />
          </van-cell-group>
          <van-divider />
          <!-- 地区 -->
          <van-cell-group inset>
            <van-field v-model.trim="collectForm.cityValue" label="省市区" :disabled="disabled" />
          </van-cell-group>
          <van-divider />
          <!-- 详细地址 -->
          <van-cell-group inset>
            <van-field
              type="textarea"
              v-model.trim="collectForm.addressSpecific"
              :disabled="disabled"
              name="详细地址"
              label="详细地址"
              autosize
            />
          </van-cell-group>
        </div>
        <!-- 寄件方式 -->
        <div class="placeanorder-mail-module">
          <div class="mail">
            <div class="placeanorder-mail-title">寄件方式</div>
           <!-- 勾选寄送方式 noCheckedIcon-->
            <div v-for="(item, index) in mailList"
              :key="index"
              class="placeanorder-mail-checked">
              <img v-if="index === mailActiveIndex" src="@/assets/baby/ckIcon.jpg" alt="">
              <img v-else src="@/assets/baby/noCheckedIcon.svg" alt="">
              <p v-html="item.types" />
            </div> 
          </div>
          <van-divider />
          <div class="calldate">
            <div class="placeanorder-mail-title">期望上门时间</div>
            <p>{{myOrderList.expectTime}}</p>
          </div>
          <van-divider />
          <div class="calldate">
            <div class="placeanorder-mail-title">物品信息</div>
            <van-field
              v-model.trim="myOrderList.remarks"
              :disabled="disabled"
              rows="2"
              autosize
              show-word-limit
            />
          </div>
          <van-divider style="margin: 0" />
        </div>
        <!-- 底部按钮 -->
        <div class="bottom-save-btn">
          <van-button class="btn" round type="primary" @click="routerGo('/')"
            >取消订单</van-button
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount } from "vue";
import { useRouter } from "vue-router";
import { Notify, Dialog } from "vant";
export default defineComponent({
  name: "EditOrder",
  setup() {
    const router = useRouter();
    const state = reactive({
      //寄
      sendForm: {
        userName: "",
        mobile: "",
        cityValue: "", //城市
        addressSpecific: "", //详细地址
      },
      //收
      collectForm: {
        userName: "",
        mobile: "",
        cityValue: "", //城市
        addressSpecific: "", //详细地址
      },

      //寄件方式
      mailList: [
        {
          types: "上门取件",
        },
        {
          types: "服务点自取",
        },
      ],
      mailActiveIndex: 0, //寄件方式高亮
      myOrderList: {
        remarks: "", //备注
        expectTime: '24小时内'
      },
      disabled: true,//禁用 
      //
      routerGo: (path) => {
        if (path === "/") {
          Dialog.confirm({
            title: "温馨提示",
            message: "您确定要取消下单吗?",
            confirmButtonColor: "#5860DA",
          })
            .then(() => {
              Notify({ type: "success", message: "取消下单成功" });
              router.replace(path);
            })
            .catch(() => {
              return;
            });
          // Toast('请您先勾选快件运单协议')
          return;
        }
        router.replace(path);
      },
    });
    onBeforeMount(() => {
      //
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.placeanorder-container {
  height: 100vh;
  overflow: hidden;
  background: #f0f0f0;
  color: #555;
  font-size: 0.14rem;
  .placeanorder-form {
    .placeanorder-body {
      width: 100%;
      height: 86vh;
      overflow: auto;
      padding: 0.1rem;
      margin: 0.1rem 0 0 0;
      box-sizing: border-box;
      .placeanorder-module {
        width: 100%;
        padding: 0.1rem 0.1rem;
        margin: 0 0 0.1rem 0;
        box-sizing: border-box;
        background: #fff;
        border-radius: 0.1rem;
        .van-divider {
          margin: 0 0.2rem 0 0.16rem;
          width: 90%;
        }
        img {
          width: 0.35rem;
          height: 0.35rem;
          margin: 0 0.15rem 0 0;
        }
        .placeanorder-title {
          display: flex;
          align-items: center;
          margin: 0 0 0.1rem 0;
        }
        :deep(.van-cell-group,
        van-cell-group--inset) {
          margin: 0;
        }
        h4 {
          min-width: 0.7rem;
        }
        p {
          margin: 0 0 0 0.1rem;
          word-break: break-all;
          word-wrap: break-word;
        }
      }
      //寄件方式
      .placeanorder-mail-module {
        width: 100%;
        padding: 0.1rem 0.1rem;
        margin: 0 0 0.2rem 0;
        box-sizing: border-box;
        background: #fff;
        border-radius: 0.1rem;
        .placeanorder-mail-title {
          font-size: 0.13rem;
          min-width: 0.8rem;
        }
        .mail {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .placeanorder-mail-checked{
            display: flex;
            align-items: center;
            img{
              width: 0.16rem;
              height: 0.16rem;
              margin: 0 0.08rem 0 0;
            }
            p {
              padding: 0.01rem 0.04rem;
              border-radius: 0.1rem;
            }
          }
        }
        .calldate {
          h4 {
            min-width: 0.8rem;
          }
          margin: 0.1rem 0;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
      }
      //底部 按钮
      .bottom-save-btn {
        width: 100%;
        height: 0.6rem;
        padding: 0 0.15rem;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        background: #fff;
        position: fixed;
        left: 0;
        bottom: 0;
        .btn {
          width: 100%;
          height: 0.35rem;
        }
      }
    }
  }
}
</style>
