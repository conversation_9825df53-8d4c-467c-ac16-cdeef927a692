<template>
  <div class="logisticsdetails-container">
    <van-nav-bar
      title="运单详情"
      left-text=""
      left-arrow
      @click-left="$routerBackFun()"
    />
    <div class="logisticsdetails-body scrollnone">
      <div v-if="myLogisticsObj" class="logisticsdetails-module">
        <img v-if="myLogisticsObj?.thirdPlatformName === 'ZJYK'" src="@/assets/baby/my/zhongJian.jpg" alt="中健云康" />
        <img v-else-if="myLogisticsObj?.logisticsProvider" src="@/assets/baby/sf.svg" alt="顺丰" />
        <div class="logisticsdetails-items">
          <div class="logisticsdetails-item">
            <h3>{{myLogisticsObj?.logisticsProvider || ''}}</h3>
          </div>
          <div class="logisticsdetails-item">
            <div>快递单号：{{myLogisticsObj?.orderNo || ''}}</div>
            <!-- :data-clipboard-text="myLogisticsObj.orderNo" -->
            <!-- <button
              class="tag-read van-icon van-icon-description"
              data-clipboard-action="copy"
              data-clipboard-target="#bar"
              @click="copyText"
            >
              复制
            </button>
            <p id="bar">{{myLogisticsObj.orderNo}}</p> -->
          </div>
        </div>
      </div>

      <van-steps
        v-if="orderParams?.orderType === 1 && myLogisticsObj?.waybillRecords?.length"
        class="scrollnone logisticslog"
        direction="vertical"
        active="-1"
      >
        <van-step v-for="(item,index) in myLogisticsObj.waybillRecords"
        :key="index">
          <p v-if="item?.logisticsInfo" v-html="item.logisticsInfo" class="logisticslog-text-color logisticslog-text-mb-1"/>
          <p v-if="item?.waybillTime" v-html="item.waybillTime" class="logisticslog-text-color" />
        </van-step>
      </van-steps>

      <van-steps
        v-else-if="orderParams?.orderType === 2 && myLogisticsObj?.materialOrderTraces?.length"
        class="scrollnone logisticslog"
        direction="vertical"
        active="-1"
      >
        <van-step v-for="(item,index) in myLogisticsObj.materialOrderTraces"
        :key="index">
          <p v-if="item?.desc" v-html="item.desc" class="logisticslog-text-color logisticslog-text-mb-1"/>
          <p v-if="item?.time" v-html="item.time" class="logisticslog-text-color" />
        </van-step>
      </van-steps>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount } from "vue";
import { useRoute } from "vue-router";
import { getLogisticsOrder, getMaterialOrder } from '@/api/myOrder';
import { logisticsDetailsStateInter, getLogisticsOrderInter } from '@/types/my';
// import Clipboard from "clipboard";

export default defineComponent({
  name: "LogisticsDetails", // 物流详情
  setup() {
    const route = useRoute()
    const state: logisticsDetailsStateInter = reactive({
      myLogisticsObj: {
        id: '',
        orderNo: '',
        waybillNo: '',
        creator: '',
        createTime: '',
        lastUpdator: '',
        lastUpdateTime: '',
        state: 0,
        signTime: '',
        signUsername: '',
        waybillRecords: [],
        expectPickupTimeDisplay: '',
        createTimeDisplay: '',
        lastUpdateTimeDisplay: '',
        patientId: '',
        dctStudyId: '',
        dctSiteId: '',
        omsProjectId: '',
        customSubTaskId: '',
        goodsTypeName: '',
        logisticsProvider: '',
        shipperName: '',
        shipperPhoneNo: '',
        shipperProvinceCode: '',
        shipperProvinceName: '',
        shipperCityCode: '',
        shipperCityName: '',
        shipperAreaCode: '',
        shipperAreaName: '',
        shipperAddressDetail: '',
        rcptName: '',
        rcptPhoneNo: '',
        rcptProvinceCode: '',
        rcptProvinceName: '',
        rcptCityCode: '',
        rcptCityName: '',
        rcptAreaCode: '',
        rcptAreaName: '',
        rcptAddressDetail: '',
        shippingMethod: '',
        expectPickupTime: '',
        remark: '',
        channelName: '',
        materialOrderTraces: [],
        thirdPlatformName: '',
      },
      orderParams: {},
      onLoad: () => {
        if (route.query?.orderItem) {
          const params = JSON.parse(route.query.orderItem as string)
          state.orderParams = params
          if ((params?.orderType === 1 || params?.orderType === 2) && params?.id) {
            const getOrderFun = params.orderType === 1 ? getLogisticsOrder : getMaterialOrder
            getOrderFun(params.id)
              .then((rest) => {
                const res = rest as getLogisticsOrderInter
                state.myLogisticsObj = res
              })
          }
        }
      },
      // 复制-回调
      // copyText: () => {
      //   const clipboard = new Clipboard(".tag-read");
      //   console.log('copy',clipboard)
      //   clipboard.on("success", () => {
      //     clipboard.destroy(); // 释放内存
      //     Notify({ message: "复制成功", type: "success" });
      //   });
      //   clipboard.on("error", () => {
      //     clipboard.destroy(); // 释放内存
      //     Notify({ message: "复制失败", type: "danger" });
      //   });
      // },
    });
    onBeforeMount(() => {
      state.onLoad()
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
:deep(.van-step__circle-container) {
  background: #DCDCDC;
  width: 13px;
  height: 13px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}
:deep(.van-step__circle) {
  width: 7px;
  height: 7px;
  background: #525252;
}
.logisticsdetails-container {
  height: 100vh;
  overflow: hidden;
  background: #fff;
  color: #333;
  font-size: 0.13rem;
  .logisticsdetails-body {
    padding: 0.2rem;
    box-sizing: border-box;
    height: 92vh;
    overflow: auto;
    .logisticsdetails-module {
      padding: 0.1rem 0rem;
      margin: 0 0 0.1rem 0;
      box-sizing: border-box;
      border-radius: 0.05rem;
      display: flex;
      img {
        width: 0.44rem;
        height: 0.44rem;
        margin: 0 0.1rem 0 0;
      }
      .logisticsdetails-items {
        .logisticsdetails-item {
          width: 100%;
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          margin: 0 0 0.08rem 0;
        }
      }
    }
    .logisticslog {
      margin: 0.2rem 0 0 0;
      .logisticslog-text-color {
        color:#8B8B8B;
      }
      .logisticslog-text-mb-1 {
        margin: 0 0 0.1rem 0;
      }
    }
  }
}
</style>
