<template>
  <div class="bindFamily-box bg-color-F5F5F5 h-screen">
    <van-nav-bar
      title="绑定家属"
      left-text=""
      left-arrow
      @click-left="$routerBackFun()"
    />
    <div>
      <div class="bindFamilyList overflow-auto scrollnone pt-025rem px-016rem box-border">
        <div v-if="bindFamilyList && bindFamilyList?.length > 0">
          <div
            v-for="(item, index) in bindFamilyList"
            :key="index"
            class="w-full box-border py-01rem pl-014rem pr-016rem bg-color-fff mb-015rem radius-005rem flex justify-between"
          >
            <div class="flex">
              <div class="mr-01rem mr-01rem w-048rem h-048rem overflow-hidden radius-50">
                <img
                    v-if="item?.avatarUrl"
                    :src="item.avatarUrl"
                    class="w-full"
                    alt=""
                  />
                  <img
                    v-else
                    src="@/assets/baby/userHeadPortrait.svg"
                    class="w-full"
                    alt=""
                  />
              </div>
              <div class="flex justify-around flex-col flex-1">
                <div class="flex leading-02rem ft-15-rem">
                  <span class="whitespace-nowrap truncate max-w-06rem">{{ item?.name || '' }}</span>
                  <span class="ml-01rem whitespace-nowrap truncate max-w-06rem">{{ item?.familyRelation || '' }}</span>
                  <span class="ml-01rem">{{ item?.mobile || '' }}</span>
                </div>
                <div v-if="item?.name" class="flex leading-02rem ft-12-rem font-color-ADADAD">
                  <span>{{ item?.bindTimeStr || '' }}</span>
                  <span class="ml-013rem">绑定成功</span>
                </div>
              </div>
            </div>
            <div class="ml-01rem flex items-center">
              <img class="w-015rem" src="@/assets/baby/bindDelete.svg" alt="" @click="deleteClick(item)">
            </div>
          </div>
        </div>
        <myStateShow
          v-else
          imgClass="w-5 bindFamilyList"
          :imgSrc="noDatasImg"
          texts="暂无数据"
        />
      </div>
      <div class="h-045rem w-full flex items-center justify-center theme-bg-btn-color font-color-fff" @click="overlayShow = true">
        + 新增绑定
      </div>
    </div>
    <van-overlay :show="overlayShow" @click="overlayShow = false">
      <div class="h-full flex items-center justify-center box-border px-03rem">
        <div class="overlay-box leading-02rem relative ft-13-rem overflow-hidden bg-color-fff radius-005rem flex flex-col items-center min-w-240rem w-full" @click.stop>
          <div class="mb-02rem ft-15-rem font-bold">提示</div>
          <div class="font-color-E26375">请出示或截图本页面给您的家属扫码</div>
          <div class="font-color-E26375 mb-025rem">完成信息登记后即可绑定成功</div>
          <div class="truncate w-full text-center">{{ msgObj?.studyName || '' }}</div>
          <div class="truncate w-full text-center">{{ msgObj?.siteName || '' }}</div>
          <div>{{ msgObj?.patNumber || '' }}</div>
          <div>{{ msgObj?.patName || '' }}</div>
          <div v-if="msgObj?.patientQrCodeUrl" class="mt-030rem box-border px-02rem">
            <img :src="msgObj.patientQrCodeUrl" alt="" class="w-full">
          </div>
          <van-icon name="clear" color="#8D8D8D" size="0.2rem" class="absolute icon-place" @click.stop="overlayShow = false" />
        </div>
      </div>
    </van-overlay>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onMounted, } from 'vue';
// import { useRoute } from "vue-router";
import noDatasImg from '@/assets/baby/noinformed.svg';
import myStateShow from "@/components/MyStateShow.vue";
import { Dialog, Notify } from 'vant';
import { getPatientMembers, getPatientQrCode, postRemovePatientMembers } from '@/api/my';
import { useStore } from 'vuex';

export default defineComponent({
  name: "BindFamily", // 绑定家属
  components: {
    myStateShow
  },
  setup() {
    // const route = useRoute()
    const store = useStore()
    // const proxy: any = getCurrentInstance()?.proxy
    const state = reactive({
      noDatasImg,
      overlayShow: false,
      bindFamilyList: [],
      msgObj: {
        studyName: '',
        patientQrCodeUrl: '',
        siteName: '',
        patNumber: '',
        patName: ''
      },
      onLoad: () => {
        if (store.state?.homeList?.dctPatientId) {
          getPatientMembers(store.state.homeList.dctPatientId).then((res: any) => {
            state.bindFamilyList = res
          })
        }
      },
      deleteClick: (item) => {
        Dialog.confirm({
          title: '提示',
          message:
            '是否确认删除？',
            confirmButtonColor: '#5860DA'
        })
          .then(() => {
            postRemovePatientMembers(item.id).then(() => {
              Notify({ type: 'success', message: '删除成功' });
              state.onLoad()
            })
          })
          .catch(() => {
            // on cancel
          });
      },
    });
    onMounted(() => {
      if (store.state?.homeList?.dctPatientId) {
        getPatientQrCode({ patientId: store.state.homeList.dctPatientId }).then((res: any) => {
          if (res?.patientQrCodeUrl) {
            res.patientQrCodeUrl = location.href.split('/patientui')[0] + res.patientQrCodeUrl
          }
          state.msgObj = res
        })
      }
      state.onLoad()
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.bindFamily-box {
  font-size: 0.14rem;
  .bindFamilyList {
    height: calc(100vh - 46px - 0.45rem);
  }
}
.overlay-box {
  padding: 0.1rem 0.265rem 0.45rem;
  .icon-place {
    top: 0.1rem;
    right: 0.15rem;
  }
}
</style>
