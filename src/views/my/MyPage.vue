<template>
  <div class="my">
    <!-- <van-nav-bar title="我的" left-text="" /> -->
    <van-nav-bar v-if="appToken == ''" title="我的" />
    <van-nav-bar v-else title="我的">
      <template #right>
        <van-icon
          name="https://dct-app.oss-cn-hangzhou.aliyuncs.com/icons/msg_1x.png"
          :badge="unreadMsgCount"
          size=".18rem"
          @click="openMsgDialog"
        />
      </template>
      <template #left>
        <van-icon
          name="https://dct-app.oss-cn-hangzhou.aliyuncs.com/icons/slide_1x.png"
          size=".18rem"
          @click="openLeftSlideDialog"
        />
      </template>
    </van-nav-bar>
    <div class="relative my-push-message overflow-auto scrollnone">
      <div class="my-message-body">
        <img
          v-if="homeList?.avatarUrl"
          :src="homeList.avatarUrl"
          class="my-message-img"
          alt=""
        />
        <img
          v-else
          src="@/assets/baby/userHeadPortrait.svg"
          class="my-message-img"
          alt=""
        />
        <div class="my-message-detail">
          <h4>{{ homeList?.patNumber || "" }}</h4>
          <!-- <h5 v-if="homeList?.greeting" v-html="homeList?.greeting" /> -->
          <p
            v-if="homeList?.patientTrialStatusText"
            v-html="homeList?.patientTrialStatusText"
            :class="{ 'text-gray-400': homeList?.patientStatus !== 10 }"
          />
        </div>
      </div>
      <div
        v-if="homeList?.frequencyFeature?.includes(16384)"
        class="my-balance-view p-01rem font-color-333"
      >
        <div class="ft-15-rem w-full mb-02rem">我的余额</div>
        <div class="w-full centerflex-h justify-between">
          <div class="ft-22-rem">¥{{ amount }}</div>
          <div
            class="w-067rem px-01rem py-008rem centerflex withdraw-deposit-left-radius withdraw-deposit-shadow"
            @click="routerGo('/my/myBalance')"
          >
            <div class="ft-14-rem font-color-5860d9">提现</div>
            <van-icon class="ml-01rem font-color-5860d9" name="arrow" />
          </div>
        </div>
      </div>
      <!-- 常用功能 -->
      <h5 v-if="homeList?.frequencyFeature?.length" class="function-title">
        常用功能
      </h5>
      <div class="commonly">
        <div
          v-if="homeList?.frequencyFeature?.includes(16)"
          class="commonly-item"
          @click="routerGo('/my/commonproblem')"
        >
          <img
            class="commonly-item-img1"
            src="@/assets/baby/commonProblemIcon.svg"
            alt=""
          />
          <p>常见问题</p>
        </div>
        <div
          v-if="homeList?.frequencyFeature?.includes(32)"
          class="commonly-item"
          @click="routerGo('/my/editmobile')"
        >
          <img
            class="commonly-item-img2"
            src="@/assets/baby/editMobileIcon.svg"
            alt=""
          />
          <p>修改信息</p>
        </div>
        <!-- '/onlinecustomerservice' -->
        <div
          v-if="homeList?.frequencyFeature?.includes(64)"
          class="commonly-item"
          @click="routerGo('/temporarilynotopened')"
        >
          <img
            class="commonly-item-img3"
            src="@/assets/baby/onlineCustomerServiceIcon.svg"
            alt=""
          />
          <p>联系客服</p>
        </div>
        <!-- '/drugapplication' temporarilynotopened  -->
        <div
          v-if="homeList?.frequencyFeature?.includes(128)"
          class="commonly-item"
          @click="routerGo('/my/drugapplication')"
        >
          <img
            class="commonly-item-img4"
            src="@/assets/baby/drugApplicationIcon.svg"
            alt=""
          />
          <p>申请药物</p>
        </div>
        <!-- APP暂时不展示 -->
        <div
          v-if="appKey !== 'app' && homeList?.frequencyFeature?.includes(32768)"
          class="commonly-item"
          @click="routerGo('/my/bindFamily')"
        >
          <img
            class="commonly-item-img1"
            src="@/assets/baby/bindFamily.svg"
            alt=""
          />
          <p>绑定家属</p>
        </div>
        <!-- '/gift' -->
        <div
          v-if="homeList?.frequencyFeature?.includes(256) && false"
          class="commonly-item"
          @click="routerGo('/my/gift')"
        >
          <img class="commonly-item-img5" src="@/assets/baby/gift.svg" alt="" />
          <p>领取礼品</p>
        </div>
        <div class="commonly-item" @click="routerGo('/my/feedback')">
          <img
            class="commonly-item-img5"
            src="@/assets/baby/Feedback.svg"
            alt=""
          />
          <p>系统使用反馈</p>
        </div>
      </div>
      <!-- 我的-地址-订单 -->
      <div
        v-if="homeList?.frequencyFeature?.length"
        class="my-address-order mb-01rem"
      >
        <div
          v-if="homeList?.frequencyFeature?.includes(512)"
          class="my-address"
          @click="routerGo('/my/myaddress')"
        >
          <div class="my-address-order-text">
            <h4>我的地址</h4>
            <p>MY ADDRESS</p>
          </div>
          <img src="@/assets/baby/myAddressIcon.svg" alt="" />
        </div>
        <div
          v-if="homeList?.frequencyFeature?.includes(1024)"
          class="my-order"
          @click="routerGo('/my/myorder')"
        >
          <!-- '/my/myorder'-->
          <div class="my-address-order-text">
            <h4>我的订单</h4>
            <p>MY ORDER</p>
          </div>
          <img src="@/assets/baby/myOrderIcon.svg" alt="" />
        </div>
      </div>
      <div
        class="w-full ft-12-rem my-[0.3rem] text-center text-[#A4A0A0] underline"
        @click="openCloseAboutus"
      >
        关于我们 
      </div>
    </div>
    <!-- 底部tabbar -->
    <MyTabbar :propsActive="3" />
    <AboutUs v-show="showAboutUs" @close="openCloseAboutus" />
  </div>
</template>

<script lang='ts'>
import { defineComponent, onBeforeMount, reactive, toRefs, getCurrentInstance } from "vue";
import { useStore } from "vuex";
import MyTabbar from "@/components/Tabbar.vue";
import AboutUs from '@/views/my/AboutUs.vue';
import { getPatientFinanceInter } from '@/types/my';
import { getPatient } from "@/api/home";
import { getshu } from '@trialdata/common-fun-css/index'
import { getPatientFinance } from '@/api/myBalance';
import { homeListInter } from "@/types/storeState";
import { getUnReadCount } from '@/api/app'

export default defineComponent({
  name: "MyPage", // 我的
  components: {
    MyTabbar,
    AboutUs,
  },
  setup() {
    const store = useStore();
    const proxy: any = getCurrentInstance()?.proxy
    const state: any = reactive({
      unreadMsgCount: '', // 未读消息数量
      appKey: store.state.appKey.toLowerCase(),
      appToken: store.state.appToken,
      homeList: {
        dctPatientId: '',
        dctSiteId: '',
        dctStudyId: '',
        displayTraining: false,
        patNumber: '',
        avatarUrl: '',
        greeting: '',
        currentStatusText: '',
        patientTrialStatusText: '',
        patientStatus: 0,
        visits: [],
        frequencyFeature: [],
        currentVisitId: '',
        currentVisitText: '',
        totalTask: 0,
        completedTask: 0,
        questionTaskStatus: 0,
        pictureTaskStatus: 0,
        otherTaskStatus: 0,
        visitMeetingList: [],
        dailyFeature: 0,
        inGroup: '',
        inGroupDay: '',
        medicationStatus: 0,
        patientICFStatus: 0,
        patientMenues: [],
        studyName: '',
        unReadChattingRecordsNum: 0,
        needInGroupGuidModal: 0,
        memberAvatarUrl: '',
        hasInteractiveCommunication: 0
      },
      amount: 0,
      routerGo: (path: string) => {
        proxy.$routerGoFun('routerInnPush', path, '', '/my')
      },
      // 获取首页数据
      getMyHomeDatas: () => {
        getPatient().then((patientRes) => {
          const res = patientRes as homeListInter
          state.homeList = res
          state.homeList.currentVisitText = state?.homeList?.currentVisitText ? state?.homeList?.currentVisitText
            .split("--")
            .join(" ~ ") : '';
          state.homeList.frequencyFeature = res?.frequencyFeature ? getshu(res.frequencyFeature) : []
          if (state.homeList?.frequencyFeature?.includes(16384) && res?.dctPatientId) {
            getPatientFinance(res.dctPatientId)
              .then((patientFinanceRes) => {
                const rest = patientFinanceRes as getPatientFinanceInter
                state.amount = rest?.amount || 0
                if (rest?.freezeAmount) {
                  state.amount -= rest.freezeAmount
                }
              })
          }
          store.dispatch('setHomeList', state.homeList);
        });
      },
      showAboutUs: false,
      openCloseAboutus: () => {
        state.showAboutUs = !state.showAboutUs;
      },
      // 打开APP消息列表
      openMsgDialog: () => {
        const msgData = {
          data: {
            action: 'msgList',
            payload: store.state.appToken
          }
        }
        // 如果是iOS
        if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.jumpToNative) {
          window.webkit.messageHandlers.jumpToNative.postMessage(msgData);
        }
        // 如果是非iOS环境(即uniapp、安卓)
        uni.postMessage(msgData);
      },
      // 打开侧边栏
      openLeftSlideDialog: () => {
        const msgData = {
          data: {
            action: 'showLeftSlide',
            payload: store.state.appToken
          }
        }
        // 如果是iOS
        if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.jumpToNative) {
          window.webkit.messageHandlers.jumpToNative.postMessage(msgData);
        }
        // 如果是非iOS环境(即uniapp、安卓)
        uni.postMessage(msgData);
      }
    });

    onBeforeMount(() => {
      if (store.state.appToken != '') {
        getUnReadCount().then(res => {
          state.unreadMsgCount = res;
        });
      }
      state.getMyHomeDatas()
    });

    return {
      ...toRefs(state),
    };
  },
});
</script>

<style lang='less' scoped>
.my {
  font-size: 0.14rem;
  height: 100vh;
  overflow: hidden;
  h4,
  h5 {
    margin: 0;
  }
  .my-push-message {
    width: 100%;
    height: calc(100vh - 46px - 56px);
    padding: 0.2rem 0.1rem;
    box-sizing: border-box;
    h2 {
      font-size: 0.16rem;
      color: #333;
      margin: 0.16rem 0;
    }
    .function-title {
      font-size: 0.15rem;
      margin-top: 30px !important;
    }
    .my-message-body {
      min-height: 1rem;
      display: flex;
      align-items: center;
      background: #fff;
      border-radius: 0.1rem;
      box-shadow: 0 0.02rem 0.26rem rgba(0, 0, 0, 0.07);
      .my-message-img {
        width: 0.6rem;
        height: 0.6rem;
        border-radius: 50%;
        margin: 0 0.1rem;
      }
      .my-message-detail {
        margin: 0 0 0 0.1rem;
        h4 {
          margin: 0.1rem 0 0 0;
          font-size: 0.17rem;
        }
        h5,
        p {
          margin: 0;
          padding: 0;
          font-size: 0.15rem;
        }
      }
    }
    // 我的订单 地址
    .my-address-order {
      display: flex;
      justify-content: space-between;
      margin-top: 0.3rem;
      .my-address,
      .my-order {
        width: 47%;
        height: 1rem;
        padding: 0.1rem 0.1rem 0;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        box-shadow: 0 0.02rem 0.26rem rgba(0, 0, 0, 0.07);
        border-radius: 0.1rem;
        .my-address-order-text {
          width: 60%;
          h4 {
            color: var(--theme-color);
          }
          p {
            font-size: 0.08rem;
            color: #767676;
          }
        }
        img {
          width: 0.61rem;
        }
      }
    }
    // 我的余额
    .my-balance-view {
      border-radius: 0.1rem;
      box-shadow: 0 0.02rem 0.26rem rgba(0, 0, 0, 0.07);
      margin-top: 0.2rem;
      padding: 0.14rem 0 0.14rem 0.14rem;
    }
    // 常用功能
    .commonly {
      border-radius: 0.1rem;
      box-shadow: 0 0.02rem 0.26rem rgba(0, 0, 0, 0.07);
      display: flex;
      margin-top: 0.2rem;
      flex-wrap: wrap;
      padding: 0.1rem;
      .commonly-item {
        width: 24%;
        height: 0.56rem;
        margin: 0.08rem 0;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        .commonly-item-img1 {
          width: 0.23rem;
          height: 0.3rem;
        }
        .commonly-item-img2 {
          width: 0.24rem;
          height: 0.3rem;
        }
        .commonly-item-img3 {
          width: 0.24rem;
          height: 0.22rem;
        }
        .commonly-item-img4 {
          width: 0.3rem;
          height: 0.3rem;
        }
        .commonly-item-img5 {
          width: 0.29rem;
          height: 0.28rem;
        }
        p {
          width: 100%;
          margin: 0.1rem 0 0 0;
          text-align: center;
          font-size: 0.12rem;
          font-weight: 700;
          color: var(--theme-color);
        }
      }
    }
  }
}
</style>