<template>
  <div class="h-100-vh ft-12-rem bg-color-F5F5F5 overflow-hidden">
    <van-nav-bar title="详情" left-arrow @click-left="$routerBackFun()" />
    <div class="h-100vh-46px overflow-auto font-color-333 bg-color-F5F5F5"
    :class="{'balance-detail-body': editFlag}">
        <div v-if="!editFlag" class="p-01rem mb-01rem bg-color-fff">
          <p class="mb-01rem">
            <!-- <span v-if="applyInfoObj.status === 0" class="ft-13-rem text-yellow-400">待确认</span>
            <span v-else-if="applyInfoObj.status === 4" class="ft-13-rem font-color-666">已退回</span> -->
            <span v-if="applyInfoObj?.status === 2" class="ft-13-rem font-color-72AE32">已打款</span>
            <span v-else-if="applyInfoObj?.status === -1" class="ft-13-rem font-color-E02020">已拒绝</span>
            <span v-else-if="applyInfoObj?.status === 1" class="ft-13-rem text-yellow-400">处理中</span>
          </p>
          <div v-if="applyInfoObj?.orderNo" class="ft-14-rem font-color-999">单号{{applyInfoObj.orderNo}}</div>
        </div>

        <div class="p-014rem ft-15-rem centerflex justify-between bg-color-fff">
          <div class="font-color-666">到账银行卡</div>
          <div v-if="applyInfoObj?.bank">{{applyInfoObj.bank}}
            <span v-if="bankInfoObj?.cardNumber">({{ bankInfoObj.cardNumber.slice(-5) }})</span>
          </div>
        </div>
        <div class="w-full my-hr"/>
        <div v-if="!editFlag" class="p-014rem ft-15-rem centerflex justify-between bg-color-fff">
          <div class="font-color-666">提现金额</div>
          <div v-if="applyInfoObj?.amount">¥{{applyInfoObj?.amount}}</div>
        </div>
        <div v-else class="p-014rem ft-15-rem pb-0-important font-color-666 bg-color-fff">
          <img class="w-008rem" src="@/assets/baby/equiredIcon.svg" alt="">
          &nbsp;提现金额
        </div>
        <div v-if="editFlag" class="px-01rem bg-color-fff">
          <div class="centerflex-h-wrap justify-end p-004rem ft-20-rem">¥{{availableAmount}}</div>
          <div class="centerflex-h-wrap justify-end pb-01rem ft-13-rem font-color-666">可用余额¥{{availableAmount}}</div>
        </div>
        <div class="p-014rem font-color-999">
          温馨提示：我们将尽快提交财务审核，信息正确则1-5个工作日内打款，打款后1-3个工作日内到账。
        </div>
        <div class="p-014rem pb-0-important bg-color-fff">
          <div class="ft-15-rem mb-01rem">流程</div>
          <!-- 流程轴 -->
          <div v-for="(item,index) in applyInfoObj.aprRuntimeLogViewModels"
          :key="index"
          class="common-date-navs-module flex pb-02rem">
            <div class="date-navs-items-left">
              <div v-if="item?.aprStatus || item?.aprBizStatus" class="date-bule-icon bg-color-5995FF" />
              <div v-else class="date-bule-icon bg-color-dfdfdf" />
              <div v-if="applyInfoObj.aprRuntimeLogViewModels.length > 1 && index !== applyInfoObj.aprRuntimeLogViewModels.length -1" class="date-after" />
              <img
                v-if="item.aprStatus === 'Approved'"
                src="@/assets/baby/compensationReimbursement/correctIcon.svg" 
                alt=""
                class="w-015rem absolute z-2"
              />
              <img
                v-else-if="item?.aprStatus === 'Rejected' && item?.aprBizStatus === 'Rejected'"
                src="@/assets/baby/compensationReimbursement/rejectedIcon.svg" 
                alt=""
                class="w-015rem absolute z-2"
              />
              <img
                v-else-if="item?.aprStatus === 'Rejected' && item?.aprBizStatus === 'Returned'"
                src="@/assets/baby/compensationReimbursement/leftArrowIcon.svg" 
                alt=""
                class="w-015rem absolute z-2"
              />
              <img
                v-else-if="item?.aprStatus === 'Processing'"
                src="@/assets/baby/compensationReimbursement/alarmClockIcon.svg" 
                alt=""
                class="w-015rem absolute z-2"
              />
            </div>
            <div class="date-navs-items-infos">
              <div class="flex justify-between mb-01rem">
                <div class="w-3/5">
                  <div v-html="item.detailName" class="none-warp-text-auto"/>
                  <div v-html="item.remark" class="ft-12-rem font-color-999 mt-01-rem"/>
                </div>
                
                <!-- <div v-if="item.approver" class="max-h-055rem centerflex-wrap">
                  <div class="approver-img mb-006rem centerflex">
                    <img src="@/assets/baby/compensationReimbursement/approverIcon.svg" class="w-018rem" alt="">
                  </div>
                  <div class="w-full centerflex ft-11-rem font-color-999">{{item.approver}}</div>
                </div> -->
                <div v-if="item?.requestDate" v-html="item.requestDate" class="ft-11-rem font-color-999"/>
              </div>
              <div v-if="item?.comment" class="ft-11-rem font-color-999 flex">
                <div class="triangle-left"/>
                <div v-html="item.comment" class="w-full p-01rem bg-color-F5F5F5"/>
              </div>
            </div>
          </div>
        </div>
    </div>
    <div
      v-if="editFlag"
      class="commmon-bottom-add-btn centerflex"
      @click="saveWithdrawDeposit"
    >
      提交
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  onBeforeMount,
  reactive,
  toRefs,
  getCurrentInstance,
} from "vue";
import { useRoute } from "vue-router";
import { useStore } from "vuex";
import { getBankAccount } from '@/api/myBalance';
import { postFinanceWithdrawalOrder } from '@/api/myBalance';
import { Notify } from "vant";
import { getFinanceWithdrawalOrderItem, getPatientFinance, } from '@/api/myBalance';
import { getAPRRuntimeLog } from '@/api/myBalance';
import { 
  getPatientFinanceInter, 
  getFinanceWithdrawalOrderItemInter, 
  aprRuntimeLogViewModels,
  balanceDetailStateInter
} from "@/types/my";

export default defineComponent({
  name: "BalanceDetail", // 详情（余额）

  setup() {
    const store = useStore()
    const route = useRoute();
    const proxy: any = getCurrentInstance()?.proxy
    const state: balanceDetailStateInter = reactive({
      applyInfoObj: {
        id: '',
        patientId: '',
        siteName: '',
        orderNo: '',
        amount: 0,
        withdrawalType: 0,
        withdrawalTypeName: '',
        bank: '',
        cardNumber: '',
        openAccountBank: '',
        accountName: '',
        idCard: '',
        status: 0,
        statusName: '',
        payDate: '',
        payAccount: '',
        transactionReceipt: '',
        aprRuntimeId: 0,
        currentRuntimeId: 0,
        canOperateUserIds: [],
        applyMan: '',
        applyManName: '',
        applyTime: '',
        attachmentEditViewModels: [],
        aprRuntimeLogViewModels: []
      },
      bankInfoObj: {
        id: '',
        bank: '',
        cardNumber: '',
        openAccountBank: '',
        accountName: '',
        idCard: '',
      },
      availableAmount: 0,
      editFlag: true,
      saveWithdrawDeposit: () => {
        // 保存成功后回到我的余额页
        const ordeRdata = {
          amount: state.availableAmount,
          withdrawalType: 1, // 银行卡
          bankAccountId: state.bankInfoObj.id,
        }
        postFinanceWithdrawalOrder(store.state.homeList.dctPatientId,ordeRdata)
        .then(() => {
          proxy.$routerBackFun(2)
          Notify({ type: "success", message: "提交成功" });
        })
      },
      onLoad: () => {
        if (store.state?.homeList?.dctPatientId) {
          // 取可用金额
          getPatientFinance(store.state.homeList.dctPatientId)
          .then((res) => {
            const rest = res as getPatientFinanceInter
            state.availableAmount = rest?.amount || 0
            if (rest?.freezeAmount) {
              state.availableAmount -= rest.freezeAmount
            }
          })
          getBankAccount(store.state.homeList.dctPatientId)
          .then((rest) => {
            const res = rest as any
            if (res && res?.length) {
              const obj = res.find(item => item.bankAccountType == route.query?.bankAccountType)
              if (obj) {
                state.bankInfoObj = obj
                state.applyInfoObj.bank = obj.bank
              }
            }
          })
        }
        // 已生成的
        if (route.query?.orderId) {
          state.editFlag = false
          getFinanceWithdrawalOrderItem(store.state.homeList.dctPatientId,route.query.orderId)
          .then((res) => {
            if (res) {
              state.applyInfoObj = res as getFinanceWithdrawalOrderItemInter
            }
          })
        } else {
          getAPRRuntimeLog(store.state.homeList.dctPatientId,2)
          .then((rest) => {
            const res = rest as aprRuntimeLogViewModels
            if (res && Array.isArray(res)) {
              state.applyInfoObj.aprRuntimeLogViewModels = res
            }
          })
        }
      },
    });
    onBeforeMount(() => {
      state.onLoad();
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.balance-detail-body {
  height: calc(100vh - 46px - 0.55rem);
}
</style>
