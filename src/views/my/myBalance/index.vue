<template>
  <div class="h-100-vh ft-12-rem overflow-hidden">
    <van-nav-bar title="我的余额" left-arrow @click-left="$routerBackFun()" />
    <div class="my-balance-infos h-1-8rem bg-size-100 p-01rem box-border">
      <div class="w-full ft-14-rem font-color-fff mt-01-rem text-right" @click="routerGo('/my/balanceDetailList')">余额明细</div>
      <div class="w-full ft-30-rem font-color-fff text-center mt-01-rem mb-02rem">¥{{availableAmount}}</div>
      <div class="w-full centerflex">
        <div class="centerflex radius-08rem w-148rem h-035rem ft-14-rem font-color-5995FF bg-color-fff"
        @click="routerGo('/my/confirmBankCardInformation')">提现至银行卡</div>
      </div>
    </div>
    <div 
    ref="myBalanceListRef"
    @scroll="scrollChage"
    class="my-balance-card-list p-01rem box-border overflow-auto scrollnone">
      <div
        v-for="(item, index) in myBalanceList"
        :key="index"
        class="commmon-card-module mb-01rem ft-14-rem"
        @click="routerGo('/my/balanceDetail', item)"
      >
        <div class="mb-01rem centerflex-h justify-between">
          <div class="font-color-999">申请时间：{{item?.applyTime || ''}}</div>
          <div class="centerflex-h">
            <span v-if="item?.status === 1" class="ft-13-rem text-yellow-400">处理中</span>
            <span v-else-if="item?.status === 2" class="ft-13-rem font-color-72AE32">已打款</span>
            <span v-else-if="item?.status === -1" class="ft-13-rem font-color-E02020">已拒绝</span>
            <van-icon class="ml-01rem" name="arrow" />
          </div>
        </div>
        <div class="my-hr w-full028rem translate-x014rem"/>
        <div class="mt-014rem mb-01rem">提现方式：{{item?.withdrawalTypeName || ''}}</div>
        <div>提现金额（元）：{{item?.amount || ''}}</div>
      </div>
      <!-- <div class="item-nos">没有更多数据了～</div> -->
    </div>
  </div>
</template>

<script lang="ts">
import { Toast } from "vant";
import {
  defineComponent,
  onBeforeMount,
  reactive,
  toRefs,
  getCurrentInstance,
} from "vue";
import { useRoute } from "vue-router";
import { getPatientFinance, getFinanceWithdrawalOrder } from '@/api/myBalance';
import { useStore } from "vuex";
import { getFinanceWithdrawalOrderInter, getPatientFinanceInter, myBalanceStateInter } from "@/types/my";

export default defineComponent({
  name: "MyBalance", // 我的余额/my/myBalance

  setup() {
    const route = useRoute();
    const proxy: any = getCurrentInstance()?.proxy
    const store = useStore()
    const state: myBalanceStateInter = reactive({
      amount: 0,
      availableAmount: 0,
      myBalanceListRef: null,
      myBalanceList: [],
      pageIndex: 1,
      pageSize: 20,
      totalItemCount: 0,
      scrollChage: () => {
        const { scrollHeight, scrollTop, clientHeight } = state.myBalanceListRef
        const scrollMoveHeight = parseInt(scrollHeight - scrollTop + '')
        const clientHeightArr = [clientHeight, clientHeight - 1, clientHeight + 1]
        if (clientHeightArr.includes(scrollMoveHeight) && state.myBalanceList?.length < state.totalItemCount) {
          state.pageIndex += 1
          state.onLoad()  
        }
      },
      routerGo: (path, item) => {
        // 可用金额的话则 state?.amount <= 0 换成 state?.availableAmount <= 0
        if (path === '/my/confirmBankCardInformation' && state?.availableAmount <= 0) {
          Toast('余额不足，无法提现')
          return
        }
        const query = {
          orderId: item?.id || ''
        };
        proxy.$routerGoFun("routerInnPush", path, query, route.path);
      },
      onLoad: () => {
        Toast.loading({
          duration: 300000,
          message: "加载中...",
          forbidClick: true
        });
        const { pageIndex, pageSize } = state;
        getFinanceWithdrawalOrder(store.state.homeList.dctPatientId,{ pageIndex, pageSize, })
          .then((rest) => {
            const res = rest as getFinanceWithdrawalOrderInter
            if (pageIndex > 1) {
              state.myBalanceList = state.myBalanceList.concat(res.items);
            } else if (res?.items?.length) {
              state.myBalanceList = res.items;
            }
            state.totalItemCount = res?.totalItemCount || 0;
            Toast.clear();
          })
          .catch(() => {
            Toast.clear();
          });
      },
    });
    onBeforeMount(() => {
      if (store.state?.homeList?.dctPatientId) {
        getPatientFinance(store.state.homeList.dctPatientId)
        .then((res) => {
          const rest = res as getPatientFinanceInter
          state.amount = rest?.amount || 0
          state.availableAmount = rest?.amount || 0
          // 计算可用金额
          if (rest?.freezeAmount) {
            state.availableAmount -= rest.freezeAmount
          }
        })
        state.onLoad();
      }
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.my-balance-infos {
  background-image: url(@/assets/baby/my/myBalanceBj.svg);
}
.my-balance-card-list {
  height: calc(100vh - 46px - 1.8rem);
}
</style>
