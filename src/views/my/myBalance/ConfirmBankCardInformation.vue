<template>
  <div class="h-100-vh ft-12-rem overflow-hidden"
  :style="{'height': outerHeight ? outerHeight + 'px' : '100vh'}">
    <div v-show="selectBankBodyShow === 0">
      <van-nav-bar title="确认银行卡" left-arrow @click-left="$routerBackFun" />
      <div class="confirm-bank-card-information-body bg-color-F5F5F5 overflow-auto"
      :style="{'height': outerHeight ? `calc(${outerHeight}px - 46px - 0.55rem)` : ''}"
      >
        <div class="ft-12-rem py-012rem px-016rem box-border flex items-center bg-color-CBCDF3 theme-color">
          <img class="w-0175rem mr-01rem" src="@/assets/baby/my/hintIcon.svg" alt="">
          <div>请确保银行卡信息正确，否则将影响打款！</div>
        </div>
        <div class="pt-025rem pb-005rem box-border bg-white">
          <div class="w-full flex justify-around">
            <!-- :class="{'font-color-C3C3C3 border-C3C3C3': patientType === 0}" -->
            <div
              class="py-01rem px-025rem radius-005rem box-border font-color-C3C3C3 border-C3C3C3 flex flex-col items-center"
              :class="{'bank-color': patientType === 0}"
              @click="relationClick(0)">
              <img v-if="patientType === 0" class="w-044rem" src="@/assets/baby/my/bankCard.svg" alt="">
              <img v-else class="w-044rem" src="@/assets/baby/my/bankCardDisable.svg" alt="">
              <span class="font-semibold">本人银行卡</span>
            </div>
            <!-- font-color-C3C3C3 border-C3C3C3 theme-color border-A6AAEB -->
            <div
              class="py-01rem px-025rem radius-005rem box-border font-color-C3C3C3 border-C3C3C3 flex flex-col items-center"
              :class="{'bank-color': patientType === 1}"
              @click="relationClick(1)">
              <img v-if="patientType === 1" class="w-044rem" src="@/assets/baby/my/bankCard.svg" alt="">
              <img v-else class="w-044rem" src="@/assets/baby/my/bankCardDisable.svg" alt="">
              <span class="font-semibold">家属银行卡</span>
            </div>
          </div>
        </div>
        <div class="bg-color-fff">
          <div class="centerflex-h justify-between">
            <div class="p-01rem centerflex-h">
                <img class="w-008rem" src="@/assets/baby/equiredIcon.svg" alt="">
                <div class="ft-15-rem">&nbsp;银行</div>
            </div>
            <div class="w-3/5 p-01rem centerflex-h justify-between"
            @click="selectBankClick">
              <van-field
                v-model="bankAccountObj.bank"
                label=""
                maxlength="200"
                :disabled="patientType === 1"
                readonly
                input-align="right"
                placeholder="请选择"
              />
              <van-icon v-if="patientType === 0" name="arrow" />
            </div>
          </div>
          <div class="my-hr"/>
          <div class="centerflex-h justify-between">
            <div class="p-01rem centerflex-h">
                <img class="w-008rem" src="@/assets/baby/equiredIcon.svg" alt="">
                <div class="ft-15-rem">&nbsp;银行卡号</div>
            </div>
            <div class="w-3/5 p-01rem centerflex-h justify-between">
              <van-field
                v-model.trim="bankAccountObj.cardNumberCopy"
                label=""
                :disabled="patientType === 1"
                maxlength="200"
                input-align="right"
                placeholder="请输入"
                @update:model-value="(value) => bankAccountObj.cardNumber = value.replace(/\s+/g, '')"
                @blur="bankAccountBlur"
                />
            </div>
          </div>
          <div class="my-hr"/>
          <div class="centerflex-h justify-between">
            <div class="p-01rem centerflex-h">
              <img class="w-008rem" src="@/assets/baby/equiredIcon.svg" alt="">
              <div class="ft-15-rem">&nbsp;开户支行</div>
            </div>
            <div class="w-3/5 p-01rem centerflex-h justify-between"
            @click="handleOpenAccountBank">
              <van-field
                v-model="bankAccountObj.openAccountBank"
                label=""
                maxlength="200"
                :disabled="patientType === 1"
                readonly
                input-align="right"
                placeholder="请选择"
              />
              <van-icon v-if="patientType === 0" name="arrow" />
            </div>
          </div>
          <div class="my-hr"/>
          <div class="centerflex-h justify-between">
            <div class="p-01rem centerflex-h">
              <img class="w-008rem" src="@/assets/baby/equiredIcon.svg" alt="">
              <div class="ft-15-rem">&nbsp;户名</div>
            </div>
            <div class="w-3/5 p-01rem centerflex-h justify-between">
              <van-field
                v-model="bankAccountObj.accountName"
                label=""
                maxlength="200"
                :disabled="patientType === 1"
                input-align="right"
                placeholder="请输入"
              />
            </div>
          </div>
          <div class="my-hr"/>
          <div class="centerflex-h justify-between">
            <div class="p-01rem centerflex-h">
              <img class="w-008rem" src="@/assets/baby/equiredIcon.svg" alt="">
              <div class="ft-15-rem">&nbsp;身份证号</div>
            </div>
            <div class="w-3/5 p-01rem centerflex-h justify-between">
              <van-field
                v-model="bankAccountObj.idCard"
                maxlength="200"
                :disabled="patientType === 1"
                label=""
                input-align="right"
                placeholder="请输入"
              />
            </div>
          </div>
        </div>
        <div class="p-01rem ft-13-rem font-color-999">
          <p class="mb-01rem">温馨提示：</p>
          <p v-if="patientType === 1" class="mb-006-rem">1、若银行卡信息有误，请联系研究人员更新；</p>
          <p class="mb-006-rem">{{patientType === 0 ? 1 : 2}}、开户支行为xx银行xx支行（请勿简写）；</p>
          <p>{{patientType === 0 ? 2 : 3}}、若您不清楚开户支行，可致电银行客服询问；</p>
        </div>
      </div>
      <div
        class="w-full commmon-bottom-add-btn centerflex"
        @click="saveConfirmBankInfo"
      >
      确认
      </div>
    </div>
    <!-- 选择银行模块 -->
    <div v-show="selectBankBodyShow > 0" class="select-bank-body bg-color-fff"
    :style="{'height': outerHeight ? outerHeight + 'px' : '100vh'}">
      <van-nav-bar :title="selectBankBodyShow === 1 ? '选择银行' : '选择支行'"
      left-arrow @click-left="selectBankBodyShow = 0;searchKey = ''" />
      <!-- @search="onSearch" -->
      <van-search
        v-model="searchKey"
        placeholder="请输入"
        class="pb-0"
        @update:model-value="changeSearchKey"
      >
      </van-search>
      <div v-if="selectBankBodyShow === 1" class="overflow-auto"
      :style="{'height': outerHeight ? `calc(${outerHeight}px - 46px - 0.54rem)` : 'calc(100vh - 46px - 0.54rem)'}">
        <TransitionGroup tag="div" name="fade" class="relative p-0">
          <div
            v-for="(item,index) in banksArr"
            :key="index"
            class="font-color-333 ft-15-rem"
            @click="setBankInfo(item)"
          >
            <div v-if="item?.bankName" class="p-014rem">{{item?.bankName}}</div>
            <div class="my-hr"/>
          </div>
        </TransitionGroup>
      </div>
      <div v-if="selectBankBodyShow === 2" class="overflow-auto"
      :style="{'height': outerHeight ? `calc(${outerHeight}px - 46px - 0.54rem)` : 'calc(100vh - 46px - 0.54rem)'}">
        <TransitionGroup tag="div" name="fade" class="relative p-0">
          <div
            v-for="(item,index) in bankInfoSubbranchList"
            :key="index"
            class="font-color-333 ft-15-rem"
            @click="setBankInfo(item)"
          >
            <div v-if="item?.lName" class="p-014rem">{{item?.lName}}</div>
            <div class="my-hr"/>
          </div>
        </TransitionGroup>
      </div>
    </div>

    <MyPopupShow 
      :myPopupShow="myPopupShow"
      title="提示"
      saveText="确认无误"
      cancelText="修改"
      :handleSave="handleSave"
      :handleCancel="handleCancel"
    >
      <template v-slot:bodyslot>
        <div class="px-015rem text-black ft-13-rem">
          <div class="mb-015rem">
            未通过银行卡验证，请确认银行卡信息是否正确？是否可以正常接收网银转账？
          </div>
          <div class="font-color-E60000 mb-015rem">*若银行卡信息有误或状态异常，将导致无法为您操作打款！</div>
          <!-- 银行信息展示 -->
          <div class="w-full text-center">
            <div class="mb-006-rem">{{ bankAccountObj?.bank }}</div>
            <div class="mb-006-rem">{{ bankAccountObj?.cardNumberCopy }}</div>
            <div class="mb-006-rem">{{ bankAccountObj?.openAccountBank }}</div>
            <div class="mb-006-rem">{{ bankAccountObj?.accountName }}</div>
          </div>
        </div>
      </template>
    </MyPopupShow>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  onBeforeMount,
  reactive,
  toRefs,
  getCurrentInstance,
} from "vue";
import { useRoute } from "vue-router";
import { Toast } from "vant";
import { delay, deepClone } from '@trialdata/common-fun-css/index';
import { getBankAccount, getBankAccountVerify, postBankAccount } from '@/api/myBalance';
import { useStore } from "vuex";
// import { getWXToken, postWXBanklist, postWXBankByNum } from '@/api/getWxBank';
import { getBankInfoList, getAccountNumberBankInfo, getBankInfoSubbranch } from '@/api/myBalance';
import { getBankInfoListInter, confirmBankCardInformationStateInter } from "@/types/my";
import MyPopupShow from "@/components/MyPopupShow.vue";

export default defineComponent({
  name: "ConfirmBankCardInformation", // 确认银行卡
  components:{
    MyPopupShow
  },
  setup() {
    const store = useStore()
    const route = useRoute();
    const proxy: any = getCurrentInstance()?.proxy
    const regex = /^[0-9]{16,19}$/; // 匹配 16 到 19 位纯数字
    // return regex.test(cardNumber);
    const state: confirmBankCardInformationStateInter = reactive({
      outerHeight: window.outerHeight,
      submitLoadingFlag: true,
      bankAccountObj: {
        id: '',
        bank: '',
        cardNumber: '',
        cardNumberCopy: '',
        openAccountBank: '',
        accountName: '',
        idCard: '',
        bankAccountType: 0, // 1受试者 2家属
      },
      patientType: 0, // 0受试者 1家属
      searchKey: '',
      selectBankBodyShow: 0, // 0主页 1选银行 2选支行
      banksArr: [],
      bankInfoSubbranchList: [],
      myPopupShow: false,
      loading: false,
      bankAccountList: [
        {
          id: '',
          bank: '',
          cardNumber: '',
          cardNumberCopy: '',
          openAccountBank: '',
          accountName: '',
          idCard: '',
          bankAccountType: 1,
        },
        {
          id: '',
          bank: '',
          cardNumber: '',
          cardNumberCopy: '',
          openAccountBank: '',
          accountName: '',
          idCard: '',
          bankAccountType: 2,
        }
      ],
      selectBankClick: () => {
        if (state.patientType === 1) return
        state.selectBankBodyShow = 1
      },
      relationClick: (num: number) => {
        if (num == state.patientType) return
        if (num == 0) {
          state.bankAccountObj = deepClone(state.bankAccountList[0])
        } else if (num == 1) {
          if (!state.bankAccountList[1]?.id && !state.bankAccountList[1]?.idCard) {
            Toast('请先联系研究人员更新家属的银行卡信息') 
            return
          } else {
            // 先赋值再取第一个值
            state.bankAccountList[0] = deepClone(state.bankAccountObj)
            state.bankAccountObj = deepClone(state.bankAccountList[1])
          }
        }
        state.patientType = num
      },
      // 确认无误报错
      handleSave: () => {
        state.bankAccountMsg()
      },
      handleCancel: () => {
        if (!state.loading) {
          state.myPopupShow = false
        }
      },
      bankAccountBlur: () => {
        state.bankAccountObj.cardNumberCopy = state.bankAccountObj.cardNumber ? state.numBlank(state.bankAccountObj.cardNumber) : state.bankAccountObj.cardNumber
        if (state.bankAccountObj?.cardNumber && state.bankAccountObj?.cardNumber?.length > 9) {
          getAccountNumberBankInfo(state.bankAccountObj.cardNumber).then((rest) => {
            const res = rest as getBankInfoListInter
            if (res?.bankName) {
              state.bankAccountObj.bank = res.bankName
            }
          })
        }
      },
      // 选择支行
      handleOpenAccountBank: () =>{
        if (state.patientType === 1) return
        if (!state.bankAccountObj?.cardNumber){
          Toast(`请输入银行卡号`)
        } else if (regex.test(state.bankAccountObj?.cardNumber)){
          state.getBankInfoSubbranchFun()
          state.selectBankBodyShow = 2
        } else {
          Toast(`请输入正确的银行卡号`)
        }
      },
      // 确认选择银行-支行
      setBankInfo: (item) => {
        if (item?.bankName && state.selectBankBodyShow === 1) {
          state.bankAccountObj.bank = item.bankName
        } else if (item?.lName && state.selectBankBodyShow === 2) {
          state.bankAccountObj.openAccountBank = item.lName
        }
        state.searchKey = ''
        state.selectBankBodyShow = 0
      },
      routerGo: (path) => {
        const query = {
          bankAccountType: state.bankAccountObj.bankAccountType,
          // itemId: item
          // bank: state.bankAccountObj.bank
        };
        proxy.$routerGoFun("routerInnPush", path, query, route.path);
      },
      changeSearchKey: (e) => {
        delay(() => {
          if (state.selectBankBodyShow === 1){
            state.getBankInfoListFun(e)
          } else {
            state.bankInfoSubbranchList = []
            state.getBankInfoSubbranchFun(e)
          }
        }, 300);
      },
      saveConfirmBankInfo: () => {
        const {
          bank,
          cardNumber,
          openAccountBank,
          accountName,
          idCard
        } = state.bankAccountObj
        if (!bank || !cardNumber ||
        !openAccountBank ||
        !accountName || !idCard) {
          Toast(`请完善信息`)
          return
        }
        state.accountVerify()
      },
      // 处理
      numBlank: (val) => {
        let result = ''
        if (val) {
          result = val.split('').reduce((acc, curr, index) => {
            if (index % 4 === 0 && index !== 0) acc += ' ';
            return acc + curr;
          }, '').trim();
        }
        return result
      },
      // 调用验证接口
      accountVerify: () => {
        if (!state.loading) {
          state.loading = true
          if (state.bankAccountObj.bankAccountType === 2 && state.patientType === 1) { // 家属
            state.bankAccountMsg()
          } else if (state.bankAccountObj.bankAccountType === 1 && state.patientType === 0) { // 受试者
            getBankAccountVerify(store.state.homeList.dctPatientId,state.bankAccountObj).then(() => {
              getAccountNumberBankInfo(state.bankAccountObj?.cardNumber)
              .then((rest) => {
                const res = rest as getBankInfoListInter
                // 将银行与卡号 一致
                if (res?.bankName) {
                  state.bankAccountObj.bank = res.bankName
                  state.bankAccountMsg()
                } else if (!res?.bankName) {
                  const result = state.numBlank(state.bankAccountObj.cardNumber)
                  state.bankAccountObj.cardNumberCopy = result
                  state.myPopupShow = true
                  state.loading = false
                  // Toast(`请填写正确的银行卡号`)
                }
              }).catch(() => {
                const result = state.numBlank(state.bankAccountObj.cardNumber)
                state.bankAccountObj.cardNumberCopy = result
                state.myPopupShow = true
                state.loading = false
              })
            }).catch(() => {
              state.loading = false
            })
          }
        }
      },
      // 确认无误
      bankAccountMsg: () => {
        postBankAccount(store.state.homeList.dctPatientId,state.bankAccountObj)
          .then(() => {
            state.routerGo('/my/balanceDetail')
            state.myPopupShow = false
            state.loading = false
          }).catch(() => { 
            state.loading = false
          })
      },
      getBankInfoListFun: (key = '',pageSize = 500) => {
        getBankInfoList({ 
          pageSize, // 条数
          key,
          // offset: 0,
          // bank_type: 0, // 1对公，2对私
        }).then(rest => {
          if (rest && Array.isArray(rest)) {
            const res = rest as getBankInfoListInter[]
            state.banksArr = res
          }
        })
      },
      // 获取支行
      getBankInfoSubbranchFun: (keyword = '', page = 1) => {
        getBankInfoSubbranch({ 
          page, // 条数
          keyword,
          accountNumber: state.bankAccountObj.cardNumber,
        }).then((rest) => {
          if(Array.isArray(rest) && rest?.length) {
            if (state.bankInfoSubbranchList?.length) {
              state.bankInfoSubbranchList = [...state.bankInfoSubbranchList,...rest]
            } else {
              state.bankInfoSubbranchList = rest
            }
            if (rest?.length === 10 && page < 2) {
              state.getBankInfoSubbranchFun(keyword, page += 1)
            }
          }
        })
      },
      onLoad: () => {
        setTimeout(() => {
          state.outerHeight = window.outerHeight;
        }, 0);
        state.getBankInfoListFun()
        if (store.state?.homeList?.dctPatientId) {
          getBankAccount(store.state.homeList.dctPatientId)
          .then((rest) => {
            const res = rest as any
            if (res && res?.length) {
              res.forEach((item) => {
                item.cardNumberCopy = state.numBlank(item?.cardNumber)
                if (item?.bankAccountType === 1) { // 不要deepclone改就改了
                  state.bankAccountList[0] = item
                }
                if (item?.bankAccountType === 2) {
                  state.bankAccountList[1] = item
                }
              })
              const show = res.every(item => item.bankAccountType === 2);
              if (show) {
                state.bankAccountObj = deepClone(state.bankAccountList[1])
                state.patientType = 1
              } else {
                state.bankAccountObj = deepClone(state.bankAccountList[0])
                state.patientType = 0
              }
            } else {
              state.bankAccountObj = deepClone(state.bankAccountList[0])
              state.patientType = 0
            }
          })
        }
      },
    });
    onBeforeMount(() => {
      state.onLoad();
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.bank-color {
  color: #5860DA;
  border: 1px solid #A6AAEB;
}
/* 1. 声明过渡效果 */
.fade-move,
.fade-enter-active,
.fade-leave-active {
  transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
}

/* 2. 声明进入和离开的状态 */
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: scaleY(0.01) translate(30px, 0);
}

/* 3. 确保离开的项目被移除出了布局流
      以便正确地计算移动时的动画效果。 */
.fade-leave-active {
  position: absolute;
}
.confirm-bank-card-information-body {
  height: calc(100vh - 46px - 0.55rem);
  .van-cell::after {
    border: none;
  }
}
:deep(.van-field__control:disabled) {
  color: #000;
  -webkit-text-fill-color: #000;
}
/*选择银行-模块*/
.select-bank-body {
  // 搜索框样式
  .van-search {
    :deep(.van-search__content) {
      border-radius: 0.2rem;
      padding: 0.05rem 0 0.05rem 0.14rem;
    }
  }
}
</style>
