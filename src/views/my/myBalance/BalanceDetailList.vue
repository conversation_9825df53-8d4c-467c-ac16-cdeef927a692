<template>
  <div class="h-100-vh ft-12-rem overflow-hidden">
    <van-nav-bar title="余额明细" left-arrow @click-left="$routerBackFun()" />
    <div 
    ref="myBalanceDetailListRef"
    @scroll="scrollChage"
    class="h-100vh-46px p-01rem box-border overflow-auto scrollnone">
      <!-- @click="routerGo('/my/balanceDetail', item)" -->
      <div
        v-for="(item, index) in balanceDetailList"
        :key="index"
        class="commmon-card-module font-color-999 mb-01rem ft-14-rem"
      >
        <div class="mb-01rem font-color-333 centerflex-h justify-between">
          <div>{{item?.remark || ''}}</div>
          <div>
            {{item?.changeTypeName || ''}}{{item?.amount || ''}}
          </div>
        </div>
        <div class="my-01rem">{{item?.createTime || ''}}</div>
        <div>单号{{item?.orderNo || ''}}</div>
      </div>
      <!-- <div class="item-nos">没有更多数据了～</div> -->
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  onBeforeMount,
  reactive,
  toRefs,
} from "vue";
import { useStore } from "vuex";
import { Toast } from "vant";
import { getFinanceChangeLog } from '@/api/myBalance';
import { getFinanceChangeLogInter, balanceDetailListStateInter } from "@/types/my";

export default defineComponent({
  name: "BalanceDetailList", // 余额明细

  setup() {
    const store = useStore()
    const state: balanceDetailListStateInter = reactive({
      myBalanceDetailListRef: null,
      balanceDetailList: [],
      pageIndex: 1,
      pageSize: 20,
      totalItemCount: 0,
      scrollChage: () => {
        const { scrollHeight, scrollTop, clientHeight } = state.myBalanceDetailListRef
        const scrollMoveHeight = parseInt(scrollHeight - scrollTop + '')
        const clientHeightArr = [clientHeight, clientHeight - 1, clientHeight + 1]
        if (clientHeightArr.includes(scrollMoveHeight) && state.balanceDetailList?.length < state.totalItemCount) {
          state.pageIndex += 1
          state.onLoad()  
        }
      },
      onLoad: () => {
        Toast.loading({
          duration: 300000,
          message: "加载中...",
          forbidClick: true
        });
        const { pageIndex, pageSize } = state;
        getFinanceChangeLog(store.state.homeList.dctPatientId,{ pageIndex, pageSize, })
          .then((rest) => {
            const res = rest as getFinanceChangeLogInter
            if (pageIndex > 1) {
              state.balanceDetailList = state.balanceDetailList.concat(res.items);
            } else if (res?.items?.length) {
              state.balanceDetailList = res.items;
            }
            state.totalItemCount = res?.totalItemCount || 0;
            Toast.clear();
          })
          .catch(() => {
            Toast.clear();
          });
      },
    });
    onBeforeMount(() => {
      state.onLoad();
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>
