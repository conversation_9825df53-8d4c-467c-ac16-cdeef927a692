<template>
  <div class="editaddress-container">
    <van-nav-bar
      title="修改地址"
      left-text=""
      left-arrow
      @click-left="$routerBackFun()"
    />
    <div class="editaddress-body">
      <div class="editaddress-module">
        <h4>我的地址</h4>
        <van-form @submit="onSubmit">
          <van-cell-group inset>
            <van-field
              v-model.trim="editAddressform.name"
              name="姓名:"
              label="姓名:"
              placeholder="请输入姓名"
              :rules="[{ required: true, message: '请输入姓名' }]"
            />
          </van-cell-group>
          <van-divider />
          <van-cell-group inset>
            <van-field
              v-model.trim="editAddressform.mobile"
              name="手机号:"
              label="手机号:"
              placeholder="请输入手机号"
              :rules="[
              {
                validator: (value) => {
                  return myreg.test(value);
                },
                message: '请输入正确的手机号',
                trigger: 'onBlur',
              },
            ]"
            />
          </van-cell-group>
          <van-divider />
          <!-- 地区 -->
          <van-field
            v-model.trim="editAddressform.cityValue"
            class="citys"
            is-link
            readonly
            label="省市区:"
            placeholder="请选择地区"
            :rules="[{ required: true, message: '请选择省市区' }]"
            @click="showAddress = true"
          />
          <van-popup v-model:show="showAddress" round position="bottom">
            <van-cascader
              v-model.trim="cascaderValue"
              title="请选择所在地区"
              :options="cityOptions"
              @close="showAddress = false"
              @finish="onFinish"
            />
          </van-popup>
          <!-- 详细地址 autosize自适应高度-->
          <van-cell-group inset>
            <van-field
              type="textarea"
              v-model.trim="editAddressform.addressDetail"
              name="详细地址:"
              label="详细地址:"
              placeholder="请输入详细地址"
              autosize
              :rules="[{ required: true, message: '请输入详细地址' }]"
            />
          </van-cell-group>
          <van-divider />           
        </van-form>
      </div>
    </div>
    <van-button class="editaddress-save-btn" round type="primary" native-type="submit"
    @click="onSubmit"
    >保存</van-button>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount, getCurrentInstance } from "vue";
import { Toast, Notify } from "vant";
import { getMyAddress, putMyAddress, getCitys } from "@/api/my";
import { recursionRemoveEmpty } from '@trialdata/common-fun-css/index'
import { EditAddress } from "@/types/my";

export default defineComponent({
  name: "EditAddress", // 修改地址
  setup() {
    const proxy: any = getCurrentInstance()?.proxy
    const state: EditAddress = reactive({
      myreg: /^[1][3-9][0-9]{9}$/,
      editAddressform: {
        addressId: "",
        name: "",
        mobile: "",
        cityValue: "", // 城市
        addressDetail: "", // 详细地址
        province: '',
        area: "",
        city: "",
        provinceCode: '',
        cityCode: '',
        areaCode: ''
      },
      showAddress: false,
      cascaderValue: "", // 地区号:330100
      cityOptions: [],
      // 获取表单数据-城市信息
      getEditAddressform: () => {
        getMyAddress()
          .then((res) => {
            if (Array.isArray(res) && res.length) {
              state.editAddressform = res[0]
              state.editAddressform.cityValue = `${res[0]?.province || ''} ${res[0]?.city || ''} ${res[0]?.area || ''}`
              state.cascaderValue = res[0].areaCode
            }
          })
        getCitys().then((res) => {
          recursionRemoveEmpty(res)
          state.cityOptions = res
        })
      },
      successBack: function* () {
        yield Notify({ type: "success", message: "修改成功" });
        yield proxy.$routerBackFun()
        return false;
      },
      // 城市选择事件
      onFinish: ({ selectedOptions }) => {
        state.showAddress = false;
        state.editAddressform.cityValue = selectedOptions
          .map((option) => option.text)
          .join(" ");
        state.editAddressform.provinceCode = selectedOptions[0]?.value
        state.editAddressform.cityCode = selectedOptions[1]?.value
        state.editAddressform.areaCode = selectedOptions[2]?.value
      },
      onSubmit: () => {
        const {
          name,
          mobile,
          cityValue,
          addressDetail,
        } = state.editAddressform;
        if (!name) {
          Toast("请输入姓名");
          return;
        } else if (!mobile) {
          Toast("请输入正确的手机号");
          return;
        } else if (!cityValue) {
          Toast("请选择城市");
          return;
        } else if (!addressDetail) {
          Toast("请输入详细地址");
          return;
        }
        const cityValues: Array<string> = cityValue.split(' ')
        state.editAddressform.province = cityValues[0]
        state.editAddressform.city = cityValues[1]
        state.editAddressform.area = cityValues[2]
        putMyAddress(state.editAddressform)
        .then(() => {
          const successBackIterator = state.successBack()
          successBackIterator.next()
          successBackIterator.next()
        });
      },
    });
    
    onBeforeMount(() => {
      state.getEditAddressform()
    });
    
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.editaddress-container {
  height: 100vh;
  overflow: hidden;
  background: #f0f0f0;
  color: #555;
  font-size: 0.14rem;
  .editaddress-body {
    width: 100%;
    height: calc(80vh - 0.15rem);
    padding: 0.1rem;
    margin: 0.25rem 0 0 0;
    box-sizing: border-box;
    .editaddress-module {
      width: 100%;
      padding: 0.1rem;
      box-sizing: border-box;
      background: #fff;
      border-radius: 0.1rem;
      // 更改 错误提示
      // :deep(.van-field__error-message) {
      //   position: absolute;
      //   bottom: -0.15rem;
      // }
      h4 {
        font-size: 0.13rem;
        margin: 0 0 0.2rem 0;
      }

      .van-divider {
        width: 90%;
        margin: 0 0.2rem 0 0.16rem;
      }
      :deep(.van-cell-group,
      van-cell-group--inset) {
        padding: 0;
        margin: 0;
      }
      :deep(.van-field__control:disabled) {
        -webkit-text-fill-color: #000;
        color: #000;
      }
      :deep(.van-cell__title,
      .van-field__label,
      .label-text) {
        width: 0.6rem;
        -webkit-text-fill-color: #000;
        color: #000;
      }
    }
  }
  .editaddress-save-btn {
    width: 80%;
    height: 0.39rem;
    margin: 0.1rem 10%;
  }
}
</style>