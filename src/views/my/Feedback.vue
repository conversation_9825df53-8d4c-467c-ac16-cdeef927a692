<template>
  <div class="feedback-container">
    <van-nav-bar
      title="系统使用反馈"
      left-text=""
      left-arrow
      @click-left="$routerBackFun"
    />
    <div class="feedback-form scrollnone">
      <div class="feedback-body">
        <!-- 型号 -->
        <div class="feedback-module">
          <!-- <h4>中心联络信息</h4> -->
          <van-cell-group inset>
            <van-field
              v-model.trim="editAddressform.mobileType"
              name="手机机型:"
              label="手机机型:"
              placeholder="请输入（例如：iphoneX）"
              maxlength="50"
            />
          </van-cell-group>
          <van-divider />
          <van-cell-group inset>
            <van-field
              v-model.trim="editAddressform.systemVersion"
              name="系统版本："
              label="系统版本："
              placeholder="请输入（例如：ios11）"
              maxlength="50"
            />
          </van-cell-group>
        </div>
        <!-- 问题描述 -->
        <div class="feedback-module">
          <van-cell-group inset>
            <van-field
              v-model.trim="editAddressform.describe"
              type="textarea"
              name="问题描述:"
              label="问题描述:"
              placeholder="请描述您在使用过程中遇到的问题"
              maxlength="300"
              autosize
            />
          </van-cell-group>
          <van-divider />
        </div>
        <!-- 问题截图 (上传和删除接口->传递)-->
        <div class="feedback-module feedback-scroll">
          <p>问题截图：</p>
          <!-- :myMultiple="false" -->
          <UploadFeedbackImg />
        </div>
      </div>
      <!-- 底部 -->
      <van-button
        :loading="submitLoadingFlag"
        loading-text="提交"
        round
        type="primary"
        class="btn"
        @click="onSubmit"
        >提交</van-button
      >
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  onBeforeMount,
  provide,
  getCurrentInstance,
} from "vue";
import { Toast, Notify } from "vant";
import UploadFeedbackImg from "@/components/UploadFeedbackImg.vue";
import { SystemFeedbackInfoAsync } from "@/api/my";
import { Feedback } from "@/types/my";
import { useStore } from "vuex";

export default defineComponent({
  name: "Feedback", // 系统使用反馈
  components: {
    UploadFeedbackImg,
  },
  setup() {
    const store = useStore();
    const proxy: any = getCurrentInstance()?.proxy;
    const state: Feedback = reactive({
      fileList: [], // 用于子组件传值的数组
      editAddressform: {
        mobileType: "", // 手机机型
        systemVersion: "", // 系统版本
        describe: "", // 问题描述
      },
      submitLoadingFlag: false,
      onSubmit: () => {
        const { systemVersion, mobileType, describe } = state.editAddressform;
        if (!mobileType) {
          Toast("请输入手机机型");
          return;
        } else if (!systemVersion) {
          Toast("请输入系统版本");
          return;
        } else if (!describe) {
          Toast("请输入问题描述");
          return;
        } else {
          state.submitLoadingFlag = true;
          // 动态传递了
          SystemFeedbackInfoAsync({
            PhoneModels: state.editAddressform.mobileType,
            SystemVersion: state.editAddressform.systemVersion,
            ProblemDescription: state.editAddressform.describe,
            CheckImageFiles: state.fileList,
            PortType: store.state?.appKey && store.state?.appKey?.toLowerCase() === 'app' ? 1 : 2,
          })
            .then(() => {
              Notify({ message: "提交成功", type: "success" });
              proxy.$routerBackFun();
            })
            .catch(() => {
              state.submitLoadingFlag = false;
            });
        }
      },
    });
    provide("fileList", state.fileList);
    onBeforeMount(() => {
      // state.editAddressform = res
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.feedback-container {
  height: 100vh;
  overflow: hidden;
  background: #f0f0f0;
  color: #555;
  font-size: 0.14rem;
  .feedback-form {
    .feedback-body {
      width: 100%;
      height: 80vh;
      padding: 0.1rem;
      margin: 0.1rem 0 0 0;
      box-sizing: border-box;
      .feedback-scroll {
        max-height: calc(100vh - 75%);
        overflow-y: scroll;
      }
      .feedback-module {
        width: 100%;
        padding: 0.1rem 0;
        box-sizing: border-box;
        margin: 0 0 0.16rem 0;
        background: #fff;
        border-radius: 0.06rem;
        .upload {
          width: 100%;
          display: flex;
          justify-content: center;
          :deep(.van-uploader) {
            width: 3.55rem;
            max-width: 355px;
            :deep(.van-uploader__wrapper) {
              width: 100%;
            }
          }
          :deep(.van-uploader__preview) {
            margin: 0 0rem 0.3rem 8.3%;
          }
          :deep(.van-uploader__upload) {
            margin: 0 0rem 0.3rem 8.3%;
          }
        }
        p {
          padding: 0 0.15rem;
          box-sizing: border-box;
          font-size: 0.13rem;
          margin: 0.01rem 0 0.2rem 0;
          color: #000;
        }
        .van-divider {
          width: 90%;
          margin: 0 0.2rem 0 0.16rem;
        }
        :deep(.van-cell-group, van-cell-group--inset) {
          padding: 0;
          margin: 0;
        }
        :deep(.van-field__control:disabled) {
          -webkit-text-fill-color: #000;
          color: #000;
        }
        // table 输入框的左label
        :deep(.van-cell__title, .van-field__label, .label-text) {
          width: 0.8rem;
          -webkit-text-fill-color: #000;
          color: #000;
        }
      }
    }
    .btn {
      width: 80%;
      height: 0.39rem;
      margin: 0.1rem 10%;
    }
  }
}
</style>