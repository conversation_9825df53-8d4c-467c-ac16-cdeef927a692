<template>
  <div class="w-full bg-[#f5f5f5] h-[100vh] absolute top-0 z-[999] aboutus-container">
    <van-nav-bar
      title="关于我们"
      left-arrow
      @click-left="$emit('close')"
    />
    <div class="whitespace-pre-wrap break-all ft-15-rem p-[0.3rem] text-center">
      {{ currentVersion }}
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import { getCurrentVersion } from '@/api/my';
import { isIOS } from '@/utils/debounce';
import { useStore } from 'vuex';

export default defineComponent({
  name: 'AboutUs',
  emits: ['close'],
  setup() {
    const store = useStore();
    const currentVersion = ref('')
    // 0 为安卓， 1 为ios , 4为  h5, 9为后台
    getCurrentVersion({
      appType: !store.state?.appToken ? 4 : (isIOS() ? 1 : 0)
    }).then(res => {
      currentVersion.value = res as string
    })
    return {
      currentVersion
    };
  },
});
</script>
