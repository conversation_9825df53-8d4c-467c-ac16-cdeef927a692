<template>
  <div class="myorder-container">
    <van-nav-bar
      title="我的订单"
      left-text=""
      left-arrow
      @click-left="myOrderBack()"
    />
    <div 
      ref="myOrderListRef"
      class="myorder-body scrollnone"
      @scroll="myOrderListScroll">
      <div 
        v-for="(item,index) in myOrderList"
        :key="index"
        class="myorder-module"
        @click="routerGo('/my/orderDetails',item)">
        <div class="myorder-head-item"
        :class="{'pb-006-rem': item?.orderType === 2}">
          <div v-if="item?.orderNo">
            订单编号: {{item.orderNo}}
          </div>
          <!-- 已下单 待取件 配送中 -->
          <div v-if="item?.orderType === 1 && item?.orderState" class="myorder-status-btn myorder-status-btn1"
          :class="{'myorder-status-btn2': item.orderState !== '已签收' && item.orderState !== '已取消'}"
          >{{item.orderState}}</div>
          <div v-else-if="item?.orderType === 2 && item?.orderState" class="text-gray-300"
          :class="{'text-blue-500': item.orderState !== '已完成' && item.orderState !== '已取消'}"
          >{{item.orderState}}</div>
        </div>
        <div class="myorder-item">
          <img v-if="item?.logisticsProviderLogo" :src="item.logisticsProviderLogo" alt="">
          <img v-else-if="item?.orderType === 1" src="@/assets/baby/sf.svg" alt="" />
          <img v-else-if="item?.orderType === 2" src="@/assets/baby/materialsIcon.svg" alt="" />
          <div>
            <div v-if="item?.orderType === 2" class="expressage-name none-warp-text-auto" v-html="item.ruleName"/>
            <div v-else-if="item?.orderType === 1" class="expressage-name none-warp-text-auto" v-html="item.logisticsProvider"/>
            <div class="order-num none-warp-text-auto">
              <div>快递单号:&nbsp;&nbsp;{{item?.waybillNo || ''}}</div>
            </div>
          </div>
        </div>
        <div v-if="item?.createTimeDisplay" v-html="item.createTimeDisplay" class="order-time flex justify-end"/>
      </div>
      <div v-if="totalItemCount > 0 && myOrderList?.length === totalItemCount" class="item-nos mt-1">没有更多了 ~</div>
      <myStateShow
        v-if="totalItemCount === 0 && myOrderList?.length === 0"
        :imgSrc="noinformedImgSrc"
        texts="暂无数据"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount, getCurrentInstance } from "vue";
import { useRoute } from 'vue-router';
import myStateShow from "@/components/MyStateShow.vue";
import noinformedImgSrc from '@/assets/baby/noinformed.svg';
import { getMyMaterialOrder } from '@/api/myOrder';
import { Toast } from 'vant';
import { myOrderStateInter, getMyMaterialOrderInter } from "@/types/my";

export default defineComponent({
  name: "MyOrder", // 我的订单
  components: {
    myStateShow
  },
  setup() {
    const proxy: any = getCurrentInstance()?.proxy
    const route = useRoute()
    const state: myOrderStateInter = reactive({
      noinformedImgSrc,
      myOrderListRef: null,
      // 订单=> 列表
      myOrderList: [],
      totalItemCount: 0,
      pageIndex: 1,
      pageSize: 20,
      // 回到上一页，其他任务新增提交后 - 回到任务详情
      myOrderBack: () => {
        if (route.query?.taskId) {
          proxy.$routerBackFun(2)
        } else {
          proxy.$routerBackFun()
        }
      },
      // 下拉加载更多
      myOrderListScroll: () => {
        const scroll = state.myOrderListRef
        if ((scroll.scrollHeight/1) - Math.ceil(scroll.scrollTop/1) === (scroll.clientHeight/1)) {
          // 触底
          if (state.myOrderList.length < state.totalItemCount) {
            state.pageIndex += 1
            state.onLoad()
          }
        } 
      },
      onLoad: () => {
        Toast.loading({
          duration: 300000,
          message: '加载中...',
          forbidClick: true,
        });
        getMyMaterialOrder({
          pageIndex: state.pageIndex,
          pageSize: state.pageSize
        }).then((rest) => {
          const res = rest as getMyMaterialOrderInter
          if (res?.items) {
            const oldOrderList = [...state.myOrderList]
            state.myOrderList = oldOrderList.concat(res.items)
          }
          state.totalItemCount = res.totalItemCount
          Toast.clear()
        }).catch(() => { Toast.clear() })
      },
      routerGo: (path: string,orderItem) => {
        const query = {
          orderItem: JSON.stringify(orderItem)
        }
        proxy.$routerGoFun('routerInnPush', path, query, route.path, route.query)
      },
    });

    onBeforeMount(() => {
      state.onLoad()
    });

    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.myorder-container {
  height: 100vh;
  overflow: hidden;
  background: #f7f7f7;
  color: #555;
  font-size: 0.12rem;
  .myorder-body {
    width: 100%;
    height: calc(100vh - 86px);
    padding: 0.1rem;
    margin: 0.1rem 0 0 0;
    box-sizing: border-box;
    overflow: auto;
    .myorder-module {
      width: 100%;
      padding: 0.1rem 0.1rem;
      margin: 0 0 0.1rem 0;
      box-sizing: border-box;
      background: #fff;
      border-radius: 0.05rem;
      .myorder-head-item{
        display: flex;
        justify-content: space-between;
      }
      .myorder-status-btn{
        padding: 0.02rem 0.04rem;
        font-size: 0.1rem;
        border-radius: 0.03rem;      
      }
      .myorder-status-btn1{     
        color: #777;
        border: 0.5px solid#c9c9c9;   
        background:#f5f5f5;
      }
      .myorder-status-btn2{ 
        color: var(--theme-color);
        border: 0.5px solid var(--theme-color);
        background:#EEEFFB;
      }
      .myorder-item {
          width: 100%;
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          margin: 0.1rem 0 0.06rem 0;
          img {
            width: 0.44rem;
            height: 0.44rem;
            margin: 0 0.1rem 0 0;
          }
          .expressage-name{
            font-size: 0.13rem;
            color: #333;
            margin: 0 0 0.06rem 0;
          }
          .order-num {
            font-size: 0.13rem;
            color: #999;
          }
      }
      .order-time{
        font-size: 0.1rem;
        color: #999;
      }
    }
  }
  // 暂无数据
  :deep(.myStateShow) {
    img {
      width: 1.36rem;
    }
  }
}
</style>
