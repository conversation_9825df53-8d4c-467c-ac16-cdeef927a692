<template>
  <div class="audit-container">
    <div class="audit-form scrollnone">
      <img
        v-if="
          patientICFStatus === 4003 ||
          patientICFStatus === 1001 ||
          auditStatus === 6 ||
          auditStatus === 21
        "
        class="w-full"
        src="@/assets/baby/audit2.png"
        alt=""
      />
      <img
        v-else-if="
          audit3ImgObj.patientICFStatusList.includes(patientICFStatus) ||
          audit3ImgObj.auditStatusList.includes(auditStatus)
        "
        class="w-full"
        src="@/assets/baby/audit3.png"
        alt=""
      />
      <img v-else class="w-full" src="@/assets/baby/audit1.png" alt="" />
      <div class="audit-form-title">审核进度</div>
      <div class="audit-body">
        <div class="audit-module centerflex-w-wrap">
          <img
            v-if="
              patientICFStatus === 4003 ||
              patientICFStatus === 1001 ||
              auditStatus === 6 ||
              auditStatus === 21
            "
            src="@/assets/baby/auditOn.svg"
            alt=""
          />
          <img
            v-else-if="
              patientICFStatus === 1003 ||
              patientICFStatus === 4005 ||
              auditStatus === 16 ||
              auditStatus === 37 ||
              auditStatus === 7 ||
              auditStatus === 8 ||
              auditStatus === 11 ||
              auditStatus === 22 ||
              auditStatus === 25
            "
            src="@/assets/baby/auditLose.svg"
            alt=""
          />
          <img
            v-else-if="
              patientICFStatus === 1004 ||
              patientICFStatus === 4006 ||
              auditStatus === 9 ||
              auditStatus === 17
            "
            src="@/assets/baby/auditSucceed.svg"
            alt=""
          />
          <img v-else src="@/assets/baby/auditAdd.svg" alt="" />
          <p
            v-if="
              patientICFStatus === 1001 ||
              patientICFStatus === 1002 ||
              patientICFStatus === 1003 ||
              patientICFStatus === 4004 ||
              (patientICFStatus >= 4005 && patientICFStatus <= 4009)
            "
            v-html="icfAuditComments"
          />
          <p
            v-else-if="
              (auditStatus >= 6 && auditStatus < 10) ||
              auditStatus === 11 ||
              auditStatus === 17 ||
              auditStatus === 16 ||
              auditStatus === 37 ||
              (auditStatus >= 21 && auditStatus < 26)
            "
            v-html="auditComments"
          />
          <p v-else>收到提交申请，正在审核中…</p>

          <van-button
            v-if="
              (patientICFStatus === 4006 ||
                auditStatus === 8 ||
                auditStatus === 23) &&
              patientICFStatus !== 1001 &&
              patientICFStatus !== 1002 &&
              patientICFStatus !== 4004
            "
            class="audit-common-btn"
            round
            type="primary"
            @click="routerGo('/questionnaire')"
            >{{ auditStatus === 8 ? "重新填写" : "继续下一步" }}</van-button
          >
          <van-button
            v-if="auditStatus === 9"
            class="audit-common-btn"
            round
            type="primary"
            @click="routerGo('/')"
            >继续下一步</van-button
          >
          <van-button
            v-if="patientICFStatus === 4008"
            class="audit-common-btn"
            round
            type="primary"
            @click="signComfirm"
            >继续下一步</van-button
          >
          <van-button
            v-else-if="patientICFStatus === 4004 || auditStatus === 22"
            class="audit-common-btn"
            round
            type="primary"
            @click="routerGo('/recordPersonalStatement')"
            >重新录制</van-button
          >
          <van-button
            v-else-if="patientICFStatus === 1002"
            class="audit-common-btn"
            round
            type="primary"
            @click="routerGo('/informedQuestionnaire')"
            >重新填写</van-button
          >
          <van-button
            v-else-if="patientICFStatus === 4009"
            class="audit-common-btn"
            round
            type="primary"
            @click="handleSignature"
            >重新签名</van-button
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount, getCurrentInstance } from "vue";
import { useRouter } from "vue-router";
import { useStore } from "vuex";
import { getPatientStatus } from '@/api/user'
import { postSupplementComfirm, postSignComfirm, getGetCASignatureStrategy } from '@/api/informed';
import { postSignComfirmInter } from '@/types/welcomeToJoinUs';

export default defineComponent({
  name: 'Audit', // 审核
  setup() {
    const proxy: any = getCurrentInstance()?.proxy
    const router = useRouter();
    const store = useStore();
    // 6 = 待审核, 7 = 审核失败, 9 = 审核成功,
    const state = reactive({
      // 头部： audit1.png Img 提交
      // audit2.png Img 审核中
      // audit3.png Img 审核完成
      audit3ImgObj: {
        patientICFStatusList: [4004, 4005, 4006, 4008, 4009, 1002, 1003, 1004],
        auditStatusList: [7, 8, 9, 11, 16, 37, 17, 21, 22, 23, 25]
      },
      // audit3ImgObj.patientICFStatusList.includes(patientICFStatus)
      auditStatus: 5, // 审核状态
      auditComments: '',// 审核语
      patientICFStatus: 0, // 知情状态
      icfAuditComments: '',
      patientStatusObj: {
        isAuthentication: false,
      },
      // 重新签名
      handleSignature: () => {
        // isAuthentication  true = 身份认证 false = 无需认证
        if (state.patientStatusObj.isAuthentication) {
          state.routerGo('/facialRecognition')
        } else {
          state.routerGo('/signInformed')
        }
      },
      // 跳转
      routerGo: (path) => {
        if (path === '/recordPersonalStatement') {
          postSupplementComfirm({
            icfStatementID: store.state?.userInformation?.icfStatementID || store.state?.userInformation?.icfStementId
          })
            .then(() => {
              router.replace(path)
            })
        } else {
          router.replace(path)
        }
      },
      // 知情继续下一步
      signComfirm: () => {
        postSignComfirm()
          .then((rest) => {
            const res = rest as postSignComfirmInter
            let path = ''
            if (res?.patientStatus === 5) {
              path = '/questionnaire' // 入排
            } else if (res?.patientStatus === 10 ||
              (res?.patientStatus > 31 && res?.patientStatus < 35)) {
              path = '/' // 入组
            }
            state.routerGo(path)
          })
      },
    })
    onBeforeMount(() => {
      store.dispatch('setGetOldQuestDataFlag', 0)
      if (store.state.auditFlag) {
        // 获取请求
        getPatientStatus({
          ICFStatementId: store.state?.userInformation?.icfStatementID || store.state?.userInformation?.icfStementId
        })
          .then(async (rest) => {
            const res: any = rest
            if (res?.patientICFStatus === 4007) {
              const data = await getGetCASignatureStrategy(store.state?.userInformation?.icfStatementID || store.state?.userInformation?.icfStementId)
              if (data === 2) {
                proxy.$routerGoFun('replace', '/signInformedFaDaDa')
                return
              }
            }
            state.patientStatusObj = res
            state.auditStatus = res.patientStatus
            state.patientICFStatus = res.patientICFStatus
            state.auditComments = res.auditComments
            state.icfAuditComments = res.icfAuditComments
            /*32 = 已绑定微信信息, 33 = 筛选期, 34 = 待入组*/
            if (!res?.patientICFStatus_Other && state.auditStatus > 31 && state.auditStatus < 35) {
              state.routerGo('/')
            }
          })
      } else {
        store.dispatch('setAuditFlag', true)
      }
    })

    return {
      ...toRefs(state)
    }
  }
});
</script>

<style scoped lang='less'>
.audit-container {
  height: 100vh;
  overflow: hidden;
  background: #fff;
  color: #555;
  font-size: 0.15rem;
  .audit-form {
    position: relative;
    .audit-form-title {
      position: absolute;
      top: 0.2rem;
      width: 100%;
      text-align: center;
      color: #fff;
    }
    .audit-body {
      width: 100%;
      height: 70vh;
      padding: 0.16rem;
      margin: 0.2rem 0 0 0;
      box-sizing: border-box;
      .audit-module {
        width: 100%;
        height: 90%;
        padding: 0.2rem 0;
        box-sizing: border-box;
        background: #fff;
        border-radius: 0.1rem;
        position: relative;
        top: -0.8rem;
        box-shadow: 0 0.02rem 0.26rem rgba(0, 0, 0, 0.07);
        img {
          width: 2.08rem;
        }
        p {
          width: 100%;
          padding: 0 0.1rem;
          box-sizing: border-box;
          text-align: center;
          font-size: 0.13rem;
          font-weight: 700;
          color: #1d40a5;
          margin-top: -0.35rem;
          word-break: break-all;
          word-wrap: break-word;
        }
      }
      .audit-common-btn {
        width: 90%;
      }
    }
  }
}
</style>
