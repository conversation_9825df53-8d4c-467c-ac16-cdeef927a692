<template>
  <div
    class="EditQuestionnairetList-container"
    :style="{ height: outerHeight ? outerHeight + 'px' : '100vh' }"
    :class="{ 'bg-color-f7f7f7': editFlag }"
  >
    <van-nav-bar title="编辑列表" left-arrow @click-left="$routerBackFun()" />
    <div class="EditQuestionnairetList-form">
      <div
        v-if="editFlag !== '1'"
        class="editVisitTaskDetails-body-r overflow-auto"
        :style="{ height: outerHeight ? outerHeight - 46 + 'px' : '90vh' }"
      >
        <div class="">
          <!-- 切换 访视名称 -->
          <div class="cut">
            <div
              v-if="questItem?.questName"
              v-html="questItem.questName"
              class="cut-body"
            />
            <div
              v-if="questItem?.fieldDescription"
              class="text-yellow-400 underline"
              @click="showFieldDescription(questItem.fieldDescription)"
            >
              填写指南
            </div>
          </div>
          <!-- 问卷-->
          <div
            v-if="questionObj?.questCrfItemes?.length"
            class="EditQuestionnairetList-module scrollnone"
          >
            <!-- 问卷 1 = 问卷完整列表 v-if="questionObj.questCrfType === 1"-->
            <div class="editdiscomfort-items">
              <div
                v-for="(item, index) in questionObj.questCrfItemes"
                :key="index"
              >
                <!-- crfFieldType: 1 = 文件集合, 2 = 文件, 3 = 列表, 4 = 普通字段, 5 = 说明字段, 6 = 子问卷 -->
                <div v-if="item.crfFieldType === 4 || item.crfFieldType === 6">
                  <!-- crfFieldControl 1 = 无控件, 2 = 文件上传, 3 = 单行文本控件, 4 = 多行文本控件, 
                  5 = 数字控件, 6 = 日期控件, 7 = 时间控件,
                  8 = 日期时间控件, 9 = 单选控件, 10 = 多选控件-->
                  <!-- 6 日期控件 -->
                  <div
                    v-if="
                      item.crfFieldControl === 6 &&
                      item.refTypesShow !== 2 &&
                      item.refTypeShow !== 2
                    "
                    class="questCrf-items"
                  >
                    <div
                      class="questCrf-items-title"
                      v-html="item.fieldLabel"
                    />
                    <div
                      v-if="item?.fieldDescription"
                      class="text-yellow-400 my-0.5 underline"
                      @click="showFieldDescription(item.fieldDescription)"
                    >
                      填写指南
                    </div>
                    <p style="white-space: pre-line; text-align: right">
                      {{ item.fieldValue }}
                    </p>
                    <div class="br" />
                    <div
                      v-if="
                        questionObj?.dataClarification &&
                        item?.clarify?.clarifyCount
                      "
                      class="underline mt-01-rem flex justify-end ft-13-rem"
                      style="color: #f19980"
                      @click="openDataClarification(item)"
                    >
                      数据澄清
                      <span v-if="item?.clarify?.clarifyCount">
                        （关闭{{ item?.clarify?.clarifyCloseCount }}/{{
                          item?.clarify?.clarifyCount
                        }}）</span
                      >
                    </div>
                  </div>
                  <!-- 3-8 单行文本控件 -->
                  <div
                    v-if="
                      item.refTypesShow !== 2 &&
                      (item.crfFieldControl === 3 ||
                        item.crfFieldControl === 4 ||
                        item.crfFieldControl === 5 ||
                        item.crfFieldControl === 7 ||
                        item.crfFieldControl === 8 ||
                        item.crfFieldControl === 11) &&
                      item.refTypeShow !== 2
                    "
                    class="questCrf-items"
                  >
                    <div
                      class="questCrf-items-title"
                      v-html="item.fieldLabel"
                    />
                    <div
                      v-if="item?.fieldDescription"
                      class="text-yellow-400 my-0.5 underline"
                      @click="showFieldDescription(item.fieldDescription)"
                    >
                      填写指南
                    </div>
                    <p
                      v-if="
                        item.crfFieldControl === 3 ||
                        (item.crfFieldControl === 5 && item?.dctQuestUnit)
                      "
                      style="white-space: pre-line; text-align: right"
                    >
                      {{ item.fieldValue }}
                      <span>{{ item.dctQuestUnit }}</span>
                    </p>
                    <p
                      v-else-if="item.crfFieldControl === 8"
                      style="white-space: pre-line"
                    >
                      {{ item.fieldValueStr }}
                    </p>
                    <p v-else style="white-space: pre-line">
                      {{ item.fieldValue }}
                    </p>
                    <div class="br" />
                    <div
                      v-if="
                        questionObj?.dataClarification &&
                        item?.clarify?.clarifyCount
                      "
                      class="underline mt-01-rem flex justify-end ft-13-rem"
                      style="color: #f19980"
                      @click="openDataClarification(item)"
                    >
                      数据澄清
                      <span v-if="item?.clarify?.clarifyCount">
                        （关闭{{ item?.clarify?.clarifyCloseCount }}/{{
                          item?.clarify?.clarifyCount
                        }}）</span
                      >
                    </div>
                    <!-- <van-field v-model="item.fieldValue" class="border-input" /> -->
                  </div>

                  <!-- 9单选类型 -->
                  <div
                    v-if="
                      item.refTypesShow !== 2 &&
                      item.crfFieldControl === 9 &&
                      item.refTypeShow !== 2
                    "
                    class="questCrf-items"
                  >
                    <div
                      class="questCrf-items-title"
                      v-html="item.fieldLabel"
                    />
                    <div
                      v-if="item?.fieldDescription"
                      class="text-yellow-400 my-0.5 underline"
                      @click="showFieldDescription(item.fieldDescription)"
                    >
                      填写指南
                    </div>
                    <van-radio-group
                      v-model="item.fieldValue"
                      direction="horizontal"
                      disabled
                    >
                      <van-radio
                        v-for="(radioItem, radioIndex) in item.fieldItems"
                        :key="radioIndex"
                        :name="radioItem.itemValue"
                        >{{ radioItem.itemName }}</van-radio
                      >
                    </van-radio-group>
                    <div
                      v-if="
                        questionObj?.dataClarification &&
                        item?.clarify?.clarifyCount
                      "
                      class="underline mt-01-rem flex justify-end ft-13-rem"
                      style="color: #f19980"
                      @click="openDataClarification(item)"
                    >
                      数据澄清
                      <span v-if="item?.clarify?.clarifyCount">
                        （关闭{{ item?.clarify?.clarifyCloseCount }}/{{
                          item?.clarify?.clarifyCount
                        }}）</span
                      >
                    </div>
                  </div>

                  <!-- 10 = 多选控件 -->
                  <div
                    v-if="
                      item.refTypesShow !== 2 &&
                      item.crfFieldControl === 10 &&
                      item.refTypeShow !== 2
                    "
                    class="questCrf-items"
                  >
                    <div
                      class="questCrf-items-title"
                      v-html="item.fieldLabel"
                    />
                    <div
                      v-if="item?.fieldDescription"
                      class="text-yellow-400 my-0.5 underline"
                      @click="showFieldDescription(item.fieldDescription)"
                    >
                      填写指南
                    </div>
                    <van-checkbox-group
                      v-model="item.fieldValue"
                      direction="horizontal"
                      disabled
                    >
                      <van-checkbox
                        v-for="(checkboxItem, checkboxIndex) in item.fieldItems"
                        :key="checkboxIndex"
                        :name="checkboxItem.itemValue"
                        >{{ checkboxItem.itemName }}</van-checkbox
                      >
                    </van-checkbox-group>
                    <div
                      v-if="
                        questionObj?.dataClarification &&
                        item?.clarify?.clarifyCount
                      "
                      class="underline mt-01-rem flex justify-end ft-13-rem"
                      style="color: #f19980"
                      @click="openDataClarification(item)"
                    >
                      数据澄清
                      <span v-if="item?.clarify?.clarifyCount">
                        （关闭{{ item?.clarify?.clarifyCloseCount }}/{{
                          item?.clarify?.clarifyCount
                        }}）</span
                      >
                    </div>
                  </div>
                </div>
                <!-- 上传控件 -->
                <!-- xx类型 -->
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        v-if="editFlag === '1'"
        class="editVisitTaskDetails-body overflow-auto"
        :style="{
          height:
            outerHeight && editFlag === '1' ? outerHeight - 91 + 'px' : '80vh',
        }"
      >
        <div class="cut">
          <div
            v-if="questItem?.questName"
            v-html="questItem.questName"
            class="cut-body"
          />
          <div
            v-if="questItem?.fieldDescription"
            class="text-yellow-400 underline"
            @click="showFieldDescription(questItem.fieldDescription)"
          >
            填写指南
          </div>
        </div>
        <div class="editVisitTaskDetails-body-module">
          <!-- 问卷进度 -->
          <div
            v-if="questionIndex !== -1 && questionObj.questCrfType === 2"
            class="questionnaire-schedule"
          >
            <div
              class="questionnaire-schedule-gotop"
              @click="saveQuestionnaire('subtract')"
            >
              <van-icon name="arrow-left" />
              <p>上一题</p>
            </div>
            <div class="questionnaire-schedule-back">
              <div
                :style="{
                  width:
                    questionObjLength > 1
                      ? ((questionIndex + 1) / questionObjLength) * 100 + '%'
                      : '100%',
                }"
                class="questionnaire-schedule-in"
              />
            </div>
            <div class="questionnaire-schedule-proportion">
              {{
                questionObjLength > 1
                  ? (questionIndex + 1) / questionObjLength > 1
                    ? "100"
                    : parseInt(((questionIndex + 1) / questionObjLength) * 100)
                  : "100"
              }}%
            </div>
          </div>
          <div
            class="questionnaire-module"
            :class="{
              'questionnaire-module-quest1': questionObj.questCrfType === 1,
            }"
          >
            <!-- 问卷类型questCrfType:1 = 问卷完整列表, 2 = 问卷向导列表,
             3 = 问卷Url, 4 = 上传图片的问卷 -->
            <div
              v-if="questionObj.questCrfType === 1"
              class="questionnaire-items"
            >
              <div
                v-for="(item, index) in questionObj.questCrfItemes"
                :key="item.id"
              >
                <!-- crfFieldType: 1 = 文件集合, 2 = 文件, 3 = 列表, 4 = 普通字段, 5 = 说明字段, 6 = 子问卷 -->
                <div v-if="item.crfFieldType === 4 || item.crfFieldType === 6">
                  <!-- crfFieldControl 1 = 无控件, 2 = 文件上传, 3 = 单行文本控件, 4 = 多行文本控件, 
                  5 = 数字控件, 6 = 日期控件, 7 = 时间控件,
                  8 = 日期时间控件, 9 = 单选控件, 10 = 多选控件 11 年月控件-->
                  <div
                    v-if="
                      item.crfFieldControl >= 3 &&
                      item.refTypeShow !== 2 &&
                      item.refTypesShow !== 2
                    "
                    class="questionnaire-item-moduel"
                  >
                    <div class="questionnaire-items-title">
                      <span
                        v-if="item.isRequired === 1"
                        class="isRequiredActive"
                        >*&nbsp;</span
                      >
                      <div v-html="item.fieldLabel" />
                    </div>
                    <div
                      v-if="item?.fieldDescription"
                      class="text-yellow-400 underline"
                      :class="{
                        'my-0.5':
                          item.crfFieldControl < 6 || item.crfFieldControl > 8,
                      }"
                      @click="showFieldDescription(item.fieldDescription)"
                    >
                      填写指南
                    </div>
                    <!-- 3单行文本控件 -->
                    <van-field
                      v-if="item.crfFieldControl === 3"
                      v-model="item.fieldValue"
                      class="border-input"
                      maxlength="999"
                      placeholder="请输入"
                      :disabled="item?.isReadOnly > 0"
                    >
                      <template #button>
                        <span
                          v-if="item?.dctQuestUnit"
                          v-html="item.dctQuestUnit"
                        />
                      </template>
                    </van-field>
                    <!-- 4多行文本控件 -->
                    <van-field
                      v-if="item.crfFieldControl === 4"
                      placeholder="请输入"
                      v-model.trim="item.fieldValue"
                      maxlength="3999"
                      type="textarea"
                      class="border-input"
                      autosize
                      :disabled="item?.isReadOnly > 0"
                    />
                    <!-- 5数字控件 -->
                    <van-field
                      v-if="item.crfFieldControl === 5"
                      v-model="item.fieldValue"
                      maxlength="16"
                      :placeholder="item?.isReadOnly > 0 ? '' : '请输入'"
                      :disabled="item?.isReadOnly > 0"
                      readonly
                      clickable
                      @focus="item.showNumberKeyboard = true"
                      class="border-input"
                    >
                      <template #button>
                        <span
                          v-if="item?.dctQuestUnit"
                          v-html="item.dctQuestUnit"
                        />
                      </template>
                    </van-field>
                    <van-number-keyboard
                      v-model="item.numberFieldValue"
                      :show="item.showNumberKeyboard"
                      theme="custom"
                      extra-key="."
                      close-button-text="完成"
                      @input="
                        (e) => {
                          item.fieldValue += e;
                          keyUpNum(item, index);
                        }
                      "
                      @delete="
                        () => {
                          if (item?.fieldValue) {
                            item.fieldValue = item.fieldValue.slice(0, -1);
                          }
                        }
                      "
                      @blur="
                        item.showNumberKeyboard = false;
                        keyUpNum(item);
                      "
                    />
                    <!-- 6日期控件 -->
                    <div v-if="item.crfFieldControl === 6">
                      <!-- 值回显 -->
                      <van-field
                        v-model="item.fieldValue"
                        is-link
                        readonly
                        name="datetimePicker"
                        label="日期选择"
                        placeholder="点击选择日期"
                        @click="pickerDate(index)"
                      />
                      <van-popup
                        v-model:show="item.showDatas"
                        position="bottom"
                        style="min-height: 308px"
                      >
                        <van-picker
                          :columns="specificColumns"
                          @change="specificColumnsChange"
                          @confirm="
                            (e) => {
                              const startIndex = e.findIndex((e) => e === 'UK');
                              if (startIndex !== -1) {
                                e.fill('UK', startIndex + 1);
                              }
                              item.fieldValue = e[0] + '-' + e[1] + '-' + e[2];
                              item.showDatas = false;
                            }
                          "
                          @cancel="item.showDatas = false"
                        >
                          <template #title>
                            <div
                              class="ft-13-rem font-color-969799"
                              @click="
                                () => {
                                  item.fieldValue = '';
                                  item.showDatas = false;
                                }
                              "
                            >
                              清空
                            </div>
                          </template>
                        </van-picker>
                      </van-popup>
                    </div>
                    <!-- 7 = 时间控件, -->
                    <div v-if="item.crfFieldControl === 7">
                      <van-field
                        v-model="item.fieldValue"
                        is-link
                        readonly
                        name="datetimePicker"
                        label="时间选择"
                        placeholder="点击选择时间"
                        @click="pickerDay(index)"
                      />
                      <van-popup
                        v-model:show="item.showTimes"
                        position="bottom"
                        style="min-height: 308px"
                      >
                        <van-picker
                          :columns="timeColumns"
                          @change="columnsTimeChange"
                          @confirm="
                            (e) => {
                              const startIndex = e.findIndex((e) => e === 'UK');
                              if (startIndex !== -1) {
                                e.fill('UK', startIndex + 1);
                              }
                              item.fieldValue = e[0] + ':' + e[1];
                              item.showTimes = false;
                            }
                          "
                          @cancel="item.showTimes = false"
                        >
                          <template #title>
                            <div
                              class="ft-13-rem font-color-969799"
                              @click="
                                () => {
                                  item.fieldValue = '';
                                  item.showTimes = false;
                                }
                              "
                            >
                              清空
                            </div>
                          </template>
                        </van-picker>
                      </van-popup>
                    </div>
                    <!-- 8 = 日期时间控件 -->
                    <div v-if="item.crfFieldControl === 8">
                      <div class="date-and-time">
                        <!-- 日期 -->
                        <van-field
                          v-model="item.fieldValue[0]"
                          is-link
                          readonly
                          name="datetimePicker"
                          label="日期选择"
                          placeholder="点击选择日期"
                          @click="pickerDate(index)"
                        />
                        <van-popup
                          v-model:show="item.showDatas"
                          position="bottom"
                        >
                          <van-picker
                            :columns="specificColumns"
                            @change="specificColumnsChange"
                            @confirm="
                              (e) => {
                                const startIndex = e.findIndex(
                                  (e) => e === 'UK'
                                );
                                if (startIndex !== -1) {
                                  e.fill('UK', startIndex + 1);
                                }
                                item.fieldValue[0] =
                                  e[0] + '-' + e[1] + '-' + e[2];
                                item.showDatas = false;
                              }
                            "
                            @cancel="item.showDatas = false"
                          >
                            <template #title>
                              <div
                                class="ft-13-rem font-color-969799"
                                @click="
                                  () => {
                                    item.fieldValue[0] = '';
                                    item.showDatas = false;
                                  }
                                "
                              >
                                清空
                              </div>
                            </template>
                          </van-picker>
                        </van-popup>
                        <!-- 时间 -->
                        <van-field
                          v-model="item.fieldValue[1]"
                          is-link
                          readonly
                          name="datetimePicker"
                          label="时间选择"
                          placeholder="点击选择时间"
                          @click="pickerDay(index)"
                        />
                        <van-popup
                          v-model:show="item.showTimes"
                          position="bottom"
                        >
                          <van-picker
                            :columns="timeColumns"
                            @change="columnsTimeChange"
                            @confirm="
                              (e) => {
                                const startIndex = e.findIndex(
                                  (e) => e === 'UK'
                                );
                                if (startIndex !== -1) {
                                  e.fill('UK', startIndex + 1);
                                }
                                item.fieldValue[1] = e[0] + ':' + e[1];
                                item.showTimes = false;
                              }
                            "
                            @cancel="item.showTimes = false"
                          >
                            <template #title>
                              <div
                                class="ft-13-rem font-color-969799"
                                @click="
                                  () => {
                                    item.fieldValue[1] = '';
                                    item.showTimes = false;
                                  }
                                "
                              >
                                清空
                              </div>
                            </template>
                          </van-picker>
                        </van-popup>
                      </div>
                    </div>
                    <!-- 9单选类型 -->
                    <van-radio-group
                      v-if="item.crfFieldControl === 9"
                      v-model="item.fieldValue"
                      direction="horizontal"
                      :disabled="item?.isReadOnly > 0"
                    >
                      <van-radio
                        v-for="(radioItem, radioIndex) in item.fieldItems"
                        :key="radioIndex"
                        :name="radioItem.itemValue"
                        @click="invertSelectionFun(radioItem.itemValue, item)"
                        >{{ radioItem.itemName }}</van-radio
                      >
                      <!-- <van-radio name="2">单选框 2</van-radio> -->
                    </van-radio-group>
                    <!-- 10 = 多选控件 -->
                    <van-checkbox-group
                      v-if="item.crfFieldControl === 10"
                      v-model="item.fieldValue"
                      :disabled="item?.isReadOnly > 0"
                    >
                      <van-checkbox
                        v-for="(checkboxItem, checkboxIndex) in item.fieldItems"
                        :key="checkboxIndex"
                        :name="checkboxItem.itemValue"
                        @click="mutualExclusion(item, checkboxItem)"
                        >{{ checkboxItem.itemName }}</van-checkbox
                      >
                      <!-- <van-checkbox name="b">复选框 b</van-checkbox> -->
                    </van-checkbox-group>
                    <!-- 11 = 年月控件 -->
                    <div v-if="item.crfFieldControl === 11">
                      <van-field
                        v-model="item.fieldValue"
                        is-link
                        readonly
                        label="年月选择"
                        placeholder="点击选择年月"
                        @click="pickerYearMonth(index)"
                      />
                      <van-popup
                        v-model:show="item.showYearMonth"
                        position="bottom"
                        style="min-height: 308px"
                      >
                        <van-picker
                          :columns="yearMonthColumns"
                          @change="yearMonthColumnsChange"
                          @confirm="
                            (e) => {
                              const startIndex = e.findIndex((e) => e === 'UK');
                              if (startIndex !== -1) {
                                e.fill('UK', startIndex + 1);
                              }
                              item.fieldValue = e[0] + '-' + e[1];
                              item.showYearMonth = false;
                            }
                          "
                          @cancel="item.showYearMonth = false"
                        >
                          <template #title>
                            <div
                              class="ft-13-rem font-color-969799"
                              @click="
                                () => {
                                  item.fieldValue = '';
                                  item.showYearMonth = false;
                                }
                              "
                            >
                              清空
                            </div>
                          </template>
                        </van-picker>
                      </van-popup>
                    </div>
                    <!-- 数据澄清 -->
                    <div
                      v-if="
                        questionObj?.dataClarification &&
                        item?.clarify?.clarifyCount
                      "
                      class="underline mt-01-rem flex justify-end ft-13-rem"
                      style="color: #f19980"
                      @click="openDataClarification(item)"
                    >
                      数据澄清
                      <span v-if="item?.clarify?.clarifyCount">
                        （关闭{{ item?.clarify?.clarifyCloseCount }}/{{
                          item?.clarify?.clarifyCount
                        }}）</span
                      >
                    </div>
                  </div>
                </div>

                <!-- 文件集合 -->
                <!-- xx类型 -->
              </div>
            </div>

            <!-- 问卷 2 = 问卷向导列表 -->
            <div
              v-if="questionObj.questCrfType === 2"
              class="questionnaire-items"
            >
              <div
                v-for="(item, index) in questionObj.questCrfItemes"
                :key="item.id"
              >
                <!-- crfFieldType: 1 = 文件集合, 2 = 文件, 3 = 列表, 4 = 普通字段, 5 = 说明字段, 6 = 子问卷 -->
                <div v-if="item.crfFieldType === 4 || item.crfFieldType === 6">
                  <!-- crfFieldControl 1 = 无控件, 2 = 文件上传, 3 = 单行文本控件, 4 = 多行文本控件, 
                  5 = 数字控件, 6 = 日期控件, 7 = 时间控件,
                  8 = 日期时间控件, 9 = 单选控件, 10 = 多选控件-->

                  <!-- 3单行文本控件-10多选控件 index === questionIndex-->
                  <div
                    v-if="
                      item.crfFieldControl >= 3 &&
                      index === questionIndex &&
                      item.refTypeShow !== 2 &&
                      item.refTypesShow !== 2
                    "
                    class="questionnaire-item-moduel"
                  >
                    <div class="questionnaire-items-title">
                      <span
                        v-if="item.isRequired === 1"
                        class="isRequiredActive"
                        >*&nbsp;</span
                      >
                      <div v-html="item.fieldLabel" />
                    </div>
                    <div
                      v-if="item?.fieldDescription"
                      class="text-yellow-400 underline"
                      :class="{
                        'my-0.5':
                          item.crfFieldControl < 6 || item.crfFieldControl > 8,
                      }"
                      @click="showFieldDescription(item.fieldDescription)"
                    >
                      填写指南
                    </div>
                    <!-- 3单行文本控件 -->
                    <van-field
                      v-if="item.crfFieldControl === 3"
                      v-model="item.fieldValue"
                      class="border-input"
                      placeholder="请输入"
                      maxlength="999"
                      :disabled="item?.isReadOnly > 0"
                    >
                      <template #button>
                        <span
                          v-if="item?.dctQuestUnit"
                          v-html="item.dctQuestUnit"
                        />
                      </template>
                    </van-field>
                    <!-- 4多行文本控件 -->
                    <van-field
                      v-if="item.crfFieldControl === 4"
                      v-model.trim="item.fieldValue"
                      maxlength="3999"
                      placeholder="请输入"
                      type="textarea"
                      class="border-input"
                      autosize
                      :disabled="item?.isReadOnly > 0"
                    />
                    <!-- 5数字控件 -->
                    <van-field
                      v-if="item.crfFieldControl === 5"
                      v-model="item.fieldValue"
                      maxlength="16"
                      :placeholder="item?.isReadOnly > 0 ? '' : '请输入'"
                      :disabled="item?.isReadOnly > 0"
                      readonly
                      clickable
                      @focus="item.showNumberKeyboard = true"
                      class="border-input"
                    >
                      <template #button>
                        <span
                          v-if="item?.dctQuestUnit"
                          v-html="item.dctQuestUnit"
                        />
                      </template>
                    </van-field>
                    <van-number-keyboard
                      v-model="item.numberFieldValue"
                      :show="item.showNumberKeyboard"
                      theme="custom"
                      extra-key="."
                      close-button-text="完成"
                      @input="
                        (e) => {
                          item.fieldValue += e;
                          keyUpNum(item, index);
                        }
                      "
                      @delete="
                        () => {
                          if (item?.fieldValue) {
                            item.fieldValue = item.fieldValue.slice(0, -1);
                          }
                        }
                      "
                      @blur="
                        item.showNumberKeyboard = false;
                        keyUpNum(item);
                      "
                    />
                    <!-- 6日期控件 -->
                    <div v-if="item.crfFieldControl === 6">
                      <!-- 值回显 -->
                      <van-field
                        v-model="item.fieldValue"
                        is-link
                        readonly
                        name="datetimePicker"
                        label="日期选择"
                        placeholder="点击选择日期"
                        @click="pickerDate(index)"
                      />
                      <van-popup
                        v-model:show="item.showDatas"
                        position="bottom"
                        style="min-height: 308px"
                      >
                        <van-picker
                          :columns="specificColumns"
                          @change="specificColumnsChange"
                          @confirm="
                            (e) => {
                              const startIndex = e.findIndex((e) => e === 'UK');
                              if (startIndex !== -1) {
                                e.fill('UK', startIndex + 1);
                              }
                              item.fieldValue = e[0] + '-' + e[1] + '-' + e[2];
                              item.showDatas = false;
                            }
                          "
                          @cancel="item.showDatas = false"
                        >
                          <template #title>
                            <div
                              class="ft-13-rem font-color-969799"
                              @click="
                                () => {
                                  item.fieldValue = '';
                                  item.showDatas = false;
                                }
                              "
                            >
                              清空
                            </div>
                          </template>
                        </van-picker>
                      </van-popup>
                    </div>
                    <!-- 7 = 时间控件, -->
                    <div v-if="item.crfFieldControl === 7">
                      <van-field
                        v-model="item.fieldValue"
                        is-link
                        readonly
                        name="datetimePicker"
                        label="时间选择"
                        placeholder="点击选择时间"
                        @click="pickerDay(index)"
                      />
                      <van-popup
                        v-model:show="item.showTimes"
                        position="bottom"
                        style="min-height: 308px"
                      >
                        <van-picker
                          :columns="timeColumns"
                          @change="columnsTimeChange"
                          @confirm="
                            (e) => {
                              const startIndex = e.findIndex((e) => e === 'UK');
                              if (startIndex !== -1) {
                                e.fill('UK', startIndex + 1);
                              }
                              item.fieldValue = e[0] + ':' + e[1];
                              item.showTimes = false;
                            }
                          "
                          @cancel="item.showTimes = false"
                        >
                          <template #title>
                            <div
                              class="ft-13-rem font-color-969799"
                              @click="
                                () => {
                                  item.fieldValue = '';
                                  item.showTimes = false;
                                }
                              "
                            >
                              清空
                            </div>
                          </template>
                        </van-picker>
                      </van-popup>
                    </div>
                    <!-- 8 = 日期时间控件 -->
                    <div v-if="item.crfFieldControl === 8">
                      <div class="date-and-time">
                        <!-- 日期 -->
                        <van-field
                          v-model="item.fieldValue[0]"
                          is-link
                          readonly
                          name="datetimePicker"
                          label="日期选择"
                          placeholder="点击选择日期"
                          @click="pickerDate(index)"
                        />
                        <van-popup
                          v-model:show="item.showDatas"
                          position="bottom"
                        >
                          <van-picker
                            :columns="specificColumns"
                            @change="specificColumnsChange"
                            @confirm="
                              (e) => {
                                const startIndex = e.findIndex(
                                  (e) => e === 'UK'
                                );
                                if (startIndex !== -1) {
                                  e.fill('UK', startIndex + 1);
                                }
                                item.fieldValue[0] =
                                  e[0] + '-' + e[1] + '-' + e[2];
                                item.showDatas = false;
                              }
                            "
                            @cancel="item.showDatas = false"
                          >
                            <template #title>
                              <div
                                class="ft-13-rem font-color-969799"
                                @click="
                                  () => {
                                    item.fieldValue[0] = '';
                                    item.showDatas = false;
                                  }
                                "
                              >
                                清空
                              </div>
                            </template>
                          </van-picker>
                        </van-popup>
                        <!-- 时间 -->
                        <van-field
                          v-model="item.fieldValue[1]"
                          is-link
                          readonly
                          name="datetimePicker"
                          label="时间选择"
                          placeholder="点击选择时间"
                          @click="pickerDay(index)"
                        />
                        <van-popup
                          v-model:show="item.showTimes"
                          position="bottom"
                        >
                          <van-picker
                            :columns="timeColumns"
                            @change="columnsTimeChange"
                            @confirm="
                              (e) => {
                                const startIndex = e.findIndex(
                                  (e) => e === 'UK'
                                );
                                if (startIndex !== -1) {
                                  e.fill('UK', startIndex + 1);
                                }
                                item.fieldValue[1] = e[0] + ':' + e[1];
                                item.showTimes = false;
                              }
                            "
                            @cancel="item.showTimes = false"
                          >
                            <template #title>
                              <div
                                class="ft-13-rem font-color-969799"
                                @click="
                                  () => {
                                    item.fieldValue[1] = '';
                                    item.showTimes = false;
                                  }
                                "
                              >
                                清空
                              </div>
                            </template>
                          </van-picker>
                        </van-popup>
                      </div>
                    </div>
                    <!-- 9单选类型 -->
                    <van-radio-group
                      v-if="item.crfFieldControl === 9"
                      v-model="item.fieldValue"
                      direction="horizontal"
                      :disabled="item?.isReadOnly > 0"
                    >
                      <van-radio
                        v-for="(radioItem, radioIndex) in item.fieldItems"
                        :key="radioIndex"
                        :name="radioItem.itemValue"
                        @click="invertSelectionFun(radioItem.itemValue, item)"
                        >{{ radioItem.itemName }}</van-radio
                      >
                      <!-- <van-radio name="2">单选框 2</van-radio> -->
                    </van-radio-group>
                    <!-- 10 = 多选控件 -->
                    <van-checkbox-group
                      v-if="item.crfFieldControl === 10"
                      v-model="item.fieldValue"
                      :disabled="item?.isReadOnly > 0"
                    >
                      <van-checkbox
                        v-for="(checkboxItem, checkboxIndex) in item.fieldItems"
                        :key="checkboxIndex"
                        :name="checkboxItem.itemValue"
                        @click="mutualExclusion(item, checkboxItem)"
                        >{{ checkboxItem.itemName }}</van-checkbox
                      >
                      <!-- <van-checkbox name="b">复选框 b</van-checkbox> -->
                    </van-checkbox-group>
                    <!-- 11 = 年月控件 -->
                    <div v-if="item.crfFieldControl === 11">
                      <van-field
                        v-model="item.fieldValue"
                        is-link
                        readonly
                        label="年月选择"
                        placeholder="点击选择年月"
                        @click="pickerYearMonth(index)"
                      />
                      <van-popup
                        v-model:show="item.showYearMonth"
                        position="bottom"
                        style="min-height: 308px"
                      >
                        <van-picker
                          :columns="yearMonthColumns"
                          @change="yearMonthColumnsChange"
                          @confirm="
                            (e) => {
                              const startIndex = e.findIndex((e) => e === 'UK');
                              if (startIndex !== -1) {
                                e.fill('UK', startIndex + 1);
                              }
                              item.fieldValue = e[0] + '-' + e[1];
                              item.showYearMonth = false;
                            }
                          "
                          @cancel="item.showYearMonth = false"
                        >
                          <template #title>
                            <div
                              class="ft-13-rem font-color-969799"
                              @click="
                                () => {
                                  item.fieldValue = '';
                                  item.showYearMonth = false;
                                }
                              "
                            >
                              清空
                            </div>
                          </template>
                        </van-picker>
                      </van-popup>
                    </div>
                    <!-- 数据澄清 -->
                    <div
                      v-if="
                        questionObj?.dataClarification &&
                        item?.clarify?.clarifyCount
                      "
                      class="underline mt-01-rem flex justify-end ft-13-rem"
                      style="color: #f19980"
                      @click="openDataClarification(item)"
                    >
                      数据澄清
                      <span v-if="item?.clarify?.clarifyCount">
                        （关闭{{ item?.clarify?.clarifyCloseCount }}/{{
                          item?.clarify?.clarifyCount
                        }}）</span
                      >
                    </div>
                  </div>
                </div>
                <!-- 文件集合 -->
                <!-- xx类型 -->
              </div>
            </div>
            <!-- 问卷 4 = 上传图片 -->
          </div>
        </div>
      </div>
    </div>
    <footer v-if="editFlag === '1'" class="w-full">
      <van-button
        v-if="
          questionIndex === questionObjLength - 1 ||
          questionObj.questCrfType === 1
        "
        style="height: 50px; padding-bottom: 7px"
        class="w-full"
        type="primary"
        loading-text="确定"
        :disabled="disabledSaveFalg.disabledSaveFalg"
        @click="saveQuestionnaire"
        >确定</van-button
      >
      <van-button
        v-else-if="
          questionObj.questCrfType === 2 && questionIndex < questionObjLength
        "
        class="w-full"
        style="height: 50px; padding-bottom: 7px"
        type="primary"
        :disabled="disabledSaveFalg.disabledSaveFalg"
        @click="saveQuestionnaire('add')"
        >下一步</van-button
      >
    </footer>
    <!-- 数据澄清页面 -->
    <DataClarificationModule
      ref="DataClarificationRef"
      :updateDataClarification="updateDataClarification"
    />
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  onBeforeMount,
  getCurrentInstance,
  nextTick
} from "vue";
import { useRoute } from "vue-router";
// 预览图片
import { Notify, Dialog, Toast } from 'vant';
import {
  getTableView,
  getTableData,
  putTableData,
  postTable,
} from "@/api/questionnaire";
import {
  deepClone,
  delay,
} from "@trialdata/common-fun-css/index";
import {
  maintainedFun, monthArr,
  hourArr, minuteArr,
  dayArr, ThirtyDaysArr,
  isLeapYear, UKArr,
  parseTimeNYDHMS, nowDayArr, nowMonthArr
} from "@/utils/debounce";
import { questionObjInter } from "@/types/quest";
import { useStore } from 'vuex';
import DataClarificationModule from "@/components/DataClarificationModule.vue";

export default defineComponent({
  name: "EditQuestionnairetList", // 列表
  components: {
    DataClarificationModule
  },
  setup() {
    const regex = /(<([^>]+)>)/ig
    const route = useRoute();
    const store = useStore();
    const proxy: any = getCurrentInstance()?.proxy
    const state: any = reactive({
      // 数据澄清相关
      routeOpenDataClarificationFlag: true,
      DataClarificationRef: null,
      openDataClarification: (item) => {
        state.DataClarificationRef.dataClarificationFlag = true
        state.DataClarificationRef.dataClarificationObj = deepClone(item)
        state.DataClarificationRef.questionObj = deepClone(state.questionObj)
        state.DataClarificationRef.onLoad()
      },
      // 时分的 自定义
      timeColumns: [
        {
          values: hourArr,
          defaultIndex: 0,
        },
        // 第二列 月
        {
          values: [],
          defaultIndex: 0,
        },
      ],
      // 时变分变
      columnsTimeChange: (e, l) => {
        if (l === 0) {
          // 时UK则 - 分UK
          if (e[0] === "UK") {
            state.timeColumns[1].defaultIndex = 0;
            state.timeColumns[1].values = UKArr;
          } else if (state.timeColumns[1].values.length < 2) {
            const minuteOrUKArr = [...minuteArr]
            if (state.questionObj?.isAllowUK) {
              minuteOrUKArr.unshift('UK');
            }
            state.timeColumns[1].values = minuteOrUKArr;
          }
        }
      },
      // 时分定位当前值所在的下标
      setTimeDefaultIndex: (timeStr) => {
        if (timeStr) {
          const result = timeStr.split(':');
          if (result?.length && result[0] === 'UK') {
            state.timeColumns[1].values = UKArr
          }
          state.saveTimeDefaultIndex(result)
        } else {
          state.timeColumns[1].values = state.questionObj?.isAllowUK ? [...UKArr, ...minuteArr] : minuteArr
          // 没值定位到当前时分
          const dateMyTimes = new Date().toLocaleTimeString().split(':')
          state.saveTimeDefaultIndex(dateMyTimes)
        }
      },
      // 保存时分 下标
      saveTimeDefaultIndex: (result) => {
        state.timeColumns[0].values.forEach((el, index) => {
          if (el === result[0]) {
            state.timeColumns[0].defaultIndex = index
          }
        });
        state.timeColumns[1].values.forEach((el, index) => {
          if (el === result[1]) {
            state.timeColumns[1].defaultIndex = index
          }
        });
      },
      // 生成年数组
      setYearMonthColumns: (columns, min, max, dateValue = '') => {
        let dateArr = []
        if (dateValue) {
          dateArr = dateValue.split('-')
        } else {
          const data = parseTimeNYDHMS(new Date(), '{y}-{m}-{d}')
          dateArr = data.split('-')
        }
        const newColumnsArr = []
        let item = min
        for (let index = min; index <= max; index++) {
          const itemstr = item + ''
          newColumnsArr.push(itemstr)
          item += 1
        }
        if (state.questionObj?.isAllowUK) {
          newColumnsArr.unshift('UK');
        }
        const yearMonthColumnsValues = state.monthShiftChange(dateArr)
        if (columns) {
          columns[0].values = newColumnsArr
          columns[1].values = yearMonthColumnsValues
          if (columns.length === 3) {
            columns[2].values = state.dayShiftChange(dateArr)
          }
        }
      },
      // 年月定位当前值所在的下标
      setYearMonthDefaultIndex: (yearMonthStr) => {
        if (yearMonthStr) {
          const result = yearMonthStr.split('-');
          if (result?.length && result[0] === 'UK') {
            state.yearMonthColumns[1].values = UKArr
          }
          state.yearMonthColumns[0].values.forEach((el, index) => {
            if (el === result[0]) {
              state.yearMonthColumns[0].defaultIndex = index
            }
          });
          state.yearMonthColumns[1].values.forEach((el, index) => {
            if (el === result[1]) {
              state.yearMonthColumns[1].defaultIndex = index
            }
          });
        } else {
          const date = new Date();
          const year = date.getFullYear();
          const month = date.getMonth() + 1;
          state.yearMonthColumns[0].values.forEach((el, idx) => {
            if (el === year + '') {
              state.yearMonthColumns[0].defaultIndex = idx
            }
          });
          state.yearMonthColumns[1].values = state.monthShiftChange(1)
          if (state.yearMonthColumns[0].values[state.yearMonthColumns[0].defaultIndex] === 'UK') {
            state.yearMonthColumns[1].values = UKArr
          }
          state.yearMonthColumns[1].values.forEach((el, idx) => {
            if (el / 1 === month) {
              state.yearMonthColumns[1].defaultIndex = idx
            }
          });
        }
      },
      yearMonthColumns: [
        // 第一列 年 默认1900-2099
        {
          values: [],
          defaultIndex: 0,
        },
        // 第二列 月
        {
          values: [],
          defaultIndex: 0,
        },
      ],
      // 年变月变
      yearMonthColumnsChange: (e, l) => {
        if (l === 0) {
          // 年UK则 - 月UK
          if (e[0] === 'UK') {
            state.yearMonthColumns[1].defaultIndex = 0
            state.yearMonthColumns[1].values = UKArr
          } else {
            const yearMonthColumnsValues = state.monthShiftChange(e)
            state.yearMonthColumns[1].values = yearMonthColumnsValues
          }
        }
      },
      // 年月日 定位当前值所在的下标
      setSpecificDefaultIndex: (specificStr) => {
        if (specificStr) {
          const result = specificStr.split('-');
          if (result?.length && result[0] === 'UK') {
            state.specificColumns[1].values = UKArr
          }
          if (result?.length > 1 && result[1] === 'UK') {
            state.specificColumns[2].values = UKArr
          }
          state.specificColumns.forEach((item, idx) => {
            item.values.forEach((el, index) => {
              if (el === result[idx]) {
                state.specificColumns[idx].defaultIndex = index
              }
            })
          });
        } else {
          state.specificColumns[0].defaultIndex = 0
          state.specificColumns[1].values = state.monthShiftChange(1)
          state.specificColumns[2].values = state.dayShiftChange(1)
          const date = new Date();
          const year = date.getFullYear();
          const month = date.getMonth() + 1;
          const today = date.getDate();
          state.specificColumns[0].values.forEach((el, idx) => {
            if (el === year + '') {
              state.specificColumns[0].defaultIndex = idx
            }
          });
          // 年能定位到今天，不是UK的情况下
          if (state.specificColumns[0].values[state.specificColumns[0].defaultIndex] !== 'UK') {
            state.specificColumns[1].values = state.monthShiftChange(1)
            state.specificColumns[1].values.forEach((el, idx) => {
              if (el / 1 === month) {
                state.specificColumns[1].defaultIndex = idx
              }
            });
            state.specificColumns[2].values = state.dayShiftChange(1)
            state.specificColumns[2].values.forEach((el, idx) => {
              if (el / 1 === today) {
                state.specificColumns[2].defaultIndex = idx
              }
            });
          }
        }
      },
      // 年月日-控件
      specificColumns: [
        // 第一列 年 默认1900-2099
        {
          values: [],
          defaultIndex: 0,
        },
        // 第二列 月
        {
          values: [],
          defaultIndex: 0,
        },
        // 第3列 日 28 29 30 31
        {
          values: [],
          defaultIndex: 0,
        },
      ],
      // 年变 月变 日变
      specificColumnsChange(e, l) {
        if (l === 0) {
          // 年UK则 - 月UK
          if (e[0] === "UK") {
            state.specificColumns[1].defaultIndex = 0;
            state.specificColumns[2].defaultIndex = 0;
            state.specificColumns[1].values = UKArr;
            state.specificColumns[2].values = UKArr;
          } else {
            state.specificColumns[1].values = state.monthShiftChange(e)
            const newE = state.specificDayShiftChange(e)
            state.specificColumns[2].values = state.dayShiftChange(newE);
            if (e[1] === "UK") {
              state.specificColumns[2].defaultIndex = 0;
              state.specificColumns[2].values = UKArr;
            }
          }
        } else if (l === 1) {
          // 月UK 则 日UK
          if (e[1] === "UK") {
            state.specificColumns[2].defaultIndex = 0;
            state.specificColumns[2].values = UKArr;
          } else {
            state.specificColumns[2].values = state.dayShiftChange(e);
          }
        }
      },
      // 处理月份
      monthShiftChange: (e) => {
        let monthArrCpoy = monthArr
        if (!state.questionObj.isFutureDate && (e[0] / 1 === new Date().getFullYear() || e === 1)) {
          monthArrCpoy = nowMonthArr()
        }
        return state.questionObj?.isAllowUK ? [...UKArr, ...monthArrCpoy] : monthArrCpoy
      },
      // 处理日期change时天数问题
      specificDayShiftChange: (e) => {
        let data = e
        if (!state.questionObj.isFutureDate && e[0] / 1 === new Date().getFullYear()) {
          const newDate = new Date()
          if (e[1] / 1 <= (newDate.getMonth() + 1)) {
          } else {
            const date = parseTimeNYDHMS(new Date(), '{y}-{m}')
            data = date.split('-')
          }
        }
        return data
      },
      // 处理天
      dayShiftChange: (e) => {
        // e为1 就是直接走改变天数
        // 年动得时候处理日  月动
        const isAllowUKNum = state.questionObj?.isAllowUK ? 1 : 0
        let dayChangeArr = state.questionObj?.isAllowUK ? [...UKArr, ...dayArr] : [...dayArr];
        const newDate = new Date()
        if (!state.questionObj.isFutureDate &&
          (
            (
              (e[0] / 1 === newDate.getFullYear() &&
                e[1] / 1 === (newDate.getMonth() + 1))
            ) ||
            e === 1
          )
        ) {
          const dayArrCopy = nowDayArr()
          dayChangeArr = state.questionObj?.isAllowUK ? [...UKArr, ...dayArrCopy] : [...dayArrCopy];
        } else {
          if (ThirtyDaysArr.indexOf(e[1]) > -1) {
            dayChangeArr.length = 30 + isAllowUKNum;
          } else if (e[1] === "02") {
            dayChangeArr.length = (isLeapYear(Number(e[0])) ? 29 : 28) + isAllowUKNum;
          }
        }
        return dayChangeArr
      },
      //
      questItem: {
        questId: '',
        pDctCode: '',
        rowId: '',
        fieldDescription: '',
        // dctSort: 0,
        // edcCrfId: -1,
        // finishStatus: 2,
        // questContentType: 1,
        // questDescribe: '',
        // questDisplayType: -1,
        // questName: '',
        // questTemplateType: -1,
        // questionUrl: '',
        // sortCode: '',
      },
      questionObj: {
        questCrfItemes: [],
        questId: '',
        crfName: '',
        crfGuideline: '',
        crfSubTitle: '',
        warningContent: '',
        finishStatus: 0,
        questUrl: '',
        publishDate: '',
        questCrfType: -1,
      },
      myPreview: [],
      editFlag: '0', // 默认只读
      questionDataObj: {}, // 问卷回答对象questCrfItemes
      questionIndex: 0, // 当前回答问题的--下标
      questionObjLength: 0, // 问题总长
      dateIndex: -1, // 日期下标
      yearMonthValue: new Date(),
      yearMonthIndex: -1, // 年月下标
      disabledSaveFalg: {
        disabledSaveFalg: false,
      },
      submitLoadingFlag: false,
      outerHeight: document.documentElement.scrollHeight,
      // 点击选择年月
      pickerYearMonth: async (index) => {
        state.yearMonthColumns = [
          // 第一列 年 默认1900-2099
          {
            values: [],
            defaultIndex: 0,
          },
          // 第二列 月
          {
            values: [],
            defaultIndex: 0,
          },
        ]
        await nextTick()
        state.yearMonthIndex = index;
        const quesObj = state.questionObj as questionObjInter;
        if (!quesObj?.questCrfItemes[index].isReadOnly) {
          state.questionObj.isAllowUK = quesObj.questCrfItemes[index].isAllowUK
          state.questionObj.isFutureDate = quesObj.questCrfItemes[index]?.isFutureDate
          // 设置年月控件
          let max = quesObj.questCrfItemes[index]?.maximumYear || 0, min = quesObj.questCrfItemes[index]?.minimumYear || 0;
          if (!quesObj.questCrfItemes[index]?.isFutureDate) {
            const date = new Date();
            const year = date.getFullYear();
            // 最小值大于最大的了不成立直接为今年
            if (min > max) {
              min = max = year;
            } else {
              min = min && min <= year ? min : year
              max = max && max <= year ? max : year
            }
          }
          if (max && min && Number(max) > Number(min)) {
            state.setYearMonthColumns(state.yearMonthColumns, min, max, quesObj.questCrfItemes[index].fieldValue)
          } else {
            state.setYearMonthColumns(state.yearMonthColumns, 1900, 2099, quesObj.questCrfItemes[index].fieldValue)
          }
          state.setYearMonthDefaultIndex(quesObj.questCrfItemes[index].fieldValue)
          quesObj.questCrfItemes[index].showYearMonth = true;
        }
      },
      // 引导
      showFieldDescription: (fieldDescription) => {
        Dialog.alert({
          title: "填写指南",
          message: `${fieldDescription}`,
          confirmButtonText: '确定',
          confirmButtonColor: '#5860da',
          messageAlign: 'left',
          allowHtml: true,
        })
        // .then(() => {})
      },
      updateDataClarification: () => {
        if (state?.questItem?.questId && state?.questItem?.pDctCode) {
          getTableView(state.questItem.questId, state.questItem.pDctCode).then(
            () => {
              const getTableDataFun = state?.questItem?.rowId
                ? getTableData(
                  state.questItem.questId,
                  state.questItem.pDctCode,
                  state.questItem.rowId
                )
                : postTable(state.questItem.questId, state.questItem.pDctCode);
              getTableDataFun.then((rest: any) => {
                // 问题 和 答案 合并
                if (rest.questCrfType === 1 || rest.questCrfType === 2) {
                  rest.questCrfItemes.forEach((item) => {
                    state.questionObj.questCrfItemes.forEach((items) => {
                      // 传值
                      if (item.dctCode === items.dctCode) {
                        items.clarify = item.clarify;
                      }
                    });
                  });
                }
              });
            }
          );
        }
      },
      // 加载数据
      onLoad: () => {
        setTimeout(() => {
          state.outerHeight = document.documentElement.scrollHeight;
        }, 0);
        // 新增时
        if (state?.questItem?.questId && state?.questItem?.pDctCode) {
          getTableView(state.questItem.questId, state.questItem.pDctCode).then(
            (res: any) => {
              const getTableDataFun = state?.questItem?.rowId
                ? getTableData(
                  state.questItem.questId,
                  state.questItem.pDctCode,
                  state.questItem.rowId
                )
                : postTable(state.questItem.questId, state.questItem.pDctCode);
              getTableDataFun.then((rest: any) => {
                state.questionDataObj = rest;
                if (!res?.questCrfType) {
                  res.questCrfType = rest.questCrfType || 1;
                }
                // 问题 和 答案 合并
                if (rest.questCrfType === 1 || rest.questCrfType === 2) {
                  rest.questCrfItemes.forEach((item) => {
                    res.questCrfItemes.forEach((items) => {
                      // 传值
                      if (item.dctCode === items.dctCode) {
                        items.fieldValue = item.fieldValue;
                        items.questItemDataId = item.questItemDataId;
                        items.numberFieldValue = item.fieldValue;
                        items.oldFieldValue = item.fieldValue;
                        items.fieldValueStr = item.fieldValueStr;
                        items.rowNum = item.rowNum;
                        items.clarify = item.clarify;
                        if (res?.dataClarification &&
                          items?.clarify.clarifyCount &&
                          items?.questItemDataId &&
                          items.questItemDataId === route.query?.questItemDataId &&
                          state.routeOpenDataClarificationFlag
                        ) {
                          state.routeOpenDataClarificationFlag = false
                          // 非列表
                          setTimeout(() => {
                            if (items.refTypeShow !== 2 && items.refTypesShow !== 2) {
                              state.openDataClarification(items)
                            }
                          }, 0);
                        }
                        // 如果是文件集合
                        if (items.crfFieldType === 1) {
                          if (!items.fileList) {
                            //为空定义
                            items.fileList = [];
                          }
                          items.fileList.push(item); //持续赋值
                          items.fileList.forEach((fileListItem) => {
                            fileListItem.url = fileListItem.fieldValue;
                          });
                          // 转换图片地址数组
                          if (items.fieldValue && items.fieldValueStr) {
                            items.fileListUrl = items.fieldValue.split(",");
                            items.fileListStr = items.fieldValueStr.split(",");
                            items.fileListUrl.length -= 1;
                            items.fileListStr.length -= 1;
                          }
                        }
                        // 多选修改成数组
                        if (items.crfFieldControl === 10) {
                          if (!items.fieldValue) {
                            items.fieldValue = [];
                          } else if (typeof items.fieldValue === "string") {
                            items.fieldValue = items.fieldValue.split(",");
                          }
                        } else if (items.crfFieldControl === 6) {
                          // 6 = 日期控件, 7 = 时间控件
                          items.showDatas = false; // 显示隐藏当前的日期 时间组件
                        } else if (items.crfFieldControl === 7) {
                          items.showTimes = false;
                        } else if (items.crfFieldControl === 11) {
                          item.showYearMonth = false
                        }
                        // 拆分时间日期
                        else if (items.crfFieldControl === 8) {
                          items.showDatas = false; // 显示隐藏当前的日期 时间组件
                          items.showTimes = false;
                          if (
                            typeof items.fieldValue === "string" &&
                            items.fieldValue
                          ) {
                            item.fieldValue = items.fieldValue.split(",");
                            items.fieldValue = items.fieldValue.split(",");
                          } else if (!items.fieldValue) {
                            items.fieldValue = ["", ""];
                          }
                        }
                      }
                    });
                  });
                  state.questionObj = res;
                  if (route.query.hasOwnProperty('dataClarification')) {
                    state.questionObj.dataClarification = route.query?.dataClarification
                  }
                  state.questionObjLength = res.questCrfItemes.length;
                  // state.questionIndex = res.crfGuideline ? -1 : 0;
                  state.refreshRelevanceAll()
                  // 有rowId不是新增，有数据
                  let rowId = state.questItem?.rowId || route.query?.rowId
                  if (rowId &&
                    state.questionObj?.questCrfItemes?.length
                    && store.state?.initialAllListArr.length) {
                    // console.log(store.state?.initialAllListArr);
                    const { initialAllListArr } = store.state
                    initialAllListArr.forEach(e => {
                      if (e.dctCode === state.questItem?.pDctCode) {
                        e.children.forEach((el) => {
                          if (el.rowId === rowId && !el?.questionObj) {
                            el.questionObj = deepClone(state.questionObj),
                            el.isChange = false
                          }
                        });
                      }
                    });
                    store.dispatch('setInitialAllListArr', initialAllListArr)
                    // console.log(initialAllListArr, '列表s initialAllListArr1');
                    // let pushFlag = true
                    // store.state.initialListArr.forEach((e) => {
                    //   if (e.rowId === rowId && e.questionObj?.questId) {
                    //     pushFlag = false
                    //   }
                    // })
                    // if (pushFlag) {
                    //   store.state.initialListArr.push({
                    //     rowId: rowId,
                    //     questionObj: deepClone(state.questionObj),
                    //     isChange: false
                    //   })
                    //   store.dispatch('setInitialListArr', store.state.initialListArr)
                    // }
                  }
                }
              });
            }
          );
        } else if (state.questItem?.questId) {
          // 获取问题-数据
        }
      },
      // 单选-反选
      invertSelectionFun: (e, item) => {
        if (state.editFlag !== '1' || item?.isReadOnly > 0) {
          return
        }
        if (!item.oldFieldValue) {
          item.oldFieldValue = item.fieldValue
        } else if (item?.oldFieldValue && item.oldFieldValue === item.fieldValue) {
          item.oldFieldValue = ''
          item.fieldValue = ''
        } else {
          item.oldFieldValue = e
        }
        state.refreshRelevanceAll()
      },
      // 是否关联显示
      showRelevance: (item) => {
        const questionObj = state.questionObj;
        questionObj.questCrfItemes.forEach((items: any) => {
          // code相同的是关联问题
          if (item.dctCode === items.refDctCode && item.crfFieldControl === 9) {
            // 初始化显示隐藏标识
            if (item.refTypeShow) {
              items.refTypeShow = item.refTypeShow
            }
            if (item.refTypesShow) {
              items.refTypesShow = item.refTypesShow
            }
            let relevanceFlag = false
            if (items?.refItemValue && items?.refItemValue.indexOf(',') > -1) {
              items.refItemValue.split(',').forEach(el => {
                if (el === item.fieldValue) {
                  relevanceFlag = true
                  return
                }
              });
            }
            if (items?.refItemValue !== "" &&
              (items?.refItemValue === item.fieldValue || relevanceFlag) &&
              (item?.fieldValue || item?.fieldValue === 0)
            ) {
              if (items.refType === 1) {
                items.refTypeShow = 1;
              } else if (items.refType === 2) {
                items.refTypeShow = 2;
                // if (Array.isArray(items.fieldValue)) {
                //   items.fieldValue = [];
                //   items.oldFieldValue = []
                // } else if(items.specialFieldType < 11 && items.specialFieldType > 14){
                //   items.fieldValue = null;
                //   items.oldFieldValue = null
                // }
                state.showRelevances(items, '');
              }
            } else if (item.fieldValue !== items.refItemValue) {
              if (items.refType === 1) {
                items.refTypeShow = 2;
                // if (Array.isArray(items.fieldValue)) {
                //   items.fieldValue = [];
                //   items.oldFieldValue = []
                // } else if(items.specialFieldType < 11 && items.specialFieldType > 14){
                //   items.fieldValue = null;
                //   items.oldFieldValue = null
                // }
              } else if (items.refType === 2) {
                items.refTypeShow = 1;
              }
            }
            // 如果父题隐藏则子关联题隐藏
            if (item.refTypeShow === 2) {
              items.refTypeShow = 2
            } else if (item.refTypesShow === 2) {
              items.refTypesShow = 2
            }
          }
        });
      },
      // 多选关联显示
      showRelevances: (item, fildItem) => {
        if (item?.isReadOnly > 0) {
          return
        }
        // mutualExclusion 互斥
        if (fildItem?.mutualExclusion) {
          if (fildItem?.itemValue && item?.fieldValue?.length && Array.isArray(item?.fieldValue)) {
            item.fieldValue = [fildItem?.itemValue || '']
          }
        }
        // 选中的有值 且 不支持反选
        else if (fildItem?.itemValue && !fildItem?.mutualExclusion) {
          item.fieldValue.forEach((element, idx) => {
            item.fieldItems.forEach(fieldItemsEl => {
              if (fieldItemsEl?.mutualExclusion && element === fieldItemsEl?.itemValue
                && element && fieldItemsEl?.itemValue) {
                item.fieldValue.splice(idx, idx + 1)
              }
            });
          });
        }
        if (item?.fieldValue?.length && Array.isArray(item.fieldValue)) {
          item.fieldValue = Array.from(new Set(item.fieldValue))
        }
        const questionObj = state.questionObj;
        questionObj.questCrfItemes.forEach((items: any) => {
          // code相同的是关联问题
          if (
            item.dctCode === items.refDctCode &&
            item.crfFieldControl === 10
          ) {
            // 初始化显示隐藏标识
            if (item.refTypeShow) {
              items.refTypeShow = item.refTypeShow
            }
            if (item.refTypesShow) {
              items.refTypesShow = item.refTypesShow
            }
            if (Array.isArray(item.fieldValue)) {
              let found = false;
              item.fieldValue.forEach((i) => {
                if (found) {
                  return;
                }
                if (items?.refItemValue?.includes(',')) {
                  const valueArr = items.refItemValue.split(',')
                  valueArr.forEach((val) => {
                    if (val === i)
                      found = true
                  });
                } else if (i === items.refItemValue) {
                  found = true
                }
              });
              if (found && items.refItemValue) {
                if (items.refType === 1) {
                  items.refTypesShow = 1;
                } else if (items.refType === 2) {
                  items.refTypesShow = 2;
                  // if (Array.isArray(items.fieldValue)) {
                  //   items.fieldValue = [];
                  //   items.oldFieldValue = []
                  // } else if(items.specialFieldType < 11 && items.specialFieldType > 14){
                  //   items.fieldValue = null;
                  //   items.oldFieldValue = null
                  // }
                  state.showRelevance(items);
                }
              } else {
                if (items.refType === 1) {
                  items.refTypesShow = 2;
                  // if (Array.isArray(items.fieldValue)) {
                  //   items.fieldValue = [];
                  //   items.oldFieldValue = []
                  // } else if(items.specialFieldType < 11 && items.specialFieldType > 14){
                  //   items.fieldValue = null;
                  //   items.oldFieldValue = null
                  // }
                } else if (items.refType === 2) {
                  items.refTypesShow = 1;
                }
              }
            } else {
              if (items.refType === 1) {
                items.refTypesShow = 2;
                // if (Array.isArray(items.fieldValue)) {
                //   items.fieldValue = [];
                //   items.oldFieldValue = []
                // } else if(items.specialFieldType < 11 && items.specialFieldType > 14){
                //   items.fieldValue = null;
                //   items.oldFieldValue = null
                // }
              } else if (items.refType === 2) {
                items.refTypesShow = 1;
              }
            }
            // 如果父题隐藏则子关联题隐藏
            if (item.refTypeShow === 2) {
              items.refTypeShow = 2
            } else if (item.refTypesShow === 2) {
              items.refTypesShow = 2
            }
          }
        });
      },
      // 只能输入数字
      keyUpNum: (item) => {
        item.fieldValue = item.fieldValue.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')
      },
      // 点击选择时间
      pickerDay: async (index) => {
        state.timeColumns = [
          {
            values: hourArr,
            defaultIndex: 0,
          },
          {
            values: [],
            defaultIndex: 0,
          },
        ]
        await nextTick()
        state.dateIndex = index;
        const quesObj: any = state.questionObj;
        if (!quesObj?.questCrfItemes[state.dateIndex].isReadOnly) {
          state.questionObj.isAllowUK = quesObj.questCrfItemes[index].isAllowUK
          state.timeColumns[0].values = state.questionObj?.isAllowUK ? [...UKArr, ...hourArr] : hourArr
          state.timeColumns[1].values = state.questionObj?.isAllowUK ? [...UKArr, ...minuteArr] : minuteArr
          if (quesObj.questCrfItemes[index].crfFieldControl === 8) {
            if (Array.isArray(quesObj.questCrfItemes[index].fieldValue) && quesObj.questCrfItemes[index].fieldValue?.length === 2) {
              state.setTimeDefaultIndex(quesObj.questCrfItemes[index].fieldValue[1])
            } else {
              state.setTimeDefaultIndex('')
            }
          } else {
            state.setTimeDefaultIndex(quesObj.questCrfItemes[index].fieldValue)
          }
          quesObj.questCrfItemes[state.dateIndex].showTimes = true;
        }
      },
      // 点击选择日期
      pickerDate: async (index) => {
        state.specificColumns = [
          // 第一列 年 默认1900-2099
          {
            values: [],
            defaultIndex: 0,
          },
          // 第二列 月
          {
            values: [],
            defaultIndex: 0,
          },
          // 第3列 日 28 29 30 31
          {
            values: [],
            defaultIndex: 0,
          },
        ]
        await nextTick()
        state.dateIndex = index;
        const quesObj: any = state.questionObj;
        if (!quesObj?.questCrfItemes[state.dateIndex].isReadOnly) {
          state.questionObj.isAllowUK = quesObj.questCrfItemes[index].isAllowUK
          state.questionObj.isFutureDate = quesObj.questCrfItemes[index]?.isFutureDate
          let max = quesObj.questCrfItemes[index]?.maximumYear || 0, min = quesObj.questCrfItemes[index]?.minimumYear || 0;
          if (!quesObj.questCrfItemes[index]?.isFutureDate) {
            const date = new Date();
            const year = date.getFullYear();
            // 最小值大于最大的了不成立直接为今年
            if (min > max) {
              min = max = year;
            } else {
              min = min && min <= year ? min : year
              max = max && max <= year ? max : year
            }
          }
          let dateValue = ''
          if (quesObj.questCrfItemes[index].crfFieldControl === 8) {
            if (Array.isArray(quesObj.questCrfItemes[index].fieldValue) && quesObj.questCrfItemes[index].fieldValue?.length) {
              dateValue = quesObj.questCrfItemes[index].fieldValue[0]
            } else {
              dateValue = ''
            }
          } else {
            dateValue = quesObj.questCrfItemes[index].fieldValue
          }
          if (max && min && Number(max) >= Number(min)) {
            state.setYearMonthColumns(state.specificColumns, min, max, dateValue)
          } else {
            state.setYearMonthColumns(state.specificColumns, 1900, 2099, dateValue)
          }
          state.setSpecificDefaultIndex(dateValue)
          quesObj.questCrfItemes[state.dateIndex].showDatas = true;
        }
      },
      // 互斥
      mutualExclusion: (item, fildItem) => {
        state.showRelevances(item, fildItem)
        state.refreshRelevanceAll()
      },
      // 刷新 关联逻辑
      refreshRelevanceAll: () => {
        state.questionObj.questCrfItemes.forEach((resItems: any) => {
          state.showRelevance(resItems)
          if (resItems.crfFieldControl === 10) {
            state.showRelevances(resItems, '')
          }
        })
      },
      // 提交问卷
      saveQuestionnaire: (flag) => {
        if (state.disabledSaveFalg.disabledSaveFalg) {
          return;
        }
        try {
          delay(async () => {
            const quesArr = deepClone(state.questionObj);
            const quesArrFlag: any = state.questionObj;
            if (flag === "add") {
              const quesObj: any = state.questionObj;
              // 是否填写 必填项？
              const crfFieldControl =
                quesObj?.questCrfItemes[state.questionIndex]?.crfFieldControl;
              const isRequired =
                quesObj?.questCrfItemes[state.questionIndex]?.isRequired;
              const fieldValue =
                quesObj?.questCrfItemes[state.questionIndex]?.fieldValue;
              const fieldItem = quesObj?.questCrfItemes[state.questionIndex]
              if (fieldItem.refTypeShow !== 2 && fieldItem.refTypesShow !== 2 &&
                isRequired === 1 && ((!fieldValue || !fieldValue?.length) ||
                  (crfFieldControl === 8 && (!fieldValue[0] || !fieldValue[1])))
              ) {
                Notify({ message: "请回答问题", type: "danger" });
                return;
              }
              if (fieldItem.refTypeShow !== 2 && fieldItem.refTypesShow !== 2 && crfFieldControl === 5 && fieldValue) {
                const reg = /^[0-9]+.?[0-9]*$/;
                if (!reg.test(fieldValue)) {
                  Notify({ message: "只可以填写数字哦", type: "danger" });
                  return;
                }
              }
              // 单选时
              if (
                quesArr.questCrfItemes[state.questionIndex]?.crfFieldControl === 9
              ) {
                quesArrFlag.questCrfItemes.forEach((showRelevanceItem) => {
                  state.showRelevance(showRelevanceItem);
                });
              } else if (
                quesArr.questCrfItemes[state.questionIndex]?.crfFieldControl ===
                10
              ) {
                quesArrFlag.questCrfItemes.forEach((showRelevanceItem) => {
                  state.showRelevances(showRelevanceItem, '');
                });
              }

              if (
                quesArrFlag.questCrfItemes[state.questionIndex + 1]
                  ?.refTypeShow === 2 ||
                quesArrFlag.questCrfItemes[state.questionIndex + 1]
                  ?.refTypesShow === 2
              ) {
                let num = 1;
                let subtractAddflag = true;
                quesArrFlag.questCrfItemes.forEach((elem, elemIndex) => {
                  if (
                    elemIndex >= state.questionIndex + 1 &&
                    (elem.refTypeShow === 2 || elem.refTypesShow === 2) &&
                    subtractAddflag
                  ) {
                    num += 1;
                  }
                  if (
                    quesArrFlag.questCrfItemes[state.questionIndex + num]
                      ?.refTypesShow !== 2 &&
                    quesArrFlag.questCrfItemes[state.questionIndex + num]
                      ?.refTypeShow !== 2 &&
                    subtractAddflag
                  ) {
                    subtractAddflag = false;
                  }
                });
                state.questionIndex += num;
                if (state.questionIndex >= quesArrFlag.questCrfItemes.length) {
                  setTimeout(() => {
                    state.saveQuestionnaire('');
                  }, 300)
                }
                return;
              }
              state.questionIndex++;
              return;
            } else if (flag === "subtract") {
              if (state.questionIndex > 0) {
                // 单选时
                // if(quesArr.questCrfItemes[state.questionIndex].crfFieldControl === 9)
                if (
                  quesArrFlag.questCrfItemes[state.questionIndex - 1]
                    ?.refTypesShow === 2 ||
                  quesArrFlag.questCrfItemes[state.questionIndex - 1]
                    ?.refTypeShow === 2
                ) {
                  let num = 1;
                  let myElemIndex = 0;
                  let subtractAddflag = true;
                  const questobj = deepClone(quesArrFlag);

                  for (let i = questobj.questCrfItemes.length - 1; i >= 0; i--) {
                    if (
                      (quesArrFlag.questCrfItemes[state.questionIndex - 1]
                        ?.crfItemId === questobj.questCrfItemes[i].crfItemId ||
                        myElemIndex) &&
                      (questobj.questCrfItemes[i].refTypeShow === 2 ||
                        questobj.questCrfItemes[i].refTypesShow === 2) &&
                      subtractAddflag
                    ) {
                      num += 1;
                      myElemIndex = i + num;
                    }
                    if (
                      myElemIndex &&
                      questobj.questCrfItemes[i]?.refTypesShow !== 2 &&
                      questobj.questCrfItemes[i]?.refTypeShow !== 2 &&
                      subtractAddflag
                    ) {
                      subtractAddflag = false;
                    }
                  }
                  state.questionIndex -= num;
                  return;
                }
                state.questionIndex--;
              }
              return;
            }
            // state.submitLoadingFlag = true
            Toast.loading({
              duration: 300000,
              message: '提交中...',
              forbidClick: true,
            });
            const quesDataObj: any = state.questionDataObj;
            let isRequiredFlag = true; // 是否填完必填字段
            //更新提交--数据数组
            quesArr.questCrfItemes.forEach((item) => {
              quesDataObj.questCrfItemes.forEach((items) => {
                //多选修改成数组
                if (item.crfFieldControl === 10) {
                  if (Array.isArray(item.fieldValue)) {
                    item.fieldValue = item.fieldValue.join(",");
                  }
                }
                if (item?.fieldValue) {
                  item.fieldValue += "";
                } else {
                  item.fieldValue = "";
                }
                // 合成字符串时间日期
                const { fieldLabel } = item
                const replaceFieldLabel = fieldLabel.replace(regex, '')
                if (
                  item.isRequired === 1 &&
                  !item.fieldValue &&
                  item.refTypeShow !== 2 &&
                  item.refTypesShow !== 2
                ) {
                  Notify({ message: `请回答${replaceFieldLabel}`, type: "danger" });
                  isRequiredFlag = false;
                  return;
                }
                if (
                  item.isRequired === 1 &&
                  item.crfFieldControl === 8 &&
                  item.refTypeShow !== 2 &&
                  item.refTypesShow !== 2
                ) {
                  // 字符串 用，分割
                  const isRequiredArr = item.fieldValue.split(",");
                  if (
                    isRequiredArr.length < 2 ||
                    isRequiredArr[0]?.length < 4 ||
                    isRequiredArr[1]?.length < 4
                  ) {
                    Notify({
                      message: `请回答${replaceFieldLabel}`,
                      type: "danger",
                    });
                    isRequiredFlag = false;
                    return;
                  }
                }

                if (item.crfFieldControl === 8) {
                  if (
                    Array.isArray(item.fieldValue) &&
                    item?.fieldValue?.length
                  ) {
                    // if (!item?.fieldValue[0] && item?.dctDateControlDefault) {
                    //   item.fieldValue[0] = dateMYDay;
                    // }
                    let val = item.fieldValue[0];
                    if (item?.fieldValue?.length > 1) {
                      // if (!item?.fieldValue[1] && item?.dctDateControlDefault) {
                      //   item.fieldValue = dateMyTime;
                      // }
                      val += " " + item.fieldValue[1];
                    }
                    item.fieldValue = val;
                  }
                }
                // 日期控件需要默认值 dateMYDay dateMyTime
                // if (
                //   item?.dctDateControlDefault &&
                //   (item?.crfFieldControl === 6 || item?.crfFieldControl === 8)
                // ) {
                //   if (!item?.fieldValue?.length || item.fieldValue.length < 3) {
                //     item.fieldValue = dateMYDay;
                //     if (item.crfFieldControl === 8) {
                //       // 日期时间控件默认值
                //       item.fieldValue += "," + dateMyTime;
                //     }
                //   }
                // }

                if (item.dctCode === items.dctCode) {
                  items.fieldValue = item.fieldValue;
                  items.fieldValueStr = item.fieldValueStr;
                }
                if (item.crfFieldControl === 5 && item.fieldValue) {
                  const reg = /^[0-9]+.?[0-9]*$/;
                  if (!reg.test(item.fieldValue)) {
                    isRequiredFlag = false;
                    Notify({
                      message: `${replaceFieldLabel}只可以填写数字哦`,
                      type: "danger",
                    });
                    return;
                  }
                }
              });
            });
            const notice = await maintainedFun(route.path || '')
            if (!isRequiredFlag || (notice && notice?.maintainedFlag)) {
              // state.submitLoadingFlag = false
              Toast.clear();
              return;
            }
            if (state.questItem?.questId) {
              // 判断是否需要change设为true
              const rowId = state.questItem?.rowId || route.query?.rowId
              if (store.state.initialAllListArr?.length) {
                const initialAllListArr = deepClone(store.state.initialAllListArr)
                initialAllListArr.forEach(e => {
                      if (e.dctCode === state.questItem?.pDctCode) {
                        e.children.forEach((el) => {
                          if (el.rowId === rowId) {
                            el.isSubmit = true;
                            // el.questionObj = deepClone(state.questionObj),
                            el.isChange = state.questionObj.questCrfItemes.some(item =>
                              el.questionObj.questCrfItemes.some(oldItem =>
                                item.dctCode === oldItem.dctCode &&
                                (item.refTypeShow !== oldItem.refTypeShow || (item.refTypeShow === oldItem.refTypeShow && oldItem.refTypeShow !== 2)) &&
                                (item.refTypesShow !== oldItem.refTypesShow || (item.refTypesShow === oldItem.refTypesShow && oldItem.refTypesShow !== 2)) &&
                                (item?.fieldValue?.length || oldItem?.fieldValue?.length || item?.fieldValue === 0 || oldItem?.fieldValue === 0) &&
                                (item.fieldValue !== oldItem.fieldValue && item.fieldValue + '' !== oldItem.fieldValue + '')
                              )
                            );
                          }
                        });
                      }
                    });
                // store.state.initialAllListArr.forEach(el => {
                  // if (el.rowId === rowId &&
                  //   state.questionObj.questId === el.questionObj.questId) {
                  //   el.isSubmit = true;
                  //   el.isChange = state.questionObj.questCrfItemes.some(item =>
                  //     el.questionObj.questCrfItemes.some(oldItem =>
                  //       item.dctCode === oldItem.dctCode && (item?.fieldValue?.length || oldItem?.fieldValue?.length || item?.fieldValue === 0 || oldItem?.fieldValue === 0) &&
                  //       (item.fieldValue !== oldItem.fieldValue && item.fieldValue + '' !== oldItem.fieldValue + '')
                  //     )
                  //   );
                  // }
                // });
                // console.log(initialAllListArr,'sinitialAllListArr1');
                store.dispatch('setInitialAllListArr', initialAllListArr)
                // console.log('store.state.initialAllListArr 列表提交',initialAllListArr);
              }
              await putTableData(
                state.questItem.questId,
                state.questItem.pDctCode,
                quesDataObj
              )
              proxy.$routerBackFun()
              // Notify({ type: "success", message: "提交成功" });
              // state.submitLoadingFlag = false
              Toast.clear();
            }
          }, 200);
        } catch {
          // state.submitLoadingFlag = false
          Toast.clear();
        }
      },
    });
    onBeforeMount(() => {
      if (route.query?.questItem) {
        state.questItem = JSON.parse(route.query.questItem as string);
      }
      state.onLoad();
      state.editFlag = route.query?.editFlag as string || '0';
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
@import "@/style/questionList.less";
.EditQuestionnairetList-container {
  color: #555;
  font-size: 0.13rem;
  :deep(.van-cell::after) {
    border: none !important;
  }
  .cut {
    padding: 0.1rem 0.2rem 0;
    box-sizing: border-box;
    .cut-body {
      font-size: 0.13rem;
      margin: 0.04rem 0;
      color: #333;
    }
  }
  .EditQuestionnairetList-form {
    // 只读
    .editVisitTaskDetails-body-r {
      width: 100%;
      // 内容
      .EditQuestionnairetList-module {
        width: 100%;
        overflow: auto;
        padding: 0.1rem 0 0 0;
        box-sizing: border-box;
        // 问卷
        .questCrf-items-title {
          margin: 0 0 0.2rem 0;
        }
        .questCrf-items-title-padding {
          padding: 0 0.2rem;
          box-sizing: border-box;
        }
        .questCrf-items {
          width: calc(100% - 0.4rem);
          min-height: 0.9rem;
          padding: 0.1rem;
          box-sizing: border-box;
          margin: 0 0 0.2rem 0.2rem;
          border-radius: 0.1rem;
          box-shadow: 0 0.02rem 0.12rem rgba(0, 0, 0, 0.07);
        }
      }
    }
    // 可编辑
    .editVisitTaskDetails-body {
      background: #f7f7f7;
      /*问卷内容区*/
      .editVisitTaskDetails-body-module {
        width: 100%;
        background: #f7f7f7;
        .border-input {
          min-height: 0.6rem;
          border-radius: 0.06rem;
          :deep(.van-field__body) {
            position: relative !important;
            top: 0.1rem !important;
            border-bottom: #f0f0f0 0.5px solid;
          }
        }
        .van-picker,
        .van-datetime-picker {
          width: 100%;
          position: fixed;
          left: 0;
          bottom: 0;
          z-index: 99;
        }
        // 上一题- 进度
        .questionnaire-schedule {
          padding: 0.2rem 0.1rem 0.1rem;
          box-sizing: border-box;
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 0.12rem;
          color: var(--theme-color);
          .questionnaire-schedule-gotop {
            display: flex;
            align-items: center;
            :deep(.van-badge__wrapper, .van-icon, .van-icon-arrow-left) {
              color: var(--theme-color) !important;
              margin: 0.03rem 0 0 0;
            }
          }
          .questionnaire-schedule-back {
            width: 2.44rem;
            height: 0.04rem;
            border-radius: 0.1rem;
            overflow: hidden;
            background: #ebf0fa;
            .questionnaire-schedule-in {
              width: 10%;
              height: 100%;
              border-radius: 0.1rem;
              overflow: hidden;
              background: linear-gradient(90deg, #3fa1fc 0%, #5860da 100%);
            }
          }
        }
        // 问卷区
        .questionnaire-module {
          width: 100%;
          height: calc(100% - 60px);
          font-size: 0.13rem;
          box-sizing: border-box;
          overflow: auto;
          // 填写指南
          .fingerpost {
            padding: 0.2rem;
            box-sizing: border-box;
            margin: 0.2rem 0 0 0;
            color: #333;
            font-size: 0.15rem;
          }
          .questionnaire-items {
            width: 100%;
            .questionnaire-item-moduel {
              width: calc(100% - 0.4rem);
              padding: 0.1rem;
              box-sizing: border-box;
              margin: 0.1rem 0 0.2rem 0.2rem;
              border-radius: 0.1rem;
              background: #fff;
              box-shadow: 0 0.02rem 0.12rem rgba(0, 0, 0, 0.07);
              border-radius: 0.1rem;
            }
            .questionnaire-items-title {
              box-sizing: border-box;
              margin: 0.1rem 0;
              overflow: hidden;
              display: flex;
              .date-and-time {
                display: flex;
              }
            }
            // 上传照片
            .upload-img {
              padding: 0.1rem;
              height: 3.3rem;
              overflow: auto;
              box-sizing: border-box;
              border-radius: 0.1rem;
              background: #fff;
            }
          }
        }
        .questionnaire-module-quest1 {
          height: 99%;
        }
        :deep(.van-field__control:disabled) {
          color: #333;
          -webkit-text-fill-color: #333;
        }
      }
    }
  }
}
</style>