<template>
  <div class="w-full font-color-555 ft-16-rem bg-color-f7f7f7">
    <CommonQuestion
    ref="InTheRowQuestionnaireRef"
    myQuestType="入排问卷"
    :myNavBarHeight="myNavBarHeight"
    :postQuestFile="postQuestFile"
    :deleteQuestFile="deleteQuestFile"
    :getQuestView="getIEGeneralInfo"
    :getQuestData="getQuestData"
    :getTableList="getTableList"
    :deleteTable="deleteTable"
    :putQuestData="putQuestData"
    :questionOnload="questionOnload"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, nextTick } from "vue";
// getQuestView, 后续访视可以用到此接口
import {
  getIEGeneralInfo,
  postQuestFile,
  deleteQuestFile,
  getQuestData,
  putQuestData,
  getTableList,
  deleteTable
} from "@/api/questionnaire";
import { useStore } from "vuex";
import CommonQuestion from "@/components/CommonQuestion.vue";
import { commonQuestionnaireInter } from "@/types/welcomeToJoinUs";

export default defineComponent({
  name: "Questionnaire", // 入排问卷
  components: {
    CommonQuestion
  },
  setup() {
    const store = useStore()
    const state = reactive({
      // All接口
      postQuestFile,
      deleteQuestFile,
      getIEGeneralInfo,
      getQuestData,
      putQuestData,
      getTableList,
      deleteTable,
      InTheRowQuestionnaireRef: null,
      myNavBarHeight: 0,
      // 问卷首次加载
      questionOnload: () => {
        nextTick(() => {
          if (state?.InTheRowQuestionnaireRef) {
            const QuestionnaireRef = state.InTheRowQuestionnaireRef as commonQuestionnaireInter
            QuestionnaireRef.editable = '1'
            if (store.state?.getOldQuestDataFlag && store.state?.oldQuestData) {
              QuestionnaireRef.questionObj = store.state.oldQuestData
              QuestionnaireRef.questionObjLength = store.state.oldQuestData.questCrfItemes.length;
              if (store?.state?.oldQuestDataIndex && QuestionnaireRef?.questionObj?.questCrfType === 2) {
                QuestionnaireRef.questionIndex = store.state.oldQuestDataIndex
              }
              QuestionnaireRef.fzFun(QuestionnaireRef.questionObj,store.state.initialQuestionObj)
              QuestionnaireRef.upListData()
              store.dispatch('setGetOldQuestDataFlag',0)
            } else {
              QuestionnaireRef.editable = '1'
              // QuestionnaireRef.questionIndex = -1;
              QuestionnaireRef.getQuestionnaiData();
            }
          }
        })
      }
    });
    
    return {
      ...toRefs(state),
    };
  },
});
</script>
