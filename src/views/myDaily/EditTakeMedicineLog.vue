<template>
  <div class="edittakemedicineLog-container">
    <van-nav-bar
      title="添加服药记录"
      left-text=""
      left-arrow
      @click-left="$routerBackFun()"
    />
    <div v-if="takeMedicineLogObj" class="edittakemedicineLog-form scrollnone">
      <!-- 名称 日期 剂量 -->
      <div class="edittakemedicineLog-infos">
        <div class="edittakemedicineLog-title">
          <div class="edittakemedicineLog-title-name">
            {{ takeMedicineLogObj?.drugDisplayName || ''	}}
          </div>
          <!-- <div class="edittakemedicineLog-title-date">
            {{takeMedicineLogObj.takenTime ? takeMedicineLogObj.takenTime : ''}}
          </div> -->
        </div>
        <!-- 服药日期 -->
        <van-field
          v-model="takeMedicineLogObj.takenTime"
          :is-link="showIsLink"
          readonly
          name="datetimePicker"
          label="服药日期"
          placeholder="请选择"
          input-align="right"
          class="datetimePicker"
          @click="() => {
            if (showIsLink){
              showDatas = true
            }
          }"
        />
        <van-popup v-model:show="showDatas" position="bottom">
          <van-datetime-picker
            v-model="currentDate"
            type="date"
            title="选择日期"
            :min-date="minDate"
            :max-date="maxDate"
            :columns-order="['year', 'month', 'day']"
            @cancel="showDatas = false"
            @confirm="confirmValue"
          />
        </van-popup>
        <!-- takenNumber -->
        <div class="edittakemedicineLog-takenNum">
          <p>服用剂量</p>
          <div class="edittakemedicineLog-takenNum-module">
            <div class="add-subtract-modules">
                <button
                  class="add-subtract-btn subtract-btn"
                  @click="addOrSubtract('s')"
                >
                  -
                </button>
                <div class="gift-num">{{ takeMedicineLogObj.takenDose ? takeMedicineLogObj.takenDose : '0' }}</div>
                <button
                  class="add-subtract-btn add-btn"
                  @click="addOrSubtract('a')"
                >
                  +
                </button>
              </div>
            {{ takeMedicineLogObj.drugDoseUnit ? takeMedicineLogObj.drugDoseUnit : ''}}
          </div>
        </div>
      </div>
      <!-- 红色文字 -->
      <div class="red-text">
        {{ takeMedicineLogObj?.drugRemind || '' }}
      </div>
      <!-- 其它说明 -->
      <div class="remark">
        <div class="remark-title">
          其它说明
          <span>(选填)</span>
        </div>
        <div class="remark-texts">
          <van-field
            v-model.trim="takeMedicineLogObj.remark"
            maxlength="999"
            type="textarea"
            placeholder="可记录本次服药是否有其他需要说明的内容（能帮助医生更好地了解您的服药情况)"
            autosize
          />
        </div>
      </div>
    </div>
    <!-- 按钮 -->
    <van-button
    round type="primary"
    :loading="submitLoadingFlag"
    loading-text="提交"
    class="edittakemedicineLog-save-btn"
    @click="saveMedicationForm">
      提交
    </van-button>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount, getCurrentInstance } from "vue";
import { useRoute } from "vue-router";
import {
  postMedication,
  putMedicationData,
} from "@/api/takeMedicineLog";
import { Notify, Toast } from "vant";
import { year } from "@trialdata/common-fun-css/index";
// import { deepClone, dateMYDay } from "@trialdata/common-fun-css/index";
import { EditTakeMedicineLog } from "@/types/myDaily";
import { useStore } from "vuex";

export default defineComponent({
  name: "EditTakeMedicineLog",
  setup() {
    const route = useRoute();
    const store = useStore()
    const proxy: any = getCurrentInstance()?.proxy
    const state: EditTakeMedicineLog = reactive({
      takeMedicineLogObj: {
        id: "",
        drugName: "",
        drugDescription: "",
        drugSpecifics: "",
        batchNumber: "",
        validateTill: "",
        storeNumber: 0,
        recommendApplyNumber: 0,
        maxApplyNumber: 0,
        minApplyNumber: 0,
        drugDoseUnit: "",
        scale: 0,
        defaultDose: 0,
        maxDose: 0,
        minDose: 0,
        takenDose: 1,
        armName: "",
        medicationTips: "",
        manufacturer: "",
        patientId: '',
        drugId: '',
        remark: '',
        takenTime: '',
        drugRemind: ''
      },
      showDatas: false,
      currentDate: new Date(), // new Date()
      minDate: new Date(year/1 - 100, 12, 31),
      maxDate: new Date(),
      showIsLink: false,
      submitLoadingFlag: false,
      confirmValue: (val) => {
        const year = val.getFullYear();
        let month = val.getMonth() + 1;
        let day = val.getDate();
        let hour = val.getHours();
        let minute = val.getMinutes();
        if (month >= 1 && month <= 9) {
          month = `0${month}`;
        }
        if (day >= 1 && day <= 9) {
          day = `0${day}`;
        }
        if (hour >= 0 && hour <= 9) {
          hour = `0${hour}`;
        }
        if (minute >= 0 && minute <= 9) {
          minute = `0${minute}`;
        }
        let currentDates = state.currentDate;
        currentDates = `${year}-${month}-${day}`;
        //日期控件时直接赋值
        state.takeMedicineLogObj.takenTime = currentDates;
        state.showDatas = false;
      },
      // 加减数据--
      addOrSubtract: (falg) => {
        const { scale, minDose, maxDose, takenDose } = state.takeMedicineLogObj
        if (falg === "s") {
          if(takenDose <= minDose){
            Notify({ message: '数量不可小于最小服用剂量', type: 'danger' })
            return
          }
          state.takeMedicineLogObj.takenDose -= scale
        } else if (falg === "a") {
          if(takenDose >= maxDose){
            Notify({ message: '数量不可大于最大服用剂量', type: 'danger' })
            return
          }
          state.takeMedicineLogObj.takenDose += scale
        }
      },
      // 提交
      saveMedicationForm: () => {
          const requestMedication = state.takeMedicineLogObj.id ? putMedicationData : postMedication
          if (!state?.takeMedicineLogObj?.takenTime) {
            Toast('请选择服药日期')
          } else if (store.state.homeList.inGroup > state.takeMedicineLogObj.takenTime){
            Notify({ message: '服药日期不能早于入组日期', type: 'danger' })
          } else if (state.takeMedicineLogObj) {
            state.submitLoadingFlag = true
            requestMedication(state.takeMedicineLogObj)
            .then(() => {
              Notify({ message: '保存成功', type: 'success' })
              if (store.state.routerInn?.length && store.state.routerInn[store.state.routerInn.length-1].backPath) {
                const routerInnBck = {...store.state.routerInn[store.state.routerInn.length-1]}
                if (routerInnBck.backPath === "/takemedicineLog") {
                  proxy.$routerBackFun()
                } else {
                  proxy.$routerBackFun(2)
                }
              }
              state.submitLoadingFlag = false
            }).catch(() => {state.submitLoadingFlag = false})
          }
      },
      // 获取
      getMedications: () => {
        const query = JSON.parse(route.query?.takemedicineLogItem as string)
        const { dctPatientId } = store.state.homeList
        if (query?.medicationLogStatus === 2) {
          state.showIsLink = true
        }
        if (query?.drugItem) {
          state.takeMedicineLogObj = query.drugItem || {}
          state.takeMedicineLogObj.takenDose = query?.takenDose ? (query?.takenDose) : query?.drugItem.defaultDose
          state.takeMedicineLogObj.patientId = dctPatientId
          state.takeMedicineLogObj.drugId = query.drugItem.id
          state.takeMedicineLogObj.takenTime = query.takenTime
          state.takeMedicineLogObj.remark = query.remark
          state.takeMedicineLogObj.id = query.id
        } else {
          if (route?.query?.addTakemedicineLogItem) {
            const rest = JSON.parse(route.query?.addTakemedicineLogItem as string)
            state.takeMedicineLogObj = rest
            state.takeMedicineLogObj.takenDose = rest.defaultDose
            state.takeMedicineLogObj.patientId = dctPatientId
            state.takeMedicineLogObj.drugId = rest.id
            state.takeMedicineLogObj.takenTime = '' // dateMYDay
            state.takeMedicineLogObj.remark = ''
            state.takeMedicineLogObj.id = ''
            state.showIsLink = true
          }
        }      
      },
    });
    onBeforeMount(() => {
      state.getMedications()
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.edittakemedicineLog-container {
  height: 100vh;
  font-size: 0.15rem;
  color: #555;
  box-sizing: border-box;
  background: #f7f7f7;
  overflow: auto;
  .edittakemedicineLog-save-btn {
    width: 80%;
    height: 0.39rem;
    margin: 0.2rem 10%;
  }
  .edittakemedicineLog-form {
    height: 70vh;
    padding: 0.3rem 0.2rem;
    overflow: auto;
    // 名称 日期 剂量
    .edittakemedicineLog-infos{
      // height: 1.45rem;
      border-radius: 0.04rem;  
      background: #fff;
      box-shadow: 0 0.02rem 0.12rem rgba(0, 0, 0, 0.07);
      overflow: hidden;
      .edittakemedicineLog-title {
        height: 0.45rem;
        padding: 0 0.1rem;
        box-sizing: border-box;
        display: flex;
        justify-content: space-around;
        align-items: center;
        flex-wrap: wrap;       
        background: linear-gradient(180deg, #3fa1fc, #5860da);
        color: #fff;
        .edittakemedicineLog-title-name {
          width: 100%;
          font-size: 0.15rem;
        }
        // .edittakemedicineLog-title-date{
        //   width: 100%;
        //   font-size: 0.12rem;
        // }
      }
      // 服药日期
      .datetimePicker{
        margin: 0.05rem 0;
        font-size: 0.15rem;
        padding: 0.1rem;
      }
      // 剂量
      .edittakemedicineLog-takenNum{
        // margin: 0.15rem 0;
        padding: 0.1rem 0.1rem 0.16rem 0.1rem;
        box-sizing: border-box;
        // height: 50%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .edittakemedicineLog-takenNum-module{
          display: flex;
          .add-subtract-modules {
          display: flex;
          margin: 0 0.1rem 0 0;
            .add-subtract-btn {
              width: 0.2rem;
              height: 0.2rem;
              display: flex;
              justify-content: center;
              align-items: center;
              font-size: 0.16rem;
              background: #fff;
              border: 0.5px solid #c3c3c3;
            }
            .subtract-btn{
              border-radius: 0.04rem 0 0 0.04rem;
              line-height: 17px;
            }
            .add-btn{
              border-radius: 0 0.04rem 0.04rem 0;
            }
            .gift-num {
              width: 0.3rem;
              height: 0.2rem;
              padding: 0;
              box-sizing: border-box;
              border: 0.5px solid #c3c3c3; 
              display: flex;
              justify-content: center;
              align-items: center;
              font-size: 0.12rem;
              background: #fff;
            }
          }
        }       
      }
    }
    // 红字段
    .red-text{
      margin: 0.2rem 0 0.5rem 0;
      padding: 0 0.1rem;
      box-sizing: border-box;
      font-size: 0.13rem;
      color: #E26375;
    }
    // 其它说明
    .remark{
      .remark-title{
        color: #333;
        & span{
          color: #C3C3C3;
        }
      }
      .remark-texts{
        padding: 0.1rem 0.1rem 0.2rem 0.1rem;
        box-sizing: border-box;
        margin: 0.1rem 0 0 0;
        font-size: 0.13rem;
        border-radius: 0.05rem;
        color: #C3C3C3;
        box-shadow: 0 0.02rem 0.12rem rgba(0, 0, 0, 0.07);
        background: #fff;
        .van-cell,.van-field{
          padding: 0;
        }
      }
    }
  }
}
</style>
