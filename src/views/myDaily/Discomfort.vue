<template>
  <div class="discomfort-container">
    <van-nav-bar
      title="不适记录"
      left-text=""
      left-arrow
      @click-left="$routerBackFun()"
    />
    <!-- 日期筛选 -->
    <!-- <FilterDate :request="getAdverseEventsDatas" /> -->
    <div
      ref="discomfortScroll"
      class="discomfort-body scrollnone"
      @scroll="scrollChage"
    >
      <!-- 轴式 -->
      <van-steps class="scrollnone" direction="vertical" :active="-1">
        <van-step
          v-for="(item, index) in discomfortList"
          :key="index"
          @click="routerGo('/editdiscomfort', item)"
          :class="{ 'active-open-back': item.activeOpen }"
        >
          <van-swipe-cell>
            <div class="active-back py-[0.1rem]">
              <div class="status-module !pb-[0.04rem]">
                <h4 class="activeName truncate">{{ item.title }}</h4>
              </div>
              <p>{{ item.happenDate }}</p>
              <div
                v-if="item?.toBeClarified"
                class="absolute centerflex ft-10-rem"
                style="top: 0px;
                left: 0;
                width: 0.48rem;
                color: #F19980;
                background-color: #FFEBE6;
                border-radius: 0.06rem 0 0.06rem 0;"
              >待澄清</div>
            </div>
            <!-- <template #right>
              <van-button
                class="h-full"
                type="danger"
                text="删除"
                @click="deleteDiscomfort(item)"
              />
            </template> -->
          </van-swipe-cell>
          <!-- 展开项 -->
          <!-- <div
            v-if="item.activeOpen && item.taskList"
            class="taskList-container"
          >
            <div
              v-for="(taskItem, taskIndexs) in item.taskList"
              :key="taskIndexs"
              class="taskList-module"
            >
              <div
                class="taskList-fieldValue"
                @click.stop="routerGo('/editdiscomfort', item)"
              >
                <div
                  v-if="taskItem.fieldLabel"
                  v-html="taskItem.fieldLabel"
                  class="taskList-fieldValue-first"
                />
                <div class="taskList-fieldValue-last">
                  {{ taskItem?.fieldValueStr || '' }}
                  {{ taskItem?.dctQuestUnit || ''}}
                </div>
              </div>
            </div>
          </div> -->
        </van-step>
      </van-steps>

      <van-button
        class="discomfort-add-btn"
        round
        type="primary"
        @click="routerGo('/editdiscomfort')"
      >
        +添加记录
      </van-button>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  onBeforeMount,
  getCurrentInstance,
} from "vue";
import { useRoute } from "vue-router";
import {
  getAdverseEvents,
  deleteAdverseEvent,
  getViewData,
  postAdverseEvent,
} from "@/api/discomfort";
import { Dialog, Notify } from "vant";
import { questionObjInter } from "@/types/quest";
import { Discomfort, getAdverseEventsInter, getDiscomfortViewDataInter } from '@/types/myDaily';

export default defineComponent({
  name: "Discomfort", // 不适记录
  setup() {
    const route = useRoute()
    const proxy: any = getCurrentInstance()?.proxy
    const state: Discomfort = reactive({
      discomfortList: [],
      discomfortScroll: null,
      dates: {
        dtBegin: "",
        dtEnd: "",
        pageIndex: 1,
        pageSize: 20,
      },
      totalItemCount: 0,
      scrollChage: () => {
        const scroll = state.discomfortScroll;
        if (scroll.scrollHeight - scroll.scrollTop === scroll.clientHeight) {
          // 触底
          if (state.discomfortList.length < state.totalItemCount) {
            state.dates.pageIndex += 1;
            state.getAdverseEventsDatas({ ...state.dates });
          }
        }
      },
      // 获取不适记录的数据
      getAdverseEventsDatas: (params) => {
        getAdverseEvents(params)
        .then((rest) => {
          const res = rest as getAdverseEventsInter
          if (params.pageIndex > 1) {
            state.discomfortList = state.discomfortList.concat(res.items);
          } else {
            state.discomfortList = res.items;
          }
          state.totalItemCount = res.totalItemCount;
          // state.discomfortList.forEach(async (item) => {
          //   // 默认进来进行中的高亮且展开
          //   item.activeOpen = item?.LogStatus === 3 ? true : false;
          //   // 获取某个其它药物
          //   if (!item?.taskList) {
          //     // 获取列表信息
          //     getViewData(item.rowId, { searchType: 3 })
          //     .then((rest) => {
          //       const res = rest as getDiscomfortViewDataInter
          //       item.taskList = res?.aecmItems || [];
          //     });
          //   }
          // });
          /*
          adverseEventLogStatus: 2
          rowId: "1"
          happenDate: "2021-07-08"
          title: "不适记录(头疼)"
          */
        });
      },
      // 点击高亮展开
      // showOpenItem: (index) => {
      //   state.discomfortList.forEach((item, indexs) => {
      //     if (index === indexs) {
      //       item.activeOpen = !item.activeOpen;
      //     } else {
      //       item.activeOpen = false;
      //     }
      //   });
      // },
      // 删除按钮
      deleteDiscomfort: (item) => {
        Dialog.confirm({
          title: "温馨提示",
          message: "您确定要删除此记录吗?",
        })
          .then(() => {
            deleteAdverseEvent(item.rowId).then(() => {
              Notify({
                type: "success",
                message: `删除${item.title ? item.title : ""}成功`,
              });
              state.getAdverseEventsDatas({ ...state.dates });
            });
          })
          .catch(() => {
            return;
          });
      },
      routerGo: async(path, item) => {
        let query = {}
        if (item) {
          if (item.questDisplayType === 3) {
            path = '/followUrlQuestionnaires'
            query = {
              questionUrl: item.questUrl,
              myTitle: '不适记录',
              type: '编辑',
              aeId: item.rowId,
              goPath: route.path // goPath 定制化问卷处理返回路径的问题
            }
          } else {
            query = {
              discomfortItem: JSON.stringify(item as string) || null,
              myTitle: "不适记录",
            }
          }
        } else {
          const rest = await postAdverseEvent() as questionObjInter
          if (rest.questCrfType === 3){
              path = '/followUrlQuestionnaires'
              query = {
                questionUrl: rest.questUrl,
                myTitle: '不适记录',
                type: '新建',
                aeId: '',
                goPath: route.path // goPath 定制化问卷处理返回路径的问题
              }
          } else {
            query = {
              discomfortItem: JSON.stringify(item as string) || null,
              myTitle: "不适记录",
            }
          }
        }
        proxy.$routerGoFun('routerInnPush', path, query, route.path)

      },
    });
    // provide("Dates", state.dates);

    onBeforeMount(() => {
      state.getAdverseEventsDatas({ ...state.dates });
    });

    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
@import "@/style/steps.less";
.discomfort-container {
  height: 100vh;
  overflow: hidden;
  color: #555;
  font-size: 0.14rem;
  .discomfort-body {
    width: 100%;
    // height: 73vh;
    height: calc(100vh - 1.3rem);
    margin: 0.1rem 0 0 0;
    box-sizing: border-box;
    overflow: auto;
    // 子列表
    .taskList-container {
      box-sizing: border-box;
      padding: 0.1rem 0.1rem 0.2rem 0.1rem;
      .taskList-module {
        width: 100%;
        color: #333;
        font-size: 0.15rem;
        margin: 0;
        margin-top: 0.13rem;
        border-bottom: 1px solid #ededed;
        .taskList-fieldValue {
          width: 100%;
          display: flex;
          flex-wrap: wrap;
          div {
            display: block;
            width: 100%;
            color: #9a9a9a;
            font-size: 0.12rem;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            &:nth-child(2) {
              text-align: right;
              margin-top: 0.05rem;
              color: #333;
            }
          }
          .taskList-fieldValue-first,.taskList-fieldValue-last {
            width: 100%;
            margin: 0 0.06rem 0 0;
          }
        }
      }
    }
    .discomfort-add-btn {
      width: 90%;
      position: fixed;
      left: 0.2rem;
      bottom: 0.2rem;
    }
  }
}
</style>
