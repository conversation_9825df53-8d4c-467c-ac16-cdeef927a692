<template>
  <div class="waybillAgreement-container">
    <van-nav-bar
      title="快件运单协议"
      left-text=""
      left-arrow
      @click-left="$routerBackFun()"
    />
    <div class="waybillAgreement-body">
      <div class="waybillAgreement-module scrollnone">
        <div v-html="waybillAgreementText" />
      </div>
      <van-button
        class="waybillAgreement-back-btn"
        round
        type="primary"
        @click="saveWaybillAgreement"
      >确定</van-button>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  onBeforeMount,
  getCurrentInstance,
} from "vue";
import { useStore } from "vuex";
import { useRoute } from "vue-router";
import { getLogisticsProviderAndArgument } from "@/api/myOrder";

export default defineComponent({
  name: "WaybillAgreement" /* 快件运单协议 */,
  setup() {
    const route = useRoute();
    const proxy: any = getCurrentInstance()?.proxy
    const store = useStore();
    const state = reactive({
      waybillAgreementText: "",
      saveWaybillAgreement: () => {
        const storePlaceAnOrderObj = {
          expectPickupTimeDisplay: store.state?.storePlaceAnOrderObj?.expectPickupTimeDisplay || "",
          remark: store.state?.storePlaceAnOrderObj?.remark || "",
          showCheckedIcon: true,
        };
        store.dispatch('setStorePlaceAnOrderObj',storePlaceAnOrderObj)
        proxy.$routerBackFun();
      },
    });
    onBeforeMount(() => {
      const { goodTypeName, channelName } = route.query;
      getLogisticsProviderAndArgument({ goodTypeName, channelName })
        .then((res: any) => {
          if (res?.length) {
            state.waybillAgreementText = res[0].agreementContent;
          }
        });
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.waybillAgreement-container {
  height: 100vh;
  color: #555;
  font-size: 0.12rem;
  overflow: hidden;
  background: linear-gradient(180deg, #eaeffa 70%, #fff 100%);
  .waybillAgreement-body {
    width: 100%;
    height: 86vh;
    margin: 0.2rem 0 0 0;
    box-sizing: border-box;
    .waybillAgreement-module {
      width: 100%;
      height: 90%;
      padding: 0.1rem;
      box-sizing: border-box;
      color: #333;
      overflow: auto;
      :deep(ul) {
        list-style-type: disc !important;
        list-style-position: inside !important;
      }
      :deep(ol) {
        list-style-type: decimal !important;
        list-style-position: inside !important;
      }
    }
    .waybillAgreement-back-btn {
      width: 90%;
      margin: 0.2rem 0 0 0.2rem;
    }
  }
}
</style>
