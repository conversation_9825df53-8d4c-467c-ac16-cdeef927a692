<template>
  <div class="takeMedicineLogList-container">
    <van-nav-bar
      title="添加记录"
      left-text=""
      left-arrow
      @click-left="$routerBackFun()"
    />
    <div class="takeMedicineLogList-form">
      <div class="takeMedicineLogList-body scrollnone">      
        <div class="takeMedicineLogList-body">
          <div class="takeMedicineLogList-nav">
            <img src="@/assets/baby/warningIcon.svg" alt="">
            <span v-if="newTakemedicineLog === '1'">实际服用请您遵从医嘱！</span>
            <span v-else>请您遵从医嘱服药</span>
          </div>
          <div v-if="takemedicineLogList" class="takeMedicineLogList-module scrollnone">
            <div v-for="(item,index) in takemedicineLogList" class="takeMedicineLogList-items"
              :key="index"
              @click="routerGo('/edittakemedicineLog',item)"
              >
              <div class="takeMedicineLogList-items-le">
                <img src="@/assets/baby/takemedicineLogLsitIcon.svg" alt="" />
                <div>
                  <p>{{item?.drugDisplayName?item.drugDisplayName:''}}</p>
                </div>
              </div>
              <van-icon name="arrow" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <MyPopupShow 
    :myPopupShow="questionPopupShowFlag"
    title="选择问卷"
    >
    <template v-slot:bodyslot>
        <div class="question-popup-body">
          <div class="question-popup-item-module">
            <div v-for="(item,index) in questionPopupList"
            :key="index"
            class="question-popup-item flex items-center"
            :class="{'question-popup-item-active': index === questionItemActive}"
            @click="routerGo('/takemedicineLogQuestionnaire',item,index)"
            >{{item.questTemplateName}}</div>
          </div>
          <div class="centerflex">
            <van-button class="question-popup-btn" round @click="questionPopupShowFlag = false">
                取消
            </van-button>
          </div>
        </div>
      </template>
    </MyPopupShow>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount, getCurrentInstance } from "vue";
import { useRoute } from "vue-router";
import MyPopupShow from "@/components/MyPopupShow.vue";
import { getDrugs, getDurgQuest } from '@/api/takeMedicineLog';

export default defineComponent({
  name: "TakeMedicineLogList", // 添加记录
  components: {
    MyPopupShow
  },
  setup() {
    const proxy: any = getCurrentInstance()?.proxy
    const route = useRoute();
    const state = reactive({
      takemedicineLogList: [],
      questionItemActive: -1, // 点击高亮的
      questionPopupList: [],
      questionPopupShowFlag: false, // 显示问卷选择弹窗
      newTakemedicineLog: '',
      durgId: '',
      // 跳转
      routerGo: (path,item,index) => {
        if (state.newTakemedicineLog && path === '/edittakemedicineLog') {
          // 新版 先选择问卷
          if (item?.id) {
            state.durgId = item.id
            getDurgQuest(item.id)
            .then((res: any) => {
              state.questionPopupList = res
              if (res?.length > 1) {
                state.questionPopupShowFlag = true
              } else if (res?.length === 1) {
                state.routerGo('/takemedicineLogQuestionnaire',res[0])
              }
            })
          }
          return
        }
        state.questionItemActive = index
        // 进入问卷
        if (item) {
          const query = {
            editable: '1',
            takemedicineLogItem: null,
            durgId: state.durgId,
            addTakemedicineLogItem: JSON.stringify(item as string)
          }
          proxy.$routerGoFun('routerInnPush', path, query, route.path, route.query)
        }
      },
    });

    onBeforeMount(() => {
      if (route?.query?.takemedicineLogList) {
        const rest = JSON.parse(route.query?.takemedicineLogList as string)
        state.takemedicineLogList = rest
      } else if (route?.query?.newTakemedicineLog) {
        // 新版服药日志
        state.newTakemedicineLog = route?.query?.newTakemedicineLog as string
        getDrugs()
        .then((res: any) => {
          state.takemedicineLogList = res
        })
      }
    });

    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.takeMedicineLogList-container {
  height: 100vh;
  overflow: hidden;
  color: #555;
  font-size: 0.14rem;
  .takeMedicineLogList-form {
    .takeMedicineLogList-body {
      width: 100%;
      height: 100%;
      .takeMedicineLogList-module {
        width: 100%;
        height: 86vh;
        overflow: auto;
        .takeMedicineLogList-items {
          height: 0.9rem;
          padding: 0.1rem 0.1rem 0.1rem 0.14rem;
          box-sizing: border-box;
          margin: 0.2rem;
          display: flex;
          justify-content: space-between;
          align-items: center;
          background: #fff;
          border-radius: 0.1rem;
          box-shadow: 0 0.02rem 0.12rem rgba(0, 0, 0, 0.07);
          .takeMedicineLogList-items-le {
            display: flex;
            align-items: center;
            p {
              display: -webkit-box;
              word-break: break-all;
              word-wrap:break-word;
              overflow: hidden;
              text-overflow: ellipsis;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
              color: #333;
              font-size: 0.15rem;
            }
            img {
              width: 0.5rem;
              height: 0.5rem;
              margin: 0 0.14rem 0 0;
            }
          }
          .takeMedicineLogList-items-ri-img {
            width: 0.54rem;
            height: 0.54rem;
          }
        }
      }
    }
  }
  // 弹窗
  .question-popup-body{
    padding: 0 10px;
    box-sizing: border-box;
    .question-popup-item-module{
      max-height: 70vh;
      overflow: auto;
      .question-popup-item{
        padding: 0 15px;
        margin: 0 0 10px 0;
        font-size: 0.13rem;
        border-radius: 0.06rem;
        min-height: 50px;
        color: #333;
        box-shadow: 0 0.02rem 0.12rem rgba(0, 0, 0, 0.07);
      }
      .question-popup-item-active{
        color: var(--theme-color);
        border: 0.5px solid var(--theme-color);
      }
    }
    .question-popup-btn{
      margin: 0.2rem 0 0.14rem 0;
      width: 80%;
      height: 35px;
      background:#fff;
      color: var(--theme-color);
      border: 0.5px solid var(--theme-color);
    }
  }
}
</style>
