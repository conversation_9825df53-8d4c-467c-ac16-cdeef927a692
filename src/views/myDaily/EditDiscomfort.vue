<template>
  <div class="w-full font-color-555 ft-16-rem bg-color-f7f7f7">
    <van-nav-bar
      :title="myTitle"
      left-text=""
      left-arrow
      @click-left="routerBack"
    />
    <CommonQuestion
      v-if="getQuestView && getQuestData"
      ref="EditDiscomfortRef"
      :myQuestType="myTitle"
      :postQuestFile="postQuestFile"
      :deleteQuestFile="deleteQuestFile"
      :getQuestView="getQuestView"
      :getQuestData="getQuestData"
      :putQuestData="putQuestData"
      :questionOnload="questionOnload"
    />
    <MyPopupShow
      :myPopupShow="immediateWithdrawalFlag"
      title="提示"
      texts="
      <div class='mb-01rem ft-13-rem'>有未提交的数据，请确认。</div>
      <div class='text-red-400 ft-13-rem'>确认退出：将不会暂存未提交的数据<div>"
      cancelText="继续填写"
      saveText="确认退出"
      cancelClass="text-black"
      saveClass="text-black"
      :handleSave="$routerBackFun"
      :handleCancel="
        () => {
          immediateWithdrawalFlag = false;
        }
      "
    />
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  onBeforeMount,
  nextTick,
  getCurrentInstance,
} from "vue";
import { useRoute } from "vue-router";
import { EditDiscomfort } from "@/types/myDaily";
import CommonQuestion from "@/components/CommonQuestion.vue";
import { putCMData, postCM, getCMData, getCM } from "@/api/drugCombination";
import {
  postAdverseEvent,
  putAdverseEventData,
  getAdverseEventData,
  getAdverseEvent,
} from "@/api/discomfort";
import MyPopupShow from "@/components/MyPopupShow.vue";
import {
  postQuestFile,
  deleteQuestFile,
  getQuestInfo,
  getQuestUnPlannedWindow,
} from "@/api/questionnaire";
import { useStore } from "vuex";

export default defineComponent({
  name: "EditDiscomfort", // 不适记录/其它药物
  components: {
    CommonQuestion,
    MyPopupShow,
  },
  setup() {
    const store = useStore();
    const route = useRoute();
    const proxy: any = getCurrentInstance()?.proxy;
    const state: EditDiscomfort = reactive({
      EditDiscomfortRef: null,
      // all接口
      postQuestFile,
      deleteQuestFile,
      putCMData,
      postCM,
      getCMData,
      getCM,
      postAdverseEvent,
      putAdverseEventData,
      getAdverseEventData,
      getAdverseEvent,
      // 自定义接口
      getQuestView: () => {},
      getQuestData: () => {},
      putQuestData: () => {},
      myRequestId: "", // id
      myTitle: "", // 不适记录 - 其它药物
      immediateWithdrawalFlag: false,
      routerBack: () => {
        if (state.EditDiscomfortRef?.initialIsChange) {
          // 提交过的： 继续填写 确认退出 （一定弹出）
          state.immediateWithdrawalFlag = true;
          return;
        }
        proxy.$routerBackFun();
      },
      discomfortCMItem: {},
      // 首次加载
      questionOnload: () => {
        nextTick(async () => {
          if (
            sessionStorage.getItem("patientToken") ||
            store?.state?.patientToken
          ) {
            try {
              if (state?.EditDiscomfortRef) {
                const QuestionnaireRef = state.EditDiscomfortRef;
                QuestionnaireRef.editable = "1";
                if (state.discomfortCMItem?.questId) {
                  const res: any = await getQuestInfo(
                    state.discomfortCMItem?.questId,
                    {
                      rowNum: state.myRequestId,
                    }
                  );
                  // 计划外的查看
                  const unPlannedRes: any = await getQuestUnPlannedWindow(
                    state.discomfortCMItem?.questId,
                    {
                      rowNum: state.myRequestId,
                    }
                  );
                  if (unPlannedRes?.id) {
                    if (
                      !unPlannedRes?.isFeedBack ||
                      (unPlannedRes?.isFeedBack && unPlannedRes?.isAgree)
                    ) {
                      QuestionnaireRef.UnplannedModificationRef.unplannedModificationModuleFlag =
                        true;
                      QuestionnaireRef.UnplannedModificationRef.unplannedModificationModuleObj =
                        unPlannedRes;
                    }
                  }
                  QuestionnaireRef.editable = res?.isEditable ? "1" : "0";
                }
                QuestionnaireRef.getQuestionnaiData(state.myRequestId);
              }
            } catch {}
          } else {
            setTimeout(() => {
              state.questionOnload();
            }, 500);
          }
        });
      },
    });

    onBeforeMount(() => {
      if (route.query?.myTitle) {
        state.myTitle = route.query.myTitle as string;
      }
      let query = {
        rowId: "",
      };
      let myRequest = () => {};
      let myRequestId = "";
      if (state.myTitle === "其它药物") {
        query = JSON.parse(route.query.CMsItem as string);
        myRequest = query ? getCM : postCM;
        myRequestId = query ? query.rowId : " ";
        state.getQuestData = getCMData;
        state.putQuestData = putCMData;
      } else {
        query = JSON.parse(route.query.discomfortItem as string);
        myRequest = query ? getAdverseEvent : postAdverseEvent;
        myRequestId = query ? query.rowId : " ";
        state.getQuestData = getAdverseEventData;
        state.putQuestData = putAdverseEventData;
      }
      state.discomfortCMItem = query;
      state.myRequestId = myRequestId;
      state.getQuestView = myRequest;
    });

    return {
      ...toRefs(state),
    };
  },
});
</script>
