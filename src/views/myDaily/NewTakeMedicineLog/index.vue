<template>
  <div class="font-color-555 ft-014-rem"
  :style="{ height: outerHeight ? outerHeight + 'px' : '100vh' }">
    <van-nav-bar
      title="用药日志"
      left-text=""
      left-arrow
      @click-left="$routerBackFun()"
    />
    <div :style="{ height: outerHeight ? outerHeight - 46 + 'px' : '90vh' }">
      <div class="takeMedicineLogList-nav justify-between">
        <div class="flex">
          <img src="@/assets/baby/warningIcon.svg" alt="">
          <div>实际服用请您遵从医嘱！</div>
        </div>
        <div class="underline" @click="myPopupShow = true">添加额外记录</div>
      </div>
      <!-- 日期筛选 -->
      <FilterDate :maxDate="filterDateMaxDate" :request="getMyTakeMedications"/>
      <div ref="newTakemedicineLogScroll" class="mt-01-rem overflow-auto scrollnone"
      :style="{ height: outerHeight ? outerHeight - 162 + 'px' : '70vh' }"
      @scroll="scrollChage">      
        <!-- 轴 -->
        <van-steps class="scrollnone" direction="vertical" :active="-1">
          <van-step
            v-for="(item, index) in medicationsList"
            :key="index"
          >
            <van-swipe-cell :class="{'van-swipe-cell-acitve': item?.drugActive}">
              <div
                class="pt-[0.1rem]"
                @click="routerGo('/takemedicineLogQuestionnaire',item)"
              >
                <div
                  v-if="item?.toBeClarified"
                  class="absolute centerflex ft-10-rem"
                  style="top: 0px;
                  left: 0;
                  width: 0.48rem;
                  color: #F19980;
                  background-color: #FFEBE6;
                  border-radius: 0.06rem 0 0.06rem 0;"
                >待澄清</div>
                <div class="status-module">
                  <h4 class="activeName">{{ item.displayTime ? item.displayTime : ''}}</h4>
                </div>
                <p class="none-warp-text-auto">{{item?.drugName || ''}}</p>
                <p v-if="item?.actualDosage">{{item.actualDosage}}{{item?.drugDoseUnit || ''}}</p>
                <p v-else-if="item?.dosage">计划{{item.dosage}}{{item?.drugDoseUnit || ''}}</p>
                <div v-if="item.drugReminderText" class="remark-texts none-warp-text-auto" v-html="item.drugReminderText"/>
                <div class="my-hr"/>
                <div class="status-btns">
                  <div v-if="item?.isFinished === 1" class="accomplish-btn">已记录</div>
                  <div v-else-if="item?.isFinished === 2" class="accomplish-btn">无需记录</div>
                  <div v-else-if="item?.isFinished === 3" class="accomplish-btn">已过期</div>
                  <div v-else-if="item?.isFinished === 0 && !item?.isReadOnly" class="eidt-log-btn flex items-center">填写用药记录{{ item.expireStr ? `（${item.expireStr}）` : '' }}
                    <img src="@/assets/baby/takemedicineLog/fillInIcon.svg" alt="">
                  </div>
                  <div v-else-if="item?.isFinished === 0 && item?.isReadOnly" class="accomplish-btn">{{ item?.expireStr }}</div>
                </div>
              </div>
              <!-- <template #right>
                <van-button
                  style="height: 100%"
                  square
                  type="danger"
                  text="删除"
                  @click="deleteTakeMedicineLog(item)"
                />
              </template> -->
            </van-swipe-cell>
          </van-step>
        </van-steps>
      </div>
    </div>
    <MyPopupShow
      :myPopupShow="myPopupShow"
      title="提示"
      :texts="notFilledInOnThatDayFlag ? myPopupShowThatDayTexts : myPopupShowTexts"
      mypopupTextsClass="font-color-666"
      cancelText="取消"
      saveText="继续添加"
      saveClass="font-color-333"
      cancelClass="font-color-333"
      :handleSave="handleSave"
      :handleCancel="handleCancel"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount, provide, getCurrentInstance } from "vue";
import { useRoute } from "vue-router";
import { getDurgRecord } from "@/api/takeMedicineLog";
import { Toast } from 'vant';
import FilterDate from '@/components/FilterDate.vue'
// import { TakeMedicineLog } from '@/types/myDaily';
import { year, month, today } from '@trialdata/common-fun-css/index';
import MyPopupShow from "@/components/MyPopupShow.vue";
import { getDurgRecordInter, newTakeMedicineLogStateInter } from '@/types/myDaily';

export default defineComponent({
  name: "NewTakeMedicineLog", // 新版-服药日志
  components: {
    FilterDate,
    MyPopupShow
  },
  
  setup() {
    const route = useRoute()
    const proxy: any = getCurrentInstance()?.proxy
    const state: newTakeMedicineLogStateInter = reactive({
      medicationsList: [],
      totalItemCount: 0, // 总条数
      pageIndex: 1,
      pageSize: 20,
      newTakemedicineLogScroll: null,
      dates: {
        dtBegin: '',
        dtEnd: ''
      },
      filterDateMaxDate: new Date(),
      outerHeight: window.outerHeight,
      newTakemedicineLogFilterDateRef: null,
      myPopupShow: false,
      myPopupShowTexts: `<div class="centerflex">请根据实际情况评估是否添加额外记</div><div class="centerflex">录？</div>`,
      myPopupShowThatDayTexts:`您今天还有计划服用日志未填写，请根据实际情况评估是否添加额外记录？`,
      notFilledInOnThatDayFlag: false,
      handleSave: () => {
        state.routerGo('/takeMedicineLogList','')
        state.myPopupShow = false
      },
      handleCancel: () => {
        state.myPopupShow = false
      },
      scrollChage: () => {
        const scroll: any = state.newTakemedicineLogScroll
        if (scroll) {
          const scrollHeight = Math.floor(scroll.scrollHeight - scroll.scrollTop)
          if( scrollHeight === scroll.clientHeight || scrollHeight === scroll.clientHeight + 1 || scrollHeight === scroll.clientHeight - 1) {
            // 触底
            if(state.medicationsList.length < state.totalItemCount){
              state.pageIndex += 1
              state.getMyTakeMedications({pageIndex:state.pageIndex,pageSize:state.pageSize,...state.dates})
            }
          }
        }
      },
      /* 删除按钮*/
      /*deleteTakeMedicineLog: (item) => {
        Dialog.confirm({
          title: "温馨提示",
          message: "您确定要删除此日志吗?",
          // confirmButtonColor: "#5860DA",
        })
          .then(() => {
            // deleteMedication(item.id).then(() => {
            //   Notify({ type: "success", message: `删除${item.title?item.title:''}成功` });
            //   state.getMyTakeMedications({pageIndex:0,pageSize:20,...state.dates})
            // })            
          })
          .catch(() => {
            return;
          });
      },*/
      // 获取服药日志数据--
      getMyTakeMedications: (params) => {
        Toast.loading({
          duration: 300000,
          message: '加载中...',
          forbidClick: true,
        });
        getDurgRecord(params)
        .then((rest) => {
          const res = rest as getDurgRecordInter
          if (res?.items) {
            res.items.forEach((el) => {
              if (el?.drugTime) {
                const dateObj = new Date(el.drugTime);
                const yeardrugTime = dateObj.getFullYear();
                const monthdrugTime = dateObj.getMonth() + 1;
                const daydrugTime = dateObj.getDate();
                // console.log(year , yeardrugTime , month ,monthdrugTime , today , daydrugTime)
                if (year === yeardrugTime && month === monthdrugTime && today === daydrugTime) {
                  el.drugActive = true
                  if (el.isFinished === 0) {
                    state.notFilledInOnThatDayFlag = true
                  }
                }
              }
            });
          }
          if (params.pageIndex > 1) {
            state.medicationsList = state.medicationsList.concat(res.items)
          } else {
            state.medicationsList = res.items
          }
          state.totalItemCount = res?.totalItemCount || 0
          Toast.clear()
         }).catch(() => { Toast.clear() })
      },
      // 跳转
      routerGo: (path: string,item) => {
        // 添加
        if (item.unPlannedWindow) {
          // 计划外继续
        } else if ((item?.isFinished === 0 && item?.isReadOnly) || item?.isFinished === 2 || ((!item?.finishedTime || item?.finishedTime === "0001-01-01T00:00:00") && item?.isFinished === 3)) {
          return
        }
        let query = {}
        
        if (path === '/takeMedicineLogList') {
          query = { newTakemedicineLog: '1' }
        }
        // 编辑药物问卷
        else if (item) {
          query = {
            editable: (item.isReadOnly / 1) === 0 ? '1' : '0',
            newTakemedicineLogItem: JSON.stringify(item as string) || null
          }
        }
        proxy.$routerGoFun('routerInnPush', path, query, route.path)
      },
    });
    provide("Dates",state.dates)

    onBeforeMount(() => {
      const { pageIndex, pageSize} = state
      state.getMyTakeMedications({pageIndex,pageSize})  
      setTimeout(() => {
        state.outerHeight = window.outerHeight
      }, 0)
    });

    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang="less">
// 更改时间轴---
.van-steps.scrollnone {
    display: flex;
    margin: 0 0 0.2rem 0;
    :deep(.van-step__circle-container) {
      top: 0.7rem !important;
    }
    :deep(.van-step__line) {
      top: 0.7rem !important;
    }
    :deep(.van-step__title) {
      box-shadow: 0 0.02rem 0.12rem rgba(0, 0, 0, 0.07); // 阴影
    }
    .van-swipe-cell-acitve {
      border: 1px #979BE6 solid;
    }
    :deep(.van-swipe-cell,
    .van-step__title--active) {
        width: 3.2rem;
        min-height: 152px;
        box-sizing: border-box;
        border-radius: 0.07rem;
        background: #fff;
        overflow: hidden;
        .status-module {
          display: flex;
          justify-content: space-between;
          padding: 0.1rem;
          .activeName {
            max-width: 2.5rem;
            margin: 0;
            word-break: break-all;
            word-wrap: break-word;
          }
        }
        h4 {
          color: var(--theme-color);
          font-weight: 300;
          /*强制换行*/
          word-break: break-all;
          word-wrap: break-word;
        }
        .remark-texts {
          padding: 0 0.1rem 0.05rem;
          color: #9A9A9A;
        }
        p {
          color: #333;
          padding: 0 0.1rem 0.1rem;
          margin: 0;
        }
        .my-hr {
          margin: 0 auto;
          height: 0.5px;
          width: calc(100% - 0.2rem);
          background: #E2E2E2;
        }
        // 按钮all
        .status-btns {
          padding: 0.1rem;
          display: flex;
          justify-content: flex-end;
          font-size: 0.1rem;
          .accomplish-btn {
            color: #9A9A9A;
            padding: 0.05rem 0.15rem;
            background: #F8F8F8;
            border: 0.5px #E6E6E6 solid;
            border-radius: 0.02rem;
          }
          .eidt-log-btn {
            color: var(--theme-color);
            padding: 0.05rem 0.1rem;
            background: #F2F3FA;
            border: 0.5px var(--theme-color) solid;
            border-radius: 0.02rem;
            img {
              margin: 0 0 0 0.04rem;
              width: 0.1013rem;
              height: 0.1013rem;
            }
          }
        }
    }

    // 去除边框
    :deep(.van-step--vertical) {
      border: none;
    }
    :deep(.van-step__circle) {
      width: 0.23rem;
      height: 0.23rem;
      transform: scale(0.5);
      background: url(@/assets/baby/dianIcon.svg);
    }
    :deep(.van-step--vertical:not(:last-child)::after) {
      border: none;
    }
    // 展开高亮
    :deep(.van-step__title,.van-swipe-cell__wrapper) {
      min-height: 0.63rem;
    }
}
</style>
