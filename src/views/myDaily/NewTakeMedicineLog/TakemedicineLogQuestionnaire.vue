<template>
  <div class="ft-12-rem">
    <van-nav-bar
      :title="title"
      left-text=""
      left-arrow
      @click-left="routerBack"
    />

    <CommonQuestion
      ref="TakemedicineLogQuestionnaireRef"
      myQuestType="用药记录"
      :postQuestFile="postQuestFile"
      :deleteQuestFile="deleteQuestFile"
      :getQuestView="getDurgView"
      :getQuestData="getDurgData"
      :getTableList="getTableList"
      :deleteTable="deleteTable"
      :putQuestData="putDurgData"
      :questionOnload="questionOnload"
    />
    <!-- 不暂存并退出 -->
    <MyPopupShow
      :myPopupShow="temporaryExitFlag"
      title="提示"
      texts="
      <div class='mb-01rem ft-13-rem'>有未提交的数据，请按需操作。</div>
      <div class='text-red-400 ft-13-rem'>不暂存并退出：将不会暂存未提交的数据<div>
      <div class='text-red-400 ft-13-rem'>暂存并退出：将会暂存未提交的数据3天<div>"
      cancelText="继续填写"
      centerBtnText="不暂存并退出"
      saveText="暂存并退出"
      cancelClass="text-black"
      centerBtnClass="text-black"
      saveClass="text-black"
      :handleSave="handleSave"
      :handleCenterBtn="handleCenterBtn"
      :handleCancel="
        () => {
          temporaryExitFlag = false;
        }
      "
    />
    <MyPopupShow
      :myPopupShow="immediateWithdrawalFlag"
      title="提示"
      texts="
      <div class='mb-01rem ft-13-rem'>有未提交的数据，请确认。</div>
      <div class='text-red-400 ft-13-rem'>确认退出：将不会暂存未提交的数据<div>"
      cancelText="继续填写"
      saveText="确认退出"
      cancelClass="text-black"
      saveClass="text-black"
      :handleSave="routerGo"
      :handleCancel="
        () => {
          immediateWithdrawalFlag = false;
        }
      "
    />
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  nextTick,
  getCurrentInstance,
} from "vue";
import { useRoute } from "vue-router";
// import { TakeMedicineLog } from '@/types/myDaily';
import {
  getDurgView,
  getDurgData,
  putDurgData,
  getCustomDrugQuest,
} from "@/api/takeMedicineLog";
import {
  postQuestFile,
  deleteQuestFile,
  getTableList,
  deleteTable,
  getQuestInfo,
  getQuestUnPlannedWindow,
} from "@/api/questionnaire";
import CommonQuestion from "@/components/CommonQuestion.vue";
import { useStore } from "vuex";
import { commonQuestionnaireInter } from "@/types/welcomeToJoinUs";
import MyPopupShow from "@/components/MyPopupShow.vue";
import { Toast } from "vant";

export default defineComponent({
  name: "TakemedicineLogQuestionnaire", // 用药记录
  components: {
    CommonQuestion,
    MyPopupShow,
  },
  setup() {
    const route = useRoute();
    const store = useStore();
    const proxy: any = getCurrentInstance()?.proxy;
    const state: any = reactive({
      getDurgView,
      getDurgData,
      putDurgData,
      postQuestFile,
      deleteQuestFile,
      getTableList,
      deleteTable,
      TakemedicineLogQuestionnaireRef: null,
      title: "编辑",
      immediateWithdrawalFlag: false,
      editable: false,
      temporaryExitFlag: false,
      handleSave: () => {
        state.TakemedicineLogQuestionnaireRef.saveTemporaryQuestionnaire();
        proxy.$routerBackFun();
      },
      handleCenterBtn: async () => {
        try {
          Toast.loading({
            duration: 300000,
            message: "加载中...",
            forbidClick: true,
          });
          await state.TakemedicineLogQuestionnaireRef.clearStagingFun();
          proxy.$routerBackFun();
          Toast.clear();
        } catch {
          proxy.$routerBackFun();
          Toast.clear();
        }
      },
      routerBack: () => {
        // 未提交过的： 继续填写 不暂存并退出 暂存并退出。（要值更新过 才弹窗）
        // if (
        //   state.editable &&
        //   (state.TakemedicineLogQuestionnaireRef?.initialQuestionObj?.isStaging ||
        //     state.TakemedicineLogQuestionnaireRef?.rowChange ||
        //     state.TakemedicineLogQuestionnaireRef?.initialIsChange ||
        //     store.state.initialAllListArr?.some(item => item.children.some(el => el?.isChange))) &&
        //   !state.TakemedicineLogQuestionnaireRef?.initialQuestionObj?.isSubmit
        // ) {
        //   state.temporaryExitFlag = true;
        //   return;
        // } else
        const questionItems =
          state.TakemedicineLogQuestionnaireRef?.questionObj.questCrfItemes ||
          [];
        const allListItems = store.state.initialAllListArr;

        const hasMatchingDctCode = questionItems.some(
          (questionItem) =>
            questionItem.crfFieldType === 3 &&
            questionItem.refTypesShow !== 2 &&
            questionItem?.refTypeShow !== 2 &&
            allListItems.some(
              (allListItem) =>
                allListItem.dctCode === questionItem.dctCode &&
                allListItem.children.some((childItem) => childItem?.isChange)
            )
        );
        if (
          state.editable &&
          state.TakemedicineLogQuestionnaireRef?.initialQuestionObj?.isSubmit &&
          (state.TakemedicineLogQuestionnaireRef?.initialIsChange ||
            state.TakemedicineLogQuestionnaireRef?.rowChange ||
            hasMatchingDctCode)
        ) {
          // 提交过的： 继续填写 确认退出 （一定弹出）
          state.immediateWithdrawalFlag = true;
          return;
        }
        proxy.$routerBackFun();
      },
      routerGo: () => {
        const routerInnBack = {
          ...store.state.routerInn[store.state.routerInn.length - 1],
        };
        if (routerInnBack.backPath === "/newTakemedicineLog") {
          proxy.$routerBackFun();
        } else if (store.state.routerInn?.length > 1) {
          proxy.$routerBackFun(2);
        } else {
          proxy.$routerBackFun();
        }
      },
      // 问卷首次加载
      questionOnload: () => {
        nextTick(async () => {
          if (
            sessionStorage.getItem("patientToken") ||
            store?.state?.patientToken
          ) {
            if (state?.TakemedicineLogQuestionnaireRef) {
              const QuestionnaireRef: any =
                state.TakemedicineLogQuestionnaireRef as commonQuestionnaireInter;
              QuestionnaireRef.editable =
                route?.query?.editable === "1" ? "1" : "0";
              // state.editable = route?.query?.editable === '1'
              state.title = route?.query?.editable === "1" ? "编辑" : "查看";
              let newTakemedicineLogItem;
              if (route.query.newTakemedicineLogItem) {
                newTakemedicineLogItem = JSON.parse(
                  route.query.newTakemedicineLogItem as string
                );
                if (
                  sessionStorage.getItem("patientToken") ||
                  store?.state?.patientToken
                ) {
                  const res: any = await getQuestInfo(
                    newTakemedicineLogItem?.patientQuestId
                  );
                  state.editable = res?.isEditable;
                  QuestionnaireRef.editable = res?.isEditable ? "1" : "0";
                  const unPlannedRes: any = await getQuestUnPlannedWindow(
                    newTakemedicineLogItem?.patientQuestId || route.query.questId
                  );
                  if (
                    unPlannedRes?.id &&
                    !(
                      store.state?.getOldQuestDataFlag &&
                      store.state?.oldQuestData
                    )
                  ) {
                    if (
                      !unPlannedRes?.isFeedBack ||
                      (unPlannedRes?.isFeedBack && unPlannedRes?.isAgree)
                    ) {
                      QuestionnaireRef.UnplannedModificationRef.unplannedModificationModuleFlag =
                        true;
                      QuestionnaireRef.UnplannedModificationRef.unplannedModificationModuleObj =
                        unPlannedRes;
                    }
                  }
                }
              }
              if (
                store.state?.getOldQuestDataFlag &&
                store.state?.oldQuestData
              ) {
                QuestionnaireRef.questionObj = store.state.oldQuestData;
                if (
                  store?.state?.oldQuestDataIndex &&
                  QuestionnaireRef.questionObj?.questCrfType === 2
                ) {
                  QuestionnaireRef.questionIndex =
                    store.state.oldQuestDataIndex;
                }
                QuestionnaireRef.questionObjLength =
                  store.state.oldQuestData.questCrfItemes.length;
                QuestionnaireRef.fzFun(
                  QuestionnaireRef.questionObj,
                  store.state.initialQuestionObj
                );
                QuestionnaireRef.upListData();
                QuestionnaireRef.isChange = store.state?.mainQuestIsChange;
                store.dispatch("setGetOldQuestDataFlag", 0);
              } else if (
                newTakemedicineLogItem &&
                (sessionStorage.getItem("patientToken") ||
                  store?.state?.patientToken)
              ) {
                const params = {
                  // questTemplateId是否是添加，自己添加则要传问卷模板Id
                  questTemplateId: newTakemedicineLogItem.questTemplateId,
                };
                await QuestionnaireRef.clearStagingFun(
                  newTakemedicineLogItem.patientQuestId
                );
                QuestionnaireRef.getQuestionnaiData(
                  newTakemedicineLogItem.patientQuestId,
                  params
                );
              } else if (route.query?.addTakemedicineLogItem) {
                const addTakemedicineLogItem = JSON.parse(
                  route.query.addTakemedicineLogItem as string
                );
                const params = {
                  // questTemplateId是否是添加，自己添加则要传问卷模板Id
                  // noPatQuestId
                  questTemplateId: addTakemedicineLogItem.questTemplateId,
                };
                const res = await getCustomDrugQuest(
                  addTakemedicineLogItem.questTemplateId,
                  store.state.userInformation.dctPatientId
                );
                if (res) {
                  QuestionnaireRef.getQuestionnaiData(res, params);
                } else {
                  Toast("获取问卷失败");
                }
              }
            }
          } else {
            setTimeout(() => {
              state.questionOnload();
            }, 500);
          }
        });
      },
    });

    return {
      ...toRefs(state),
    };
  },
});
</script>
