<template>
  <div class="placeanorder-container">
    <van-nav-bar
      title="下单寄送"
      left-text=""
      left-arrow
      @click-left="routerBackFun"
    />
    <div class="placeanorder-body scrollnone">
      <div class="placeanorder-module">
        <van-field
          v-model="placeAnOrderObj.goodsTypeName"
          is-link
          readonly
          input-align="right"
          label="物品信息"
          placeholder="请选择物品信息"
          @click="showItemInfoFun"
        />
        <van-popup v-model:show="showItemInfo" round position="bottom">
          <!-- value-key="goodsTypeName" -->
          <van-picker
            title="选择物品信息"
            :columns="itemInfoColumns"
            :columns-field-names="columnsFieldNames"
            :default-index="defaultItemInfoIndex"
            @confirm="onConfirmItemInfo"
            @cancel="showItemInfo = false"
          />
        </van-popup>
      </div>
      <!-- 寄件人信息 -->
      <div class="placeanorder-module">
        <div class="placeanorder-title justify-between"
        @click="routerGo('/my/editaddress')">
          <div class="centerflex-h">
            <img src="@/assets/baby/sendIcon.svg" alt="">
            <h4>寄件人信息</h4>
          </div>
          <i class="van-badge__wrapper van-icon van-icon-arrow van-cell__right-icon"/>
        </div>
        <van-cell-group inset>
          <van-field v-model="placeAnOrderObj.shipperName" readonly input-align="right" label="联系人" placeholder="" />
        </van-cell-group>
        <van-divider />
        <van-cell-group inset>
          <van-field v-model="placeAnOrderObj.shipperPhoneNo" readonly input-align="right" label="联系电话" placeholder="" />
        </van-cell-group>
        <van-divider />
        <!-- 地区 -->
          <van-field
            v-model="placeAnOrderObj.cityValue"
            input-align="right"
            readonly
            label="所在地区"
            placeholder=""
          />
          <!-- 详细地址 -->
          <van-cell-group inset>
            <van-field
              input-align="right"
              readonly
              v-model="placeAnOrderObj.shipperAddressDetail"
              name="详细地址"
              label="详细地址"
              type="textarea"
              autosize
              placeholder=""
            />
          </van-cell-group>
          <van-divider />
      </div>
      <!-- 收件人信息 -->
      <div class="placeanorder-module">
        <div class="placeanorder-title">
          <img src="@/assets/baby/collectIcon.svg" alt="">
          <h4>收件人信息</h4>
        </div>
        <van-cell-group inset>
          <van-field v-model="placeAnOrderObj.rcptName" readonly input-align="right" label="联系人" />
        </van-cell-group>
        <van-divider />
        <van-cell-group inset>
          <van-field v-model="placeAnOrderObj.rcptPhoneNo" readonly input-align="right" label="联系电话" />
        </van-cell-group>
        <van-divider />
        <!-- 地区 is-link箭头是否需要 @click="showCitys = true"-->
        <van-field
          v-model="placeAnOrderObj.cityCollectValue"
          input-align="right"
          readonly
          label="所在地区"
          placeholder=""
        />
        <van-popup v-model:show="showCitys" round position="bottom">
          <van-cascader
            v-model="cascaderValue"
            title="请选择所在地区"
            :options="cityOptions"
            @close="showCitys = false"
            @finish="onFinishCollectForm"
          />
        </van-popup>
        <!-- 详细地址 -->
        <van-cell-group inset>
          <van-field
            input-align="right"
            readonly
            v-model="placeAnOrderObj.rcptAddressDetail"
            name="详细地址"
            label="详细地址"
            placeholder=""
            type="textarea"
            autosize
          />
        </van-cell-group>
        <van-divider />
      </div>
      <!-- 寄件方式 -->
      <div class="placeanorder-module">
        <van-cell-group inset>
          <van-field v-model="placeAnOrderObj.shippingMethod" readonly input-align="right" label="寄件方式" placeholder="" />
        </van-cell-group>
        <van-divider />
        <van-cell-group inset>
          <van-field
            v-model="placeAnOrderObj.expectPickupTimeDisplay"
            is-link
            readonly
            input-align="right"
            label="期望上门时间"
            placeholder="请选择"
            @click="showMyDatePopupFun"
          />
        </van-cell-group>
        <!-- 日期 时间-区间控件 -->
        <MyDateTimeHourPopup
        ref="placeanorderDateTimeRef"
        :currentDateTime="placeAnOrderObj.expectPickupTimeDisplay"
        :getDateTimeValueFun="getDateTimeValueFun"/>
        <van-divider />
        <div class="">
          <div class="ml-16-px my-10-px">备注</div>
          <van-field
            v-model="placeAnOrderObj.remark"
            rows="2"
            maxlength="20"
            autosize
            placeholder="请输入"
            show-word-limit
          />
        </div>
        <van-divider style="margin: 0" />
      </div>
      <!-- 底部按钮 -->
      <div class="bottom-save-btn"
      :style="{'justify-content': !waybillAgreementShow ? 'end' : ''}">
          <div v-if="waybillAgreementShow" class="consent"
          >
            <img v-show="showCheckedIcon" src="@/assets/baby/checkedIcon.svg" alt=""
            @click="showCheckedIcon = false">
            <img v-show="!showCheckedIcon" src="@/assets/baby/noCheckedIcon.svg" alt=""
            @click="showCheckedIcon = true">
            <div>同意</div>
            <p @click="routerGo('/waybillAgreement')">《快件运单协议》</p>
          </div>
            <van-button :loading="loadingBtn" loading-text="提交中..." class="placeanorder-save-btn" round type="primary" @click="savePlaceAnOrderForm"
        >提交</van-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount, getCurrentInstance } from 'vue';
import { useRoute } from 'vue-router';
import { Toast, Notify } from 'vant';
import MyDateTimeHourPopup from '@/components/MyDateTimeHourPopup.vue'
import { useStore } from 'vuex';
import { putLogisticsOrder, postLogisticsOrder } from '@/api/myOrder';
import { getMyAddress } from '@/api/my'
import { getLogisticsOrder, getGoodsType } from '@/api/myOrder';
import { getTaskSubTasksDetails } from '@/api/tasks';
import { deepClone } from '@trialdata/common-fun-css/index';
import { getLogisticsProviderAndArgument } from '@/api/myOrder';
import { getMyAddressInter } from '@/types/myDaily';

export default defineComponent({
  name: "PlaceAnOrder", // 下单寄送
  components: {
    MyDateTimeHourPopup
  },
  setup() {
    const proxy: any = getCurrentInstance()?.proxy
    const route = useRoute()
    const store = useStore()
    const state = reactive({
      placeAnOrderObj: {
        channelName: '',
        cityValue: '',
        cityCollectValue: '',
        patientId: '',
        dctStudyId: '',
        dctSiteId: '',
        dctStudyVersionNumber: '',
        omsProjectId: '',
        goodsTypeName: '',
        logisticsProvider: '顺丰速运',
        // 寄
        shipperName: '',
        shipperPhoneNo: '',
        shipperProvinceCode: '',
        shipperProvinceName: '',
        shipperCityCode: '',
        shipperCityName: '',
        shipperAreaCode: '',
        shipperAreaName: '',
        shipperAddressDetail: '',
        // 收
        rcptName: '',
        rcptPhoneNo: '',
        rcptProvinceCode: '',
        rcptProvinceName: '',
        rcptCityCode: '',
        rcptCityName: '',
        rcptAreaCode: '',
        rcptAreaName: '',
        rcptAddressDetail: '',
        shippingMethod: '上门取件',
        expectPickupTime: '',
        expectPickupTimeDisplay: '',
        remark: '',
        customSubTaskId: '',
      },
      loadingBtn: false,
      showCitys: false,
      // 勾选同意
      showCheckedIcon: false,
      // 显示隐藏选择物品信息
      showItemInfo: false,
      cascaderValue: "", // 地区号:330100
      cityOptions: [
        // {
        //   text: "浙江省",
        //   value: "330000",
        //   children: [{ text: "杭州市", value: "330100" }],
        // }
      ],
      // 日期相关控件
      placeanorderDateTimeRef: null,
      // 物品信息list
      itemInfoColumns: [],
      // 自定义选择物品
      columnsFieldNames: {text: 'goodsTypeName'},
      // 默认选中的是
      defaultItemInfoIndex: 0,
      subTasksDetailsObj: {},
      setDefaultItemInfoIndexFun: () => {
        if (state?.placeAnOrderObj?.goodsTypeName && state?.itemInfoColumns?.length) {
          state.itemInfoColumns.forEach((el: any,idx) => {
            if (el.goodsTypeName === state.placeAnOrderObj.goodsTypeName) {
              state.defaultItemInfoIndex = idx
            }
          });
        }
      },
      waybillAgreementShow: 0,
      //
      showItemInfoFun: () => {
        if (route.query?.taskId) {
          state.showItemInfo = true
        }
      },
      // 确认物品信息
      onConfirmItemInfo: (e) => {
        // e.siteLogisticsConfigs // siteShipperAddress patientShipperAddress
        state.placeAnOrderObj.goodsTypeName = e.goodsTypeName
        state.showItemInfo = false
        state.setDefaultItemInfoIndexFun()
        state.setRcptFun(e.siteLogisticsConfig)
        state.getLogisticsProviderAndArgumentFun(state.placeAnOrderObj.goodsTypeName,e?.siteLogisticsConfig?.channelName || '')
      },
      // 显示日期时间段控件
      showMyDatePopupFun: () => {
        const placeanorderDateTimeRef: any = state.placeanorderDateTimeRef
        placeanorderDateTimeRef.showDatetimePickerFun()
      },
      getDateTimeValueFun: (res) => {
        state.placeAnOrderObj.expectPickupTimeDisplay = res
      },
      // 城市选择事件
      // onFinishSend: ({ selectedOptions }) => {
      //   state.placeAnOrderObj.cityValue = selectedOptions
      //     .map((option) => option.text)
      //     .join(" ");
      // },
      // 城市全name
      onFinishCollectForm: ({ selectedOptions }) => {
        state.showCitys = false;
        state.placeAnOrderObj.cityCollectValue = selectedOptions
          .map((option) => option.text)
          .join(" ");
      },
      // 提交订单
      savePlaceAnOrderForm: () => {
        if (!state.showCheckedIcon && state.waybillAgreementShow > 0) {
          Toast('请勾选同意《快件运单协议》')
          return
        }
        state.loadingBtn = true
        const savePlaceAnOrderFun = route.query?.taskId ? postLogisticsOrder : putLogisticsOrder
        let params = {
          expectPickupTime: '',
          expectPickupTimeDisplay: '',
        }
        if (route.query?.taskId) {
          params = deepClone(state.placeAnOrderObj)
          params.expectPickupTime = params.expectPickupTimeDisplay
          // state.subTasksDetailsObj.forEach((el) => {
          //   if (el.goodsTypeName === params.goodsTypeName) {
          //     params.customSubTaskId = el.id
          //     return
          //   }
          // });
          // console.log(params,state.subTasksDetailsObj)
          // return
        } else {
          params = deepClone(state.placeAnOrderObj)
        }
        // console.log(params)
        // return
        savePlaceAnOrderFun(params)
        .then(() => {
          Notify({ type: 'success', message: '保存成功' })
          state.loadingBtn = false
          if (route.query?.taskId) {
            // 新增提交后 - 从我的订单 - 回到任务详情
            state.routerGo('/my/myorder')
            return
          }
          proxy.$routerBackFun()
        }).catch(() => {state.loadingBtn = false})
      },
      // 存储上门取件时间及备注
      setStorePlaceAnOrderObjFun: (res,showCheckedIcon) => {
        const storePlaceAnOrderObj = {
          expectPickupTimeDisplay: res?.expectPickupTimeDisplay || '',
          remark: res?.remark || '',
          showCheckedIcon: showCheckedIcon
        }
        store.dispatch('setStorePlaceAnOrderObj',storePlaceAnOrderObj)
      },
      setPlaceAnOrderObjFun: () => {
        if (store.state?.storePlaceAnOrderObj?.expectPickupTimeDisplay) {
          state.placeAnOrderObj.expectPickupTimeDisplay = store.state.storePlaceAnOrderObj.expectPickupTimeDisplay
        }
        if (store.state?.storePlaceAnOrderObj?.remark) {
          state.placeAnOrderObj.remark = store.state.storePlaceAnOrderObj.remark
        }
        if (store.state?.storePlaceAnOrderObj?.showCheckedIcon) {
          state.showCheckedIcon = store.state.storePlaceAnOrderObj.showCheckedIcon
        }
      },
      routerBackFun: () => {
        state.setStorePlaceAnOrderObjFun(null,false)
        proxy.$routerBackFun()
      },
      routerGo: (path: string) => {
        let query = {}
        state.setStorePlaceAnOrderObjFun(null,false)
        if (route.query?.taskId && path === '/my/myorder') {
          query = {
            taskId: route.query.taskId
          }
        } else if (path === '/waybillAgreement') {
          state.setStorePlaceAnOrderObjFun(state.placeAnOrderObj,state.showCheckedIcon)
          query = {
            goodTypeName: state.placeAnOrderObj.goodsTypeName,
            channelName: state.placeAnOrderObj.channelName
          }
        }
        proxy.$routerGoFun('routerInnPush', path, query, route.path, route.query)
      },
      onLoad: async () => {
        const itemInfoColumnsLIst: any = await getGoodsType()
        state.itemInfoColumns = itemInfoColumnsLIst
        if (route.query?.orderItem) {
          const params = JSON.parse(route.query.orderItem as string)
          if (params?.id) {
            getLogisticsOrder(params.id)
              .then((rest: any) => {
                if (rest.shippingMethod !== '上门取件') {
                  rest.shippingMethod = '上门取件'
                }
                state.placeAnOrderObj = rest
                state.setPlaceAnOrderObjFun()
                state.placeAnOrderObj.cityCollectValue = `${rest?.rcptProvinceName || ''} ${rest?.rcptCityName || ''} ${rest?.rcptAreaName || ''}`
                state.getMyAddressFun()
                if (state.placeAnOrderObj?.goodsTypeName && state.placeAnOrderObj?.channelName)
                state.getLogisticsProviderAndArgumentFun(state.placeAnOrderObj.goodsTypeName,state.placeAnOrderObj.channelName)
              })
          }
        } else if (route.query?.taskId) {
          state.getMyAddressFun()
          getTaskSubTasksDetails(route.query.taskId)
            .then((res: any) => {
              state.subTasksDetailsObj = res
              state.setPlaceAnOrderObjFun()
              const itemInfoColumnsArr: any = []
              if (res?.length) {
                res.forEach((el) => {
                  el.specificItemList.forEach((item) => {
                    state.itemInfoColumns.forEach((allItem: any) => {
                      if (item === allItem.id) {
                        itemInfoColumnsArr.push(allItem)
                        state.placeAnOrderObj.goodsTypeName = allItem.goodsTypeName
                        state.placeAnOrderObj.customSubTaskId = el.id
                        state.setDefaultItemInfoIndexFun()
                        if (allItem?.siteLogisticsConfig) {
                          state.setRcptFun(allItem.siteLogisticsConfig)
                          state.getLogisticsProviderAndArgumentFun(state.placeAnOrderObj.goodsTypeName,allItem.siteLogisticsConfig.channelName)
                        }
                        return
                      }
                    })
                  })
                })
                state.itemInfoColumns = itemInfoColumnsArr
              }
            })
        }
      },
      // 获取物流供应商协议
      getLogisticsProviderAndArgumentFun: (goodTypeName,channelName) => {
        state.placeAnOrderObj.channelName = channelName
        getLogisticsProviderAndArgument({goodTypeName,channelName})
          .then((res: any) => {
            if (res?.length) {
              state.waybillAgreementShow = res[0].isShow
              state.placeAnOrderObj.logisticsProvider = res[0].logisticsProviderName
            }
          })
      },
      // 设置收件人信息
      setRcptFun: (rest) => {
        if (rest?.patientShipperAddress) {
          const res = rest.patientShipperAddress
          state.placeAnOrderObj.rcptName = res.name
          state.placeAnOrderObj.rcptPhoneNo = res.mobile
          state.placeAnOrderObj.rcptProvinceName = res.province
          state.placeAnOrderObj.rcptCityName = res.city
          state.placeAnOrderObj.rcptAreaName = res.area
          state.placeAnOrderObj.rcptProvinceCode = res.provinceCode
          state.placeAnOrderObj.rcptCityCode = res.cityCode
          state.placeAnOrderObj.rcptAreaCode = res.areaCode
          state.placeAnOrderObj.rcptAddressDetail = res.addressDetail
          state.placeAnOrderObj.cityCollectValue = `${res?.province || ''} ${res?.city || ''} ${res?.area || ''}`
        } else {
          state.placeAnOrderObj.rcptName = ''
          state.placeAnOrderObj.rcptPhoneNo = ''
          state.placeAnOrderObj.rcptProvinceName = ''
          state.placeAnOrderObj.rcptCityName = ''
          state.placeAnOrderObj.rcptAreaName = ''
          state.placeAnOrderObj.rcptProvinceCode = ''
          state.placeAnOrderObj.rcptCityCode = ''
          state.placeAnOrderObj.rcptAreaCode = ''
          state.placeAnOrderObj.rcptAddressDetail = ''
          state.placeAnOrderObj.cityCollectValue = ''
        }
      },
      // 获取寄件人信息
      getMyAddressFun: () => {
        getMyAddress()
          .then((rest) => {
            if (Array.isArray(rest) && rest?.length) {
              const res = rest[0] as getMyAddressInter
              state.placeAnOrderObj.shipperName = res.name
              state.placeAnOrderObj.shipperPhoneNo = res.mobile
              state.placeAnOrderObj.shipperProvinceName = res.province
              state.placeAnOrderObj.shipperCityName = res.city
              state.placeAnOrderObj.shipperAreaName = res.area
              state.placeAnOrderObj.shipperProvinceCode = res.provinceCode
              state.placeAnOrderObj.shipperCityCode = res.cityCode
              state.placeAnOrderObj.shipperAreaCode = res.areaCode
              state.placeAnOrderObj.shipperAddressDetail = res.addressDetail
              state.placeAnOrderObj.cityValue = `${res?.province || ''} ${res?.city || ''} ${res?.area || ''}`
            }
            // console.log(state.placeAnOrderObj)
            state.setDefaultItemInfoIndexFun()
          })
      }
    });
    onBeforeMount(() => {
      state.onLoad()
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.placeanorder-container {
  height: 100vh;
  overflow: hidden;
  background: #f0f0f0;
  color: #555;
  font-size: 0.14rem;
  .placeanorder-body {
    width: 100%;
    height: 86vh;
    overflow: auto;
    padding: 0.1rem;
    margin: 0.1rem 0 0 0;
    box-sizing: border-box;
    // 底部 按钮
    .bottom-save-btn {
      width: 100%;
      height: 0.6rem;
      padding: 0 0.15rem;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #fff;
      position: fixed;
      left: 0;
      bottom: 0;
      .consent{
        display: flex;
        align-items: center;
        img{
          width: 0.18rem;
          height: 0.18rem;
          margin: 0 0.1rem 0 0;
        }
        p{
          color: var(--theme-color);
        }
      }
      .placeanorder-save-btn{
        width: 1.09rem;
        height: 0.35rem;
      }
    }
  }
}
</style>
