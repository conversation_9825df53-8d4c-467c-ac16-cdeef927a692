<template>
  <div class="drugCombination-container">
    <van-nav-bar
      title="其它药物"
      left-text=""
      left-arrow
      @click-left="$routerBackFun()"
    />
    <div
      ref="drugCombinationScroll"
      class="drugCombination-body scrollnone"
      @scroll="scrollChage"
    >
      <!-- 轴式 -->
      <van-steps class="scrollnone" direction="vertical" :active="-1">
      <van-step
        v-for="(item, index) in CMsList"
        :key="item.rowId"
        @click="routerGo('/editdiscomfort',item)"
        :class="{ 'active-open-back': item.activeOpen }"
      >
        <van-swipe-cell>
          <div class="active-back py-[0.1rem]">
            <div class="status-module !pb-[0.04rem]">
              <h4 class="activeName">{{ item.title }}</h4>
            </div>
            <p>{{ item.happenDate }}</p>
            <div
              v-if="item?.toBeClarified"
              class="absolute centerflex ft-10-rem"
              style="top: 0px;
              left: 0;
              width: 0.48rem;
              color: #F19980;
              background-color: #FFEBE6;
              border-radius: 0.06rem 0 0.06rem 0;"
            >待澄清</div>
          </div>
          <!-- <template #right>
            <van-button
              class="h-full"
              type="danger"
              text="删除"
              @click="deleteDrugCombination(item)"
            />
          </template> -->
        </van-swipe-cell>
        <!-- 展开项 -->
        <!-- <div v-if="item.activeOpen && item.taskList" class="taskList-container">
          <div
            v-for="(taskItem, taskIndexs) in item.taskList"
            :key="taskIndexs"
            class="taskList-module"
          >
            <div class="taskList-fieldValue"
            @click.stop="routerGo('/editdiscomfort',item)">
              <div v-if="taskItem.fieldLabel" v-html="taskItem.fieldLabel" class="taskList-fieldValue-first"/>
              <div class="taskList-fieldValue-last">
                {{ taskItem?.fieldValueStr || '' }}
                {{ taskItem?.dctQuestUnit || ''}}
              </div>
            </div>              
          </div>
        </div> -->
      </van-step>
      </van-steps>

      <van-button
        class="drug-combination-add-btn"
        round
        type="primary"
        @click="routerGo('/editdiscomfort')"
      >
        +添加记录
      </van-button>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount, getCurrentInstance } from "vue";
import { useRoute } from "vue-router";
import { getCMs, deleteCM } from '@/api/drugCombination'
import { Dialog, Notify } from 'vant'
import { getCMsInter, DrugCombination } from "@/types/myDaily";

export default defineComponent({
  name: "DrugCombination", // 其它药物
  setup() {
    const route = useRoute()
    const proxy: any = getCurrentInstance()?.proxy
    const state: DrugCombination = reactive({
      CMsList: [],
      dates: {
        dtBegin: '',
        dtEnd: '',
        pageIndex: 1,
        pageSize: 20,
      },
      totalItemCount: 0,
      drugCombinationScroll: null,
      scrollChage: () => {
        const scroll = state.drugCombinationScroll
        if (scroll.scrollHeight - scroll.scrollTop === scroll.clientHeight) {
          // 触底
          if(state.CMsList.length < state.totalItemCount){
            state.dates.pageIndex += 1
            state.getCMsDatas({...state.dates})
          }
        } 
      },
      getCMsDatas: (params) => {
        getCMs(params).then((rest) => {
          const res = rest as getCMsInter
          if (params.pageIndex > 1) {
            state.CMsList = state.CMsList.concat(res.items)
            state.totalItemCount = res.totalItemCount
          } else {
            state.CMsList = res.items || []
            state.totalItemCount = res.totalItemCount
          }
          // state.CMsList.forEach(async (item) => {
          //   // 默认进来进行中的高亮且展开
          //   if (item.LogStatus === 3) {
          //     item.activeOpen = true;
          //   } else {
          //     item.activeOpen = false;
          //   }      
          //   if (!item?.taskList) {
          //   // 获取列表信息
          //   getViewData(item.rowId,{searchType:2})
          //     .then((rest) => {
          //       const res = rest as getDiscomfortViewDataInter
          //       item.taskList = res?.aecmItems || [];
          //     })
          //     // 获取某个其它药物
          //   }         
          // });
          /*
          rowId: "1"
          cmLogStatus: 2
          happenDate: "2021-07-08"
          title: "合并用药(弥可保)"
          */
        })
      },
      // 点击高亮展开
      // showOpenItem: (index) => {
      //   state.CMsList.forEach((item, indexs) => {
      //     if (index === indexs) {
      //       item.activeOpen = !item.activeOpen;
      //     } else {
      //       item.activeOpen = false;
      //     }
      //   });
      // },
      // 删除按钮
      deleteDrugCombination: (item) => {
        Dialog.confirm({
          title: "温馨提示",
          message: "您确定要删除此药物吗?",
        })
          .then(() => {
            deleteCM(item.rowId).then(() => {
              Notify({ type: "success", message: `删除${item.title?item.title:''}成功` });
              state.getCMsDatas({...state.dates})// 重新加载
            })            
          })
          .catch(() => {
            return;
          });
      },
      routerGo: (path,item) => {
        const query = {
          CMsItem: JSON.stringify(item as string) || null,
          myTitle: '其它药物'
        }
        proxy.$routerGoFun('routerInnPush', path, query, route.path)
      },
    });

    onBeforeMount(() => {
      state.getCMsDatas({...state.dates})
    });

    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
@import "@/style/steps.less";
.drugCombination-container {
  height: 100vh;
  overflow: hidden;
  color: #555;
  font-size: 0.14rem;
  .drugCombination-body {
    width: 100%;
    // height: 73vh;
    height: calc(100vh - 1.3rem);
    margin: 0.1rem 0 0 0;
    box-sizing: border-box;
    overflow: auto;
    //子列表
    .taskList-container {
      box-sizing: border-box;
      padding: 0.1rem 0.1rem 0.2rem 0.1rem;
      .taskList-module {
        width: 100%;
        color: #333;
        font-size: 0.15rem;
        margin: 0;
        margin-top: 0.13rem;
        border-bottom: 1px solid #ededed;
        .taskList-fieldValue {
          width: 100%;
          display: flex;
          flex-wrap: wrap;
          div {
            display: block;
            width: 100%;
            color: #9a9a9a;
            font-size: 0.12rem;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            &:nth-child(2) {
              margin-top: 0.05rem;
              color: #333;
            }
          }
          .taskList-fieldValue-first,
          .taskList-fieldValue-last {
            width: 100%;
            margin: 0 0.06rem 0 0;
          }
        }
      }
    }
    .drug-combination-add-btn {
      width: 90%;
      position: fixed;
      left: 0.2rem;
      bottom: 0.2rem;
    }
  }
}
</style>
