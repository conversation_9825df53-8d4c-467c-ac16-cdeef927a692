<template>
  <div class="myCalendar-container">
    <van-nav-bar
      title="用药日志"
      left-text=""
      left-arrow
      @click-left="$routerBackFun()"
    />
    <!-- 日期筛选 -->
    <div class="filter-date">
      <div>日期筛选</div>
      <div class="start-date">
        <van-field
          v-model.trim="startCurrentDate"
          readonly
          name="datetimePicker"
          placeholder="开始日期"
          @click="pickerDate('start')"
        />
        <van-popup v-model:show="showStartDatas" position="bottom">
          <van-datetime-picker
            v-model="currentDate"
            type="date"
            title="选择日期"
            :min-date="minDate"
            :max-date="maxDate"
            :columns-order="['year', 'month', 'day']"
            @cancel="showStartDatas = false"
            @confirm="confirmValue"
          />
        </van-popup>
        <van-icon name="notes-o" style="margin: 0 0.1rem 0 0" />
      </div>
      <div>－</div>
      <div class="start-date">
        <van-field
          v-model.trim="endCurrentDate"
          readonly
          name="datetimePicker"
          placeholder="结束日期"
          @click="pickerDate('end')"
        />
        <van-popup v-model:show="showEndDatas" position="bottom">
          <van-datetime-picker
            v-model="currentDate"
            type="date"
            title="选择日期"
            :min-date="minDate"
            :max-date="maxDate"
            :columns-order="['year', 'month', 'day']"
            @cancel="showEndDatas = false"
            @confirm="confirmValue"
          />
        </van-popup>
        <van-icon name="notes-o" style="margin: 0 0.1rem 0 0" />
      </div>
    </div>
    <!-- 日历 -->
    <!-- end-calendar-modules -->
    <div
      class="calendar-modules"
      :class="{ 'end-calendar-modules': openEndCalendarFlag }"
      @click="openEndCalendar"
    >
      <div class="clalendar-title">
        <!-- <img src="@/assets/baby/userHeadPortrait.svg" alt=""> -->
        <div class="left-clalendar-title" @click.stop="addSubtract(0)">
          <van-icon style="color: #c3c3c3 !important" name="arrow-left" />
        </div>
        <p>{{ CalendarYear + "年" + CalendarMonth + "月" }}</p>
        <div class="left-clalendar-right" @click.stop="addSubtract(1)">
          <van-icon style="color: #c3c3c3 !important" name="arrow" />
        </div>
        <!-- <p>{{ymDay}}</p> -->
      </div>
      <!-- 日-六 -->
      <div class="calendar-nav">
        <p>日</p>
        <p>一</p>
        <p>二</p>
        <p>三</p>
        <p>四</p>
        <p>五</p>
        <p>六</p>
      </div>
      <!-- 每一天 -->
      <div class="calendar-days">
        <div
          v-for="(item, index) in calendarDaysList"
          :key="index"
          class="calendar-days-item"
        >
          <p>{{ item.days }}</p>
          <div class="days-colors">
            <i v-if="item.myColor" class="days-color-no-completeIcon"></i>
            <i
              v-if="item.myColor1"
              class="days-color-completeIcon"
              :class="{ 'color-move-left': item.myColor }"
            ></i>
            <!-- <i
              v-if="item.myColor2"
              class="days-color-blue"
              :class="{
                'color-move-left': item.myColor || item.myColor1,
                'color-move-left2': item.myColor && item.myColor1,
              }"
            ></i> -->
          </div>
        </div>
      </div>
    </div>

    <!-- 任务 -->
    <div class="calendar-task">
      <div class="Calendar-tasks-item1 Calendar-tasks-item-all">
        <div class="Calendar-tasks-item-le">
          <img src="@/assets/baby/drugsNoCompleteIcon.svg" alt="" />
          <div>
            <p>药物名称A</p>
            <p class="Calendar-tasks-item-le-date">2021-01-01</p>
          </div>
        </div>
        <div class="Calendar-tasks-item-ri">
          <p v-if="CalendarList.questionTaskStatus === 1">未完成</p>
          <van-icon v-if="CalendarList.questionTaskStatus !== 4" name="arrow" />
        </div>
      </div>
      <!-- <van-swipe-cell>
        滑动删除放置区域
        <template #right>
          <van-button style="height: 88%" square type="danger" text="删除" />
        </template>
      </van-swipe-cell> -->

      <div class="Calendar-tasks-item2 Calendar-tasks-item-all">
        <div class="Calendar-tasks-item-le">
          <img src="@/assets/baby/drugsCompleteIcon.svg" alt="" />
          <div>
            <p>药物名称B</p>
            <p class="Calendar-tasks-item-le-date">2021-01-01</p>
          </div>
        </div>
        <div class="Calendar-tasks-item-ri">
          <p v-if="CalendarList.pictureTaskStatus === 2">已完成</p>
          <van-icon v-if="CalendarList.pictureTaskStatus !== 4" name="arrow" />
        </div>
      </div>
    </div>

    <van-button
      class="btn"
      round
      type="primary"
      @click="routerGo('/edittakemedicineLog')"
    >
      +添加记录
    </van-button>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount } from "vue";
import { year, month, today } from "@trialdata/common-fun-css/index";
import { useRouter } from "vue-router";
// import { getMedications, deleteMedication } from "@/api/takeMedicineLog";

export default defineComponent({
  name: "MyCalendar",
  setup() {
    const router = useRouter();
    const state = reactive({
      ymDay:
        year +
        "-" +
        (month < 10 ? "0" + month : month) +
        "-" +
        (today < 10 ? "0" + today : today),
      openEndCalendarFlag: false, //默认展开
      CalendarYear: year / 1,
      CalendarMonth: month / 1,
      calendarDaysList: [
        {
          days: 30,
          myColor: true,
          myColor1: true,
          myColor2: true,
        },
        {
          days: 31,
          myColor: false,
          myColor1: true,
          myColor2: true,
        },
        {
          days: 1,
          myColor: true,
          myColor1: false,
          myColor2: true,
        },
        {
          days: 2,
          myColor: true,
          myColor1: true,
          myColor2: false,
        },
        {
          days: 3,
          myColor: true,
          myColor1: false,
          myColor2: false,
        },
        {
          days: 4,
          myColor: false,
          myColor1: false,
          myColor2: true,
        },
        {
          days: 5,
          myColor: false,
          myColor1: true,
          myColor2: false,
        },
        {
          days: 6,
          myColor: true,
          myColor1: true,
          myColor2: true,
        },
        {
          days: 7,
          myColor: true,
          myColor1: true,
          myColor2: true,
        },
        {
          days: 8,
          myColor: true,
          myColor1: true,
          myColor2: true,
        },
        {
          days: 9,
          myColor: true,
          myColor1: true,
          myColor2: true,
        },
        {
          days: 10,
          myColor: true,
          myColor1: true,
          myColor2: true,
        },
        {
          days: 11,
          myColor: true,
          myColor1: true,
          myColor2: true,
        },
        {
          days: 12,
          myColor: true,
          myColor1: true,
          myColor2: true,
        },
        {
          days: 13,
          myColor: true,
          myColor1: true,
          myColor2: true,
        },
      ],
      CalendarList: {
        questionTaskStatus: 1,
        pictureTaskStatus: 2,
      },
      medicationsList: [], //日历获取接口对象
      //日期选择
      currentDate: new Date(), //new Date()
      showStartDatas: false,
      startCurrentDate: "", //开始日期
      showEndDatas: false,
      endCurrentDate: "", //结束日期
      minDate: new Date(year/1 - 100, 12, 31),
      maxDate: new Date(year/1 + 100, 12, 31),
      //日期2021-01-01
      confirmValue: (val) => {
        const year = val.getFullYear();
        let month = val.getMonth() + 1;
        let day = val.getDate();
        let hour = val.getHours();
        let minute = val.getMinutes();
        if (month >= 1 && month <= 9) {
          month = `0${month}`;
        }
        if (day >= 1 && day <= 9) {
          day = `0${day}`;
        }
        if (hour >= 0 && hour <= 9) {
          hour = `0${hour}`;
        }
        if (minute >= 0 && minute <= 9) {
          minute = `0${minute}`;
        }
        let currentDates = state.currentDate;
        currentDates = `${year}-${month}-${day}`;
        // //日期控件时直接赋值
        // const endCurrentDate: any = state.endCurrentDate
        // const startNum: number = state.startCurrentDate.split('-').join('')/1
        // const endNum: number = endCurrentDate.split('-').join('')/1
        if (state.showStartDatas) {
          state.startCurrentDate = currentDates;
          // if(startNum > endNum){
          //   console.log('dale')
          // }
          state.showStartDatas = false;
        } else if(state.showEndDatas){
         state.endCurrentDate= currentDates;
         state.showEndDatas = false;
        }        
      },
      //点击选择日期
      pickerDate: (falg) => {
        if (falg === "start") {
          state.showStartDatas = true;
        }else if(falg === 'end'){
          state.showEndDatas = true;
        }
      },

      routerGo: (path, id) => {
        let query = null
        if (id) {
          query = { id }
        }
        router.replace({
          path,
          query
        });
      },
      //加减日历
      addSubtract: (falg) => {
        if (falg === 1) {
          if (state.CalendarMonth === 12) {
            state.CalendarYear++;
            state.CalendarMonth = 1;
          } else {
            state.CalendarMonth++;
          }
        } else {
          if (state.CalendarMonth === 1) {
            state.CalendarYear--;
            state.CalendarMonth = 12;
          } else {
            state.CalendarMonth--;
          }
        }
      },
      //打开关闭日历
      openEndCalendar: () => {
        state.openEndCalendarFlag = !state.openEndCalendarFlag;
      },
    });

    onBeforeMount(() => {
      //getMedications().then((res: any) => {
        //state.medicationsList = res;
        //console.log(res)//1 = 未完成, 2 = 已完成
      //});
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style lang='less' scoped>
.myCalendar-container {
  height: 100vh;
  font-size: 0.14rem;
  overflow: auto;
  .filter-date {
    padding: 0 0.1rem;
    margin: 0.15rem 0;
    box-sizing: border-box;
    display: flex;
    justify-content: space-around;
    align-items: center;
    .start-date {
      width: 1.3rem;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #f7f7f7;
      border-radius: 0.05rem;
      overflow: hidden;
    }
    .van-cell {
      width: 1.1rem;
      height: 0.3rem;
      background: #f7f7f7;
      border: none;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  /*日历模块*/
  .calendar-modules {
    width: 100%;
    height: 3.89rem;
    margin: 0.1rem 0 0 0;
    padding: 0 0.08rem 0.5rem 0.08rem;
    box-sizing: border-box;
    color: #333;
    overflow: hidden;
    background: url(@/assets/baby/calendarOpen.svg) no-repeat -0.2rem -0.5rem;
    background-size: 110% 120%;
    border-radius: 0 0 0.2rem 0.2rem;
    //box-shadow: 0 0.02rem 0.26rem rgba(0, 0, 0, 0.07);
    .clalendar-title {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 0 0 0.16rem 0;
      padding: 0 0.16rem;
      box-sizing: border-box;
      img {
        width: 0.24rem;
        height: 0.24rem;
        border-radius: 50%;
        margin: 0 0.3rem 0 0;
      }
      p {
        min-width: 1rem;
      }
    }
    /*日-六*/
    .calendar-nav {
      display: flex;
      justify-content: space-between;
      color: #999;
      margin: 0 0 0.1rem 0;
      p {
        width: 13%;
        text-align: center;
      }
    }
    /*每一天*/
    .calendar-days {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      .calendar-days-item {
        width: 13%;
        height: 0.6rem;
        margin: 0 0 0.1rem 0;
        padding: 0.05rem 0 0 0;
        box-sizing: border-box;
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        border-radius: 0.02rem;
        p {
          width: 100%;
          display: flex;
          justify-content: center;
        }
        .days-colors {
          width: 100%;
          display: flex;
          justify-content: center;
          i {
            width: 0.08rem;
            height: 0.08rem;
            border-radius: 50%;
          }
          .days-color-no-completeIcon {
            background: var(--theme-color);
          }
          .days-color-completeIcon {
            background: #3fa1fc;
          }
          // .days-color-blue {
          //   background: #40a3fd;
          // }
          .color-move-left {
            position: relative;
            left: -0.02rem;
          }
          .color-move-left2 {
            left: -0.04rem;
          }
        }
      }
    }
  }
  //关闭日历
  .end-calendar-modules {
    height: 1.4rem;
    background: url(@/assets/baby/calendarEnd.svg) no-repeat -1.45rem -1rem;
    background-size: 180% 200%;
  }
  //任务
  .calendar-task {
    width: 100%;
    color: #fff;
    :deep(.van-icon) {
      color: #fff !important;
      margin: 0 0 0 0.1rem;
    }
    .Calendar-tasks-item-all {
      width: 100%;
      height: 0.8rem;
      padding: 0.1rem;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 0.15rem;
      .Calendar-tasks-item-le {
        // height: 100%;
        display: flex;
        align-items: center;
        margin: 0 0 0.1rem 0;
        .Calendar-tasks-item-le-date {
          font-size: 0.11rem;
          margin: 0.06rem 0 0 0;
        }
        img {
          width: 0.5rem;
          height: 0.5rem;
          margin: 0 0.1rem 0 0;
        }
      }
      .Calendar-tasks-item-ri {
        display: flex;
        align-items: center;
        margin: 0 0.2rem 0.1rem 0;
        img {
          width: 0.8rem;
          height: 0.35rem;
        }
      }
    }
    // .Calendar-tasks-item1 {
    //   background: url(@/assets/baby/homeBack1.svg) no-repeat -0.2rem -0.2rem;
    //   background-size: 110% 150%;
    // }
    // .Calendar-tasks-item2 {
    //   background: url(@/assets/baby/homeBack2.svg) no-repeat -0.2rem -0.2rem;
    //   background-size: 110% 150%;
    // }
    // .Calendar-tasks-item3 {
    //   background: url(@/assets/baby/homeBack3.svg) no-repeat -0.2rem -0.2rem;
    //   background-size: 110% 150%;
    // }
    img {
      width: 100%;
    }
  }
  .btn {
    width: 80%;
    height: 0.39rem;
    margin: 0.2rem 10%;
  }
}
</style>
