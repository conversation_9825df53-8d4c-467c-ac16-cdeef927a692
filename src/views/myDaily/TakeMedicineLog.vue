<template>
  <div class="takemedicineLog-container">
    <van-nav-bar
      title="用药日志"
      left-text=""
      left-arrow
      @click-left="$routerBackFun()"
    />

    <div class="takemedicineLog-form">
      <!-- 日期筛选 -->
      <FilterDate :request="getMyTakeMedications"/>
      <div ref="takemedicineLogScrollRef" class="takemedicineLog-body scrollnone"
      @scroll="scrollChage">      
        <!-- 轴 -->
        <van-steps class="scrollnone" direction="vertical" :active="-1">
          <van-step
            v-for="(item, index) in medicationsList"
            :key="index"
            @click="showOpenItem(index)"
            :class="{ 'active-open-back': item.activeOpen }"
          >
            <van-swipe-cell>
              <div class="active-back">
                <div class="status-module">
                  <h4 class="activeName">{{ item.drugItem?.drugDisplayName ? item.drugItem?.drugDisplayName : ''}} </h4>              
                  <span v-if="item.medicationLogStatus === 2" class="active-status4"
                    >已完成</span
                  >
                  <span v-else class="active-status3"
                    >未完成</span
                  >
                </div>
                <p>{{ item.takenTime }}</p>
              </div>
              <template #right>
                <van-button
                  v-if="item.medicationLogStatus === 2"
                  style="height: 100%"
                  square
                  type="danger"
                  text="删除"
                  @click="deleteTakeMedicineLog(item)"
                />
              </template>
            </van-swipe-cell>

            <div v-if="item.activeOpen" @click.stop="routerGo('/edittakemedicineLog',item)">
                <p class="takenTime">服药日期：{{item.takenTime?item.takenTime:''}}</p>
                <p>服用药物：{{item.drugItem?.drugDisplayName ? item.drugItem?.drugDisplayName : ''}}</p>
                <p v-if="item.takenDose !== 0">服用剂量：{{item.takenDose?item.takenDose:'0'}}{{item.drugItem?.drugDoseUnit?item.drugItem?.drugDoseUnit:''}}</p>
                <p v-else>服用剂量：</p>
                <p>其它说明：{{item.remark?item.remark:''}}</p>
            </div>
          </van-step>
        </van-steps>

        <van-button class="btn" round type="primary" @click="routerGo('/edittakemedicineLog')">
          +添加记录
        </van-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount, provide, getCurrentInstance } from "vue";
import { useRoute } from "vue-router";
import { getMedications, deleteMedication, getDrugs } from "@/api/takeMedicineLog";
// getMedication, getMedicationData
import { Dialog, Notify } from "vant";
import FilterDate from '@/components/FilterDate.vue'
import { TakeMedicineLog, getMedicationsInter } from '@/types/myDaily';

export default defineComponent({
  name: "TakeMedicineLog", // 服药日志
  components: {
    FilterDate,
  },
  
  setup() {
    const route = useRoute()
    const proxy: any = getCurrentInstance()?.proxy
    const state: TakeMedicineLog = reactive({
      medicationsList: [],
      totalItemCount: 0, // 总条数
      pageIndex: 1,
      pageSize: 20,
      takemedicineLogScrollRef: null,
      dates: {
        dtBegin: '',
        dtEnd: ''
      },
      scrollChage: () => {
        if (scroll) {
          const scroll = state.takemedicineLogScrollRef
          if(scroll.scrollHeight - scroll.scrollTop === scroll.clientHeight){
            // 触底
            if(state.medicationsList.length < state.totalItemCount){
              state.pageIndex += 1
              state.getMyTakeMedications({pageIndex:state.pageIndex,pageSize:state.pageSize,...state.dates})
            }
          }
        }
      },
      // 点击高亮展开
      showOpenItem: (index) => {
        state.medicationsList.forEach((item, indexs) => {
          if (index === indexs) {
            item.activeOpen = !item.activeOpen;
          } else {
            item.activeOpen = false;
          }
        });
      },
      // 删除按钮
      deleteTakeMedicineLog: (item) => {
        Dialog.confirm({
          title: "温馨提示",
          message: "您确定要删除此日志吗?",
          // confirmButtonColor: "#5860DA",
        })
          .then(() => {
            deleteMedication(item.id).then(() => {
              Notify({ type: "success", message: `删除${item.title?item.title:''}成功` });
              state.getMyTakeMedications({pageIndex:0,pageSize:20,...state.dates})
            })            
          })
          .catch(() => {
            return;
          });
      },
      // 获取服药日志数据--
      getMyTakeMedications: (params) => {
        getMedications(params).then((rest) => {
          const res = rest as getMedicationsInter
          if (params.pageIndex > 1) {
            state.medicationsList = state.medicationsList.concat(res.items)
          } else {
            state.medicationsList = res.items
          }
          state.totalItemCount = res.totalItemCount
         })
      },
      // 跳转
      routerGo: (path,item) => {
        // 是否选择药品
        let query = {}
        if (item) {
          query = { takemedicineLogItem: JSON.stringify(item as string) || null }
          proxy.$routerGoFun('routerInnPush', path, query, route.path)
        } else {
          getDrugs().then((res) => {
            if(Array.isArray(res) && res.length){
              if (res.length === 1) {
                query = {
                  takemedicineLogItem: null,
                  addTakemedicineLogItem: JSON.stringify(res[0] as string) || null,
                }
              } else {
                path = '/takeMedicineLogList'
                query = { takemedicineLogList: JSON.stringify(res) }
              }
              proxy.$routerGoFun('routerInnPush', path, query, route.path)
            }         
          })
        }
      },
    });
    provide("Dates",state.dates)

    onBeforeMount(() => {
      const { pageIndex, pageSize} = state
      state.getMyTakeMedications({pageIndex,pageSize})  
    });

    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
@import '@/style/steps.less';
.takemedicineLog-container {
  height: 100vh;
  overflow: hidden;
  color: #555;
  font-size: 0.14rem;
  .takemedicineLog-form {
    .takemedicineLog-body {
      width: 100%;
      height: 73vh;
      overflow: auto;
      margin: 0.1rem 0 0 0;
      box-sizing: border-box;
      //子列表
      .taskList-module{
        width: 100%; 
        color: #333;
        font-size: 0.15rem;   
        .taskList-fieldValue{
          display: flex;
          .taskList-fieldValue-first{
            margin: 0 0.06rem 0 0;
          }
        }
      }
      .btn {
        width: 90%;
        position: fixed;
        left: 0.2rem;
        bottom: 0.3rem;
      }
    }
  }
}
</style>
