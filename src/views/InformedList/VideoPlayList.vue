<template>
  <div class=" 
    bg-color-F5F5F5
    h-100-vh
    overflow-hidden
    ft-014-rem"
  >
    <van-nav-bar
      title="视频"
      left-text=""
      left-arrow
      @click-left="$routerBackFun"
    />
    <div :style="{'height': outerHeight ? outerHeight - 46 + 'px' : 'calc(100vh - 46px)'}" class="videoPlayList-box px-016rem box-border py-015rem">
      <div v-for="(item, index) in videoObj.videos" :key="index" @click="playVideo(item)">
        <div class="mb-02rem radius-005rem flex justify-between bg-white items-center px-015rem py-015rem">
          <div class="flex-1 mr-02rem truncate">{{ item?.title || '' }}</div>
          <van-icon name="arrow" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount, getCurrentInstance } from 'vue';
import { useRoute } from 'vue-router';
import { getStudyIntroduceVideoNewStartTime } from '@/api/informed';

export default defineComponent({
  name: 'VideoPlayList', // 视频列表
  setup() {
    const route = useRoute()
    const proxy: any = getCurrentInstance()?.proxy;
    const state = reactive({
      vidTitle: '',
      outerHeight: window.outerHeight,
      videoObj: {
        videos: [],
      },
      playVideo: (item) => {
        const videoItem = {
          videoCoverUrl: item?.videoCoverUrl,
          videoUrl: item?.videoUrl,
        };
        const query = {
          vidTitle: item.title,
          videoItem: JSON.stringify(videoItem),
        };
        proxy.$routerGoFun(
          "routerInnPush",
          '/videoPlay',
          query,
          route.path,
          route.query
        );
      }
    })
    
    onBeforeMount(() => {
      if (route.query?.ICFStatementId) {
        getStudyIntroduceVideoNewStartTime({
          ICFStatementId: route.query?.ICFStatementId
        }).then((res: any) => {
          state.videoObj.videos = res || []
        })
      }
      setTimeout(() => {
        state.outerHeight = window.outerHeight;
      }, 0);
    })

    return {
      ...toRefs(state)
    }
  },
});
</script>

<style lang="less" scope>
.videoPlayList-box {
  height: calc(100vh - 46px);
  overflow: auto;
}
</style>
