<template>
  <div
    class="ft-12-rem font-color-555 h-100-vh overflow-hidden"
    :class="{ 'informedPreview-pdf-container': IcfObj?.studyICFUrl }"
  >
    <van-nav-bar
      title="知情同意书"
      left-arrow
      left-text=""
      @click="$routerBackFun"
    />
    <!-- 知情同意书 -->
    <div
      class="informedPreview-form"
      :class="[!IcfObj?.studyICFUrl ? 'h-100vh-80px' : '']"
    >
      <div
        :class="{
          'informedPreview-pdf-body': IcfObj?.studyICFUrl,
          'h-83-vh': IcfObj?.studyICF,
        }"
      >
        <div
          class="informedPreview-module"
          :class="{ 'informedPreview-pdf-module': IcfObj?.studyICFUrl }"
        >
          <PdfOpen
            v-if="IcfObj?.studyICFUrl"
            ref="pdfIcfRef"
            :myPdfurl="IcfObj.studyICFUrl"
          />
          <div v-else-if="IcfObj?.studyICF" v-html="IcfObj.studyICF" />
          <van-button
            v-if="IcfObj?.studyICFUrl"
            type="primary"
            class="w-full h-055rem fixed"
            style="bottom: 0"
            @click="dFile"
          >
            下载
          </van-button>
        </div>
      </div>
    </div>
  </div>
  <!-- 蒙层 -->
  <div
    v-show="isWeChatFlag"
    @click.stop="
      () => {
        isWeChatFlag = false;
      }
    "
    class="pt-01rem font-bold ft-18-rem text-white fixed h-100vh w-100vw z-9999"
    style="background: rgba(0, 0, 0, 0.6); left: 0; top: 0"
  >
    <div class="mb-03rem pr-02rem flex justify-end">
      <img
        style="width: 1.58rem"
        src="@/assets/baby/InformedList/guidanceIcon.svg"
        alt=""
      />
    </div>
    <div class="centerflex">请点击右上角···</div>
    <div class="centerflex my-01rem">选择“在浏览器打开”</div>
    <div class="centerflex">完成下载</div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onMounted } from 'vue';
import { useRoute } from "vue-router";
import PdfOpen from "@/components/PdfOpen.vue";
import { getStatementInfo } from '@/api/informed';
import { useStore } from "vuex";

export default defineComponent({
  name: "InformedPreview", // 预览知情同意书
  components: {
    PdfOpen,
  },
  setup() {
    const route = useRoute();
    const store = useStore();
    const state = reactive({
      pdfIcfRef: null,
      IcfObj: {
        studyICF: '',
        studyKnowReadingTime: 0,
        isAlreadyCompleteICF: 0,
        studyICFUrl: '',
        goBack: 0,
        explainICFType: 0
      },
      isWeChatFlag: false,
      isWeChatEnv: () => {
        const userAgent = navigator.userAgent.toLowerCase();
        return /micromessenger/.test(userAgent);
      },
      isIOS: () => {
        const userAgent = navigator.userAgent.toLowerCase();
        return /iphone|ipad|ipod/.test(userAgent);
      },
      // 下载
      dFile: () => {
        if (store.state?.appKey?.toLowerCase() === 'app') {
          const msgData = {
            data: {
              action: "pdfDownload",
              payload: {
                url: state.IcfObj.studyICFUrl,
                fileName: `知情同意书${new Date().toISOString().slice(0, 10).replace(/-/g, '')}.pdf`
              }
            }
          };
          // 如果是iOS
          if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.jumpToNative) {
            window.webkit.messageHandlers.jumpToNative.postMessage(msgData);
          }
          // 如果是非iOS环境(即uniapp、安卓)
          uni.postMessage(msgData);
        } else {
          // saveAs(state.IcfObj.studyICFUrl, `知情同意书${new Date}.pdf`);
          if (state.isWeChatEnv() && state.isIOS()) {
            state.isWeChatFlag = true
          } else {
            window.open(state.IcfObj.studyICFUrl)
          }
          // const a = document.createElement('a');
          // a.href = state.IcfObj.studyICFUrl;
          // a.download = '知情同意书' + new Date();
          // document.body.appendChild(a);
          // a.click();
          // document.body.removeChild(a);

          // downloadFile(state.IcfObj.studyICFUrl, `知情同意书${new Date}`)
        }
      }
    });
    onMounted(() => {
      if (route.query?.ICFStatementId) {
        setTimeout(() => {
          getStatementInfo({ ICFStatementId: route.query.ICFStatementId }).then((res) => {
            if (res.icfVersions.length) {
              state.IcfObj = res.icfVersions[0]
              if (route.query?.id) {
                res.icfVersions.map((e) => {
                  if (e.id === route.query.id) {
                    state.IcfObj = e
                  }
                })
              }
            }
          })
        }, 200)
      }
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.informedPreview-form {
  // height: calc(100vh - 80px);
  background: linear-gradient(180deg, #eaeffa 70%, #fff 100%);
}
.informedPreview-module {
  width: 100%;
  height: 100%;
  padding: 0.1rem;
  box-sizing: border-box;
  overflow: auto;
  :deep(ul) {
    list-style-type: disc !important;
    list-style-position: inside !important;
  }
  :deep(ol) {
    list-style-type: decimal !important;
    list-style-position: inside !important;
  }
}
.informedPreview-pdf-container {
  background: #fff;
  :deep(.pdfjs),
  :deep(.pdfViewer) {
    padding: 0;
  }
  .informedPreview-form {
    margin: 0;
    .informedPreview-pdf-body {
      margin: 0;
      .informedPreview-pdf-module {
        margin: 0;
        padding: 0;
        box-shadow: 0 0.02rem 0.12rem rgba(0, 0, 0, 0.07);
        overflow: hidden !important;
        :deep(.pinch-zoom-container) {
          height: calc(100vh - 44px - 0.55rem) !important;
          max-height: calc(100vh - 44px - 0.55rem) !important;
        }
      }
    }
  }
}
:deep(.van-button__text) {
  font-size: 0.13rem;
}
</style>
