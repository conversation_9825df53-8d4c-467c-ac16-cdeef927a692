<template>
  <div class="bg-color-F5F5F5 h-100-vh overflow-hidden ft-014-rem">
    <van-nav-bar title="知情同意书" left-arrow @click-left="$routerBackFun" />
    <div
      :style="{
        height: outerHeight ? outerHeight - 46 + 'px' : 'calc(100vh - 46px)',
      }"
      class="overflow-auto px-016rem box-border py-015rem"
    >
      <div
        v-for="(e, idx) in icfVersions"
        :key="idx"
        class="centerflex-h justify-between bg-color-fff mb-02rem px-01rem py-015rem radius-004rem"
        @click="routerGo(e)"
      >
        <div>
          <div class="mb-01rem wrap1">知情版本：{{ e?.icfVersionNumber }}</div>
          <div>版本日期：{{ e?.icfVersionDate }}</div>
        </div>
        <van-icon class="font-color-666-important" name="arrow" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  onBeforeMount,
  getCurrentInstance,
} from "vue";
import { useRoute } from "vue-router";
import { getStatementInfo } from '@/api/informed';

export default defineComponent({
  name: "IcfList", // 知情同意书列表
  setup() {
    const route = useRoute();
    const proxy: any = getCurrentInstance()?.proxy;
    const state = reactive({
      outerHeight: window.outerHeight,
      icfVersions: [],
      routerGo: (e) => {
        const query = {
          ICFStatementId: route.query?.ICFStatementId,
          id: e.id
        };
        proxy.$routerGoFun(
          "routerInnPush",
          "/informedPreview",
          query,
          route.path,
          route.query
        );
      },
    });

    onBeforeMount(() => {
      if (route.query?.ICFStatementId) {
        getStatementInfo({ 
          ICFStatementId: route.query?.ICFStatementId
        }).then((res) => {
          state.icfVersions = res.icfVersions
        })
      }
      setTimeout(() => {
        state.outerHeight = window.outerHeight;
      }, 0);
    });

    return {
      ...toRefs(state),
    };
  },
});
</script>
