<template>
  <div class="h-100-vh overflow-hidden font-color-333 ft-15-rem bg-color-F5F5F5">
    <van-nav-bar
      title="详情"
      left-arrow
      @click="$routerBackFun"
    />
    <div class="px-01rem py-015rem box-border">
      <div class="bg-color-fff mb-02rem px-01rem py-015rem radius-004rem">
        <div
          v-for="(e, idx) in statementInfoObj.icfVersions"
          :key="idx"
        >
          <div class="mb-01rem truncate">
            知情版本：{{e?.icfVersionNumber}}
          </div>
          <div :class="{'mb-01rem': idx !== statementInfoObj.icfVersions.length - 1}">
            版本日期：{{e?.icfVersionDate}}
          </div>
        </div>
      </div>
      <div v-if="videoItem?.videos?.length" class="centerflex-h justify-between bg-color-fff mb-02rem px-01rem py-015rem radius-004rem"
      @click="routerGo('/informedDetailsVideoPlayList', '视频')">
        <div>视频</div>
        <van-icon class="font-color-666-important" name="arrow" />
      </div>
      <div
        v-if="icfObj?.length"
        class="centerflex-h justify-between bg-color-fff mb-02rem px-01rem py-015rem radius-004rem"
        @click="routerGo('/informedPreview', '知情同意书')"
      >
        <div>知情同意书</div>
        <van-icon class="font-color-666-important" name="arrow" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount, getCurrentInstance } from "vue";
import { useRoute } from "vue-router";
import { getStatementInfo, getStudyIntroduceVideoNewStartTime } from '@/api/informed';
import { formatDate } from '@/utils/debounce';
import { StatementInfoObj } from '@/types/welcomeToJoinUs';
import { getIcf } from '@/api/user';

export default defineComponent({
  name: "InformedDetails", // 预览知情详情
  setup() {
    const route = useRoute();
    const proxy: any = getCurrentInstance()?.proxy
    const state: any = reactive({
      icfObj: [],
      statementInfoObj: {
        icfVersionNumber: '',
        icfVersionDate: '',
      },
      videoItem: {
        videos: []
      },
      routerGo: (path: string, title: string) => {
        let query: any = {
          ICFStatementId: route.query?.ICFStatementId
        }
        if (title === '视频') {
          query = {
            vidTitle: title,
            ICFStatementId: route.query?.ICFStatementId
          }
          if (state.videoItem?.videos?.length === 1) {
            query = {
              vidTitle: state.videoItem?.videos[0].title,
              videoItem: JSON.stringify(state.videoItem?.videos[0]),
            };
            path = '/videoPlay'
          }
        } else {
          if (state.icfObj.length > 1) {
            path = '/icfList'
          }
        }
        proxy.$routerGoFun('routerInnPush', path, query, route.path, route.query)
      },
    });
    onBeforeMount(() => {
      if (route.query?.ICFStatementId) {
        const params = { 
          ICFStatementId: route.query?.ICFStatementId
        }
        getStatementInfo(params).then((rest) => {
          const res = rest as StatementInfoObj
          if (res?.icfVersionDate) {
            res.icfVersionDate = formatDate(res.icfVersionDate,'YYYY-MM-DD')
          }
          state.statementInfoObj = res
        })
        getStudyIntroduceVideoNewStartTime(params)
        .then((res) => {
          state.videoItem = {
            videos: res || []
          }
        });
        getIcf(params).then((res) => {
          state.icfObj = res?.infoList
        })
      }
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>
