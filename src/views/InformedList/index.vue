<template>
  <div
    class="h-100-vh overflow-hidden font-color-333 ft-12-rem bg-color-F5F5F5"
  >
    <van-nav-bar
      title="我的知情同意"
      left-text=""
      :left-arrow="showLeftArror"
      @click-left="goTop"
    />
    <div
      v-if="
        informedList?.length &&
        informedList[0].patientICFStatements?.length &&
        pageShow
      "
      class="h-100vh-50px overflow-auto scrollnone"
    >
      <div
        class="common-border-radius common-shadow my-02rem mx-015rem overflow-hidden bg-color-fff"
        v-for="(item, index) in informedList"
        :key="index"
      >
        <div
          class="py-01rem px-015rem font-color-7871FB min-h-053rem box-border bg-color-E0DFFF"
        >
          <div class="wrap2">
            {{ item.dctStudyName }}
          </div>
        </div>
        <div
          v-for="(statement, statementIndex) in item.patientICFStatements"
          :key="statementIndex"
        >
          <!-- 需要进入知情的则-进入主流程 -->
          <div class="font-color-333">
            <div class="py-01rem px-015rem box-border">
              <div class="flex justify-between mb-01rem">
                <div class="centerflex-h">
                  <div
                    v-if="statement.status === '进行中'"
                    class="font-color-29A035"
                  >
                    进行中
                  </div>
                  <div
                    v-else-if="statement.status === '已结束'"
                    class="font-color-999 mr-01rem"
                  >
                    已结束
                  </div>
                  <div
                    v-if="statement.icfStatus === 404"
                    class="py-002rem px-004rem ft-09-rem font-color-E60000 border-E92626 radius-004rem"
                  >
                    转线下知情
                  </div>
                </div>
                <div
                  class="font-color-999"
                  @click.stop="routerGo('/informedDetails', statement.id)"
                >
                  详情
                </div>
              </div>
              <div v-for="(e, idx) in statement.icfVersions" :key="idx">
                <div class="truncate mb-01rem">
                  版本名称：{{ e.icfVersionNumber }}
                </div>
                <div class="mb-01rem">
                  版本日期：{{
                    e?.icfVersionDate
                      ? formatDate(e.icfVersionDate, "YYYY-MM-DD")
                      : ""
                  }}
                </div>
              </div>
              <div
                v-if="statement.icfStatusName && statement.status === '进行中'"
                class="centerflex-h"
                @click.stop="routerGo('/?', statement)"
              >
                <span
                  >当前节点：
                  <span v-if="statement.icfStatusName === '退回重签'"
                    >待受试者签名</span
                  >
                  <span v-else-if="statement.icfStatusName === '知情待认证身份'"
                    >待核验身份</span
                  >
                  <span v-else>{{ statement.icfStatusName }}</span>
                  &nbsp;</span
                >
                <van-icon class="font-color-666-important" name="arrow" />
              </div>
            </div>
            <div class="w-full h-05px bg-color-F5F5F5" />
          </div>
        </div>
      </div>
    </div>
    <myStateShow
      v-else-if="pageShow"
      imgClass="max-w-3-1rem"
      :imgSrc="noDatasImg"
      texts="暂无数据"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onMounted, getCurrentInstance, onUnmounted } from 'vue';
import { useRoute } from "vue-router";
import { getGetICFInfo, getStatements } from "@/api/user";
import { InformedListStateInter, StatementResult } from "@/types/welcomeToJoinUs";
import noDatasImg from '@/assets/baby/compensationReimbursement/noDatasImg.svg';
import myStateShow from "@/components/MyStateShow.vue";
import { useStore } from 'vuex';
import { returnPatientStatusUrl, formatDate } from '@/utils/debounce';
import { getGetCASignatureStrategy, getIdentityFlow, getUpdateICFStatus } from '@/api/informed';

export default defineComponent({
  name: "InformedList", // 我的知情同意列表
  components: {
    myStateShow
  },
  setup() {
    const store = useStore();
    const route = useRoute();
    let tokenInterval: any = null
    const proxy: any = getCurrentInstance()?.proxy
    const state: InformedListStateInter = reactive({
      showLeftArror: false,
      noDatasImg,
      informedList: [],
      loading: false,
      pageShow: 0,
      formatDate,
      routerGo: (path, ICFStatementId) => {
        const query = {
          ICFStatementId,
        }
        // 判断>是否需要进入主流程
        if (path === '/?') {
          if (!state.loading) {
            state.loading = true
            getIdentityFlow({ ICFStatementId: ICFStatementId?.id }).then((res) => {
              if (!res) {
                state.nextClick(ICFStatementId)
                state.loading = false
              } else {
                getGetICFInfo({ ICFStatementId: ICFStatementId?.id }).then((res) => {
                  state.nextClick(res)
                  state.loading = false
                }).catch(() => {
                  state.loading = false
                })
              }
            }).catch(() => {
              state.loading = false
            })
          }
        } else {
          proxy.$routerGoFun('routerInnPush', path, query, route.path, route.query)
        }
      },
      nextClick: async (ICFStatementId) => {
        const query: any = {
          ICFStatementId: ICFStatementId?.id
        }
        query.bindIDcard = true
        const { userInformation } = store.state
        if (ICFStatementId?.id) {
          userInformation.icfStementId = ICFStatementId.id
          userInformation.icfStatementID = ICFStatementId.id
        }
        if (ICFStatementId?.icfStatus) {
          userInformation.patientICFStatus = ICFStatementId?.icfStatus
        }
        store.dispatch('setUserInformation', userInformation)
        let url = returnPatientStatusUrl({
          patientICFStatus: ICFStatementId?.icfStatus
        })
        if (ICFStatementId?.icfStatus === 4001 || ICFStatementId?.icfStatus === 4007) {
          const data = await getGetCASignatureStrategy(store.state?.userInformation?.icfStatementID || store.state?.userInformation?.icfStementId)
          if (data === 2) {
            url = '/signInformedFaDaDa'
          }
        }
        state.loading = false
        if (url) {
          proxy.$routerGoFun('replace', url, query)
        }
      },
      goTop: () => {
        if (store.state.appKey.toLowerCase() === 'app') {
          let curUrl = window.location.href.split('?')[1];
          if (window.location.href.split('?')[1]?.indexOf('#/') > -1) {
            curUrl = window.location.href.split('?')[1]?.substring(0, window.location.href.split('?')[1]?.indexOf('#/'));
          }
          let urlParams = new URLSearchParams(curUrl);
          if (urlParams != undefined) {
            if (urlParams.get('fromleft') === '1') {
              const msgData = {
                data: {
                  action: 'navigateBack',
                  payload: ''
                }
              }
              // 如果是iOS
              if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.jumpToNative) {
                window.webkit.messageHandlers.jumpToNative.postMessage(msgData);
              }
              // 如果是非iOS环境(即uniapp、安卓)
              uni.postMessage(msgData);
            }
            else {
              proxy.$routerGoFun('replace', '/waitExplainInformed');
            }
          }
        }
      }
    });
    onMounted(() => {
      // 判断是否来源于app
      if (store.state.appKey.toLowerCase() === 'app') {
        state.showLeftArror = true
        // let curUrl = window.location.href.split('?')[1];
        // if (window.location.href.split('?')[1]?.indexOf('#/') > -1) {
        //   curUrl = window.location.href.split('?')[1]?.substring(0, window.location.href.split('?')[1]?.indexOf('#/'));
        // }
        // let urlParams = new URLSearchParams(curUrl);
        // if (urlParams != undefined) {
        //   if (urlParams.get('fromleft') === '1') {
        //     state.showLeftArror = true
        //   }
        // }
      }
      let tokenFlag = true;
      tokenInterval = setInterval(async () => {
        const token = sessionStorage.getItem("patientToken");
        if (token && token !== "null" && token !== "undefined" && tokenFlag) {
          tokenFlag = false;
          await getUpdateICFStatus()
          getStatements().then((rest) => {
            const res = rest as StatementResult;
            state.pageShow = 1
            if (res) {
              state.informedList = [res]
            }
          }).catch(() => {
            state.pageShow = 1
          })
          clearInterval(tokenInterval);
        }
      }, 200);
    });
    onUnmounted(() => {
      clearInterval(tokenInterval);
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>
