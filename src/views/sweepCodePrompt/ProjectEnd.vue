<template>
  <div class="h-100-vh font-color-333 ft-16-rem bg-color-fff overflow-hidden">
    <div>
      <img class="w-full" src="@/assets/baby/project.svg" alt="" />
      <div class="h-49-vh pb-004-rem border-box overflow-auto scrollnone">
        <h3 class="w-full mt-02-rem ft-17-rem theme-color text-center">感谢您的关注</h3>
        <h5 v-if="finishLanguage" v-html="finishLanguage"
        class="p-02rem border-box text-center"/>
      </div>
    </div>
    <div class="w-screen fixed b-02-rem centerflex-w-wrap">
      <div class="theme-color ft-13-rem">
        本研究由创达医药科技（上海）有限公司负责具体实施
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onBeforeMount } from "vue";
import { useStore } from "vuex";
// 结束研究页面
const store = useStore()
const finishLanguage = ref('')
onBeforeMount(() => {
  finishLanguage.value = store.state?.userInformation?.finishLanguage || ''
})
</script>
