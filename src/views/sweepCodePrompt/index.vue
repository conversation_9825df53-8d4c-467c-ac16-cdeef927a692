<template>
  <div class="h-100-vh overflow-hidden">
    <img v-if="store?.state?.userInformation?.memberStatus !== 2" class="w-full" src="@/assets/baby/sweepCodePrompt.svg" alt="" />
    <img v-else-if="store?.state?.userInformation?.memberStatus === 2" class="w-full" src="@/assets/baby/sweepCodePromptFamily.svg" alt="" />
  </div>
</template>

<script setup lang="ts">
import { useStore } from 'vuex';
const store = useStore()
</script>
