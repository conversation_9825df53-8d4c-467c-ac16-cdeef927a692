<template>
  <div class="datelog-container">
    <van-nav-bar v-if="appToken == ''" title="随访计划" />
    <van-nav-bar v-else title="随访计划">
      <template #right>
        <van-icon
          name="https://dct-app.oss-cn-hangzhou.aliyuncs.com/icons/msg_1x.png"
          :badge="unreadMsgCount"
          size=".18rem"
          @click="openMsgDialog"
        />
      </template>
      <template #left>
        <van-icon
          name="https://dct-app.oss-cn-hangzhou.aliyuncs.com/icons/slide_1x.png"
          size=".18rem"
          @click="openLeftSlideDialog"
        />
      </template>
    </van-nav-bar>
    <div class="datelog-body">
      <div class="datelog-module scrollnone">
        <!-- :active="0"-->
        <van-steps class="scrollnone" direction="vertical" :active="-1">
          <van-step
            v-for="(item, index) in dateLogList"
            :key="index"
            :id="'datelog' + index"
            @click="showOpenItem(index, item)"
            :class="{ 'active-open-back': item.activeOpen }"
          >
            <div class="active-back">
              <div class="status-module">
                <h4 class="activeName">{{ item.visitName }}</h4>
                <span v-if="item.finishStatus === 3" class="active-status1"
                  >进行中</span
                >
                <span v-else-if="item.finishStatus === 1" class="active-status3"
                  >未完成</span
                >
                <span v-else-if="item.finishStatus === 4" class="active-status3"
                  >待开始</span
                >
                <span v-else-if="item.finishStatus === 2" class="active-status4"
                  >已完成</span
                >
                <span
                  v-else-if="item.finishStatus === 6"
                  class="text-yellow-300"
                  >提前开始</span
                >
              </div>
              <p>{{ item.beginDate }} ~ {{ item.endDate }}</p>
            </div>
            <div
              v-if="item.activeOpen && item.visitRemindType"
              class="follow-reminder centerflex-h"
              @click="routerGoRemider(item)"
            >
              <img
                v-if="item.visitRemindType / 1 === 1"
                src="@/assets/baby/home/<USER>"
                alt=""
              />
              <img
                v-else-if="item.visitRemindType / 1 === 2"
                src="@/assets/baby/home/<USER>"
                alt=""
              />
              <span
                >预约{{
                  item.visitRemindType / 1 === 1 ? "到院" : "电话"
                }}随访：</span
              >
              <span>{{ item?.meetingDateTime || "" }}</span>
            </div>
            <div v-if="item.activeOpen && item.taskList">
              <div
                v-for="(taskItem, taskIndexs) in item.taskList"
                :key="taskIndexs"
                class="taskList-module centerflex-h"
                @click.stop="
                  routerGoFollowQuestion(taskItem, item.editable, item)
                "
              >
                <img
                  v-if="taskItem.finishStatus === 2"
                  src="@/assets/baby/checkedIcon.svg"
                  alt=""
                />
                <img v-else src="@/assets/baby/noCheckedIcon.svg" alt="" />
                <div class="h-018rem flex-1 wrap1">
                  {{ taskItem.questName }}
                </div>
                <div
                  v-if="
                    taskItem?.isStaging &&
                    taskItem?.finishStatus !== 2 &&
                    !taskItem?.isSubmit
                  "
                  class="ml-01rem w-04rem h-019rem centerflex radius-0025rem font-color-9A9A9A bg-F8F8F8 ft-10-rem"
                  style="border: 0.5px solid #e6e6e6"
                >
                  暂存
                </div>
                <!-- <div
                  v-if="taskItem?.reviewStatus === 1"
                  class="ml-01rem centerflex ft-10-rem w-045rem h-019rem"
                  style="
                    color: #41b592;
                    border: 0.5px #41b592 solid;
                    background-color: #effffb;
                    border-radius: 0.02rem;
                  "
                >
                  已审阅
                </div> -->
                <div
                  v-if="taskItem?.toBeClarified"
                  class="ml-01rem centerflex ft-10-rem"
                  style="
                    width: 0.48rem;
                    color: #f19980;
                    background-color: #ffebe6;
                    border-radius: 0.06rem;
                  "
                >
                  待澄清
                </div>
              </div>
            </div>
          </van-step>
        </van-steps>
        <myStateShow
          v-if="dateLogList?.length === 0"
          :imgSrc="noinformedImgSrc"
          :texts="noinformedTexts"
        />
        <div
          v-else-if="homeList.patientStatus !== 10"
          class="centerflex text-gray-400"
        >
          入组后将更新后续随访计划
        </div>
      </div>
    </div>
    <!-- 底部tabbar -->
    <MyTabbar :propsActive="1" />
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount, getCurrentInstance } from 'vue';
import { getVisitQuOr } from "@/api/tasks"; // 获取访视中的全部问卷列表
import MyTabbar from "@/components/Tabbar.vue";
import { useStore } from "vuex";
import { useRoute } from "vue-router";
import { getPatient } from "@/api/home";
import { dateLogInter } from '@/types/dateLog'
import { Toast } from "vant";
import myStateShow from "@/components/MyStateShow.vue";
import noinformedImgSrc from '@/assets/baby/noinformed.svg';
import { homeListInter } from '@/types/storeState';
import { getUnReadCount } from '@/api/app'

export default defineComponent({
  name: "DateLog", // 随访计划
  components: {
    MyTabbar,
    myStateShow
  },
  setup() {
    const store = useStore()
    const route = useRoute()
    const proxy: any = getCurrentInstance()?.proxy
    const state: dateLogInter = reactive({
      unreadMsgCount: '', // 未读消息数量
      appToken: store.state.appToken,
      dateLogList: [],
      noinformedImgSrc,
      homeList: {
        dctPatientId: '',
        dctSiteId: '',
        dctStudyId: '',
        displayTraining: false,
        patNumber: '',
        avatarUrl: '',
        greeting: '',
        currentStatusText: '',
        patientTrialStatusText: '',
        patientStatus: 0,
        visits: [],
        frequencyFeature: [],
        currentVisitId: '',
        currentVisitText: '',
        totalTask: 0,
        completedTask: 0,
        questionTaskStatus: 0,
        pictureTaskStatus: 0,
        otherTaskStatus: 0,
        visitMeetingList: [],
        dailyFeature: 0,
        inGroup: '',
        inGroupDay: '',
        medicationStatus: 0,
        patientICFStatus: 0,
        patientMenues: [],
        studyName: '',
        unReadChattingRecordsNum: 0,
        needInGroupGuidModal: 0,
        memberAvatarUrl: '',
        hasInteractiveCommunication: 0
      },
      noinformedTexts: `<div>暂无随访计划</div>`,
      // 点击高亮展开
      showOpenItem: async (index, ite) => {
        state?.dateLogList?.forEach((item, indexs) => {
          if (index === indexs) {
            item.activeOpen = !item.activeOpen;
          }
        });
        if (ite.finishStatus === 4 && ite.activeOpen) {
          ite.taskList = await getVisitQuOr(ite.visitId, 4);
        }
      },
      //
      routerGoRemider: (questItem) => { // 从原routerGo拆出提醒得跳转, 主要是问卷跳转有两种情况, 单纯得url不够
        const query = {
          visitId: questItem.visitId
        }
        const path = '/followReminder'
        proxy.$routerGoFun('routerInnPush', path, query, route.path)
      },
      routerGoFollowQuestion: (questItem, editable, item) => { // 从原routerGo拆出问卷跳转, 主要是问卷有两种跳转
        if (questItem) {
          if (item.finishStatus === 4 || (item.finishStatus === 0 && item.visitBaseline === 2)) {
            Toast("该访视还未开始");
            return
          }
          if (questItem.finishStatus === 2 && questItem?.isOffline) {
            Toast("无法查看，该任务在线下完成");
            return
          }
          let path = 'followquestionnaires'
          // if (questItem.questDisplayType === 3) {//如果是url问卷, 把url替换掉成另外一个组件.
          //   path = '/followurlquestionnaires'
          // }
          const visDate = item.beginDate + ' ~ ' + item.endDate
          const query = {
            questId: questItem.questId,
            questionUrl: questItem.questionUrl,
            editable,
            questItem: JSON.stringify(questItem as string),
            visDate,
            visitName: item.visitName,
            goPath: route.path, // 定制化问卷处理返回路径的问题
            visitId: item?.visitId || ''
          }
          proxy.$routerGoFun('routerInnPush', path, query, route.path)
        }
      },
      // 打开APP消息列表
      openMsgDialog: () => {
        const msgData = {
          data: {
            action: 'msgList',
            payload: store.state.appToken
          }
        }
        // 如果是iOS
        if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.jumpToNative) {
          window.webkit.messageHandlers.jumpToNative.postMessage(msgData);
        }
        // 如果是非iOS环境(即uniapp、安卓)
        uni.postMessage(msgData);
      },
      // 打开侧边栏
      openLeftSlideDialog: () => {
        const msgData = {
          data: {
            action: 'showLeftSlide',
            payload: store.state.appToken
          }
        }
        // 如果是iOS
        if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.jumpToNative) {
          window.webkit.messageHandlers.jumpToNative.postMessage(msgData);
        }
        // 如果是非iOS环境(即uniapp、安卓)
        uni.postMessage(msgData);
      }
    });
    onBeforeMount(() => {
      if (store.state.appToken != '') {
        getUnReadCount().then(res => {
          state.unreadMsgCount = res;
        });
      }
      getPatient().then((rest) => {
        const res = rest as homeListInter
        state.homeList = res
        state.dateLogList = res?.visits || [];
        if (res.patientStatus !== 10) {
          state.noinformedTexts = `<div>暂无随访计划</div>
          <div>入组后将更新后续随访计划</div>`
        }
        store.dispatch('setHomeList', res);
        let stepNum = -1
        state.dateLogList.forEach(async (item, index) => {
          // 默认进来进行中的高亮且展开 
          // 0 = 未知, 1 = 未完成, 2 = 已完成, 3 = 进行中, 4 = 待开始, 5 = 医生要求重填
          if (item.editable === 1 && item.isCurrent === 1 && item.finishStatus !== 4) {
            item.activeOpen = true;
            if (stepNum === -1) {
              stepNum = index
            }
          } else {
            item.activeOpen = false;
          }
          // 获取子任务列表
          if (item.finishStatus !== 4) {
            item.taskList = await getVisitQuOr(item.visitId, 4);
          }
          // 最后设置定位到高亮卡片
          if (index + 1 === res.visits.length) {
            if (stepNum > -1) {
              setTimeout(() => {
                const stepItem = document.getElementById('datelog' + stepNum)
                if (stepItem)
                  stepItem.scrollIntoView({ block: 'start', behavior: 'smooth' });
              }, 1000);
            }
          }
        });
      });
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
@import "@/style/steps.less";
.datelog-container {
  height: 100vh;
  overflow: hidden;
  background: #fefdff;
  color: #555;
  font-size: 0.16rem;
  .datelog-body {
    // height: 90vh;
    height: calc(100vh - 46px - 56px);
    .datelog-module {
      height: 100%;
      overflow: auto;
      padding: 0.1rem 0;
      box-sizing: border-box;
      background: #fff;
    }
  }
  :deep(.myStateShow) {
    img {
      width: 1.36rem;
    }
  }
}
</style>
