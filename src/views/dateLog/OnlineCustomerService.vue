<template>
  <div class="onlineservice-container">
    <van-nav-bar
      title="在线咨询"
      left-text=""
      left-arrow
      @click-left="$routerBackFun()"
    />
    <div class="onlineservice-form scrollnone">
      <div
        v-for="(item, index) in onlineCustomerServiceList"
        :key="index"
        class="onlineservice-body"
      >
        <!-- 客服回复 -->
        <div v-if="item.direction === 2" class="onlineservice-module">
          <div class="onlineservice-items">
            <img src="@/assets/baby/onlineservice.svg" alt="" />
            <p v-if="item.contentType === 1">{{ item.content }}</p>
          </div>
        </div>
        
        <!-- 我的发送内容 -->
        <div v-if="item.direction === 1" class="mysend-module">
          <div class="mysend-items">
            <p v-if="item.contentType === 1">{{ item.content }}</p>
            <img src="@/assets/baby/userHeadPortrait.svg" alt="" />
          </div>
        </div>
      </div>
    </div>
    <!-- 底部输入  @focus="sendMessagFocus" @blur="sendMessagBlur"-->
    <div class="sendmessage">
      <van-field
        v-model.trim="sendMessagValue"
        class="sendmessage-input"
        maxlength="500"
        placeholder="请输入想要咨询的问题"
      />
      <img v-show="sendOrAdd" src="@/assets/baby/interactiveCommunicationAddIcon.svg" alt="" />
      <p v-show="!sendOrAdd" @click="addMyText()">发送</p>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount, watch } from "vue";
import { postChatText } from "@/api/chat";
// postChatImage, postChatAudio,
import { onlineCustomerServiceInter } from '@/types/dateLog'

export default defineComponent({
  name: "OnlineCustomerService",
  setup() {
    const state: onlineCustomerServiceInter = reactive({
      sendOrAdd: true, // 发送键是否展示
      onlineCustomerServiceList: [],
      sendMessagValue: "", // 输入的信息
      // 发送消息text
      addMyText: () => {
        if (state.sendMessagValue) {
          const onlineCustomerServiceList =
            state.onlineCustomerServiceList;
          onlineCustomerServiceList.push({
            direction: 1, //自己
            contentType: 1, //文本类型
            content: state.sendMessagValue,
          });
          // 发送消息 -成功后
          postChatText( 
            "0005ce8b-c494-4577-0000-000000000000",
            state.sendMessagValue,).then((res) => {
            onlineCustomerServiceList.push(res);
            state.sendMessagValue = "";
            const scroll = document.getElementsByClassName(
              "onlineservice-form"
            )[0];
            scroll.scrollTop = scroll.scrollHeight;
            // console.log(res,onlineCustomerServiceList);
          });
        }
      },
    });
    // 监听 输入是否有值
    watch(
      state,
      () => {
        //  console.log(val)
        state.sendOrAdd = !state.sendMessagValue;
      },
      {
        immediate: true, // 是否初始化立即执行一次, 默认是false
        deep: true, // 是否是深度监视, 默认是false
      }
    );

    onBeforeMount(() => {
      /*getChat().then((res) => {
chatId	string :聊天记录的Id
chatTime	string($date-time):聊天发生时间 具体到秒 如: 2020-12-07 12:15:13
direction	number问题的方向 0 = 未知, 1 = 自己, 2 = 对方
contentType 聊天问题 0 = 未知, 1 = 文本, 2 = 图片, 3 = 语音
content	string: 我家宝宝最好了 如果聊天内容是文本, 则内容在这里
contentUrl	string : http://abc.com/abc 如果聊天内容为图片或者语音, 则url在这里
        state.onlineCustomerServiceList = res;
      });*/
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.onlineservice-container {
  height: 100vh;
  overflow: hidden;
  background: #f0f0f0;
  color: #333;
  font-size: 0.14rem;
  .onlineservice-form {
    height: 90vh;
    overflow: auto;
    .onlineservice-body {
      width: 100%;
      padding: 0.2rem 0.1rem;
      box-sizing: border-box;
      .onlineservice-module {
        width: 100%;
        box-sizing: border-box;
        .onlineservice-items {
          display: flex;
          align-items: center;
          margin: 0 0 0.1rem 0;
          img {
            width: 0.42rem;
            height: 0.42rem;
            border-radius: 50%;
            margin: 0 0.1rem 0 0;
          }
          p {
            width: 2.43rem;
            padding: 0.1rem;
            box-sizing: border-box;
            border-radius: 0 0.1rem 0.1rem 0.1rem;
            word-break: break-all;
            word-wrap: break-word;
            background: #fff;
          }
        }
      }
      //我的
      .mysend-module {
        width: 100%;
        box-sizing: border-box;
        .mysend-items {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          margin: 0 0 0.1rem 0;
          img {
            width: 0.42rem;
            height: 0.42rem;
            border-radius: 50%;
            margin: 0 0 0 0.1rem;
          }
          p {
            width: 2.43rem;
            padding: 0.1rem;
            box-sizing: border-box;
            border-radius: 0.1rem 0 0.1rem 0.1rem;
            word-break: break-all;
            word-wrap: break-word;
            color: #fff;
            background-image: linear-gradient(180deg, #3fa1fc, #5860da);
          }
        }
      }
    }
  }
  /*发送消息*/
  .sendmessage {
    width: 100%;
    height: 0.5rem;
    position: fixed;
    background: #fff;
    left: 0;
    bottom: 0rem;
    display: flex;
    // justify-content: space-around;
    align-items: center;
    .sendmessage-input {
      width: 84%;
      margin: 0 0.12rem 0 0.1rem;
      border-radius: 0.04rem;
      background: #f0f0f0;
    }
    img {
      width: 0.26rem;
      height: 0.26rem;
    }
  }
}
</style>
