<template>
  <div class="drugApplication-container">
    <van-nav-bar
      title="申请药物"
      left-text=""
      left-arrow
      @click-left="$routerBackFun()"
    />
    <div class="drugApplication-form scrollnone">
      <div class="drugApplication-body">
        <div
          v-for="item in drugApplicationList"
          :key="item.id"
          class="drugApplication-module"
          :class="{ 'drugApplication-back-checked': item.checkedItem }"
        >
          <div class="drugApplication-img-or-title">
            <img
              class="drugApplication-img"
              src="@/assets/baby/drugApplicationIcon.svg"
              alt=""
            />
            <div class="drugApplication-title">药物</div>
          </div>

          <div class="drugApplication-items-body">
            <div class="drugApplication-items">
              <h4>药物名称:</h4>
              <p>{{ item.drugDisplayName }}</p>
            </div>
            <div class="drugApplication-items">
              <h4>药物规格:</h4>
              <p>{{ item.drugSpecifics }}</p>
            </div>
            <div class="drugApplication-items">
              <h4>药物数量:</h4>
              <!-- <p>{{ item.drugApplicationTime }}</p> -->
              <div class="add-subtract-modules">
                <button
                  class="add-subtract-btn"
                  @click="addOrSubtract('s', item)"
                >
                  -
                </button>
                <div class="drugApplication-num">
                  {{ item.recommendApplyNumber }}
                </div>
                <button
                  class="add-subtract-btn"
                  @click="addOrSubtract('a', item)"
                >
                  +
                </button>
              </div>
            </div>
          </div>

          <img
            v-show="item.checkedItem"
            class="checked"
            src="@/assets/baby/checkedIcon.svg"
            alt=""
            @click="checkedItems(item)"
          />
          <img
            v-show="!item.checkedItem"
            class="checked"
            src="@/assets/baby/noCheckedIcon.svg"
            alt=""
            @click="checkedItems(item)"
          />
        </div>

        <van-button
          class="drug-application-save-btn"
          round
          type="primary"
          @click="drugApplicationAdd()"
          >确定</van-button
        >
      </div>
    </div>
    <!-- 提交药物申请 -->
    <van-dialog
      v-model:show="showNeedImprove"
      title="是否确认申请？"
      confirmButtonColor="#5860DA"
      @confirm="confirmNeedImprove"
      show-cancel-button
    >
      <van-form @submit="confirmNeedImprove">
        <van-field
          v-model.trim="reasons.suppymentReason"
          type="textarea"
          class="delete-Texts"
          placeholder="请填写申请理由"
          maxlength="200"
          :rules="[{ required: true, message: '请填写申请理由' }]"
          autosize
        />
      </van-form>
    </van-dialog>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  onBeforeMount,
  getCurrentInstance,
} from "vue";
import { Notify } from "vant";
import { drugApplication, drugDrugApply } from "@/api/my";
import { drugApplicationInter, drugApplicationListInter } from "@/types/dateLog";

export default defineComponent({
  name: "DrugApplication", // 申请药物
  setup() {
    const proxy: any = getCurrentInstance()?.proxy;
    const state: drugApplicationInter = reactive({
      showNeedImprove: false, // 提交药物申请
      reasons: {
        suppymentReason: "", // 需完善的内容
      },
      drugApplicationList: [],
      // 点击选中药物
      checkedItems: (i) => {
        i.checkedItem = !i.checkedItem;
        // state.drugApplicationList[i].checkedItem = !state.drugApplicationList[i].checkedItem
      },
      // 加减数据--
      addOrSubtract: (falg, item) => {
        if (falg === "s" && item.recommendApplyNumber > 0) {
          if (item.recommendApplyNumber <= item.minApplyNumber) {
            Notify({ message: "数量不可小于最少可申请数量", type: "danger" });
            return;
          }
          item.recommendApplyNumber -= item.scale;
        } else if (falg === "a") {
          if (item.recommendApplyNumber >= item.maxApplyNumber) {
            Notify({ message: "数量不可大于最多可申请数量", type: "danger" });
            return;
          }
          item.recommendApplyNumber += item.scale;
        }
      },
      // 提交
      drugApplicationAdd: () => {
        const falg = state.drugApplicationList.find(
          (element) => element.checkedItem === true
        );
        // if(item.recommendApplyNumber <= item.minApplyNumber){
        //     Notify({ message: '数量不可小于最少可申请数量', type: 'danger' })
        //   console.log(111)
        //   return
        // }
        if (falg) {
          state.showNeedImprove = true;
        } else {
          Notify({ message: "您未选中任何药物哦", type: "danger" });
        }
      },
      // 提交药物申请
      confirmNeedImprove: () => {
        if (state.reasons.suppymentReason == "") {
          Notify({ message: "请先输入需完善的内容再提交", type: "danger" });
        } else {
          const drugAppliesArr: drugApplicationListInter[] = [],
            drugItems: any[] = [];
          state.drugApplicationList.map((items) => {
            if (items.checkedItem) {
              drugAppliesArr.push(items);
            }
          });
          drugAppliesArr.forEach((item: drugApplicationListInter) => {
            drugItems.push({
              drugId: item.id,
              applyNumber: item.recommendApplyNumber,
            });
          });
          drugDrugApply({
            applyReason: state.reasons.suppymentReason,
            drugItems: drugItems,
          });
          Notify({ message: "申请成功", type: "success" });
          proxy.$routerBackFun();
        }
      },
    });
    onBeforeMount(() => {
      drugApplication({ isApply: true }).then((res) => {
        state.drugApplicationList = res as drugApplicationListInter[];
      });
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.drugApplication-container {
  height: 100vh;
  overflow: hidden;
  background: #f7f7f7;
  color: #333;
  font-size: 0.13rem;
  h4 {
    margin: 0;
  }
  .drugApplication-form {
    .drugApplication-body {
      width: 100%;
      height: 81vh;
      overflow: auto;
      padding: 0.2rem;
      margin: 0.1rem 0 0 0;
      box-sizing: border-box;
      .drugApplication-module {
        width: 100%;
        padding: 0.1rem 0.2rem;
        margin: 0 0 0.1rem 0;
        box-sizing: border-box;
        background: #fff;
        border: 0.5px solid #fff;
        box-shadow: 0 0.02rem 0.26rem rgba(0, 0, 0, 0.07); //阴影
        border-radius: 0.1rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .drugApplication-img-or-title {
          display: flex;
          justify-content: center;
          flex-wrap: wrap;
          align-items: center;
          .drugApplication-img {
            width: 0.35rem;
            height: 0.32rem;
          }
          .drugApplication-title {
            width: 100%;
            margin: 0.06rem 0 0 0;
            font-size: 0.12rem;
            color: var(--theme-color);
            text-align: center;
          }
        }

        .checked {
          width: 0.15rem;
          height: 0.15rem;
        }
        .drugApplication-items-body {
          width: 2rem;
          .drugApplication-items {
            display: flex;
            align-items: center;
            margin: 0 0 0.1rem 0;
            .add-subtract-modules {
              display: flex;
              margin: 0 0 0 0.1rem;
              .add-subtract-btn {
                width: 0.2rem;
                height: 0.2rem;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 0.16rem;
                background: #fff;
                border: 0.5px solid #c3c3c3;
              }
              .drugApplication-num {
                width: 0.3rem;
                height: 0.2rem;
                padding: 0;
                box-sizing: border-box;
                border: 0.5px solid #c3c3c3;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 0.12rem;
                background: #fff;
              }
            }
          }
          h4 {
            min-width: 0.7rem;
          }
          p {
            margin: 0 0 0 0.1rem;
            //单行文本溢出省略
            //  // white-space: nowrap;
            //   overflow: hidden;
            //   text-overflow: ellipsis;

            display: -webkit-box;
            word-break: break-all;
            word-wrap: break-word;
            overflow: hidden;
            /*…省略形式*/
            text-overflow: ellipsis;
            /*从上向下垂直排列子元素*/
            -webkit-box-orient: vertical;
            /*文本可分两行*/
            -webkit-line-clamp: 2;
          }
        }
      }
      .drugApplication-back-checked {
        border: 0.5px solid var(--theme-color);
      }
      .drug-application-save-btn {
        width: 90%;
        position: fixed;
        bottom: 0.2rem;
      }
    }
  }
}
</style>
