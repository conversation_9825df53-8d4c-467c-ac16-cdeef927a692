<template>
  <div class="gift-container">
    <van-nav-bar
      title="领取礼品"
      left-text=""
      left-arrow
      @click-left="$routerBackFun()"
    />
    <div class="gift-form scrollnone">
      <div class="gift-body">
        <div
          v-for="(item, index) in giftList"
          :key="index"
          class="gift-module"
          :class="{ 'gift-back-checked': item.checkedItem }"
        >
          <div class="gift-img-or-title">
            <img class="gift-img" src="@/assets/baby/gift.svg" alt="" />
            <div class="gift-title">礼品</div>
          </div>

          <div class="gift-items-body">
            <div class="gift-items">
              <h4>礼品名称:</h4>
              <p>{{ item.giftName ? item.giftName : "" }}</p>
            </div>
            <div class="gift-items">
              <h4>礼品规格:</h4>
              <p>{{ item.productStandard ? item.productStandard : "" }}</p>
            </div>
            <div class="gift-items">
              <h4>礼品数量:</h4>
              <!-- <p>{{ item.giftTime }}</p> -->
              <div class="add-subtract-modules">
                <button
                  class="add-subtract-btn"
                  @click="addOrSubtract('s', item)"
                >
                  -
                </button>
                <div class="gift-num">
                  {{ item.defaultApplyNum ? item.defaultApplyNum : "" }}
                </div>
                <button
                  class="add-subtract-btn"
                  @click="addOrSubtract('a', item)"
                >
                  +
                </button>
              </div>
            </div>
          </div>

          <img
            v-show="item.checkedItem"
            class="checked"
            src="@/assets/baby/checkedIcon.svg"
            alt=""
            @click="checkedItems(item)"
          />
          <img
            v-show="!item.checkedItem"
            class="checked"
            src="@/assets/baby/noCheckedIcon.svg"
            alt=""
            @click="checkedItems(item)"
          />
        </div>

        <van-button class="gift-save-btn" round type="primary" @click="giftAdd"
          >确定</van-button
        >
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  onBeforeMount,
  getCurrentInstance,
} from "vue";
import { Dialog, Notify } from "vant";
import { getPatientGiftList, postPatientGiftApplyInfoAsync } from "@/api/my";
import { giftInter } from "@/types/dateLog";

export default defineComponent({
  name: "Gift", // 领取礼品
  setup() {
    const proxy: any = getCurrentInstance()?.proxy;
    const state: giftInter = reactive({
      giftList: [
        {
          presentMaxNumber: 0,
          presentName: "",
          presentSpecific: "",
          specificOptions: [],
          presentNumber: 1,
          checkedItem: false,
          giftName: "",
          productStandard: "",
          defaultApplyNum: 1,
        },
      ],
      // 点击选中礼品
      checkedItems: (i) => {
        i.checkedItem = !i.checkedItem;
      },
      // 加减数据--
      addOrSubtract: (falg, item) => {
        if (falg === "s" && item.defaultApplyNum > 0) {
          if (item.defaultApplyNum <= item.minApplyNum) {
            Notify({ message: "数量不可小于最少可申请数量", type: "danger" });
            return;
          }
          item.defaultApplyNum--;
        } else if (falg === "a") {
          if (item.defaultApplyNum >= item.stockQTY) {
            Notify({ message: "数量不可大于最多可申请数量", type: "danger" });
            return;
          }
          item.defaultApplyNum++;
        }
      },
      // 提交
      giftAdd: () => {
        const falg = state.giftList.find(
          (element) => element.checkedItem === true
        );
        if (falg) {
          Dialog.confirm({
            title: "操作",
            message: "是否确认领取？",
            confirmButtonColor: "#5860DA",
            confirmButtonText: "是",
            cancelButtonText: "否",
          })
            .then(() => {
              const params = [];
              state.giftList.forEach((item) => {
                if (item.checkedItem) {
                  params.push({
                    giftId: item.giftId,
                    applyQty: item.defaultApplyNum,
                  });
                }
              });
              postPatientGiftApplyInfoAsync(params).then(() => {
                Notify({ message: "领取成功", type: "success" });
                proxy.$routerBackFun();
              });
            })
            .catch(() => {
              return false;
            });
        } else {
          Notify({ message: "您未选中任何礼品哦", type: "primary" });
        }
      },
    });
    onBeforeMount(() => {
      getPatientGiftList().then((res) => {
        res.forEach((item) => {
          if (item.defaultApplyNum < 1) {
            item.defaultApplyNum = 1;
          }
        });
        state.giftList = res;
      });
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.gift-container {
  height: 100vh;
  overflow: hidden;
  background: #f7f7f7;
  color: #333;
  font-size: 0.13rem;
  h4 {
    margin: 0;
  }
  .gift-form {
    .gift-body {
      width: 100%;
      height: 81vh;
      overflow: auto;
      padding: 0.2rem;
      margin: 0.1rem 0 0 0;
      box-sizing: border-box;
      .gift-module {
        width: 100%;
        padding: 0.1rem 0.2rem;
        margin: 0 0 0.1rem 0;
        box-sizing: border-box;
        background: #fff;
        border: 0.5px solid #fff;
        box-shadow: 0 0.02rem 0.26rem rgba(0, 0, 0, 0.07); //阴影
        border-radius: 0.1rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .gift-img-or-title {
          display: flex;
          justify-content: center;
          flex-wrap: wrap;
          align-items: center;
          .gift-img {
            width: 0.35rem;
            height: 0.32rem;
          }
          .gift-title {
            width: 100%;
            margin: 0.06rem 0 0 0;
            font-size: 0.12rem;
            color: var(--theme-color);
            text-align: center;
          }
        }

        .checked {
          width: 0.15rem;
          height: 0.15rem;
        }
        .gift-items-body {
          width: 2rem;
          .gift-items {
            display: flex;
            align-items: center;
            margin: 0 0 0.1rem 0;
            .add-subtract-modules {
              display: flex;
              margin: 0 0 0 0.1rem;
              .add-subtract-btn {
                width: 0.2rem;
                height: 0.2rem;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 0.16rem;
                background: #fff;
                border: 0.5px solid #c3c3c3;
              }
              .gift-num {
                width: 0.3rem;
                height: 0.2rem;
                padding: 0;
                box-sizing: border-box;
                border: 0.5px solid #c3c3c3;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 0.12rem;
                background: #fff;
              }
            }
          }
          h4 {
            min-width: 0.7rem;
          }
          p {
            margin: 0 0 0 0.1rem;
            //单行文本溢出省略
            //  // white-space: nowrap;
            //   overflow: hidden;
            //   text-overflow: ellipsis;

            display: -webkit-box;
            word-break: break-all;
            word-wrap: break-word;
            overflow: hidden;
            /*…省略形式*/
            text-overflow: ellipsis;
            /*从上向下垂直排列子元素*/
            -webkit-box-orient: vertical;
            /*文本可分两行*/
            -webkit-line-clamp: 2;
          }
        }
      }
      .gift-back-checked {
        //background: rgba(88, 96, 218, 0.1);
        border: 0.5px solid var(--theme-color);
      }
      .gift-save-btn {
        width: 90%;
        position: fixed;
        bottom: 0.2rem;
      }
    }
  }
}
</style>
