<template>
  <div class="commonproblem-container">
    <van-nav-bar
      title="常见问题"
      left-text=""
      left-arrow
      @click-left="$routerBackFun()"
    />
    <div class="commonproblem-body scrollnone">
      <div class="commonproblem-module">
        <!-- 每一项 常见问题 -->
        <div
          class="commonproblem-items"
          v-for="(item, index) in commonProblemList"
          :key="index"
        >
          <div class="issue">
            <img src="@/assets/baby/Q.svg" alt="" />
            <p>{{ item.question }}</p>
          </div>
          <div class="issue">
            <img src="@/assets/baby/A.svg" alt="" />
            <p>{{ item.answer }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount } from "vue";
import { getFAQ } from "@/api/my";
import { commonProblemInter, commonProblemList } from "@/types/dateLog";

export default defineComponent({
  name: "CommonProblem", // 常见问题
  setup() {
    const state: commonProblemInter = reactive({
      commonProblemList: [],
    });
    onBeforeMount(() => {
      getFAQ()
        .then((res) => {
          state.commonProblemList = res as commonProblemList[];
        });
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.commonproblem-container {
  height: 100vh;
  overflow: hidden;
  background: #f0f0f0;
  color: #555;
  font-size: 0.14rem;
  .commonproblem-body {
    width: 100%;
    height: 86vh;
    padding: 0.1rem;
    margin: 0.1rem 0 0 0;
    box-sizing: border-box;
    overflow: auto;
    .commonproblem-module {
      width: 100%;
      padding: 0.1rem;
      margin: 0 0 0.1rem 0;
      box-sizing: border-box;
      border-radius: 0.1rem;
      .commonproblem-items {
        padding: 0.1rem;
        border-radius: 0.05rem;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        margin: 0 0 0.1rem 0;
        background: #fff;
        .issue {
          width: 100%;
          display: flex;
          margin: 0 0 0.1rem 0;
          img {
            width: 0.2rem;
            height: 0.22rem;
          }
          p {
            margin: 0 0 0 0.1rem;
            word-break: break-all;
            word-wrap: break-word;
          }
        }
      }
    }
  }
}
</style>
