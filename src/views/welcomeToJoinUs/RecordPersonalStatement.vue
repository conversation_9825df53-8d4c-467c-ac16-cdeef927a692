<template>
  <div class="recordPersonalStatement-container">
    <van-nav-bar
      title="录制知情声明"
      left-text=""
    />
    <div class="recordPersonalStatement-form scrollnone">
      <nav>
        <div>请录制一段您的个人知情声明，以确认您自愿参与本次研究，录制完成后请上传视频。</div>
        <div class="nav-last-text">*视频请保持正脸面对镜头并朗读以下内容，为保障您的隐私安全，请在私密的环境下进行录制，此视频仅研究医生可见。</div>
      </nav>
      <div class="recordPersonalStatement-body centerflex-w">
          <div class="recordPersonalStatement-body-center centerflex-w-wrap">
            <div class="recordPersonalStatement-body-h">
              <div class="recordPersonalStatement-body-h-ts centerflex">您可记录以下内容后，再开始录制</div>
              <div class="recordPersonalStatement-body-h-texts" v-html="studyReciteText" />
              <div class="recordPersonalStatement-body-br"/>
              <div class="recordPersonalStatement-upload-module">
                <div class="upload-items">
                  <div class="upload-items-title"><i>*</i>上传身份证</div>
                  <div class="upload-items-hint">(头像面)</div>
                  <UploadImg
                      :maxCount="1"
                      :myMultiple="false"
                      :postImgFun="postImgFun"
                      :deleteImg="deleteIdentityCard"
                      :identityObj="identityObj"
                      :disabledSaveFalg="disabledSaveFalg"
                    />
                </div>
                <div class="upload-items">
                  <div class="upload-items-title"><i>*</i>录制知情声明</div>
                  <div class="upload-items-hint">(视频大小不超过100MB)</div>
                  <UploadVideo :videoItem="videoItem" :postVideos="postVideoFile" :deleteVideo="deleteVideoFile" :disabledSaveFalg="disabledSaveFalg"/>
                </div>
              </div>
          </div>
        </div>
      </div>
    </div>
    <div class="recordPersonalStatement-btn centerflex-w">
        <van-button
          class="recordPersonalStatement-btn-body"
          round
          type="primary"
          :loading="loadingBtn"
          loading-text="提交中..."
          @click="saveVideoForm"
        >提交</van-button>
      </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onMounted, getCurrentInstance } from 'vue';
import UploadVideo from "@/components/UploadVideo.vue";
import { Toast, Notify } from "vant";
import { getStatement, postVideoFile, postVideoComfirm, deleteVideoFile, postIdentityCard, deleteIdentityCard } from '@/api/user'
import UploadImg from "@/components/UploadImg.vue";
import { returnPatientStatusUrl } from '@/utils/debounce';
import { useStore } from 'vuex';
import { getPatientStatus } from '@/api/user';
import {
  getStatementInter,
  postIdentityCardInter,
  postVideoComfirmInter
} from '@/types/welcomeToJoinUs';

export default defineComponent({
  name: "RecordPersonalStatement", // 录制个人声明
  components: {
    UploadVideo,
    UploadImg
  },
  setup() {
    const proxy: any = getCurrentInstance()?.proxy
    const store = useStore()
    const state = reactive({
      videoItem: {
        videoUrl: '',
        statementFileId: '',
        videoCoverUrl: '',
        // identityCardUrl: '',
      },
      disabledSaveFalg: {
        disabledSaveFalg: false
      },
      studyReciteText: '',
      loadingBtn: false,
      identityObj: {
        identityCardFileUrl: '',
        statementFileId: '',
      },
      postVideoFile,
      deleteVideoFile,
      deleteIdentityCard,
      // 获取状态的请求
      getPatientStatusFun: () => {
        const icfStatementIdData = {
          icfStatementID:
            store.state?.userInformation?.icfStatementID ||
            store.state?.userInformation?.icfStementId,
        }
        return new Promise(async(resolve, reject) => {
          try {
            const patientStatus: any = await getPatientStatus(icfStatementIdData)
            if (patientStatus?.patientICFStatus === 404) {
              Notify({ type: 'danger', message: '已转线下知情' })
              proxy.$routerGoFun('replace', returnPatientStatusUrl(res))
              return
            }
            resolve(1)
          } catch (e) {
            reject(e)
          }
        })
      },
      // 提交
      saveVideoForm: async() => {
        await state.getPatientStatusFun() 
        if (state.disabledSaveFalg.disabledSaveFalg) {
          Toast("视频未上传完成，请稍后再试");
        } else if (!state.identityObj.identityCardFileUrl){
          Toast("请上传身份证");
        } else if (!state?.videoItem?.videoUrl) {
          Toast("请录制知情声明");
        } else {
          state.loadingBtn =  true
          postVideoComfirm(state.videoItem?.statementFileId || '0',{
            icfStatementID: store.state?.userInformation?.icfStatementID || store.state?.userInformation?.icfStementId
          })
          .then((rest) => {
            const res = rest as postVideoComfirmInter
            state.loadingBtn =  false
            Toast("提交成功");
            proxy.$routerGoFun('replace', returnPatientStatusUrl(res))
          }).catch(() => {
            state.loadingBtn =  false
          })
        }
      },
      postImgFun: async(file) => {
        await state.getPatientStatusFun() 
        state.disabledSaveFalg.disabledSaveFalg = true
        postIdentityCard(file,state.identityObj.statementFileId)
        .then((rest) => {
          const res = rest as postIdentityCardInter
          Toast("上传成功");
          state.identityObj.identityCardFileUrl = res.identityCardUrl
          state.disabledSaveFalg.disabledSaveFalg = false
        }).catch(() => {
          // Toast("上传失败");
          state.disabledSaveFalg.disabledSaveFalg = false
        })
      },
      onLoad: () => {
        getStatement({
          ICFStatementId: store.state?.userInformation?.icfStatementID || store.state?.userInformation?.icfStementId
        }).then((rest) => {
          const res = rest as getStatementInter
          state.studyReciteText = res?.studyReciteText || ''
          state.videoItem.videoUrl = res?.videoUrl || ''
          state.videoItem.statementFileId = res?.statementFileId || ''
          state.identityObj.statementFileId = res?.statementFileId || ''
          state.videoItem.videoCoverUrl = res?.videoCoverUrl || ''
          state.identityObj.identityCardFileUrl = res?.identityCardFileUrl || ''
        })
      }
    });

    onMounted(() => {
      state.onLoad()
    })
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.recordPersonalStatement-container {
  height: 100vh;
  position: relative;
  overflow: hidden;
  background: #F7F7F7;
  color: #333;
  font-size: 0.12rem;
  .recordPersonalStatement-form {
    height: calc(100vh - 46px);
    padding: 0rem 0.2rem;
    box-sizing: border-box;
    nav{
      margin: 0.1rem 0;
      font-size: 0.15rem;
      color: var(--theme-color);
      .nav-last-text{
        margin: 0.1rem 0;
        font-size: 0.12rem;
        color: #E26375;
      }
    }
    .recordPersonalStatement-body{
      width: 100%;
      // height: 3.425rem;
      .recordPersonalStatement-body-center{
        width: 100%;
        height: 100%;
        padding: 0.2rem 0.1rem 0.1rem;
        // box-sizing: border-box;
        border-radius: 0.1rem;
        background: #fff;
        .recordPersonalStatement-body-h{
            width: 100%;
            .recordPersonalStatement-body-h-ts{
              margin: 0 0 0.1rem 0;
              font-size: 0.13rem;
              color: #9A9A9A;
            }
            .recordPersonalStatement-body-h-texts{
              height: 1.73rem;
              padding: 0 0 0.1rem 0;
              font-size: 0.17rem;
              color: #333;
              overflow: auto;
            }
            .recordPersonalStatement-body-br{
              height: 0.5px;
              margin: 0.1rem 0;
              background: #F7F7F7;
            }
            // 上传区
            .recordPersonalStatement-upload-module{
              width: 100%;
              display: flex;
              .upload-items{
                width: 50%;
                display: flex;
                flex-wrap: wrap;
                :deep(.van-uploader__upload),:deep(.van-uploader__preview-image),:deep(.van-uploader__preview){
                  width: 0.9rem;
                  height: 0.9rem;
                  margin: 0;
                }
                .upload-items-title{
                  width: 100%;
                  font-size: 0.13rem;
                  color: #333;
                }
                .upload-items-hint{
                  width: 100%;
                  margin: 0.04rem 0 0.1rem;
                  font-size: 0.1rem;
                  color: #9A9A9A;
                }
                i{
                  color: #E26375;
                }
              }
            }
        }
      }
    }
  }
  .recordPersonalStatement-btn{
    width: 100%;
    position: absolute;
    bottom: 0.2rem;
    .recordPersonalStatement-btn-body{
      width: 3.2rem;
      height: 0.455rem;
      background: #4A86EE;
    }
  }
}
</style>
