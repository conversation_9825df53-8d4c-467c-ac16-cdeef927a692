<template>
  <div class="privacy-agreement-container">
    <div class="privacy-agreement-form scrollnone">
      <h3 v-if="showTitleFlag === '1'" class="h-[25px] max-h-[25px]">服务条款</h3>
      <h3 v-else-if="showTitleFlag === '2'" class="h-[25px] max-h-[25px]">隐私协议</h3>
      <div class="privacy-agreement-body">
        <div ref="privacyAgreemenModuleRef" class="privacy-agreement-module">
          <div v-if="showTitleFlag === '2'" v-html="studyTermObj.studyPrivacy"/>
          <div v-else-if="showTitleFlag === '1'" v-html="studyTermObj.studySLA"/>
        </div>
        <van-button
          v-if="saveTimes && pageLoadingEndFlag === 2"
          class="bg-forbidden text-white w-full border-none rounded-none h-[50px]"
        >我同意{{ saveTimes }}秒</van-button>
        <van-button
          v-else-if="pageLoadingEndFlag === 2"
          class="w-full border-none rounded-none h-[50px]"
          type="primary"
          @click="routerBack"
        >我同意</van-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from "vue";
import { useRoute } from "vue-router";
// import { getPrivacy, getServiceLevelAgreement } from "@/api/user";
// import { privacyAgreementInter } from '@/types/welcomeToJoinUs';

export default defineComponent({
  name: "PrivacyAgreementTermsOfService", /* 服务条款 隐私协议 */
  props: {
    closePage: {
      type: Function,
      default: () => {},
    },
  },
  setup(props) {
    const route = useRoute();
    // const router = useRouter();
    const state: any = reactive({
      studyTermObj: {
        privacyMustBeRead: false,
        privacyMustBeReadSeconds: 0,
        slaMustBeRead: false,
        slaMustBeReadSeconds: 0,
        studyPrivacy: "",
        studySLA: "",
        studyPrivacyIsRead: false,
        studySLAIsRead: false,
      },
      pageLoadingEndFlag: 0,
      saveTimes: 0,
      showTitleFlag: route.query.id as string,
      // privacyObj: "",
      // serviceLevelAgreementObj: "",
      // 是否阅读过
      readPrivacyAgreement: false,
      readTermsOfService: false,
      routerBack: () => {
        if (state.privacyAgreemenModuleRef?.scrollTop) {
          state.privacyAgreemenModuleRef.scrollTop = 0;
        }
        // 设为已读
        if (state.showTitleFlag === '2') {
          state.readPrivacyAgreement = true
        } else {
          state.readTermsOfService = true
        }
        props.closePage(state.showTitleFlag)
        // router.replace({
        //   path: route.query?.backUrl as string || '/welcomeToJoinUs',
        //   query: {
        //     backQuery: route.query?.backQuery || '',
        //     checked: '1',
        //   }
        // });
      },
      privacyAgreemenModuleRef: null,
      onLoad: () => { 
        state.pageLoadingEndFlag = 2
        // 先检查是否需要强制阅读
        let timer: any = null
        // 没有已读则 studySLAIsRead state.studyTermObj.studyPrivacyIsRead && 
        if (!state.studyTermObj.studySLAIsRead && !state.readTermsOfService && state.showTitleFlag === '1' && state.studyTermObj.slaMustBeRead && state.studyTermObj.slaMustBeReadSeconds) {
          // 服务条款倒计时
          state.saveTimes = state.studyTermObj.slaMustBeReadSeconds;
          timer && clearInterval(timer);
          timer = setInterval(() => {
            if (state.saveTimes > 0) {
              state.saveTimes--;
            }
            if (state.saveTimes === 0) {
              clearInterval(timer);
              timer = null;
            }
          }, 1000);
        } else if (!state.studyTermObj.studyPrivacyIsRead && !state.readPrivacyAgreement && state.showTitleFlag === '2' && state.studyTermObj.privacyMustBeRead && state.studyTermObj.privacyMustBeReadSeconds) {
          // 隐私协议倒计时
          state.saveTimes = state.studyTermObj.privacyMustBeReadSeconds;
          timer && clearInterval(timer);
          timer = setInterval(() => {
            if (state.saveTimes > 0) {
              state.saveTimes--;
            }
            if (state.saveTimes === 0) {
              clearInterval(timer);
              timer = null;
            }
          }, 1000);
        } else {
          state.saveTimes = 0;
        }
      }
    })

    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.privacy-agreement-container {
  position: absolute;
  height: 100vh;
  // top: 0;
  z-index: 9999;
  color: #555;
  font-size: 0.12rem;
  overflow: hidden;
  background: linear-gradient(180deg, #eaeffa 70%, #fff 100%);
  .privacy-agreement-form {
    h3 {
      width: 100%;
      margin: 20px 0 0 0;
      font-size: 0.16rem;
      color: var(--theme-color);
      text-align: center;
    }
    .privacy-agreement-body {
      width: 100%;
      height: calc(100vh - 115px);
      margin: 20px 0 0 0;
      box-sizing: border-box;
      .privacy-agreement-module {
        width: 100%;
        height: 100%;
        padding: 0.1rem;
        box-sizing: border-box;
        color: #333;
        overflow: auto;
        :deep(ul) {list-style-type:disc !important; list-style-position:inside !important;} 
        :deep(ol) {list-style-type:decimal !important; list-style-position:inside !important;}
      }
    }
  }
  .bg-forbidden {
    background: linear-gradient(180deg, #B7B6B6, #5C5A5A 100%);
  }
}
</style>
