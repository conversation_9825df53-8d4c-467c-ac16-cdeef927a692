<template>
  <div class="overflow-auto scrollnone">
    <van-nav-bar
      :title="vidTitle"
      left-text=""
      left-arrow
      @click-left="$routerBackFun()"
    />
    <!-- class="pb-03rem box-border" -->
    <MyVideoPlay
      v-if="videoObj?.videoUrl"
      :fileUrl="videoObj.videoUrl"
      :coverUrl="videoObj.videoCoverUrl"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount, } from 'vue';
import { useRoute } from 'vue-router';
import MyVideoPlay from "@/components/MyVideoPlay.vue";

export default defineComponent({
  name: 'VideoPlay', // 视频播放
  components: {
    MyVideoPlay
  },
  setup() {
    const route = useRoute();
    const state = reactive({
      vidTitle: '知情声明视频',
      videoObj: {
        videoCoverUrl: '',
        videoUrl: '',
      },
      // 播放时的回调
      // onPlayerEnded: () => {
      //   //
      // }
    })

    onBeforeMount(()=>{
      if (route?.query?.videoItem) {
        state.videoObj = JSON.parse(route.query.videoItem as string)
      }
      if (route?.query?.vidTitle)
        state.vidTitle = route.query.vidTitle as string
    })

    return {
      ...toRefs(state)
    }
  },
});
</script>

<style lang="less" scoped>
// 播放器样式 抬起0.3rem
:deep(.vjs-control-bar) {
  bottom: 0.3rem;
}
:deep(.vjs-tech) {
  height: calc(100% - 0.3rem);
}
</style>