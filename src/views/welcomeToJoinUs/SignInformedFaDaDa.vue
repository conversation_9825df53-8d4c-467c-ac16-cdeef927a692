<template>
  <div
    class="h-100-vh overflow-hidden font-color-555 ft-15-rem"
    :style="{ height: outerHeight ? outerHeight + 'px' : '100vh' }"
  >
    <van-nav-bar
      title="签署知情同意"
      left-arrow
      @click-left="$routerGoFun('replace', '/informedList')"
    />
    <div
      v-if="caSignatureStrategyNum !== -1"
      class="w-full h-100vh-46px bg-color-F5F5F5 overflow-auto scrollnone"
      :style="{ height: `calc(${outerHeight - 46}px - 0.51rem)` }"
    >
      <div
        v-if="statementInfoObj?.icfVersions?.length"
        class="mt-01-rem px-015rem pt-01rem box-border"
      >
        <div class="bg-color-fff px-015rem py-015rem radius-004rem">
          <div v-for="(e, idx) in statementInfoObj.icfVersions">
            <div class="mb-01rem wrap1">
              版本名称：{{ e?.icfVersionNumber }}
            </div>
            <div class="centerflex-h justify-between">
              <div
                :class="[
                  idx !== statementInfoObj.icfVersions.length - 1
                    ? 'mb-01rem'
                    : '',
                ]"
              >
                版本日期：{{ e?.icfVersionDate }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 第二 -->
      <div class="mb-02rem mt-01-rem px-015rem pt-01rem box-border">
        <div class="bg-color-fff px-015rem py-015rem radius-004rem">
          <div class="mb-02rem centerflex-h">
            <div
              v-if="!statementInfoObj.terminated"
              class="w-01rem h-01rem mr-01rem bg-color-5490EC radius-50"
            />
            <div
              v-else-if="statementInfoObj.terminated"
              class="w-01rem h-01rem mr-01rem bg-color-F42F3E radius-50"
            />
            <p v-if="!statementInfoObj.terminated">
              <span
                v-if="
                  statementInfoObj?.signTasks?.length &&
                  statementInfoObj.signTasks.some(
                    (item) => item?.signTaskFillStatus === 1
                  )
                "
                >填写中</span
              >
              <span v-else>签署中</span>
            </p>
            <p v-else-if="statementInfoObj.terminated">已终止</p>
          </div>
          <div
            v-if="
              caSignatureStrategyNum === 3 &&
              !statementInfoObj?.signTasks.every(
                (e) => e.signTaskFillStatus === 3
              )
            "
          >
            <div class="mb-015rem centerflex-h">
              <p>
                <span class="font-medium">填写</span>
              </p>
            </div>
            <div
              v-for="(item, index) in statementInfoObj.signTasks"
              :key="item.id"
            >
              <div
                class="mb-015rem centerflex-h justify-between"
                :class="{ 'mt-02-rem': index !== 0 }"
              >
                <p v-if="item.userType === 2" class="font-color-333">受试者</p>
                <p v-else-if="item.userType === 1" class="font-color-333">
                  研究者
                </p>
                <p v-else-if="item.userType === 3" class="font-color-333">
                  监护人
                </p>
                <p v-else-if="item.userType === 4" class="font-color-333">
                  公正见证人
                </p>
                <p
                  v-if="item.signTaskFillStatus === 1"
                  class="font-color-5490EC"
                >
                  待填写
                </p>
                <p
                  v-else-if="item.signTaskFillStatus === 2"
                  class="font-color-0DBE88"
                >
                  已填写
                </p>
              </div>
              <div class="centerflex-h font-color-666">
                <p>
                  完成时间：{{
                    item?.signTaskFillStatus === 1 ? "" : item?.fillUpdateTime
                  }}
                </p>
              </div>
            </div>
          </div>

          <div style="margin: 0.1rem 0; height: 0.5px; background: #e2e2e2" />
          <div class="mb-015rem centerflex-h">
            <p>
              <span class="font-medium">签署</span>
              <span class="font-color-666">（有序）</span>
            </p>
          </div>
          <div
            v-for="(item, index) in statementInfoObj.signTasks"
            :key="item.id"
          >
            <div
              class="mb-015rem centerflex-h justify-between"
              :class="{ 'mt-02-rem': index !== 0 }"
            >
              <p v-if="item.userType === 2" class="font-color-333">受试者</p>
              <p v-else-if="item.userType === 1" class="font-color-333">
                研究者
              </p>
              <p v-else-if="item.userType === 3" class="font-color-333">
                监护人
              </p>
              <p v-else-if="item.userType === 4" class="font-color-333">
                公正见证人
              </p>
              <p v-if="item.signTaskStatus === 3" class="font-color-F94040">
                已拒签
              </p>
              <p
                v-else-if="item.signTaskStatus === 1"
                class="font-color-5490EC"
              >
                待签署
              </p>
              <p
                v-else-if="item.signTaskStatus === 2"
                class="font-color-0DBE88"
              >
                已签署
              </p>
            </div>
            <div class="centerflex-h font-color-666">
              <p>
                完成时间：{{
                  item?.signTaskStatus === 1 ? "" : item?.signUpdateTime
                }}
              </p>
            </div>
            <div
              v-if="item?.remark"
              class="mt-015rem centerflex-h font-color-333 font-medium"
            >
              <p>拒签原因：{{ item.remark }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="flex">
      <van-button
        v-if="hasInteractiveCommunication"
        style="width: 50%"
        class="w-full ft-15-rem h-051rem rounded-none theme-color"
        @click="
          $routerGoFun(
            'routerInnPush',
            '/interactivecommunication',
            '',
            '/signInformedFaDaDa'
          )
        "
        >互动沟通</van-button
      >
      <!-- 研究已填写 statementInfoObj.signTasks.some(e => e.userType === 1 && e.signTaskFillStatus === 2) && 
        我受试者待填写 -->
      <van-button
        :style="{ width: hasInteractiveCommunication ? '50%' : '100%' }"
        v-if="
          statementInfoObj.signTasks?.length &&
          statementInfoObj.signTasks.some(
            (e) => e.userType === 2 && e.signTaskFillStatus === 1
          )
        "
        block
        :loading="submitLoadingFlag"
        loading-text="填写"
        @click="onSubmit(1)"
        class="w-full ft-15-rem h-051rem bg-color-5490EC font-color-fff"
      >
        填写
      </van-button>
      <!-- 4个人都已填写 && 我受试者待签署 -->

      <van-button
        :style="{ width: hasInteractiveCommunication ? '50%' : '100%' }"
        v-else-if="
          statementInfoObj.signTasks?.length &&
          caSignatureStrategyNum === 2 &&
          statementInfoObj.signTasks.some(
            (e) => e.userType === 2 && e.signTaskStatus === 1
          )
        "
        block
        :loading="submitLoadingFlag"
        loading-text="签署"
        @click="onSubmit(1)"
        class="w-1/2 ft-15-rem h-051rem bg-color-5490EC font-color-fff"
      >
        签署
      </van-button>
      <van-button
        :style="{ width: hasInteractiveCommunication ? '50%' : '100%' }"
        v-else-if="
          statementInfoObj.signTasks?.length &&
          (statementInfoObj.signTasks.every(
            (e) => e.signTaskFillStatus === 2
          ) ||
            statementInfoObj.signTasks.every(
              (e) => e.signTaskFillStatus === 3
            )) &&
          statementInfoObj.signTasks.some(
            (e) => e.userType === 2 && e.signTaskStatus === 1
          )
        "
        block
        :loading="submitLoadingFlag"
        loading-text="签署"
        @click="onSubmit(1)"
        class="w-1/2 ft-15-rem h-051rem bg-color-5490EC font-color-fff"
      >
        签署
      </van-button>
      <van-button
        :style="{ width: hasInteractiveCommunication ? '50%' : '100%' }"
        v-else
        block
        :loading="submitLoadingFlag"
        loading-text="查看详情"
        @click="onSubmit(2)"
        class="w-1/2 ft-15-rem h-051rem bg-color-5490EC font-color-fff"
      >
        查看详情
      </van-button>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  onBeforeMount,
  getCurrentInstance,
  watchEffect,
} from "vue";
import { useStore } from "vuex";
import { useRoute } from "vue-router";
import { returnPatientStatusUrl } from "@/utils/debounce";
import {
  getGetSignTask,
  getThirdActorSignTaskUrl,
  getThirdSignTaskDetailUrl,
  // getGetCASignatureStrategy
} from "@/api/informed";
import { Notify } from 'vant';
import { getPatientStatus } from "@/api/user";
import { getPatient } from "@/api/home";

export default defineComponent({
  name: "SignInformedFaDaDa", // 签署知情同意 法大大
  setup() {
    const proxy: any = getCurrentInstance()?.proxy;
    const store = useStore();
    const route = useRoute();
    const state: any = reactive({
      hasInteractiveCommunication: true,
      caSignatureStrategyNum: -1,
      outerHeight: window.outerHeight,
      submitLoadingFlag: false,
      // 知情版本信息
      statementInfoObj: {},
      // 获取状态的请求
      getPatientStatusFun: () => {
        const icfStatementIdData = {
          icfStatementID:
            store.state?.userInformation?.icfStatementID ||
            store.state?.userInformation?.icfStementId,
        }
        return new Promise(async (resolve, reject) => {
          try {
            const patientStatus: any = await getPatientStatus(icfStatementIdData)
            if (patientStatus?.patientICFStatus === 404) {
              Notify({ type: 'danger', message: '已转线下知情' })
              proxy.$routerGoFun('replace', returnPatientStatusUrl(patientStatus));
              return
            }
            resolve(1)
          } catch (e) {
            reject(e)
          }
        })
      },
      // 点击签署
      onSubmit: async (idx) => {
        await state.getPatientStatusFun();
        const params = {
          userType: 2,
          businessType: 6,
          code: store.state.patientCode,
          appKey: sessionStorage.getItem('appKey')
        }
        const icfStatementID = store.state?.userInformation?.icfStatementID || store.state?.userInformation?.icfStementId
        state.submitLoadingFlag = true;
        if (idx === 1) {
          getThirdActorSignTaskUrl(
            icfStatementID,
            params
          ).then((res: any) => {
            state.submitLoadingFlag = false;
            if (res) {
              window.location.replace(res)
            }
          }).catch(() => {
            state.submitLoadingFlag = false;
          })
        } else if (idx === 2) {
          getThirdSignTaskDetailUrl(
            icfStatementID,
            params
          ).then((res: any) => {
            state.submitLoadingFlag = false;
            if (res) {
              window.location.replace(res)
            }
          }).catch(() => {
            state.submitLoadingFlag = false;
          })
        }
      },
    });
    const handleResize = () => {
      // 处理 window.outerHeight 变化的逻辑
      state.outerHeight = window.outerHeight;
    };
    watchEffect(() => {
      window.addEventListener("resize", handleResize);
      // 在监听函数中返回一个清除函数，用于在组件销毁时取消监听
      return () => {
        window.removeEventListener("resize", handleResize);
      };
    });
    onBeforeMount(async () => {
      const { userInformation } = store.state
      if (route.query?.icfStatementId) {
        userInformation.icfStementId = route.query.icfStatementId
        userInformation.icfStatementID = route.query.icfStatementId
      }
      // state.caSignatureStrategyNum = await getGetCASignatureStrategy(userInformation?.icfStatementID || userInformation?.icfStementId) as number

      store.dispatch('setUserInformation', userInformation)
      const ICFStatementID = store.state?.userInformation?.icfStatementID ||
        store.state?.userInformation?.icfStementId
      getGetSignTask(ICFStatementID || route.query?.icfStatementId).then((res: any) => {
        state.caSignatureStrategyNum = res.signStrategy
        if (state.caSignatureStrategyNum <= 1) {
          // 4001= 待受试者签名,
          const path = res.icfStatus === 4001 ? '/signInformed' : '/audit'
          proxy.$routerGoFun('replace', path)
          return
        }
        // if (res?.signStrategy < 2) {
        //   const goUrl = returnPatientStatusUrl({patientICFStatus: res?.icfStatus})
        //   proxy.$routerGoFun('replace', goUrl)
        // }
        // 如果都签名完成还要在进行流转
        if (res?.signTasks?.length > 0) {
          res.terminated = res.signTasks.some(item => item?.signTaskStatus === 3)
          const haveSignedBol = res.signTasks.some(item => item?.signTaskStatus !== 1)
          if (haveSignedBol) {
            res.haveSigned = 1
          } else {
            res.haveSigned = 2
          }
        }
        state.statementInfoObj = res;
        handleResize();
      })
        .catch(handleResize);
      getPatient().then((rest) => {
        state.hasInteractiveCommunication = rest.hasInteractiveCommunication;
      })
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>
