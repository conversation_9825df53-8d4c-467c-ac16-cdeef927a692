<template>
  <div
    v-show="caSignatureStrategyNum !== -1"
    class="h-100-vh
    overflow-hidden
    font-color-555
    ft-16-rem"
    :style="{ height: outerHeight ? outerHeight + 'px' : '100vh' }"
  >
    <van-nav-bar
      title="签署知情同意"
      :left-arrow="statementMobileObj?.hasVideoStatement" 
      @click-left="$routerGoFun('replace', '/recordPersonalStatement')"
    />
    <div class="w-full h-100vh-46px-051rem bg-color-F5F5F5 overflow-auto scrollnone"
    :style="{height: `calc(${outerHeight}px - ${patientSignatureType === 3 ? 246 : 46 }px - ${patientSignatureType === 3 ? 0.94 : 0.51 }rem)`}">
      <div class="mt-01-rem px-015rem pt-01rem box-border">
        <div class="bg-color-fff px-015rem py-015rem radius-004rem">
          <div class="mb-01rem">版本名称：{{ statementInfoObj?.icfVersionNumber }}</div>
          <div class="centerflex-h justify-between" 
          @click="routerGo('/informedDetails')">
            <div>版本日期：{{ statementInfoObj?.icfVersionDateStr }}</div>
            <van-icon class="font-color-666-important" name="arrow" />
          </div>
        </div>
      </div>
      <div class="mt-06-rem mb-04-rem centerflex">
        <h3 class="w-full px-03rem font-color-333 font-normal ft-15-rem mb-01rem">
          <span v-if="patientSignatureType === 3">{{ signatureCfrStr }}</span> 
          <span v-else-if="patientSignatureType === 1">{{ cfrPartStr }}</span> 
        </h3>
      </div>
      <!-- 手机号 验证码 信息表单 -->
      <van-form v-if="patientSignatureType === 1">
        <!-- 手机号 验证登录 left-icon="phone-circle-o"-->
        <van-cell-group inset style="position: relative">
          <div class="mobile-inputs">
            <img src="@/assets/baby/mobileIcon.svg" alt="" />
            <van-field
              v-model.trim="form.mobile"
              placeholder="手机号"
              :readonly="disabledInput"
              :rules="[
                {
                  validator: (value) => {
                    return /^[1][3-9][0-9]{9}$/.test(value);
                  },
                  message: '请输入正确的手机号',
                  trigger: 'onBlur',
                },
              ]"
            />
          </div>
          <!-- 输入验证码 left-icon="comment-o"-->
          <div class="mobile-inputs">
            <img src="@/assets/baby/verifyCode.svg" alt="" />
            <van-field
              v-model.trim="form.verifyCode"
              placeholder="验证码"
              maxlength="6"
              :rules="[{ required: true, message: '请填写验证码' }]"
            />
          </div>

          <div class="absolute t-012rem r-024rem ft-14-rem font-color-429AF8 centerflex-h" @click="getVerifycode">
            |&nbsp;&nbsp;{{ verifyTime > 59 ? "获取验证码" : verifyTime }}
          </div>
        </van-cell-group>
      </van-form>
    </div>
    <!-- 手写板 -->
    <div v-if="patientSignatureType === 3">
      <div class="p-01rem h-04rem box-border ft-12-rem text-center font-color-F4A22E bg-color-fff">请正楷签署您的姓名，避免错别字或过于潦草</div>
      <div v-if="outerWidth" class="relative" @touchstart="touchstartFlag = false">
        <canvas ref="signatureCanvas" :width="outerWidth" height="200"></canvas>
        <span v-if="touchstartFlag" class="absolute-center ft-24-rem font-color-999 z--9">请 在 此 处 签 名</span>
      </div>
      <div class="flex">
        <van-button
          block
          @click="clearSignature"
          class="h-051rem ft-15-rem bg-color-fff font-color-333">
          重写
        </van-button>
        <van-button
          block
          :loading="submitLoadingFlag"
          loading-text="签名确认"
          @click="onSubmit"
          class="h-051rem ft-15-rem bg-color-5490EC font-color-fff">
          签名确认
        </van-button>
      </div>
    </div>
    <van-button
      v-else
      block
      :loading="submitLoadingFlag"
      loading-text="签名确认"
      @click="onSubmit"
      class="h-051rem ft-15-rem bg-color-5490EC font-color-fff">
      签名确认
    </van-button>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount, getCurrentInstance, nextTick, watchEffect } from 'vue';
import { Notify, Toast } from 'vant';
import { getPatientStatus, getStatementMobile, postStatementSentVerifyCode, postStatementVerifyCode, } from "@/api/user";
import { useStore } from "vuex";
import { statementMobileObjInter, postStatementVerifyCodeInter } from '@/types/welcomeToJoinUs';
import { useRoute } from 'vue-router';
import SignaturePad from "signature_pad";
import { returnPatientStatusUrl } from '@/utils/debounce'
import { postSignatureFile, postStatementHandwrittenSignature, getStatementInfo, getGetCASignatureStrategy } from '@/api/informed';
import { baseFileObjInter } from '@/types/welcomeToJoinUs';

export default defineComponent({
  name: "SignInformed", // 签署电子知情同意
  setup() {
    const proxy: any = getCurrentInstance()?.proxy
    const store = useStore()
    const route = useRoute()
    const myreg = /^[1][3-9][0-9]{9}$/;
    const state: any = reactive({
      patientSignatureType: 1, // 1短信验证 2 验证码 3手写
      touchstartFlag: true,
      signaturePad: null,
      signatureCanvas: null,
      outerWidth: window.outerWidth,
      outerHeight: window.outerHeight,
      cfrPartStr: `本系统电子签名采用手机短信验证方式，符合21 CFR Part 11 标准，本次签名将仅用于签署电子知情同意。`,
      signatureCfrStr: `本系统电子签名采用手写签名方式，符合 21 CFR Part 11标准，本次签名将仅用于签署电子知情同意。`,
      verifyTime: 60,
      form: {
        mobile: "",
        verifyCode: "",
      },
      disabledInput: true, // 默认禁用
      statementMobileObj: {
        patientPhone: '',
        hasVideoStatement: true
      },
      submitLoadingFlag: false,
      // 知情版本信息
      statementInfoObj: {},
      caSignatureStrategyNum: -1,
      // 清空手写板->重写
      clearSignature: () => {
        state.touchstartFlag = true
        state.signaturePad.clear();
      },
      // 获取验证码
      getVerifycode: () => {
        if (state.verifyTime / 1 < 60) return;
        const { mobile } = state.form;
        if (!myreg.test(mobile) || !mobile) {
          Notify({ type: "danger", message: "请输入正确的手机号" });
          return;
        }
        state.verifyTime = 59
        postStatementSentVerifyCode({
          mobile: state.form.mobile,
        })
        const TIMEOUT = setInterval(() => {
            state.verifyTime--;
            if (state.verifyTime / 1 === 0) {
              state.verifyTime = 60;
              clearInterval(TIMEOUT);
            }
          }, 1000);
      },
      routerGo: (path: string) => {
        const query: any = {}
        if (path === '/informedDetails') {
          query.ICFStatementId = store.state?.userInformation?.icfStatementID || store.state?.userInformation?.icfStementId
        }
        proxy.$routerGoFun('routerInnPush', path, query, route.path, route.query)
      },
      // 获取状态的请求
      getPatientStatusFun: () => {
        const icfStatementIdData = {
          icfStatementID:
            store.state?.userInformation?.icfStatementID ||
            store.state?.userInformation?.icfStementId,
        }
        return new Promise(async(resolve, reject) => {
          try {
            const patientStatus: any = await getPatientStatus(icfStatementIdData)
            if (patientStatus?.patientICFStatus === 404) {
              Notify({ type: 'danger', message: '已转线下知情' })
              proxy.$routerGoFun('replace',returnPatientStatusUrl(patientStatus));
              return
            }
            resolve(1)
          } catch (e) {
            reject(e)
          }
        })
      },
      // 点击确定
      onSubmit: async() => {
        await state.getPatientStatusFun() 
        if (state.patientSignatureType === 1) {
          const { mobile, verifyCode } = state.form;
          state.submitLoadingFlag = true
          state.commonSubmit(postStatementVerifyCode({
            mobile,
            verifyCode,
            icfStatementID: store.state?.userInformation?.icfStatementID || store.state?.userInformation?.icfStementId
          }))
        } else if (state.patientSignatureType === 3) {
          // 获取签名的Base64数据
          const base64File = state.signaturePad.toDataURL();
          // 获取是否为空板 true = 未填写，false 已填写。
          if (!state.signaturePad.isEmpty()) {
            // const blob = new Blob([dataURL], { type: 'image/png' })
            // const url = URL.createObjectURL(blob)
            state.submitLoadingFlag = true
            postSignatureFile({base64File})
            .then((baseRest) => {
              const baseRes = baseRest as baseFileObjInter
              state.commonSubmit(postStatementHandwrittenSignature({
                "signaturePath_Patient": baseRes.relativePath,
                "signatureUrl_Patient": baseRes.fileUrl,
                icfStatementID: store.state?.userInformation?.icfStatementID || store.state?.userInformation?.icfStementId
              }))
            }).catch(() => {state.submitLoadingFlag = false})
          } else {
            Toast('请完成手写签名')
          }
        }
      },
      commonSubmit: (promise) => {
        promise.then((rest) => {
          const res = rest as postStatementVerifyCodeInter
          if (res?.patientICFStatus === 4003 || res?.patientICFStatus === 4007) {
            store.dispatch('setAuditFlag',false)
          }
          Notify({ type: "success", message: "签名成功" });
          proxy.$routerGoFun('replace', returnPatientStatusUrl(res))
          state.submitLoadingFlag = false
        }).catch(() => {state.submitLoadingFlag = false})
      },
    });
    const handleResize = () => {
      // 处理 window.outerHeight 变化的逻辑
      state.outerHeight = window.outerHeight
      state.outerWidth = window.outerWidth
    }
    watchEffect(() => {
      window.addEventListener('resize', handleResize)
      // 在监听函数中返回一个清除函数，用于在组件销毁时取消监听
      return () => {
        window.removeEventListener('resize', handleResize)
      }
    })
    onBeforeMount(async () => {
      try {
        state.caSignatureStrategyNum = await getGetCASignatureStrategy(store.state?.userInformation?.icfStatementID || store.state?.userInformation?.icfStementId) as number
        if (state.caSignatureStrategyNum >= 2) {
          proxy.$routerGoFun('replace', '/signInformedFaDaDa')
          return
        }
        const params = { 
          ICFStatementId: store.state?.userInformation?.icfStatementID || store.state?.userInformation?.icfStementId
        }
        if (store.state?.userInformation?.dctStudyId) {
          getStatementMobile(params).then((rest) => {
            const res = rest as statementMobileObjInter
            state.statementMobileObj = res
            state.form.mobile = res?.patientPhone || ''
            state.disabledInput = res?.patientPhone?.length === 11 || false
            state.patientSignatureType = res?.patientSignatureType || 0
            if (res?.signatureStrategy === 2) {
              proxy.$routerGoFun('replace', '/signInformedFaDaDa')
            }
            nextTick(() => {
              const canvas = state.signatureCanvas;   
              if (canvas && state.patientSignatureType === 3) {
                state.signaturePad = new SignaturePad(canvas, {
                  minWidth: 3,
                  maxWidth: 4,
                  // penColor: "rgb(66, 133, 244)"
                });
              }
            })
          })
        }
        // else {
        //   Toast('无dctStudyId')
        // }
        getStatementInfo(params).then((res: any) => {
          state.statementInfoObj = res
          handleResize()
        }).catch(handleResize)
      } catch {
        //
      }
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.van-cell-group {
  background: transparent;
}
.van-cell {
  background: #fff;
}
.mobile-inputs {
  display: flex;
  align-items: center;
  background: #fff;
  height: 0.455rem;
  border-radius: 0.1rem;
  overflow: hidden;
  margin: 0 0 0.16rem 0;
  img {
    width: 0.14rem;
    margin: 0 0 0 0.16rem;
  }
}
</style>
