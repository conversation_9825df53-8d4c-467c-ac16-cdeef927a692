<template>
  <div class="w-full font-color-333 ft-13-rem bg-color-f7f7f7" >
    <van-nav-bar title="知情同意书" />
    <CommonQuestion
    ref="InformedQuestionnaireRef"
    myQuestType="知情问卷"
    :postQuestFile="postQuestFile"
    :deleteQuestFile="deleteQuestFile"
    :getQuestView="getQuestICFGeneralInfo"
    :getQuestData="getQuestData"
    :getTableList="getTableList"
    :deleteTable="deleteTable"
    :putQuestData="putICFGeneralInfo"
    :postICFGeneralInfoTime="postICFGeneralInfoTime"
    :questionOnload="questionOnload"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, nextTick } from "vue";
import {
  postQuestFile,
  deleteQuestFile,
  getQuestData,
  getTableList,
  deleteTable
} from "@/api/questionnaire";
import { getQuestICFGeneralInfo, putICFGeneralInfo, postICFGeneralInfoTime } from "@/api/informed";
import { useStore } from "vuex";
import CommonQuestion from "@/components/CommonQuestion.vue";
import { commonQuestionnaireInter } from '@/types/welcomeToJoinUs';

export default defineComponent({
  name: "InformedQuestionnaire", // 知情问卷
  components: {
    CommonQuestion
  },
  setup() {
    const store = useStore();
    const state = reactive({
      // All接口
      postQuestFile,
      deleteQuestFile,
      getQuestData,
      getTableList,
      deleteTable,
      getQuestICFGeneralInfo, 
      putICFGeneralInfo,
      postICFGeneralInfoTime,
      InformedQuestionnaireRef: null,
      questionOnload: () => {
        nextTick(() => {
          if (state?.InformedQuestionnaireRef) {
            const QuestionnaireRef = state.InformedQuestionnaireRef as commonQuestionnaireInter
            QuestionnaireRef.editable = '1'
            if (store.state?.getOldQuestDataFlag && store.state?.oldQuestData) {
              QuestionnaireRef.questionObj = store.state.oldQuestData
              QuestionnaireRef.questionObjLength = store.state.oldQuestData.questCrfItemes.length;
              QuestionnaireRef.fzFun(QuestionnaireRef.questionObj,store.state.initialQuestionObj)
              QuestionnaireRef.upListData()
              if (store?.state?.oldQuestDataIndex && QuestionnaireRef.questionObj?.questCrfType === 2) {
                QuestionnaireRef.questionIndex = store.state.oldQuestDataIndex
              }
              store.dispatch('setGetOldQuestDataFlag',0)
            } else {
              QuestionnaireRef.getQuestionnaiData();
            }
          }
        })
      }
    });
    
    return {
      ...toRefs(state),
    };
  },
});
</script>
