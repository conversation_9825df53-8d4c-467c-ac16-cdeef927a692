<template>
  <div class="waitInformed-container">
    <div class="waitInformed-form scrollnone">
      <div class="waitInformed-body">
        <div class="waitInformed-body-center">
          <div class="waitInformed-body-h">
          <div>研究人员会尽快联系您并解答您的疑问</div>
          <div>请保持手机通畅～</div>
        </div>
        <img src="@/assets/baby/waitInformedIcon.svg" alt="">
        </div>
      </div>
      <div class="waitInformed-btn">
        <van-button
          class="waitInformed-btn-body"
          round
          type="primary"
          @click="$routerGoFun('replace','/informed')"
          >查看知情同意书</van-button
        >
      </div>       
    </div>
  </div>
</template>

<script setup lang="ts">

</script>

<style scoped lang='less'>
.waitInformed-container {
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(180deg, #eaeffa 70%, #fff 100%);
  font-size: 0.12rem;
  .waitInformed-form {
    position: relative;
    height: 100%;
    .waitInformed-body{
      width: 100%;
      height: 3.425rem;
      padding: 1rem 0 0 0;
      // box-sizing: border-box;
      display: flex;
      justify-content: center;
      font-size: 0.165rem;
      font-weight: 700;
      color: var(--theme-color);
      .waitInformed-body-center{
        width: 3.3rem;
        height: 100%;
        padding: 0.4rem 0 0 0;
        box-sizing: border-box;
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        border-radius: 0.06rem;
        background: #fff;
        .waitInformed-body-h{
          text-align: center;
          max-height: 0.8rem;
        }
        img{
          width: 1.27rem;
          height: 1.06rem;
        }
      }
    }
    .waitInformed-btn{
      width: 100%;
      display: flex;
      justify-content: center;
      position: absolute;
      bottom: 0.2rem;
      .waitInformed-btn-body{
        width: 3.2rem;
        height: 0.455rem;
        background: #4A86EE;
      }
    }
  }
}
</style>
