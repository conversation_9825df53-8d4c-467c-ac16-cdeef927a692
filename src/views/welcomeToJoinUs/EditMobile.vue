<template>
  <div class="editmobile-container">
    <van-nav-bar
      title="修改信息"
      left-text=""
      left-arrow
      @click-left="$routerBackFun"
    />
    <div class="editmobile-form scrollnone">
      <div class="editmobile-title">
        <h3>修改{{ !isFamilyMember ? "受试者" : "" }}手机号</h3>
      </div>
      <!-- 手机号 验证码 信息表单 -->
      <van-form @submit="onSubmit" class="form-modules">
        <!-- 手机号 验证登录 left-icon="phone-circle-o"-->
        <van-cell-group inset style="position: relative">
          <div class="mobile-inputs">
            <img src="@/assets/baby/mobileIcon.svg" alt="" />
            <van-field
              v-model.trim="form.mobile"
              placeholder="手机号"
              :rules="[
                {
                  // 自定义校验规则
                  validator: (value) => {
                    return /^[1][3-9][0-9]{9}$/.test(value);
                  },
                  message: '请输入正确的手机号',
                  trigger: 'onBlur',
                },
              ]"
            />
          </div>
          <!-- 输入验证码 left-icon="comment-o"-->
          <div class="mobile-inputs">
            <img src="@/assets/baby/verifyCode.svg" alt="" />
            <van-field
              v-model.trim="form.verifyCode"
              placeholder="验证码"
              :rules="[{ required: true, message: '请写填验证码' }]"
            />
          </div>

          <div class="getverify" @click="getVerifycode">
            |&nbsp;&nbsp;{{ verifyTime > 59 ? "获取验证码" : verifyTime }}
          </div>
        </van-cell-group>

        <div style="margin: 16px">
          <van-button
            round
            block
            :loading="submitLoadingFlag"
            loading-text="确认修改"
            type="primary"
            native-type="submit"
          >
            确认修改
          </van-button>
        </div>
      </van-form>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, getCurrentInstance } from "vue";
import { Notify } from "vant";
import { postSentVerifyCode, postVerifyCodeModifyMobile } from "@/api/user";
import { useStore } from "vuex";

export default defineComponent({
  name: "EditMobile", // 修改信息
  setup() {
    const proxy: any = getCurrentInstance()?.proxy
    const myreg = /^[1][3-9][0-9]{9}$/;
    const store = useStore()
    const { isFamilyMember } = store.state.userInformation
    
    const state = reactive({
      verifyTime: 60,
      form: {
        mobile: "",
        verifyCode: "",
      },
      submitLoadingFlag: false,
      // 获取验证码
      getVerifycode: () => {
        if (state.verifyTime / 1 < 60) return;
        const { mobile } = state.form;
        if (!myreg.test(mobile) || !mobile) {
          Notify({ type: "danger", message: "请输入正确的手机号" });
          return;
        }
        state.verifyTime = 59
        postSentVerifyCode({
          mobile: state.form.mobile,
        })
        const TIMEOUT = setInterval(() => {
          state.verifyTime--;
          if (state.verifyTime / 1 === 0) {
            state.verifyTime = 60;
            clearInterval(TIMEOUT);
          }
        }, 1000);
      },
      // 点击确认
      onSubmit: () => {
        state.submitLoadingFlag = true
        const { mobile, verifyCode } = state.form;
        postVerifyCodeModifyMobile({
          mobile,
          verifyCode,
        }).then(() => {
          Notify({ type: "success", message: "修改成功" });
          proxy.$routerBackFun()
          state.submitLoadingFlag = false
        }).catch(() => { state.submitLoadingFlag = false })
      },
    });
    return {
      isFamilyMember,
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.editmobile-container {
  height: 100vh;
  overflow: hidden;
  background: #fff;
  color: #555;
  font-size: 0.16rem;
  .editmobile-form {
    height: 92%;
    overflow: auto;
    .editmobile-title {
      margin: 0.8rem 0 0.4rem 0;
      h3 {
        width: 100%;
        text-align: center;
        font-weight: 300;
        color: rgba(62, 62, 62, 1);
        font-size: 0.17rem;
        font-weight: 700;
        margin: 0 0 0.1rem 0;
      }
    }
    .form-modules {
      .mobile-inputs {
        display: flex;
        align-items: center;
        background: #f7f7f7;
        height: 0.44rem;
        border-radius: 1rem;
        overflow: hidden;
        margin: 0 0 0.16rem 0;
        .van-cell::after {
          border: none;
        }
        img {
          width: 0.14rem;
          margin: 0 0 0 0.1rem;
        }
      }
      .van-cell {
        background: #f7f7f7;
      }
      .getverify {
        position: absolute;
        font-size: 0.14rem;
        right: 0.24rem;
        top: 0.12rem;
        color: var(--theme-color);
        display: flex;
        align-items: center;
      }
    }
  }
}
</style>
