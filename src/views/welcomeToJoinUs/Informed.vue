<template>
  <div
    class="Informed-container" 
    :class="{ 'Informed-pdf-container': IcfObj?.studyICFUrl }"
    :style="{ height: outerHeight ? outerHeight + 'px' : '100vh' }"
  >
    <div class="Informed-form">
      <van-nav-bar
        title="知情同意"
        :left-arrow="IcfObj?.goBack > 0"
        @click-left="$routerGoFun('replace','/informedVideo',{
          isAuthorization: route.query?.isAuthorization
        })"
      />
      <!-- 50(协议) - 64(按钮) - 46(title) - 30（按钮距离底部） -->
      <div
        class="Informed-body"
        :class="{ 'Informed-pdf-body': IcfObj?.studyICFUrl,'Informed-body-ch' : IcfObj?.studyICF }"
      >
        <div
          class="Informed-module scrollnone" 
          :class="{ 'Informed-pdf-module': IcfObj?.studyICFUrl }"
          :style="{ height: `calc(${outerHeight}px - ${icfIdx === IcfList.length -1 ? '1.08rem' : '0.78rem'} - 46px)`}"
        >
          <PdfOpen v-if="IcfObj?.studyICFUrl" :myPdfurl="IcfObj.studyICFUrl"/>
          <div v-else-if="IcfObj?.studyICF" v-html="IcfObj.studyICF" />
        </div>
        <!-- :style="{ 'opacity': icfIdx === IcfList.length -1 ? 1 : 0 }" -->
        <div
          v-if="icfIdx === IcfList.length -1"
          class="clause"
        >
          <input type="checkbox" v-model="informedChecked" />
          <span>我已阅读上述知情同意内容</span>
        </div>
        <!-- 底部按钮all -->
        <div v-if="recCleRecorderEndFlag && rec?.start" class="flex">
          <div
            v-if="icfIdx !== 0"
            class="w-full text-white h-078rem bg-[#A2C2F7]"
            @click.stop="() => {
              if (nextInformedCheckedFlag) {return}
              nextInformedChecked(-1)
            }"
          >
            <div class="pb-01rem pt-018rem box-border text-center w-full ft-14-rem">上一份</div>
            <div class="w-full centerflex">
              <div class="pb-01rem box-border max-w-[1.2rem] ft-10-rem px-02rem truncate">{{ IcfList[icfIdx-1]?.versionName }}</div>
            </div>
          </div>
          <!-- 无知情签署 则不显示提问按钮 -->
          <div
            v-if="IcfObj?.studyICFUrl && flowType === 2"
            class="w-full text-white h-078rem tw-bg"
          >
            <div
              class="pt-018rem pb-005rem box-border text-center w-full ft-14-rem"
              @touchstart.stop="recOpen"
              @touchmove.stop="recMove"
              @touchend.stop="recStop"
            >提问</div>
            <div class="pb-01rem box-border mt-005rem text-center w-full ft-10-rem">按住说话</div>
            <div
              v-show="startFlag"
              class="guanbiyuyin-icon absolute w-042rem text-white bg-4D4C4C h-042rem text-center"
              style="
                font-size: 0.35rem;
                border-radius: 50%;
                left: 0px;
                bottom: 21%;
                left: 50%;
                z-index: 9999;
                transform: translateX(-50%)
              "
              :class="{ 'bg-color-fff text-4D4C4C': isActive }"
              @click="recStopClick"
            /> 
          </div>

          <div
            v-if="IcfList.length && icfIdx !== IcfList.length -1"
            class="w-full text-white h-078rem theme-bg-btn-color"
            @click.stop="() => {
              if (informedTimeout > 0 || nextInformedCheckedFlag) {return}
              nextInformedChecked(1)
            }"
          >
            <div class="pb-01rem pt-018rem box-border text-center w-full ft-14-rem">下一份
              <span v-if="informedTimeout && informedTimeoutText">{{
              informedTimeoutText
              }}</span>
            </div>
            <div class="w-full centerflex">
              <div class="max-w-[1.2rem] pb-01rem box-border px-02rem ft-10-rem truncate">
                <span>{{ IcfList[icfIdx+1].versionName }}</span>
              </div>
            </div>
          </div>
          <!-- 无需签署的是 下一步按钮" -->
          <div
            v-else
            class="text-white centerflex-wrap w-full h-078rem rounded-none theme-bg-btn-color"
            :disabled="informedTimeout > 0 || nextInformedCheckedFlag"
            @click.stop="() => {
              if (informedTimeout > 0 || nextInformedCheckedFlag) {return}
              informedCheckedSave()
            }"
          >
            {{ flowType !== 2 ? '下一步' : '确定' }}
            <span v-if="informedTimeout && informedTimeoutText" class="py-01rem text-center box-border">{{
              informedTimeoutText
            }}</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 自写弹出 -->
    <MyPopupShow 
      :myPopupShow="myPopupShow"
      title="提示"
      texts="是否需要研究人员为您讲解知情同意书内容？"
      cancelText="需讲解"
      saveText="无需讲解"
      :handleSave="handleSave"
      :handleCancel="handleCancel"
    />
    <!-- 语音弹出窗 -->
    <div
      v-if="startFlag"
      class="absolute w-100vw h-100vh flex items-end"
      style="background-color: rgba(0,0,0,0.5);
      left: 0px;
      top: 0;
      z-index: 999;"
    >
      <div class="w-full centerflex-w-wrap">
        <div
          class="w-full bg-yuyin"
          style="height: 1.355rem;"
        />
      </div>
    </div>
    <div
      v-show="saveEndFlag"
      class="text-center centerfle-h py-[0.15rem] px-[0.1rem] w-[2.4rem] fixed ft-14-rem bg-black text-white"
      style="top: 50%;
      left: 50%;transform: translate3d(-50%, -50%, 0);
      border-radius: 0.06rem;"
    >
      <div>已提交，后续会有研究人员为您解答，</div>
      <div>您可以继续观看知情同意书</div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  onBeforeMount,
  nextTick,
  onMounted,
  onBeforeUnmount
} from "vue";
import { useRouter, useRoute } from "vue-router";
import { getIcf } from "@/api/user";
import { Toast, Notify } from "vant";
import { formatSeconds, deepClone } from "@trialdata/common-fun-css/index";
import PdfOpen from '@/components/PdfOpen.vue'
import MyPopupShow from "@/components/MyPopupShow.vue";
import { postStudyIcfFinishjInter } from '@/types/welcomeToJoinUs';
import { returnPatientStatusUrl } from '@/utils/debounce';
import { useStore } from 'vuex';
import {
  postStudyIcfStartTime,
  postStudyIcfEndTime,
  postStudyIcfFinish,
  postStatementFile
} from '@/api/informed';
import { getPatientStatus } from '@/api/user';
// 必须引入的核心
import Recorder from "recorder-core";
import "recorder-core/src/engine/mp3";
import "recorder-core/src/engine/mp3-engine";
// 可选的插件支持项，这个是波形可视化插件
// import "recorder-core/src/extensions/waveview";

export default defineComponent({
  name: "Informed", // 知情同意书
  components: {
    PdfOpen,
    MyPopupShow
  },
  setup() {
    const router = useRouter();
    const route = useRoute();
    const store = useStore();
    const icfStatementIdData = { 
      icfStatementID: store.state?.userInformation?.icfStatementID || store.state?.userInformation?.icfStementId
    }
    const state = reactive({
      // 是否结束
      isEnd: true,
      // 是否授权
      isAuthorization: false,
      // 打开语音
      startFlag: false,
      // x移入时
      isActive: false,
      hasPermissionText: "",
      moveText: "",
      // 录音相关
      // recwave: null,
      rec: null,
      // wave: null,
      recCleRecorderEndFlag: 0,
      // 移入
      recMove:(e) => {
        const clientX = e.changedTouches[0].clientX
        const clientY = e.changedTouches[0].clientY
        const x = window.outerWidth / 2
        const y = window.outerHeight - window.outerHeight * 0.21
        const distanceFromCenterX = Math.abs(clientX - x);
        const distanceFromCenterY = clientY - y;
        // console.log(x , distanceFromCenterX, y, clientY, distanceFromCenterY);
        if (distanceFromCenterX <= 25 && distanceFromCenterY <= 0  ) {
          state.isActive = true
        } else {
          state.isActive = false
        }
      },
      // 创建录音对象
      recCleRecorder: () => {
        /*
        type: 'wav', // 设置音频格式为 WAV
        bitRate: 1411.2, // 设置比特率为 1411.2 kbps（CD 音质）
        sampleRate: 44100, // 设置采样率为 44100 Hz
        bitDepth: 16, // 设置位深度为 16 bit
        channels: 2, // 设置声道为立体声
        */
        state.rec = Recorder({
          type: "mp3", //录音格式，可以换成wav等其他格式
          sampleRate: 16000, //录音的采样率，越大细节越丰富越细腻
          bitRate: 16, //录音的比特率，越大音质越好
          onProcess: (
            buffers,
            powerLevel,
            bufferDuration,
            bufferSampleRate,
            newBufferIdx,
            asyncEnd
          ) => {
            if (bufferDuration && bufferDuration / 1000 >= 59) {
              state.recStop();
              // state.text = "录音时长"+ bufferDuration;
            }
          },
        });
      },
      recCle: () => {
        state.rec.open(
          () => {
            // if (state.recwave) {
            //   // 创建音频可视化图形绘制对象
            //   state.wave = Recorder.WaveView({ elem: state.recwave });
            // }
            // state.hasPermissionText += '创建'
            if (state.isAuthorization) {
              state.recStart();
            } else {
              state.isAuthorization = true
            }
          },
          (msg, isUserNotAllow) => {
            // 用户拒绝了录音权限，或者浏览器不支持录音
            // state.hasPermissionText += '-拒绝了录音权限'
            // state.isAuthorization = false
          }
        );
      },
      // 按下提问
      recOpen: async(e) => {
        await state.getPatientStatusFun();
        // 获取是否授权
        if (!state.isAuthorization && navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
          navigator.mediaDevices
            .getUserMedia({ audio: true })
            .then((stream) => {
              state.isAuthorization = true
              // console.log('state.isAuthorization',state.isAuthorization);
            })
            .catch((error) => {
              state.isAuthorization = false
              // console.log('state.isAuthorization',state.isAuthorization);
            });
        }
        if (state.isAuthorization && state.isEnd) {
          state.isEnd = false
          state.recCle()
        }
        // 判断是否有权限
        // if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        //   navigator.mediaDevices
        //     .getUserMedia({ audio: true })
        //     .then((stream) => {
        //       state.hasPermissionText += '-有权限'
        //       // state.recStart();
        //       // 授权和开启录音要一起执行
        //       setTimeout(() => {
        //         state.recStart();
        //       }, 1000);
        //     })
        //     .catch((error) => {
        //       // 打开录音，获得权限
        //       state.recCle()
        //       state.hasPermissionText += '无权限'
        //       // console.error("获取麦克风权限失败:", error);
        //     });
        // }
      },
      // 开始录音
      recStart: () => {
        if (!state.rec) {
          // state.hasPermissionText += '-未打开录音'
          return;
        }
        if (state.rec.start) {
          state.rec.start();
          state.startFlag = true
        }
        //  else {
        //   state.hasPermissionText += '-打失败开录音'+state.rec
        // }
      },
      recStopClick: () => {
        state.startFlag = false
        if (!state.rec) {
          // console.error("未打开录音");
          state.isEnd = true
          return;
        }
        state.isActive = true
        state.rec.stop(
          (blob, duration) => {
            // 简单利用URL生成本地文件地址，此地址只能本地使用，比如赋值给audio.src进行播放，赋值给a.href然后a.click()进行下载（a需提供download="xxx.mp3"属性）
            // var localUrl = (window.URL || webkitURL).createObjectURL(blob);
            // console.log("录音成功", blob, localUrl, "时长:" + duration + "ms");
            state.rec.close(); // 关闭录音，释放录音资源，当然可以不释放，后面可以连续调用start
            state.rec = null;
            state.recCleRecorder();
            state.isActive = false
            state.isEnd = true
          },
          (err) => {
            // console.error("结束录音出错：" + err);
            state.rec.close(); //关闭录音，释放录音资源，当然可以不释放，后面可以连续调用start
            state.rec = null;
            state.recCleRecorder();
            state.isEnd = true
            state.isActive = false
          }
        );
      },
      // 停止录音
      recStop: (e) => {
        state.startFlag = false
        let clientX = e?.changedTouches ? e.changedTouches[0].clientX : 0
        let clientY = e?.changedTouches ? e.changedTouches[0].clientY : 0
        let x = window.outerWidth / 2
        let y = window.outerHeight - window.outerHeight * 0.21
        let distanceFromCenterX = Math.abs(clientX - x);
        let distanceFromCenterY = clientY - y;
        // x , distanceFromCenterX, 
        // console.log(y, clientY, distanceFromCenterY);
        if (!state.rec) {
          // console.error("未打开录音");
          state.isEnd = true
          return;
        }
        // state.text = "松开2";
        state.rec.stop(
          (blob, duration) => {
            // 简单利用URL生成本地文件地址，此地址只能本地使用，比如赋值给audio.src进行播放，赋值给a.href然后a.click()进行下载（a需提供download="xxx.mp3"属性）
            // var localUrl = (window.URL || webkitURL).createObjectURL(blob);
            // console.log("录音成功", blob, localUrl, "时长:" + duration + "ms");
            state.rec.close(); // 关闭录音，释放录音资源，当然可以不释放，后面可以连续调用start
            state.rec = null;
            state.recCleRecorder();
            if (distanceFromCenterX <= 25 && distanceFromCenterY <= 0) {
              state.isActive = false
              state.isEnd = true
              return
            }
            // state.text = "录音时长" + duration;
            const intDuration = duration / 1000
            if (intDuration < 60 && intDuration >= 1) {
              // blob就是我们要的录音文件对象，可以上传，或者本地播放
              state.recBlob = blob;
              state.upload(blob,intDuration); // 把blob文件上传到服务器
            } else if (intDuration < 1) {
              Toast.fail("录音时间太短");
            }
            state.isEnd = true
          },
          (err) => {
            // console.error("结束录音出错：" + err);
            state.rec.close(); //关闭录音，释放录音资源，当然可以不释放，后面可以连续调用start
            state.rec = null;
            state.recCleRecorder();
            state.isEnd = true
          }
        );
      },
      saveEndFlag: false,
      // 上传
      upload(blob,intDuration) {
        postStatementFile(icfStatementIdData.icfStatementID,state.IcfObj.patientICFVersionFileId,parseInt(intDuration),blob).then(() => {
          // Toast.success('已提交，后续会有研究人员为您解答，您可以继续观看视频');
          state.saveEndFlag = true
          setTimeout(() => {
            state.saveEndFlag = false
          }, 3000);
        })
      },
      visibilitychange: () => {
        if (document.hidden) { // 当页面被切换至后台时
          state.startFlag = false
          if (!state.rec) {
            state.isEnd = true
            return;
          }
          state.rec.stop(
            (blob, duration) => {
              // 简单利用URL生成本地文件地址，此地址只能本地使用，比如赋值给audio.src进行播放，赋值给a.href然后a.click()进行下载（a需提供download="xxx.mp3"属性）
              // var localUrl = (window.URL || webkitURL).createObjectURL(blob);
              // console.log("录音成功", blob, localUrl, "时长:" + duration + "ms");
              state.rec.close(); // 关闭录音，释放录音资源，当然可以不释放，后面可以连续调用start
              state.rec = null;
              state.recCleRecorder();
              state.isEnd = true
            },
            (err) => {
              state.rec.close();
              state.rec = null;
              state.recCleRecorder();
              state.isEnd = true
            }
          );
        } 
        // else {
        //   // 当页面重新显示在前台时
        //   Toast("页面已经切换回前台");
        //   // 这里可以添加相关处理逻辑
        // }
      },
      flowType: 1, // 0 = 未知, 1 = 不签署, 2 = 签署
      IcfList: [],
      informedChecked: false,
      informedTimeout: 0,
      informedTimeoutText: '',
      IcfObj: {
        studyICF: '',
        studyKnowReadingTime: 0,
        isAlreadyCompleteICF: 0,
        studyICFUrl: '',
        goBack: 0,
        explainICFType: 0,
        patientICFVersionFileId: ''
      },
      myPopupShow: false,
      outerHeight: window.outerHeight,
      submitLoadingFlag: true,
      informedCheckedSave: async() => {
        await state.getPatientStatusFun()        
        if (!state.informedChecked) { // 如果没勾选=>不得进入知情同意书页面
          Toast("请勾选我已充分阅读上述知情同意内容"); 
        } else if (state.IcfObj?.explainICFType === 2) {  // 自愿讲解弹出对话框让用户选择是否需要自愿讲解
          state.myPopupShow = true
        } else if(state.IcfObj?.explainICFType === 1) {  // 强制讲解自动进入讲解状态
          state.setStudyIcfFinish({doubt: 1})
        } else {
          // 当这个状态既不是1也不是2的时候默认不电签(2022-10-17)
          state.setStudyIcfFinish({doubt: 0})
        }
      },
      setStudyIcfFinish: (data) => {
        if (state.submitLoadingFlag) {
          state.submitLoadingFlag = false
          postStudyIcfFinish({...data,...icfStatementIdData})
            .then((rest) => {
            const res = rest as postStudyIcfFinishjInter
            if (res) {
              router.replace(returnPatientStatusUrl(res))
            }
            state.submitLoadingFlag = true
          }).catch(() => {state.submitLoadingFlag = true})
        }
      },
      handleSave: () => {
        state.setStudyIcfFinish({doubt: 0})
      },
      handleCancel: () => {
        postStudyIcfFinish({ doubt: 1, ...icfStatementIdData }).then(() => {
          router.replace('/waitExplainInformed')
          return false;
        })
      },
      // 当前在第几份
      icfIdx: 0,
      businessStartId: '',
      nextInformedCheckedFlag: false,
      // 获取状态的请求
      getPatientStatusFun: () => {
        return new Promise(async(resolve, reject) => {
          try {
            const patientStatus: any = await getPatientStatus(icfStatementIdData)
            if (patientStatus?.patientICFStatus === 404) {
              Notify({ type: 'danger', message: '已转线下知情' })
              router.replace(returnPatientStatusUrl(patientStatus));
              return
            }
            resolve(1)
          } catch (e) {
            reject(e)
          }
        })
      },
      TIMEOUT: null,
      // 5秒记录一次结束时间
      setTimeOutsEndTime: () => {
        if (state.TIMEOUT) {
          clearInterval(state.TIMEOUT)
        }
        state.TIMEOUT = setInterval(() => {
          state.postStudyIcfEndTimeFun()
        }, 5000);
      },
      postStudyIcfEndTimeLoadingFlag: false,
      /* （loadingFlag 接口正在调用时 ，不重复执行）
        1.初次 调5秒一次结束 ， 接着到读秒时间调用一次结束。
        2. 切换下一份的时候 关闭掉5秒定时器，记录一次结束，切到下一份。
        3.下一份 开始先 5秒定时 ，读秒到了记录一次结束。
        4.下一步 调用完成接口之前 去关闭5秒定时。
        5.离开页面需要关闭定时器。
      */
      postStudyIcfEndTimeFun: () => {
        if (state.businessStartId && !state.postStudyIcfEndTimeLoadingFlag) {
          state.postStudyIcfEndTimeLoadingFlag = true
          postStudyIcfEndTime({
            ...icfStatementIdData,
            businessId: state.IcfObj.patientICFVersionFileId,
            businessStartId: state.businessStartId
          })
          .then(() => {
            state.postStudyIcfEndTimeLoadingFlag = false
          })
          .catch(() => {
            state.postStudyIcfEndTimeLoadingFlag = false
          })
        }
      },
      // 知情书切换
      nextInformedChecked: async (num) => {
        state.nextInformedCheckedFlag = true
        await state.getPatientStatusFun()
        state.postStudyIcfEndTimeFun()
        if (num > 0) {
          // 标记为这份阅读时长已完成
          state.IcfList[state.icfIdx].isAlreadyCompleteICF = 1
        }
        //
        state.icfIdx += num
        state.IcfObj.studyICFUrl = ''
        await nextTick()
        state.IcfObj = deepClone(state.IcfList[state.icfIdx])
        state.alreadyCompleteFun()
      },
      //
      timeInterval: null,
      alreadyCompleteFun: () => {
        state.informedTimeout = 0
        state.informedTimeoutText = ''
        clearInterval(state.timeInterval)
        // isAlreadyCompleteICF: 0 // 是否读完 0 否 1是
        // studyICF: "暂为空白"
        // studyKnowReadingTime: 20 // 倒计时秒数
        postStudyIcfStartTime({
          ...icfStatementIdData,
          businessId: state.IcfObj.patientICFVersionFileId
        }).then((res) => {
          state.businessStartId = res as string
          state.nextInformedCheckedFlag = false
          state.setTimeOutsEndTime()
          // 临时测试
          // state.IcfObj.isAlreadyCompleteICF = 0
          if (!state.IcfObj?.isAlreadyCompleteICF && state.IcfObj?.studyKnowReadingTime) {
            state.informedTimeout = state.IcfObj.studyKnowReadingTime
            state.timeInterval = setInterval(() => {
              state.informedTimeout--
              state.IcfList[state.icfIdx].studyKnowReadingTime = state.informedTimeout
              state.informedTimeoutText = formatSeconds(state.informedTimeout) !== '0秒' ? formatSeconds(state.informedTimeout) : ''
              if (state.informedTimeout < 1) {
                clearInterval(state.timeInterval)
                state.informedTimeout = 0
                state.IcfList[state.icfIdx].isAlreadyCompleteICF = 1
                state.postStudyIcfEndTimeFun()
                // 这里测试EndTime 之后 不用StartTime
                // postStudyIcfEndTime({
                //   ...icfStatementIdData,
                //   businessId: state.IcfObj.patientICFVersionFileId,
                //   businessStartId: state.businessStartId
                // }).then(() => {
                //   postStudyIcfStartTime({
                //     ...icfStatementIdData,
                //     businessId: state.IcfObj.patientICFVersionFileId
                //   })
                // })
              }
            },1000)
          }
        }).catch(() => {
          state.nextInformedCheckedFlag = false
        })
      },
    });
    const handleResize = () => {
      setTimeout(() => {
        state.outerHeight = window.outerHeight;
      }, 800);
    };

    onBeforeMount(() => {
      if (route.query?.isAuthorization) {
        state.isAuthorization = true
      }
      // 禁用复制选中
      document.body.style.webkitUserSelect = "none";
      document.body.style.userSelect = "none";
      document.body.style.overscrollBehavior = 'none';
      document.body.addEventListener('scroll', function (event) {
        event._isScroller = true;
      }, { passive: true }
      );
      document.body.style.overflow = 'hidden';
      // 锁定页面当前位置
      document.body.style.position = 'fixed';
      document.body.style.width = '100%';
      state.recCleRecorder();
      getIcf({
        ICFStatementId: store.state?.userInformation?.icfStatementID || store.state?.userInformation?.icfStementId
      }).then((res: any) => {
        // res.map(e => e.isAlreadyCompleteICF = 0)
        // 测试富文本的
        // res.map(e => e.studyICFUrl = '')

        // res.infoList 是数组
        state.flowType = res?.flowType
        state.IcfList = res?.infoList
        state.IcfObj = deepClone(res?.infoList[0])
        state.alreadyCompleteFun()
        setTimeout(() => {
          state.recCleRecorderEndFlag = 1
        }, 100)
      });
    });
    onMounted(() => {
      document.addEventListener('visibilitychange', state.visibilitychange);
      handleResize()
    })
    onBeforeUnmount(() => {
      if (state.TIMEOUT) {
        clearInterval(state.TIMEOUT)
      }
      document.body.style.position = 'static';
      document.removeEventListener('visibilitychange', state.visibilitychange);
    });
    return {
      ...toRefs(state),
      route
    };
  },
});
</script>

<style scoped lang='less'>
.syd-bg {
  background: #DADADC;
}
.guanbiyuyin-icon::after {
  content: 'x';
  position: relative;
  top: -0.02rem;
}
img {
  -webkit-touch-callout: none;/* 禁止长按弹出默认菜单 */
  user-select: none;/* 禁止选择图片 */
}
.bg-yuyin {
  background: url('@/assets/baby/welcomeToJoinUs/yuyin.svg') no-repeat;
  background-size: 100% 100%;
}
.Informed-container {
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(180deg, #eaeffa 70%, #fff 100%);
  color: #555;
  font-size: 0.12rem;
  .Informed-form {
    .icf-back {
      position: relative;
      color: #323233;
      height: 46px;
      font-size: 0.15rem;
      font-weight: 700;
      background: rgba(0, 0, 0, 0);
      .icf-back-lt{
        position: absolute;
        width: 52px;
        height: 100%;
        left: 0;
      }
    }
    h3 {
      margin: 0.2rem 0 0 0;
      text-align: center;
      font-size: 0.16rem;
      color: var(--theme-color);
    }
    .Informed-body {
      .Informed-module {
        height: 90%;
        padding: 0.3rem 0.1rem 0.1rem;
        box-sizing: border-box;
        overflow: auto;
        :deep(ul) {list-style-type:disc !important; list-style-position:inside !important;} 
        :deep(ol) {list-style-type:decimal !important; list-style-position:inside !important;}
      }
      .clause {
        height: 0.2rem;
        display: flex;
        align-items: center;
        font-size: 0.13rem;
        color: var(--theme-color);
        margin: 0.1rem 0;
        input {
          width: 0.2rem;
          margin: 0 0 0 0.2rem;
        }
      }
      .Informed-save-btn {
        width: 90%;
        margin: 0.2rem 0 0 0.2rem;
      }
    }
    .Informed-body-ch{
      height: 80vh;
    }
  }
}
.Informed-pdf-container {
  background: #fff;
  :deep(.pdfjs) ,
  :deep(.pdfViewer) {
    padding: 0;
  }
  .Informed-form {
    margin: 0;
    .Informed-pdf-body {
      margin: 0;
      :deep(.pdf-view-height) {
        height: 100%;
      }
      .Informed-pdf-module{
        margin: 0;
        padding: 0;
        box-shadow: 0 0.02rem 0.12rem rgba(0, 0, 0, 0.07); // 阴影
        overflow: hidden !important;
        :deep(.pinch-zoom-container) {
          // max-height: 4.8rem !important;
          max-height: 70vh !important;
        }
      }
    }
  }
}
:deep(.van-button__text) {
  font-size: 0.13rem;
}
</style>
