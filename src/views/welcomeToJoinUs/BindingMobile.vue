<template>
  <div class="binding-mobile-container">
    <van-nav-bar :title="bindOrRegisterflag ? '绑定手机号' : '受试者信息登记'" 
    :left-arrow="bindIDcardLeft"
    @click-left="routerGo('/welcomeToJoinUs')"
    />
    <div class="binding-mobile-form scrollnone">
      <div class="binding-mobile-title">
        <h3/>
      </div>
      <!-- 手机号 验证码 信息表单 -->
      <van-form @submit="onSubmit" class="form-modules">
        <!-- 手机号 验证登录 left-icon="phone-circle-o"-->
        <van-cell-group inset style="position: relative">
          <div v-if="!bindOrRegisterflag" class="mobile-inputs">
            <img src="@/assets/baby/nameIcon.svg" class="name-identity-icon" alt="" />
            <van-field
              v-model.trim="form.realName"
              placeholder="姓名"
              maxlength="99"
              :rules="[{ required: true, message: '请输入姓名' }]"
            />
          </div>
          <div v-if="!bindOrRegisterflag" class="mobile-inputs">
            <img src="@/assets/baby/identityIcon.svg" class="name-identity-icon" alt="" />
            <van-field
              v-model.trim="form.identityCardNo"
              placeholder="身份证号"
              maxlength="20"
              :rules="[{ required: true, message: '请输入身份证号' }]"
            />
          </div>
          <div class="mobile-inputs">
            <img src="@/assets/baby/mobileIcon.svg" alt="" />
            <van-field
              v-model.trim="form.mobile"
              placeholder="手机号"
              :rules="[
                {
                  validator: (value) => {
                    return /^[1][3-9][0-9]{9}$/.test(value);
                  },
                  message: '请输入正确的手机号',
                  trigger: 'onBlur',
                },
              ]"
            />
            <div class="getverify centerflex-h" @click="getVerifycode">
              |&nbsp;&nbsp;{{ verifyTime > 59 ? "获取验证码" : verifyTime }}
            </div>
          </div>
          <!-- 输入验证码 left-icon="comment-o"-->
          <div class="mobile-inputs">
            <img src="@/assets/baby/verifyCode.svg" alt="" />
            <van-field
              v-model.trim="form.verifyCode"
              placeholder="验证码"
              maxlength="6"
              :rules="[{ required: true, message: '请填写验证码' }]"
            />
          </div>
        </van-cell-group>
        <div class="register-hint-module centerflex">
          <div v-show="!bindOrRegisterflag" class="register-hint">应临床研究相关法规要求，须采集受试者姓名、身份证号、手机号以识别您的唯一身份，我们会对您的个人信息严格保密。</div>  
        </div>
        <div class="centerflex">
          <van-button round block
          type="primary" native-type="submit"
          class="binding-mobile-save-btn">
            {{ bindOrRegisterflag ? '绑定' : '确定' }}
          </van-button>
        </div>
      </van-form>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount } from "vue";
import { Notify } from "vant";
import { useRouter, useRoute } from "vue-router";
import { postSentVerifyCode, postVerifyCode, getMobileFlow } from "@/api/user";
import { bindingMobileInter, postVerifyCodeInter } from '@/types/welcomeToJoinUs';
import { returnPatientStatusUrl } from '@/utils/debounce';
import { useStore } from 'vuex';

export default defineComponent({
  name: "BindingMobile", // 绑定手机号 | 受试者信息登记
  setup() {
    const router = useRouter();
    const route = useRoute();
    const store = useStore();
    const myreg = /^[1][3-9][0-9]{9}$/;
    const state: bindingMobileInter = reactive({
      verifyTime: 60,
      form: {
        mobile: '',
        verifyCode: '',
        identityCardNo: '',
        realName: ''
      },
      bindOrRegisterflag: true, // 绑定或者受试者登记
      submitLoadingFlag: true,
      bindIDcardLeft: true,
      // 获取验证码
      getVerifycode: () => {
        if (state.verifyTime / 1 < 60) return;
        const { mobile } = state.form;
        if (!myreg.test(mobile) || !mobile) {
          Notify({ type: "danger", message: "请输入正确的手机号" });
          return;
        }
        state.verifyTime = 59
        postSentVerifyCode({
          mobile: state.form.mobile,
        })
        const TIMEOUT = setInterval(() => {
          state.verifyTime--;
          if (state.verifyTime / 1 === 0) {
            state.verifyTime = 60;
            clearInterval(TIMEOUT);
          }
        }, 1000);
      },
      // 点击 确定
      onSubmit: () => {
        if (state.submitLoadingFlag) {
          state.submitLoadingFlag = false
          postVerifyCode({
            icfStatementID: store.state?.userInformation?.icfStatementID || store.state?.userInformation?.icfStementId || '',
            ...state.form
          }).then((rest) => {
            const res = rest as postVerifyCodeInter
            if (!route.query?.bindIDcard) {
              const { userInformation } = store.state
              userInformation.icfStatementID = res?.icfStatementID
              if (res?.icfStementId) {
                userInformation.icfStementId = res?.icfStementId
              }
              store.dispatch('setUserInformation',userInformation)
            }
            Notify({ type: "success", message: "绑定成功" });
            state.routerGo(returnPatientStatusUrl(res))
            state.submitLoadingFlag = true
          }).catch(() => {state.submitLoadingFlag = true})
        }
      },
      //
      routerGo: (path: string) => {
        if (path)
        router.replace(path)
      }
    });
    onBeforeMount(() => {
      if (route.query?.bindIDcard) {
        state.bindOrRegisterflag = false
        state.bindIDcardLeft = false
        return
      }
      getMobileFlow().then((res) => {
        state.bindOrRegisterflag = !res || false
      })
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.binding-mobile-container {
  height: 100vh;
  color: #555;
  font-size: 0.16rem;
  overflow: hidden;
  background: #fff;
  .binding-mobile-form {
    height: 92%;
    overflow: auto;
    .binding-mobile-title {
      margin: 0.8rem 0 0.4rem 0;
      h3 {
        width: 100%;
        min-height: 0.5px;
        text-align: center;
        font-weight: 300;
        color: rgba(62, 62, 62, 1);
        font-size: 0.17rem;
        font-weight: 700;
        margin: 0 0 0.1rem 0;
      }
    }
    .form-modules {
      .van-cell-group,.van-cell-group--inset{
        margin: 0;
        padding: 0 0.3rem;
        box-sizing: border-box;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
      }
      .mobile-inputs {
        width: 3rem;
        position: relative;
        display: flex;
        align-items: center;
        background: #f7f7f7;
        height: 0.455rem;
        border-radius: 1rem;
        overflow: hidden;
        margin: 0 0 0.16rem 0;
        .van-cell::after{
          border: none;
        }
        img {
          width: 0.14rem;
          margin: 0 0 0 0.16rem;
        }
        .name-identity-icon{
          width: 0.1471rem;
        }
      }
      .van-cell {
        background: #f7f7f7;
      }
      .getverify {
        position: absolute;
        font-size: 0.14rem;
        right: 0.24rem;
        top: 0.12rem;
        color: var(--theme-color);
      }
      .register-hint-module{
        min-height: 1.01rem;
        .register-hint{
          width: 3rem;
          margin: 0.2rem 0;
          font-size: 0.13rem;
          color: #9A9A9A;
        }
      }
      .binding-mobile-save-btn{
        width: 3rem;
        background: #4B84ED;
      }
    }
  }
}
</style>
