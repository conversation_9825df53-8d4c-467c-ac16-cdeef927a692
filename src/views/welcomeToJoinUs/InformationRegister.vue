<template>
  <div class="information-register-container">
    <van-nav-bar title="绑定受试者" />
    <div class="information-register-form scrollnone">
      <div class="information-register-title">
        <h3/>
      </div>
      <!-- 手机号 验证码 信息表单 -->
      <van-form @submit="onSubmit" class="form-modules">
        <!-- 手机号 验证登录 left-icon="phone-circle-o"-->
        <van-cell-group inset style="position: relative">
          <div class="mobile-inputs">
            <img src="@/assets/baby/nameIcon.svg" class="name-identity-icon" alt="" />
            <van-field
              v-model.trim="form.realName"
              placeholder="姓名"
              maxlength="99"
              :rules="[{ required: true, message: '请输入姓名' }]"
            />
          </div>
          <div class="mobile-inputs">
            <img src="@/assets/baby/identityIcon.svg" class="name-identity-icon" alt="" />
            <van-field
              v-model.trim="form.identityCardNo"
              placeholder="身份证号"
              maxlength="20"
              :rules="[{ required: true, message: '请输入身份证号' }]"
            />
          </div>
          <div class="mobile-inputs">
            <img src="@/assets/baby/mobileIcon.svg" alt="" />
            <van-field
              v-model.trim="form.mobile"
              placeholder="手机号"
              :rules="[
                {
                  validator: (value) => {
                    return /^[1][3-9][0-9]{9}$/.test(value);
                  },
                  message: '请输入正确的手机号',
                  trigger: 'onBlur',
                },
              ]"
            />
            <div class="getverify centerflex-h" @click="getVerifycode">
              |&nbsp;&nbsp;{{ verifyTime > 59 ? "获取验证码" : verifyTime }}
            </div>
          </div>
          <!-- 输入验证码 left-icon="comment-o"-->
          <div class="mobile-inputs">
            <img src="@/assets/baby/verifyCode.svg" alt="" />
            <van-field
              v-model.trim="form.verifyCode"
              placeholder="验证码"
              maxlength="6"
              :rules="[{ required: true, message: '请填写验证码' }]"
            />
          </div>
        </van-cell-group>
        <div class="register-hint-module centerflex">
          <div class="register-hint">应临床研究相关法规要求，须采集受试者姓名、身份证号、手机号以识别您的唯一身份，我们会对您的个人信息严格保密。</div>  
        </div>
        <div class="centerflex">
          <van-button round block
          type="primary"
          :loading="submitLoadingFlag"
          loading-text="确定"
          native-type="submit"
          class="information-register-form-save-btn">
            确定
          </van-button>
        </div>
      </van-form>
      <div class="clause centerflex">
        <input type="checkbox" v-model="checked" />
        <span
          >我已充分了解并同意
          <span
            class="links"
            @click="routerGo('/privacyagreementtermsofservice', 1)"
          >服务条款</span>
          和
          <span
            class="links"
            @click="routerGo('/privacyagreementtermsofservice', 2)"
          >隐私协议</span>的内容
        </span>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount } from 'vue';
import { Notify, Toast } from 'vant';
import { useRouter, useRoute } from "vue-router";
import { informationRegisterInter } from '@/types/welcomeToJoinUs';
import { 
  postBindingInfoSentVerifyCode,
  putBindingInfo
} from "@/api/user";

export default defineComponent({
  name: "InformationRegister", // 绑定受试者
  setup() {
    const router = useRouter();
    const route = useRoute();
    const myreg = /^[1][3-9][0-9]{9}$/;
    const state: informationRegisterInter = reactive({
      verifyTime: 60,
      checked: false,
      submitLoadingFlag: false,
      form: {
        mobile: "",
        verifyCode: "",
        identityCardNo: '',
        realName: ''
      },
      // 获取验证码
      getVerifycode: () => {
        if (state.verifyTime / 1 < 60) return;
        const { mobile } = state.form;
        if (!myreg.test(mobile) || !mobile) {
          Notify({ type: "danger", message: "请输入正确的手机号" });
          return;
        }
        state.verifyTime = 59
        postBindingInfoSentVerifyCode({
          mobile: state.form.mobile,
        })
        const TIMEOUT = setInterval(() => {
          state.verifyTime--;
          if (state.verifyTime / 1 === 0) {
            state.verifyTime = 60;
            clearInterval(TIMEOUT);
          }
        }, 1000);
      },
      // 点击 确定
      onSubmit: () => {
        if (!state.checked) {
          Toast("请先了解隐私协议和服务条款的内容");
          return;
        }
        state.submitLoadingFlag = true
        putBindingInfo({
          ...state.form
        }).then(() => {
          Notify({ type: "success", message: "绑定成功" })
          state.routerGo('/')
          state.submitLoadingFlag = false
        }).catch(() => { state.submitLoadingFlag = false })
      },
      routerGo: (path,id) => {
        let query
        if (id) {
          query = { 
            backUrl: '/informationRegister',
            id,
            backQuery: JSON.stringify({...state.form})
          }
        }
        router.replace({
          path,
          query
        })
      }
    });

    onBeforeMount(() => {
      if (route.query?.backQuery) {
        state.form = JSON.parse(route.query.backQuery as string)
      }
    });

    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.information-register-container {
  height: 100vh;
  color: #555;
  font-size: 0.16rem;
  overflow: hidden;
  background: #fff;
  .information-register-form {
    height: 92%;
    overflow: auto;
    .clause {
      margin: 0.2rem 0 0 0;
    }
    .information-register-title {
      width: 100%;
      margin: 0.6rem 0 0.4rem 0;
      h3 {
        width: 100%;
        min-height: 0.5px;
        text-align: center;
        font-weight: 300;
        color: rgba(62, 62, 62, 1);
        font-size: 0.17rem;
        font-weight: 700;
        margin: 0 0 0.1rem 0;
      }
    }
    .form-modules {
      .van-cell-group,.van-cell-group--inset {
        margin: 0;
        padding: 0 0.3rem;
        box-sizing: border-box;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
      }
      .mobile-inputs {
        width: 3rem;
        position: relative;
        display: flex;
        align-items: center;
        background: #f7f7f7;
        height: 0.455rem;
        border-radius: 1rem;
        overflow: hidden;
        margin: 0 0 0.16rem 0;
        .van-cell::after {
          border: none;
        }
        img {
          width: 0.14rem;
          margin: 0 0 0 0.16rem;
        }
        .name-identity-icon {
          width: 0.1471rem;
        }
      }
      .van-cell {
        background: #f7f7f7;
      }
      .getverify {
        position: absolute;
        font-size: 0.14rem;
        right: 0.24rem;
        top: 0.12rem;
        color: var(--theme-color);
      }
      .register-hint-module {
        min-height: 1.01rem;
        .register-hint{
          width: 3rem;
          margin: 0.2rem 0;
          font-size: 0.13rem;
          color: #9A9A9A;
        }
      }
      .information-register-form-save-btn {
        width: 3rem;
        background: #4B84ED;
      }
    }
  }
}
</style>
