<template>
  <div class="waitExplainInformed-container relative">
    <div class="waitExplainInformed-form">
      <div></div>
      <div class="waitExplainInformed-body mt-015rem">
        <div class="waitExplainInformed-body-center">
          <div class="w-full centerflex mb-[0.58rem]">
            <img
              style="margin: 0 auto"
              src="@/assets/baby/waitInformedIcon.svg"
              alt=""
            />
          </div>
          <div class="waitExplainInformed-body-h">
            <div>研究人员会尽快与您取得联系，</div>
            <div>并为您充分讲解知情同意书内容。</div>
            <div>在确保您都理解和同意后，</div>
            <div>研究人员会发起电子知情同意书签署流程。</div>
          </div>
        </div>
      </div>
      <div
        v-for="(item, index) in conferenceList"
        :key="index"
        class="bg-white py-01rem px-015rem box-border"
        style="
          font-size: 0.13rem;
          width: 3.3rem;
          margin: 0.15rem auto 0.1rem;
          border-radius: 0.02rem;
        "
        @click="routerGo(item)"
      >
        <div class="mb-006rem">用户 {{ item?.createUserName }} 发起的会议</div>
        <div>
          {{
            item?.start && formatDate(item?.start, "YYYY-MM-DD") === dateText
              ? "今天"
              : formatDate(item?.start, "YYYY-MM-DD")
          }}
          {{ item?.startHourStr }}

          <span
            v-if="
              item?.start &&
              new Date(item.start).getTime() >= new Date().getTime()
            "
            >开始会议</span
          >
          <span v-else>会议已开始，请尽快加入</span>
        </div>
      </div>
    </div>
    <div class="waitExplainInformed-btn">
      <van-button
        v-if="state.hasInteractiveCommunication === 1"
        class="w-full h-065rem rounded-none theme-color"
        @click="
          $routerGoFun(
            'routerInnPush',
            '/interactivecommunication',
            '',
            '/waitExplainInformed'
          )
        "
        >互动沟通</van-button
      >
      <van-button
        class="w-full h-065rem theme-bg-btn-color rounded-none"
        type="primary"
        @click="$routerGoFun('replace', '/informedList')"
        >我的知情同意</van-button
      >
    </div>
  </div>
</template>

<script setup lang="ts">
import { toWildfire, formatDate } from "@/utils/debounce";
import { useStore } from "vuex";
import { ref, onBeforeMount, inject, reactive } from "vue";
import { getConference } from "@/api/user";
import { dateText } from "@trialdata/common-fun-css";
import { Toast } from 'vant';
import { getPatient } from "@/api/home";

const fireConferenceObj: any = inject('fireConferenceObj')
const store = useStore();
const conferenceList: any = ref([]);
// console.log(store?.state?.homeList?)
const state: any = reactive({
  hasInteractiveCommunication: 0
})
function routerGo(item) {
  // 判断当前环境是否为app，如果是app则跳转app页面
  if (store.state.appToken != '' && fireConferenceObj.src) {
    const msgData = {
      data: {
        action: "joinConference",
        payload: {
          ConferenceId: item.conferenceId,
        }
      }
    };
    // 如果是iOS
    if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.jumpToNative) {
      window.webkit.messageHandlers.jumpToNative.postMessage(msgData);
    }
    // 如果是非iOS环境(即uniapp、安卓)
    uni.postMessage(msgData);
    return;
  }
  if (fireConferenceObj.src) {
    Toast.fail('当前已经在会议中')
    return
  }
  sessionStorage.removeItem('conferenceIMNum')
  const { dctPatientId, icfStementId, patientNum } =
    store.state.userInformation;
  const token =
    store?.state?.patientToken || sessionStorage.getItem("patientToken");
  fireConferenceObj.src = toWildfire(token, dctPatientId, icfStementId, false, "", patientNum, item.conferenceId);
}

onBeforeMount(() => {
  if (store.state?.userInformation?.icfStementId) {
    getConference({
      ICFStatementID: store.state.userInformation.icfStementId,
    }).then((res: any) => {
      if (Array.isArray(res) && res?.length > 0) {
        // res.push({isDestroy: false})
        // res.push({isDestroy: true})
        // res.push({isDestroy: true})
        // res.push({isDestroy: false})
        // console.log(res);
        res = res.filter((e) => !e.isDestroy && !e.isComplete &&
          (e?.end && new Date(e.end).getTime() > new Date().getTime()))
        if (res?.length > 0)
          conferenceList.value = res;
      }
    });
  }
  getPatient().then((rest) => {
    state.hasInteractiveCommunication = rest.hasInteractiveCommunication;
    let tmp = store?.state?.homeList;
    tmp.hasInteractiveCommunication = rest.hasInteractiveCommunication;
    store.dispatch('setHomeList', tmp);
  })
});
</script>

<style scoped lang="less">
.waitExplainInformed-container {
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(180deg, #eaeffa 70%, #fff 100%);
  font-size: 0.12rem;
  .waitExplainInformed-form {
    height: calc(100vh - 0.65rem);
    .waitExplainInformed-body {
      width: 100%;
      height: 3.425rem;
      display: flex;
      justify-content: center;
      font-size: 0.13rem;
      color: #333;
      .waitExplainInformed-body-center {
        width: 3.3rem;
        height: 100%;
        padding: 0.4rem 0 0 0;
        box-sizing: border-box;
        border-radius: 0.06rem;
        background: #fff;
        .waitExplainInformed-body-h {
          text-align: center;
          max-height: 0.8rem;
          div {
            margin: 0 0 0.1rem 0;
          }
        }
        img {
          width: 1.27rem;
          height: 1.06rem;
        }
      }
    }
  }
  .waitExplainInformed-btn {
    width: 100vw;
    display: flex;
    justify-content: center;
    position: absolute;
    bottom: 0px;
  }
}
</style>
