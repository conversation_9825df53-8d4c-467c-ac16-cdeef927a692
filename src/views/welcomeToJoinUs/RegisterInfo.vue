<template>
  <div class="registerInfo-box h-screen ft-014-rem">
    <div class="pt-02rem box-border h-065rem ft-015-rem text-center font-semibold leading-02rem">登记信息</div>
    <van-form @submit="onSubmit" label-width="1.1rem">
      <div class="groupMessage-main overflow-auto" :style="{height: outerHeight ? `calc(${outerHeight}px - 0.65rem - 0.45rem - 0.6rem)` : ''}">
        <div class="groupMessage-box box-border my-015rem pb-015rem bg-white">
          <van-cell-group inset>
            <div class="border-bottom-F5F5F5">
              <!-- ui图 -->
              <!-- <van-field
                v-model.trim="form.relation"
                label="与受试者关系"
                required
                :rules="[{ required: true, message: '请选择' }]"
                input-align="right"
                error-message-align="right"
              >
                <template #input>
                  <div class="flex criterion font-color-999">
                    <div class="criterion-check flex items-center" @click="checkChange(1)">
                      <div v-if="form.relation === 1" class="circle-Y" />
                      <div v-else class="circle-Y circle-N"/>
                      <div>本人</div>
                    </div>
                    <div class="criterion-check flex items-center ml-015rem" @click="checkChange(2)">
                      <div v-if="form.relation === 2" class="circle-Y"/>
                      <div v-else class="circle-Y circle-N"/>
                      <div>家属</div>
                    </div>
                  </div>
                </template>
              </van-field> -->
              <van-field
                v-model="form.relation"
                label="与受试者关系"
                required
                :rules="[{ required: true, message: '请选择' }]"
                input-align="right"
                error-message-align="right"
              >
                <template #input>
                  <div class="criterion-check flex items-center ml-015rem">
                    <div>家属</div>
                  </div>
                </template>
              </van-field>
            </div>
            <div class="border-bottom-F5F5F5">
              <van-field
                v-model.trim="form.relationShipName"
                label="具体关系"
                placeholder="请输⼊ (例如: 父亲，女儿)"
                required
                :rules="[{ required: true, message: '请输入' }]"
                input-align="right"
                error-message-align="right"
                maxlength="99"
              />
            </div>
            <div class="border-bottom-F5F5F5">
              <van-field
                v-model.trim="form.realName"
                label="姓名"
                placeholder="请输入"
                required
                :rules="[{ required: true, message: '请输入' }]"
                input-align="right"
                error-message-align="right"
                maxlength="20"
              />
            </div>
            <div class="border-bottom-F5F5F5">
              <van-field
                v-model.trim="form.identityCardNo"
                label="身份证号"
                placeholder="请输入"
                required
                :rules="[{ required: true, message: '请输入' }]"
                input-align="right"
                error-message-align="right"
              />
            </div>
            <div class="border-bottom-F5F5F5">
              <van-field
                v-model.trim="form.mobile"
                label="手机号"
                placeholder="请输入"
                required
                :rules="[{ required: true, message: '请输入' },{
                  validator: (value) => {
                    return /^[1][3-9][0-9]{9}$/.test(value);
                  },
                  message: '请输入正确的手机号',
                  trigger: 'onBlur',
                },]"
                input-align="right"
                error-message-align="right"
              />
            </div>
            <div class="border-bottom-F5F5F5 flex mobile-inputs">
              <div>
                <van-field
                  v-model.trim="form.verifyCode"
                  label="验证码"
                  maxlength="6"
                  required
                  :rules="[{ required: true, message: '请填写验证码' }]"
                  input-align="right"
                  error-message-align="right"
                  style="padding-right: 0.1rem;"
                />
              </div>
              <div class="getverify whitespace-nowrap" @click="getVerifycode">
                |&nbsp;&nbsp;{{ verifyTime > 59 ? "获取验证码" : verifyTime }}
              </div>
            </div>
          </van-cell-group>
          <div class="font-color-999 p-014rem ft-13-rem">
            温馨提示：应临床研究相关法规要求，须采集姓名、身份证号、手机号以识别您的唯一身份，我们会对您的个人信息严格保密。
          </div>
        </div>
      </div>
      <div class="clause pl-016rem mb-025rem mt-015rem ft-12-rem flex items-center h-02rem">
        <input type="checkbox" v-model="checked" class="checkboxInput" />
        <span
          class="pl-01rem"
          >我已充分了解并同意
          <span
            class="links"
            @click="routerGo('/privacyagreementtermsofservice', 1)"
            >服务条款</span
          >
          和
          <span
            class="links"
            @click="routerGo('/privacyagreementtermsofservice', 2)"
            >隐私协议</span
          >
          的内容</span
        >
      </div>
      <div class="flex w-full">
        <van-button
          type="primary"
          :loading="loading"
          native-type="submit"
          class="theme-bg-btn-color h-045rem flex-1 ft-15-rem font-color-fff border-0 border-radius-none">
          确定
        </van-button>
      </div>
    </van-form>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onMounted, } from 'vue';
import { useRoute, useRouter } from "vue-router";
import { Toast, Notify } from 'vant';
import { postPatientMemberIdentityVerifyCode, postSentVerifyCode } from '@/api/user';
import { returnPatientStatusUrl } from '@/utils/debounce';

export default defineComponent({
  name: "RegisterInfo", // 登记信息
  setup() {
    const route = useRoute()
    const router = useRouter()
    // const proxy: any = getCurrentInstance()?.proxy
    const myreg = /^[1][3-9][0-9]{9}$/;
    const state = reactive({
      loading: false,
      outerHeight: window.outerHeight,
      form: {
        relation: 2,
        relationShipName: '',
        realName: '',
        identityCardNo: '',
        mobile: '',
        verifyCode: '',
      },
      checked: false,
      verifyTime: 60,
      getVerifycode: () => {
        if (state.verifyTime / 1 < 60) return;
        const { mobile } = state.form;
        if (!mobile) {
          Notify({ type: "danger", message: "请输入手机号" });
          return;
        } else if (!myreg.test(mobile)) {
          Notify({ type: "danger", message: "请输入正确的手机号" });
          return
        }
        state.verifyTime = 59
        postSentVerifyCode({
          mobile: state.form.mobile,
        })
        const TIMEOUT = setInterval(() => {
          state.verifyTime--;
          if (state.verifyTime / 1 === 0) {
            state.verifyTime = 60;
            clearInterval(TIMEOUT);
          }
        }, 1000);
      },
      onSubmit: () => {
        if (!state.checked) {
          Toast("请先了解隐私协议和服务条款的内容");
          return;
        }
        state.loading = true
        postPatientMemberIdentityVerifyCode({...state.form}).then((res: any) => {
          state.loading = false
          Notify({ type: "success", message: "绑定成功" });
          let url = returnPatientStatusUrl(res)
          if (
            res?.patientStatus === 6 || 
            res?.patientStatus === 7 || 
            res?.patientStatus === 8 || 
            res?.patientStatus === 9 ||
            res?.patientStatus === 16
          ) {
            url = '/audit'
          } else if (
            res?.patientStatus === 13 ||
            res?.patientStatus === 14 ||
            res?.patientStatus === 15
          ) {
            url = '/projectEnd'
          } else if (res?.patientStatus === 17) {
            url = '/'
          }
          if (url) {
            router.replace(url)
          }
        }).catch(() => {
          state.loading = false
        })
      },
      routerGo: (path ,id) => {
        let query
        if (id) {
          query = { 
            backUrl: '/registerInfo',
            id,
            backQuery: JSON.stringify({...state.form})
          }
        }
        router.replace({
          path,
          query
        })
      },
      checkChange: (idx) => {
        console.log(idx)
        // if (idx === 1) {
        //   state.form.relation = 1
        // } else if (idx === 2) {
        //   state.form.relation = 2
        // }
      },
    });
    onMounted(() => {
      setTimeout(() => {
        state.outerHeight = window.outerHeight;
      }, 0);
      if (route.query?.backQuery) {
        state.form = JSON.parse(route.query.backQuery as string)
      }
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang='less'>
.groupMessage-main {
  height: calc(100vh - 0.65rem - 0.45rem - 0.6rem);
  .mobile-inputs {
    position: relative;
  }
  .getverify {
    font-size: 0.14rem;
    margin-right: 0.28rem;
    margin-top: 0.15rem;
    line-height: 24px;
    color: var(--theme-color);
  }
  .circle-Y {
    box-sizing: border-box;
    width: 0.14rem;
    height: 0.14rem;
    border-radius: 50%;
    border: 0.05rem solid #4D7DE9;
    margin-right: 0.05rem;
  }
  .circle-N {
    border: 0.5px solid #999;
  }
}
.checkboxInput {
  width: 0.14rem;
}
.groupMessage-box {
  :deep(.van-cell-group--inset) {
    margin: 0;
  }
  :deep(.van-cell) {
    padding: 0.15rem 0.28rem 0.15rem 0.15rem;
    .van-field__label {
      color: #333333;
      font-size: 0.15rem;
    }
    .van-cell__value {
      color: #999999;
      font-size: 0.13rem;
    }
  }
}
</style>
