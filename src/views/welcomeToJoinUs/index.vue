<template>
  <div class="relative w-full h-100-vh overflow-hidden font-color-333 ft-16-rem">
    <div v-show="showPrivacyAgreementTermsOfServiceFlag === 0">
      <div class="welcomeToJoinUs-form">
        <div class="welcomeToJoinUs-img">
          <img
            v-if="
              projectObj?.rotationChartUrl?.length &&
              projectObj?.rotationChartUrl[0]
            "
            :src="projectObj.rotationChartUrl[0]"
            alt=""
          />
          <img v-else src="@/assets/baby/project.svg" alt="" />
        </div>
        <div class="welcomebody scrollnone">
          <h3>诚挚邀请您加入</h3>
          <h5 v-if="projectObj?.title" v-html="projectObj.title" />
          <div class="welcomeToJoinUs-body">
            <div class="welcomeToJoinUs-module">
              <p
                v-if="projectObj?.projectContent"
                v-html="projectObj.projectContent"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="welcomeToJoinUs-bottom centerflex-w-wrap">
        <van-button
          class="begin"
          round
          type="primary"
          @click="showPopup = true"
          >开始</van-button
        >
      </div>
    </div>
    <!-- 隐私政策和服务条款页面 -->
    <PrivacyAgreementTermsOfService 
      v-show="showPrivacyAgreementTermsOfServiceFlag !== 0"
      ref="PrivacyAgreementTermsOfServiceRef"
      :closePage="(flag) => {
        showPrivacyAgreementTermsOfServiceFlag = 0
        if (flag === '2') {
          privacyAgreementChecked = true
        } else
          termsOfServiceChecked = true
      }"
    />
    <!-- 弹窗集成 -->
    <MyPopupShow
      :myPopupShow="showPopup"
      title="提示"
      cancelText="返回"
      saveText="继续"
      cancelClass="text-black"
      myPopupModuleClass="relative"
      myPopupTitleClass="!mt-[0.15rem]"
      :saveClass="!privacyAgreementChecked || !termsOfServiceChecked ? 'text-[#999]' : ''"
      :handleCancel="handlePopupCancel"
      :handleSave="handlePopupSave"
    >
      <template #bodyslot>
        <div class="ft-13-rem text-center px-[0.25rem] pt-[0.2rem] text-[#333]">
          为了保障您的权益，请务必完整阅读并理解我们的服务条款和隐私政策，同意后方可继续使用。
        </div>
        <div class="mt-[0.2rem] ml-[0.4rem] mr-[0.1rem]">
          <div class="flex items-center mb-[0.1rem] clause">
            <img v-show="!termsOfServiceChecked" src="@/assets/baby/welcomeToJoinUs/terms-no-checked-icon.svg" @click="() => {
              termsOfServiceChecked = !termsOfServiceChecked
              checkedChange(termsOfServiceChecked, '1')
            }" />
            <img v-show="termsOfServiceChecked" src="@/assets/baby/welcomeToJoinUs/terms-checked-icon.svg" @click="() => {
              termsOfServiceChecked = !termsOfServiceChecked
              checkedChange(termsOfServiceChecked, '1')
            }" />
            <label for="clause2" class="ft-13-rem ml-[0.08rem]">
              我已认真阅读并同意 <span class="links" @click="openPrivacyAgreementTermsOfService('1')">《服务条款》</span>
            </label>
          </div>
          <div class="flex items-center mb-[0.1rem] clause">
            <img v-show="!privacyAgreementChecked" src="@/assets/baby/welcomeToJoinUs/terms-no-checked-icon.svg" @click="() => {
              privacyAgreementChecked = !privacyAgreementChecked
              checkedChange(privacyAgreementChecked, '2')
            }" />
            <img v-show="privacyAgreementChecked" src="@/assets/baby/welcomeToJoinUs/terms-checked-icon.svg" @click="() => {
              privacyAgreementChecked = !privacyAgreementChecked
              checkedChange(privacyAgreementChecked, '2')
            }" />
            <label for="clause1" class="ft-13-rem ml-[0.08rem]">
              我已认真阅读并同意 <span class="links" @click="openPrivacyAgreementTermsOfService('2')">《隐私协议》</span>
            </label>
          </div>
        </div>
        <img 
          src="@/assets/baby/welcomeToJoinUs/welcome-join-close-icon.svg" class="top-[0.12rem] right-[0.12rem] w-[0.2rem] absolute"
          alt=""
          @click="handlePopupCancel"
        >
      </template>
    </MyPopupShow>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount, nextTick } from "vue";
import { useRouter } from 'vue-router';
import { Toast } from "vant";
import { getStudyIntroduction, getStudyTerm, postUpdateStudyTermRecord } from "@/api/user";
import { getStudyIntroductionInter } from '@/types/welcomeToJoinUs';
import { getPatientAllStatus } from "@/utils/debounce";
import { useStore } from "vuex";
import MyPopupShow from '@/components/MyPopupShow.vue';
import PrivacyAgreementTermsOfService from '@/views/welcomeToJoinUs/PrivacyAgreementTermsOfService.vue';

export default defineComponent({
  name: "WelcomeToJoinUs", // 诚挚邀请您加入项目页
  components: {
    MyPopupShow,
    PrivacyAgreementTermsOfService
  },
  setup() {
    const router = useRouter();
    // const route = useRoute();
    const store = useStore();
    const state = reactive({
      studyTermObj: {
        privacyMustBeRead: false,
        privacyMustBeReadSeconds: 0,
        slaMustBeRead: false,
        slaMustBeReadSeconds: 0,
        studyPrivacy: "",
        studySLA: "",
        studyPrivacyIsRead: false,
        studySLAIsRead: false,
      },
      termsOfServiceChecked: false,
      privacyAgreementChecked: false, 
      showPopup: false,
      projectObj: {
        projectCode: '', // code码
        projectContent: '', // 项目介绍
        title: '', // 标题
        rotationChartUrl: [],
        // 下个页面流转的状态
      },
      showPrivacyAgreementTermsOfServiceFlag: 0,
      PrivacyAgreementTermsOfServiceRef: null,
      // 打开 隐私协议 服务条款
      openPrivacyAgreementTermsOfService: (flag) => {
        state.showPrivacyAgreementTermsOfServiceFlag = flag
        if (state.PrivacyAgreementTermsOfServiceRef) {
          state.PrivacyAgreementTermsOfServiceRef.showTitleFlag = flag
          state.PrivacyAgreementTermsOfServiceRef.onLoad()
        }
      },
      routerGo: async (path: string, id: number) => {
        if (store.state.appToken != '') {
          router.replace('/informedVideo');
          return
        }
        // 如果没勾选=>不得进入知情同意书页面
        // if (id === 3 && !state.checked) {
        //   Toast("请先了解隐私协议和服务条款的内容");
        //   return;
        // }
        try {
          if (id === 3) {
            // APP来的 无需绑定身份、手机号，根据状态流转下个页面。
            // getPatientAllStatus调用时字段名-换下
            // const pathStr = getPatientAllStatus(state.projectObj);
            // router.replace(pathStr || path)
            router.replace(path);
          } else {
            router.replace({
              path,
              query: { id },
            });
          }
        } catch {
          // console.log(e);
        }
      },
      // 点击时 看看是否强制阅读
      checkedChange: (checkedFlag,typeFlag) => {
        // console.log(checkedFlag,typeFlag)
        // && state.PrivacyAgreementTermsOfServiceRef.强制标识
        if (!state.studyTermObj.studyPrivacyIsRead && checkedFlag && state.studyTermObj?.privacyMustBeRead && typeFlag === '2' && !state.PrivacyAgreementTermsOfServiceRef?.readPrivacyAgreement) {
          state.openPrivacyAgreementTermsOfService('2')
        } else if (!state.studyTermObj.studySLAIsRead && checkedFlag && state.studyTermObj?.slaMustBeRead && typeFlag === '1'
        && !state.PrivacyAgreementTermsOfServiceRef?.readTermsOfService) {
          state.openPrivacyAgreementTermsOfService('1')
        }
      },
      saveLoadingFlag: false,
    });

    // 弹窗按钮事件
    const handlePopupCancel = () => {
      state.showPopup = false;
    };
    
    const handlePopupSave = async() => {
      if (!state.termsOfServiceChecked || !state.privacyAgreementChecked) {
        Toast('请先勾选并同意隐私协议和服务条款');
        return;
      } else if (state.saveLoadingFlag) {
        return
      }
      state.saveLoadingFlag = true
      // 强制读 且 不是已读的时候 => 标记已读
      if ((!state.studyTermObj.studySLAIsRead || !state.studyTermObj.studyPrivacyIsRead)
        && (state.studyTermObj?.privacyMustBeRead || state.studyTermObj?.slaMustBeRead)
        ) {
        await postUpdateStudyTermRecord(2)
        await postUpdateStudyTermRecord(3)
      }
      state.showPopup = false;
      state.routerGo('/bindingmobile', 3)
      state.saveLoadingFlag = false
    };

    onBeforeMount(() => {
      getStudyIntroduction().then((res) => {
        state.projectObj = res as getStudyIntroductionInter;
      });
      getStudyTerm(store.state?.userInformation.dctStudyId)
      .then((res: any) => {
        state.studyTermObj = res;
        nextTick(() => {
          state.PrivacyAgreementTermsOfServiceRef.studyTermObj = res
        });
      });
    });

    return {
      ...toRefs(state),
      handlePopupCancel,
      handlePopupSave,
    };
  },
});
</script>

<style scoped lang='less'>
.welcomeToJoinUs-bottom {
  width: 100%;
  position: absolute;
  bottom: 0.1rem;
  left: 0;
  .begin {
    width: 3rem;
    margin: 0.1rem 0 0.2rem 0;
  }
}
.welcomeToJoinUs-form {
  padding-bottom: 10rem;
  .welcomeToJoinUs-img {
    max-height: 1.85rem;
    overflow: hidden;
    width: 100%;
    img {
      width: 100%;
    }
  }
  .welcomebody {
    height: 49vh;
    overflow: auto;
    h3 {
      width: 100%;
      font-size: 0.17rem;
      color: var(--theme-color);
      margin: 0.2rem 0 0 0;
      text-align: center;
    }
    h5 {
      padding: 0.2rem;
      box-sizing: border-box;
      color: #333;
      text-align: center;
    }
    .welcomeToJoinUs-body {
      width: 100%;
      padding: 0.2rem;
      margin: 0.2rem 0 0 0;
      box-sizing: border-box;
      .welcomeToJoinUs-module {
        width: 100%;
        padding: 0.1rem;
        box-sizing: border-box;
        font-size: 0.15rem;
        min-height: 30%;
      }
    }
  }
}
</style>
