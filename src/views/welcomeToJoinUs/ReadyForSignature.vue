<template>
  <div class="waitExplainInformed-container relative">
    <div class="waitExplainInformed-form scrollnone">
      <div></div>
      <div class="waitExplainInformed-body mt-015rem">
        <div class="waitExplainInformed-body-center">
          <img src="@/assets/baby/waitInformedIcon.svg" alt="" />
          <div class="waitExplainInformed-body-h">
            <div>等待研究人员发起知情同意书签署流程</div>
          </div>
        </div>
      </div>
    </div>
    <div class="waitExplainInformed-btn">
      <van-button
        v-if="hasInteractiveCommunication === 1"
        class="w-full h-065rem rounded-none theme-color"
        @click="
          $routerGoFun(
            'routerInnPush',
            '/interactivecommunication',
            '',
            '/waitExplainInformed'
          )
        "
        >互动沟通</van-button
      >
      <van-button
        class="w-full h-065rem theme-bg-btn-color rounded-none"
        type="primary"
        @click="$routerGoFun('replace', '/informedList')"
        >我的知情同意</van-button
      >
    </div>
  </div>
</template>

<script setup >
import { ref } from "vue";
import { useStore } from "vuex";

const store = useStore();

const hasInteractiveCommunication = ref(
  store?.state?.homeList?.hasInteractiveCommunication
);
</script>

<style scoped lang="less">
.waitExplainInformed-container {
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(180deg, #eaeffa 70%, #fff 100%);
  font-size: 0.12rem;
  .waitExplainInformed-form {
    padding-bottom: 0.2rem;
    box-sizing: border-box;
    height: calc(100vh - 0.75rem);
    overflow: auto;
    .waitExplainInformed-body {
      width: 100%;
      display: flex;
      justify-content: center;
      font-size: 0.13rem;
      color: #333;
      .waitExplainInformed-body-center {
        width: 3.3rem;
        height: 100%;
        padding: 0.4rem 0 0 0;
        box-sizing: border-box;
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        border-radius: 0.06rem;
        background: #fff;
        .waitExplainInformed-body-h {
          text-align: center;
          margin: 0.5rem 0;
        }
        img {
          width: 1.27rem;
          height: 1.06rem;
        }
      }
    }
  }
  .waitExplainInformed-btn {
    width: 100vw;
    display: flex;
    justify-content: center;
    position: absolute;
    bottom: 0px;
  }
}
</style>
