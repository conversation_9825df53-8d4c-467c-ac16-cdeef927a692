<template>
  <div class="InformedVideo-container">
    <van-nav-bar :title="IcfObj?.title" />
    <div class="InformedVideo-body overflow-hidden">
      <MyVideoPlay
        v-if="IcfObj?.videoUrl && !cslyFlag"
        ref="InformedVideoRef"
        class="InformedVideo-module scrollnone"
        :fileUrl="IcfObj.videoUrl"
        :coverUrl="IcfObj.videoCoverUrl"
        :onPlay="onPlayerPlay"
        :onEnded="onPlayerEnded"
      />
      <!-- 测试录音功能 -->
      <div v-if="cslyFlag">
        <div>
          <!-- <button style="width: 60px; height: 60px" @click="recPlay">
            本地试听
          </button> -->
          <div>是否有权限{{ hasPermissionText }}</div>
          <div>move:{{ moveText }}</div>
        </div>
        <div style="padding-top: 5px">
          <!-- 波形绘制区域 -->
          <div
            style="
              display: inline-block;
              vertical-align: bottom;
            "
          >
            <div
              style="height: 100px; width: 100%" 
              ref="recwave"
            ></div>
          </div>
        </div>
      </div>
    </div>
    <!-- 底部按钮all -->
    <div v-if="recCleRecorderEndFlag && rec?.start" class="flex">
      <div
        v-if="checkVideoIndex !== 0"
        class="w-full text-white h-078rem bg-[#A2C2F7]"
        @click.stop="nextInformedChecked(-1)"
      >
        <div class="pb-01rem pt-018rem box-border text-center w-full ft-14-rem">
          上一段
        </div>
        <div
          class="pb-01rem box-border text-center w-full ft-10-rem px-02rem truncate"
        >
          {{ allVideoObj[checkVideoIndex - 1].title }}
        </div>
      </div>

      <div class="w-full text-white h-078rem tw-bg">
        <div
          class="pt-018rem pb-005rem box-border text-center w-full ft-14-rem"
          @touchstart.stop="recOpen"
          @touchmove.stop="recMove"
          @touchend.stop="recStop"
        >
          提问
        </div>
        <div class="pb-01rem box-border mt-005rem text-center w-full ft-10-rem">
          按住说话
        </div>
        <div
          v-show="startFlag"
          ref="endRef"
          class="guanbiyuyin-icon absolute w-042rem text-white bg-4D4C4C h-042rem text-center"
          style="
            font-size: 0.35rem;
            border-radius: 50%;
            left: 0px;
            bottom: 21%;
            left: 50%;
            z-index: 9999;
            transform: translateX(-50%);
          "
          :class="{ 'bg-color-fff text-4D4C4C': isActive }"
          @click="recStopClick"
        />
      </div>

      <div
        v-if="allVideoObj.length && checkVideoIndex !== allVideoObj.length - 1"
        class="w-full text-white h-078rem theme-bg-btn-color"
        @click.stop="nextInformedChecked(1)"
      >
        <div class="pb-01rem pt-018rem box-border text-center w-full ft-14-rem">
          下一段
          <span v-if="informedTimeout && informedTimeoutText">{{
            informedTimeoutText
          }}</span>
        </div>
        <div
          class="pb-01rem box-border text-center centerflex w-full px-02rem ft-10-rem truncate"
        >
          <span>{{ allVideoObj[checkVideoIndex + 1].title }}</span>
        </div>
      </div>
      <!-- :disabled="informedTimeout > 0" -->
      <div
        v-else
        class="text-white centerflex-wrap w-full h-078rem rounded-none theme-bg-btn-color"
        @click="informedCheckedSave"
      >
        下一步
        <span
          v-if="informedTimeout && informedTimeoutText"
          class="py-01rem text-center box-border"
          >{{ informedTimeoutText }}</span
        >
      </div>
    </div>
    <!-- 自定义弹出窗 -->
    <div
      v-if="startFlag"
      class="absolute w-100vw h-100vh flex items-end"
      style="
        background-color: rgba(0, 0, 0, 0.5);
        left: 0px;
        top: 0;
        z-index: 999;
      "
    >
      <div class="w-full centerflex-w-wrap">
        <div class="w-full bg-yuyin" style="height: 1.355rem" />
      </div>
    </div>
  </div>
  <div
    v-show="saveEndFlag"
    class="text-center centerfle-h py-[0.15rem] px-[0.1rem] w-[2.4rem] fixed ft-14-rem bg-black text-white"
    style="
      top: 50%;
      left: 50%;
      transform: translate3d(-50%, -50%, 0);
      border-radius: 0.06rem;
    "
  >
    <div>已提交，后续会有研究人员为您解答，</div>
    <div>您可以继续观看视频</div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount, onMounted, onBeforeUnmount } from 'vue';
import { useRouter, useRoute } from "vue-router";
import {
  postStudyIntroduceVideoStartTime,
  getStudyIntroduceVideoNewStartTime,
  postStudyIntroduceVideoEndTime,
  postStudyIntroduceVideoFinish,
  postAudioFile,
} from "@/api/informed";
import MyVideoPlay from "@/components/MyVideoPlay.vue";
import {
  // IcfObjInter,
  postStudyIntroduceVideoFinishInter,
} from "@/types/welcomeToJoinUs";
import { returnPatientStatusUrl } from "@/utils/debounce";
import { useStore } from "vuex";
// 必须引入的核心
import Recorder from "recorder-core";
// 引入mp3格式支持文件；如果需要多个格式支持，把这些格式的编码引擎js文件放到后面统统引入进来即可
import "recorder-core/src/engine/mp3";
import "recorder-core/src/engine/mp3-engine";
// 录制wav格式的用这一句就行
// import 'recorder-core/src/engine/wav'
// 可选的插件支持项，这个是波形可视化插件
import "recorder-core/src/extensions/waveview";
import { deepClone, formatSeconds } from '@trialdata/common-fun-css';
import { Toast, Notify } from 'vant';
import { getPatientStatus } from '@/api/user';
// ts import 提示：npm包内已自带了.d.ts声明文件（不过是any类型）
import { useHead } from '@unhead/vue'

export default defineComponent({
  name: "InformedVideo", // 知情视频
  components: {
    MyVideoPlay,
  },
  setup() {
    const router = useRouter();
    const route = useRoute();
    const store = useStore();
    // const baseuUrl = window.TrialData_UI_Config.VUE_APP_API_BASE_URL
    const icfStatementIdData = {
      icfStatementID:
        store.state?.userInformation?.icfStatementID ||
        store.state?.userInformation?.icfStementId,
    };
    const state: any = reactive({
      // 是否结束
      isEnd: true,
      // 是否授权
      isAuthorization: false,
      // 全部视频
      allVideoObj: [],
      checkVideoIndex: 0,
      navTitle: '视频标题',
      InformedVideoRef: null,
      Icf: null,
      informedChecked: false,
      informedTimeout: 0,
      informedTimeoutText: "",
      IcfObj: {
        videoCoverUrl: "",
        videoUrl: "",
        studyVideoViewSeconds: 0,
        isCompletedVideo: 0,
      },
      videoPlayer: null,
      playerFlag: true,
      timeInterval: null,
      startId: '',
      // 获取状态的请求
      getPatientStatusFun: () => {
        /*
        const icfStatementIdData = {
          icfStatementID:
            store.state?.userInformation?.icfStatementID ||
            store.state?.userInformation?.icfStementId,
        }
        */
        return new Promise(async (resolve, reject) => {
          try {
            const patientStatus: any = await getPatientStatus(icfStatementIdData)
            if (patientStatus?.patientICFStatus === 404) {
              Notify({ type: 'danger', message: '已转线下知情' })
              router.replace(returnPatientStatusUrl(patientStatus));
              return
            }
            resolve(1)
          } catch (e) {
            reject(e)
          }
        })
      },
      getPatientStatusLoadingFlag: false,
      // 下一段
      nextInformedChecked: async (num) => {
        if (state.getPatientStatusLoadingFlag ||
          (num > 0 && state.informedTimeout > 0)) {
          return
        }
        state.getPatientStatusLoadingFlag = true
        await state.getPatientStatusFun();
        if (!state?.playerFlag) {
          state.endTime()
        }
        clearInterval(state.timeInterval)
        const { informedTimeout } = state
        state.allVideoObj[state.checkVideoIndex].studyVideoViewSeconds = informedTimeout
        state.playerFlag = true;
        state.InformedVideoRef.pause()
        state.IcfObj.videoUrl = ''
        state.checkVideoIndex = state.checkVideoIndex + num
        setTimeout(() => {
          state.setIcfObj(state.allVideoObj[state.checkVideoIndex])
          state.getPatientStatusLoadingFlag = false
        }, 200)
      },
      // 下一步 - 流转到下个状态
      informedCheckedSave: async () => {
        if (state.informedTimeout > 0) {
          return
        }
        await state.getPatientStatusFun();
        postStudyIntroduceVideoFinish(icfStatementIdData).then((rest) => {
          const res = rest as postStudyIntroduceVideoFinishInter;
          if (res) {
            router.replace({
              path: returnPatientStatusUrl(res),
              query: {
                isAuthorization: state.isAuthorization
              }
            });
          }
        });
      },
      // 视频播放 onPlayerPlay: (player) => {}
      onPlayerPlay: async () => {
        //  &&
        //   !state.IcfObj?.isCompletedVideo &&
        //   state?.informedTimeout
        await state.getPatientStatusFun();
        if (
          state.playerFlag
        ) {
          state.playerFlag = false;
          if (!state.IcfObj?.isCompletedVideo && state?.informedTimeout) {
            state.startTime();
            state.timeInterval = setInterval(() => {
              state.informedTimeout--;
              state.informedTimeoutText = formatSeconds(state.informedTimeout);
              if (state.informedTimeout <= 0) {
                clearInterval(state.timeInterval);
                state.informedTimeout = 0;
                state.allVideoObj[state.checkVideoIndex].studyVideoViewSeconds = 0
                // const data = {
                //   icfStatementID:
                //     store.state?.userInformation?.icfStatementID ||
                //     store.state?.userInformation?.icfStementId,
                //   title: state.IcfObj.title,
                //   businessId: state.IcfObj.id,
                //   businessStartId: state.startId,
                // }
                // postStudyIntroduceVideoEndTime(data).then((res) => {
                //   state.startTime()
                // });
                state.endTime()
                state.startTime()
              }
            }, 1000);
          } else {
            state.startTime();
          }
        }
      },
      startTime: () => {
        const data = {
          icfStatementID:
            store.state?.userInformation?.icfStatementID ||
            store.state?.userInformation?.icfStementId,
          title: state.IcfObj.title,
          businessId: state.IcfObj.id,
        }
        postStudyIntroduceVideoStartTime(data).then((res) => {
          state.startId = res;
        });
      },
      endTime: () => {
        const data = {
          icfStatementID:
            store.state?.userInformation?.icfStatementID ||
            store.state?.userInformation?.icfStementId,
          title: state.IcfObj.title,
          businessId: state.IcfObj.id,
          businessStartId: state.startId,
        }
        if (state.startId) {
          postStudyIntroduceVideoEndTime(data);
          state.startId = ''
        }
      },
      // 视频播完回调onPlayerEnded: (e) => {}
      onPlayerEnded: async () => {
        await state.getPatientStatusFun();
        state.playerFlag = true
        state.endTime()
      },
      // 播放状态改变回调
      playerStateChanged: (e) => {
        if (e?.canplaythrough) {
          // state.videoPlayer.player.play()
        }
      },
      // 打开语音
      startFlag: false,
      endRef: null,
      // x移入时
      isActive: false,
      // 测试录音功能
      cslyFlag: false,
      text: "",
      hasPermissionText: "",
      moveText: "",
      // 录音相关
      recwave: null,
      rec: null,
      wave: null,
      recCleRecorderEndFlag: 0,
      // 移入
      recMove: (e) => {
        const clientX = e.changedTouches[0].clientX
        const clientY = e.changedTouches[0].clientY
        const x = window.outerWidth / 2
        const y = window.outerHeight - window.outerHeight * 0.21
        const distanceFromCenterX = Math.abs(clientX - x);
        const distanceFromCenterY = clientY - y;
        // console.log(x , distanceFromCenterX, y, clientY, distanceFromCenterY);
        if (distanceFromCenterX <= 25 && distanceFromCenterY <= 0) {
          state.isActive = true
        } else {
          state.isActive = false
        }
      },
      // 创建录音对象
      recCleRecorder: () => {
        /*
        type: 'wav', // 设置音频格式为 WAV
        bitRate: 1411.2, // 设置比特率为 1411.2 kbps（CD 音质）
        sampleRate: 44100, // 设置采样率为 44100 Hz
        bitDepth: 16, // 设置位深度为 16 bit
        channels: 2, // 设置声道为立体声
        */
        state.rec = Recorder({
          type: "mp3", //录音格式，可以换成wav等其他格式
          sampleRate: 16000, //录音的采样率，越大细节越丰富越细腻
          bitRate: 16, //录音的比特率，越大音质越好
          onProcess: (
            buffers,
            powerLevel,
            bufferDuration,
            bufferSampleRate,
            newBufferIdx,
            asyncEnd
          ) => {
            if (bufferDuration && bufferDuration / 1000 >= 59) {
              state.recStop();
              // state.text = "录音时长"+ bufferDuration;
            }
            //录音实时回调，大约1秒调用12次本回调
            //可实时绘制波形，实时上传（发送）数据
            if (state.wave)
              state.wave.input(
                buffers[buffers.length - 1],
                powerLevel,
                bufferSampleRate
              );
          },
        });
      },
      recCle: () => {
        state.rec.open(
          () => {
            if (state.recwave) {
              // 创建音频可视化图形绘制对象
              state.wave = Recorder.WaveView({ elem: state.recwave });
            }
            // state.hasPermissionText += '创建'
            if (state.isAuthorization) {
              state.recStart();
            } else {
              state.isAuthorization = true
            }
          },
          (msg, isUserNotAllow) => {
            // 用户拒绝了录音权限，或者浏览器不支持录音
            // state.hasPermissionText += '-拒绝了录音权限'
            // state.isAuthorization = false
          }
        );
      },
      // 按下提问
      recOpen: async (e) => {
        await state.getPatientStatusFun();
        // 获取是否授权
        if (!state.isAuthorization && navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
          navigator.mediaDevices
            .getUserMedia({ audio: true })
            .then((stream) => {
              state.isAuthorization = true
              // console.log('state.isAuthorization',state.isAuthorization);
            })
            .catch((error) => {
              state.isAuthorization = false
              // console.log('state.isAuthorization',state.isAuthorization);
            });
        }
        if (state.isAuthorization && state.isEnd) {
          state.isEnd = false
          state.recCle()
        }
        // 判断是否有权限
        // if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        //   navigator.mediaDevices
        //     .getUserMedia({ audio: true })
        //     .then((stream) => {
        //       state.hasPermissionText += '-有权限'
        //       // state.recStart();
        //       // 授权和开启录音要一起执行
        //       setTimeout(() => {
        //         state.recStart();
        //       }, 1000);
        //     })
        //     .catch((error) => {
        //       // 打开录音，获得权限
        //       state.recCle()
        //       state.hasPermissionText += '无权限'
        //       // console.error("获取麦克风权限失败:", error);
        //     });
        // }
      },
      // 开始录音
      recStart: () => {
        if (!state.rec) {
          // state.hasPermissionText += '-未打开录音'
          return;
        }
        if (state.rec.start) {
          state.InformedVideoRef.pause()
          state.rec.start();
          state.startFlag = true
          // state.hasPermissionText = "已开始录音" // +state.rec.start;
        }
        //  else {
        //   state.hasPermissionText += '-打失败开录音'+state.rec
        // }
      },
      recStopClick: () => {
        state.startFlag = false
        if (!state.rec) {
          // console.error("未打开录音");
          state.isEnd = true
          return;
        }
        state.isActive = true
        state.rec.stop(
          (blob, duration) => {
            // 简单利用URL生成本地文件地址，此地址只能本地使用，比如赋值给audio.src进行播放，赋值给a.href然后a.click()进行下载（a需提供download="xxx.mp3"属性）
            // var localUrl = (window.URL || webkitURL).createObjectURL(blob);
            // console.log("录音成功", blob, localUrl, "时长:" + duration + "ms");
            state.rec.close(); // 关闭录音，释放录音资源，当然可以不释放，后面可以连续调用start
            state.rec = null;
            state.recCleRecorder();
            state.isActive = false
            state.isEnd = true
          },
          (err) => {
            // console.error("结束录音出错：" + err);
            state.rec.close(); //关闭录音，释放录音资源，当然可以不释放，后面可以连续调用start
            state.rec = null;
            state.recCleRecorder();
            state.isEnd = true
            state.isActive = false
          }
        );
      },
      // 停止录音
      recStop: (e) => {
        state.startFlag = false
        let clientX = e.changedTouches[0].clientX
        let clientY = e.changedTouches[0].clientY
        let x = window.outerWidth / 2
        let y = window.outerHeight - window.outerHeight * 0.21
        let distanceFromCenterX = Math.abs(clientX - x);
        let distanceFromCenterY = clientY - y;
        // x , distanceFromCenterX, 
        // console.log(y, clientY, distanceFromCenterY);
        if (!state.rec) {
          // console.error("未打开录音");
          state.isEnd = true
          return;
        }
        // state.text = "松开2";
        state.rec.stop(
          (blob, duration) => {
            // 简单利用URL生成本地文件地址，此地址只能本地使用，比如赋值给audio.src进行播放，赋值给a.href然后a.click()进行下载（a需提供download="xxx.mp3"属性）
            // var localUrl = (window.URL || webkitURL).createObjectURL(blob);
            // console.log("录音成功", blob, localUrl, "时长:" + duration + "ms");
            state.rec.close(); // 关闭录音，释放录音资源，当然可以不释放，后面可以连续调用start
            state.rec = null;
            state.recCleRecorder();
            if (distanceFromCenterX <= 25 && distanceFromCenterY <= 0) {
              state.isActive = false
              state.isEnd = true
              return
            }
            // state.text = "录音时长" + duration;
            const intDuration = duration / 1000
            if (intDuration < 60 && intDuration >= 1) {
              // blob就是我们要的录音文件对象，可以上传，或者本地播放
              state.recBlob = blob;
              state.upload(blob, intDuration); // 把blob文件上传到服务器
            } else if (intDuration < 1) {
              Toast.fail("录音时间太短");
            }
            state.isEnd = true
          },
          (err) => {
            // console.error("结束录音出错：" + err);
            state.rec.close(); //关闭录音，释放录音资源，当然可以不释放，后面可以连续调用start
            state.rec = null;
            state.recCleRecorder();
            state.isEnd = true
          }
        );
      },
      saveEndFlag: false,
      // 上传
      upload(blob, intDuration) {
        // state.moveText += '1upload1'
        postAudioFile(state.IcfObj.icfStatementId, state.IcfObj.id, parseInt(intDuration), blob).then(() => {
          // Toast.success('已提交，后续会有研究人员为您解答，您可以继续观看视频');
          state.saveEndFlag = true
          setTimeout(() => {
            state.saveEndFlag = false
          }, 3000);
        })
      },
      // 本地播放录音试听
      recPlay: () => {
        //本地播放录音试听，可以直接用URL把blob转换成本地播放地址，用audio进行播放
        var localUrl = URL.createObjectURL(state.recBlob);
        var audio = document.createElement("audio");
        audio.controls = true;
        document.body.appendChild(audio);
        audio.src = localUrl;
        audio.play(); //这样就能播放了
        //注意不用了时需要revokeObjectURL，否则霸占内存
        setTimeout(function () {
          URL.revokeObjectURL(audio.src);
        }, 5000);
      },
      // 设置为当前视频
      setIcfObj: (res) => {
        state.IcfObj = deepClone(res);
        if (res.videoUrl) {
          if (res?.studyVideoViewSeconds && !res?.isCompletedVideo) {
            state.informedTimeout = res.studyVideoViewSeconds;
            state.informedTimeoutText = formatSeconds(state.informedTimeout);
          } else {
            state.informedTimeout = 0
          }
        }
      },
      visibilitychange: () => {
        if (document.hidden) { // 当页面被切换至后台时
          state.startFlag = false
          if (!state.rec) {
            state.isEnd = true
            return;
          }
          state.rec.stop(
            (blob, duration) => {
              // 简单利用URL生成本地文件地址，此地址只能本地使用，比如赋值给audio.src进行播放，赋值给a.href然后a.click()进行下载（a需提供download="xxx.mp3"属性）
              // var localUrl = (window.URL || webkitURL).createObjectURL(blob);
              // console.log("录音成功", blob, localUrl, "时长:" + duration + "ms");
              state.rec.close(); // 关闭录音，释放录音资源，当然可以不释放，后面可以连续调用start
              state.rec = null;
              state.recCleRecorder();
              state.isEnd = true
            },
            (err) => {
              state.rec.close();
              state.rec = null;
              state.recCleRecorder();
              state.isEnd = true
            }
          );
        }
        // else {
        //   // 当页面重新显示在前台时
        //   Toast("页面已经切换回前台");
        //   // 这里可以添加相关处理逻辑
        // }
      }
    });
    onBeforeMount(() => {
      if (route.query?.isAuthorization) {
        state.isAuthorization = true
      }
      // 禁用复制选中
      document.body.style.webkitUserSelect = "none";
      document.body.style.userSelect = "none";
      document.body.style.overscrollBehavior = 'none';
      document.body.addEventListener('scroll', function (event) {
        event._isScroller = true;
      }, { passive: true }
      );
      document.body.style.overflow = 'hidden';
      // 锁定页面当前位置
      document.body.style.position = 'fixed';
      document.body.style.width = '100%';
      state.recCleRecorder();

      getStudyIntroduceVideoNewStartTime({
        ICFStatementId:
          store.state?.userInformation?.icfStatementID ||
          store.state?.userInformation?.icfStementId,
      }).then((res: any) => {
        state.allVideoObj = res
        if (res?.length) {
          state.setIcfObj(res[0])
        }
        setTimeout(() => {
          state.recCleRecorderEndFlag += 1
        }, 100)
      })

      // 暂停 state.InformedVideoRef.pause()
      // 重播
      // state.IcfObj.videoUrl = ''
      // state.InformedVideoRef.toReplay()
    });
    onMounted(() => {
      document.addEventListener('visibilitychange', state.visibilitychange);
    })
    onBeforeUnmount(() => {
      document.body.style.position = 'static';
      document.removeEventListener('visibilitychange', state.visibilitychange);
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang="less">
/* 穿透全局样式 + 增强优先级 */
.no-safe-area-page :deep(body) {
  padding-bottom: 0 !important;
  margin-bottom: 0 !important;
  height: auto !important;
}
.no-safe-area-page :deep(#app) {
  min-height: 100vh !important;
  padding-bottom: 0 !important;
  box-sizing: border-box;
}
.no-safe-area-page {
  --safe-area-inset-bottom: 0px !important;
}

.syd-bg {
  background: #dadadc;
}
.guanbiyuyin-icon::after {
  content: "x";
  position: relative;
  top: -0.02rem;
}
img {
  -webkit-touch-callout: none;/* 禁止长按弹出默认菜单 */
  user-select: none;/* 禁止选择图片 */
}
.bg-yuyin {
  background: url("@/assets/baby/welcomeToJoinUs/yuyin.svg") no-repeat;
  background-size: 100% 100%;
}
.InformedVideo-container {
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(180deg, #eaeffa 70%, #fff 100%);
  color: #555;
  font-size: 0.12rem;
  .InformedVideo-body {
    width: 100%;
    height: calc(100vh - 46px - 0.78rem);
    // overflow: auto;
    :deep(.van-button__text) {
      font-size: 0.13rem;
    }
    .InformedVideo-module {
      max-width: 100%;
      max-height: 100%;
      overflow: auto;
      :deep(.vjs_video_3-dimensions.vjs-fluid) {
        padding-top: 173.77777777777777%;
      }
    }
  }
}
</style>
